class VersionPlugin {
    constructor(version){
        this.version = version;
    }
    apply(compiler){
        /* 
            tapable钩子
            compile: 编译开始时调用
            compilation: 创建新的编译对象时调用
            emit: 生成资源到输出目录
            done: 编译完成后调用
            钩子方法 (name, (compilation, callback) => {})
            tap
            tapAsync
            tapPromise
        */
        compiler.hooks.emit.tapAsync('VersionPlugin', (compilation, callback) => {
            let fileContent = JSON.stringify({
                version: this.version
            })
            compilation.assets['version.json'] = {
                source: function() {
                    return fileContent;
                },
                size: function() {
                    return fileContent.length;
                }
            }
            callback();
        });
    }
}

/* 
// options {filename, content, contentHandler}, 可以做父类，但是这个父类的参数有点多，pluginName可以在子类中自定义，但还要保证父类的名字是固定的
const defaultOptions = {filename: 'filename', content: 'file content', contentHandler: content=>content }
class GenerateFilePlugin {
    constructor(options){
        options = Object.assign(options, defaultOptions)
        this.filename = options.filename;
        this.content = options.content;
        this.contentHandler = options.contentHandler;
        const ctorName = this.constructor.name;
        // 是父类 ? 固定名称 : (有参数 ? 使用参数 : 使用类名), 或许可以直接使用类名
        this.pluginName = ctorName === 'GenerateFilePlugin' ? 'GenerateFilePlugin' : options.pluginName || ctorName;
    }
    apply(compiler){
        compiler.hooks.emit.tapAsync(this.pluginName, (compilation, cb) => {
            compilation.assets[this.filename] = {
                source: () => this.contentHandler(this.content),
                size: () => this.content.length
            }
            cb();
        })
    }
}
class VersionPlugin extends GenerateFilePlugin {
    constructor(content) {
        super({filename: 'version.json', content: content, contentHandler: JSON.stringify, pluginName: 'VersionPlugin'})
    }
}

*/
module.exports = VersionPlugin;

// 保证父类
class Parent {
    constructor(){
    }
    func(){
        console.log(this.pluginName)
    }
    get pluginName(){
        return "Parent";
    }
}
class Child extends Parent {
    constructor(){
        super();
    }
    get pluginName(){
        return "Child";
    }
}