{"arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": false, "tabWidth": 4, "trailingComma": "es5", "useTabs": false, "vueIndentScriptAndStyle": false, "filepath": "c:\\working\\tartan-platform-portal\\src\\views\\dashboard\\index.vue", "parser": "vue"}