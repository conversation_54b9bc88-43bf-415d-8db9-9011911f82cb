<template>
    <el-autocomplete
        class="inline-input"
        v-model="wellNumber"
        :fetch-suggestions="querySearch"
        placeholder="请输入内容"
        @select="handleSelect"
        value-key="wellNumber"
        :size="size"
        :clearable="clearable"
    ></el-autocomplete>
</template>
<script lang="ts">
import { apiGetWellListWithJobs } from "@/api/wellInfo";
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({
    name: "FuzzyWell",
})
export default class extends Vue {
    private wellNumber = "";
    @Prop({ default: "medium" }) private size!: String;
    @Prop({ default: true }) private clearable!: Boolean;
    private querySearch(wellNumberKey = "", cb: any) {
        // const form = new FormData();
        // form.append("wellNumberKey", wellNumberKey);
        apiGetWellListWithJobs({wellNumberKey}).then((res) => {
            cb(res.data?.data || []);
        });
    }
    private handleSelect(item: any) {
        this.$emit('onSelectWellNumber', item);
    }
}
</script>

<style lang="scss" scoped>
</style>
