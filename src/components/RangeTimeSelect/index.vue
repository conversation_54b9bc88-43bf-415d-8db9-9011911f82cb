<!-- HH:mm的时间范围选择器组件, 间隔为30分钟, 只供工时填报使用, 没做扩展 -->
<!-- 如果开始和结束时间不同时存在, emit为null, 这个行为和is-range的time-picker一样 -->
<template>
    <div style="width: 100%;">
        <el-time-select
            style="width: calc(50% - 6px);"
            el-time-select
            placeholder="起始时间"
            v-model="startTime"
            @change="onChange"
            :picker-options="{
                start:'00:00',
                step: '00:30',
                end: '23:30',
                maxTime: endTime
            }">
        </el-time-select>
        <div style="display: inline-block;width: 11px; text-align: center;">-</div>
        <el-time-select
            style="width: calc(50% - 6px);"
            placeholder="结束时间"
            v-model="endTime"
            @change="onChange"
            :picker-options="{
                start:'00:00',
                step: '00:30',
                end: '23:30',
                minTime: startTime
            }">
        </el-time-select>
    </div>
</template>
<script>
export default {
    name: 'RangeTimeSelect',
    props: {
        value: {
            type: Array,
            default: null
        }
    },
    data(){
        return {
            startTime: null,
            endTime: null,
            isEmitNull: false
        }
    },
    watch:{
        /* 
            watch           emit            显示
            [s, e]          NA              [s, e]
            [s, null]       null            [s, null] 
            [null, e]       null            [null, e]
            [null, null]    null            [null, null]
            null(value)     NA              [null, null]    ----
            null(emit)      NA              ignore          ---- 这两个null行为区别在于是主动赋值的还是间接emit出的
            
            除了null以外的其他情况, onChange和直接赋值的行为是一致的
        */
        value: {
            immediate: true,
            handler(val){
                if(Array.isArray(val)){
                    this.startTime = val[0] || null;
                    this.endTime = val[1] || null;
                    if(!(this.startTime && this.endTime)){
                        // 主动赋值或用户操作导致的非标准时间范围数组要emit为null
                        this.isEmitNull = true;
                        this.$emit('input', null)
                    }
                }else{
                    // 直接赋值的null要清空两个输入框, 但如果是从上面中emit出来的则静默
                    if(!this.isEmitNull){
                        this.startTime = null;
                        this.endTime = null;
                        this.isEmitNull = false;
                    }
                }
            }
        }
    },
    methods: {
        /* 
            onChange -> onChange_emit -> value_watch    watch_emit      显示                
            [s, e]                                      NA              [s, e]
            [s, null]                                   null            [s, null]
            [null, e]                                   null            [null, e]
            [null, null]                                null            [null, null]
        */
        onChange(){
            // 这里会稳定触发value的watch, 虽然这里不会直接emit出null, 但有可能会经过watch后间接emit出null
            this.$emit('input', [this.startTime, this.endTime])
            this.$emit('change', this.startTime && this.endTime ? [this.startTime, this.endTime] : null)
        }
    }
}
</script>