<template>
    <div v-loading="fetchLoading">
        <div class="file-list" v-if="fileList.length">
            <div class="file-item" v-for="item in fileList" :key="item.massId">
                <span :title="item.massFileName" class="file-title">
                    {{ item.massFileName }}
                </span>
                <i v-if="getPermission(previewPermission)" @click="onPreviewFile(item.massId)" class="el-icon-view oper-icon"></i>
                <a :href="item.massFilePath"  v-if="getPermission(downloadPermision)">
                    <i class="el-icon-download oper-icon"></i>
                </a>
                <i v-if="getPermission(deletePermision)" @click="onDeleteFile(item.massId)" class="el-icon-delete oper-icon"></i>
            </div>
        </div>
        <div v-else-if="emptyText&&!fetchLoading">
            {{ emptyText }}
        </div>
        <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadFile"
            >
            <div ref="ghostFileUploader" style="width:0;height:0"></div>
        </el-upload>
    </div>
</template>
<script>
import { apiDeleteMassFile, apiGetMassFileList, apiPreviewMassFile, apiUploadMassFile } from "@/api/file";
const previewPermission =  1;
const downloadPermision =  1 << 1;
const deletePermision   =  1 << 2;
export default {
    name: "FileManage",
    props: {
        bussinessType: {
            type: String,
            required: true,
        },
        bussinessId: {
            type: [String, Number],
        },
        mode: {
            type: String,
            default: 'READ',
        },
        emptyText: String
    },
    data(){
        return {
            previewPermission,
            downloadPermision,
            deletePermision,
            fileList: [],
            isEdit: false,
            fetchLoading: false
        }
    },
    computed: {
        permissionCode(){
            switch(this.mode){
                case 'READ':
                    return previewPermission | downloadPermision;
                case 'EDIT':
                    return previewPermission | downloadPermision | deletePermision;
                default:
                    return previewPermission | downloadPermision;
            }
        }
    },
    watch: {
        bussinessId: {
            immediate: true,
            async handler(n){
                if(n){
                    this.fetchLoading = true;
                    try{
                        await this.getFileList();
                    }finally{
                        this.fetchLoading = false;
                    }
                }
            }
        }
    },
    methods: {
        getPermission(code){
            return this.permissionCode & code
        },
        onDeleteFile(massId){
            this.$confirm("确认删除该文件?", "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(()=>{
                const form = new FormData();
                form.append('type', 'MASSID');
                form.append('idList', [massId].toString())
                apiDeleteMassFile(form).then(()=>{
                    this.getFileList()
                    this.$message.success('操作成功！')
                })
            })
        },
        onUploadFile(file){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            const form = new FormData();
            form.append('id', String(this.bussinessId));
            form.append('file', file);
            form.append('fileType', this.bussinessType);
            apiUploadMassFile(form).then(()=>{
                this.getFileList();
                this.$message.success('操作成功！')
            }).finally(()=>{
                loading.close();
            })
        },
        getFileList(){
            return apiGetMassFileList({id: this.bussinessId, massFileType: this.bussinessType},{current: 1, size: 9999}).then(res=>{
                this.fileList = res.data?.data?.records || [];
            })
        },
        onPreviewFile(massId){
            apiPreviewMassFile({massId}).then(res=>{
                const previewPath = res.data.data?.previewPath;
                if(previewPath){
                    window.open(previewPath);
                }
            })
        },
        onUpload(){
            this.$refs.ghostFileUploader.click();
        }
    }
    
}
</script>

<style lang="scss" scoped>
.file-list{
    
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 380px;
        margin-bottom: 10px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>