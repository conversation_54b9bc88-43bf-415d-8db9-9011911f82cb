<template>
    <el-select
        v-model="modelValue"
        :remote-method="remoteMethod"
        v-bind="computedAttrs"
        v-on="$listeners"
        style="width: 100%;"
    >
        <el-option
            v-for="item in filterUserList"
            :key="item.memberId"
            :label="item.name"
            :value="item.memberId"
        >
        </el-option>
  </el-select>
</template>
<script>
import { apiGetMemberList } from '@/api/users';
import { apiGetMemberListByPM } from '@/api/workhours';
const defaultProps = {
    filterable: true,
    remote: true,
    'reserve-keyword': true,
    clearable: true,
    placeholder:"请输入关键词"
}
const searchApiMap = {
    ALL: {
        searchApi: apiGetMemberList,
        searchKey: 'name',
        dataGetter: data => data.memberInfoList
    },
    PM: {
        searchApi: apiGetMemberListByPM,
        searchKey: 'memberName',
        dataGetter: data => data
    },
}
export default {
    name: "UserSelect",
    props: {
        initList: {
            type: Array,
            default: ()=>[]
        },
        value: {
        },
        type:{
            default: "ALL"
        }
    },
    data(){
        return {
            filterUserList: [],
            modelValue: null
        }
    },
    computed: {
        computedAttrs() {
            return {
                ...defaultProps,
                ...this.$attrs
            }
        },
    },
    watch:{
        modelValue:{
            deep: true,
            handler(n){
                this.$emit("input",n)
            }
        },
        initList:{
            deep: true,
            immediate: true,
            handler(n){
                this.filterUserList = n;
            }
        },
        value: {
            deep: true,
            handler(n){
                this.modelValue = n
            }
        }
    },
    mounted(){
        this.modelValue = this.value;
        if(!this.initList.length){
            this.remoteMethod("")
        }
    },
    methods: {
        remoteMethod(qs){
            const { searchApi, dataGetter, searchKey } = searchApiMap[this.type]
            searchApi({[searchKey]:qs}).then(res=>{
                const data = res.data.data || {};
                this.filterUserList = dataGetter(data) || []
            })
        }
    }
}
</script>