<template>
    <div class="gpc-image-upload-container">
        <el-upload
            class="avatar-uploader"
            action="String"
            :show-file-list="false"
            :before-upload="onValidateFile"
            accept="image/*"
        >
            <i
                v-if="showUplaoder"
                class="el-icon-plus avatar-uploader-icon"
            ></i>
            <div v-else class="img-container avatar-uploader-icon">
                <img id="img" height="100%" width="100%" :src="src" alt="" />
                <div class="img-icon-container">
                    <i
                        @click.stop.prevent="showImageDialog = true"
                        class="el-icon-zoom-in img-icon"
                    ></i>
                    <i
                        @click.stop.prevent="onDeleteImage"
                        class="el-icon-delete img-icon"
                    ></i>
                </div>
            </div>
        </el-upload>
        <el-dialog
            append-to-body
            width="600px"
            custom-class="gpc-upload-dialog"
            :visible.sync="showImageDialog"
        >
            <img width="100%" :src="src" alt="" />
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { apiUploadFile } from "@/api/file";
@Component({
    name: "UploadImage",
})
export default class extends Vue {
    @Prop({ default: "" }) private src!: string;
    private showImageDialog: boolean = false;
    get showUplaoder() {
        return !this.src;
    }
    private onValidateFile(file: File) {
        let form = new FormData();
        form.append("file", file);
        apiUploadFile(form).then((res) => {
            this.$emit("uploadImageSuccess", res.data.data);
        });
    }
    private onDeleteImage() {
        this.$emit("deleteImageSuccess");
    }
}
</script>
<style lang="scss" scoped>
.gpc-image-upload-container {
    .img-container {
        position: relative;
        &:hover {
            img {
                filter: brightness(0.2);
            }
            .img-icon-container {
                opacity: 1;
            }
        }
        .img-icon-container {
            width: 100%;
            height: 20px;
            position: absolute;
            top: 50%;
            display: flex;
            justify-content: center;
            opacity: 0;
            .img-icon {
                margin-left: 20px;
                color: white;
                font-size: 24px;
                cursor: pointer;
            }
        }
    }
}
</style>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 200px;
  line-height: 200px;
  text-align: center;
}
</style>
