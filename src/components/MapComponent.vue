<template>
  <div class="map-component">
    <!-- 地图容器 -->
    <div id="map-container"></div>

    <!-- 带选项卡的控制面板 -->
    <el-card class="map-controls" shadow="never">
      <div class="map-tabs-container">
        <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
          <el-tab-pane
            v-for="(label, key) in jobStatusMap"
            :key="key"
            :name="key"
          >
            <span slot="label">
              {{ label }} <span v-if="getStatusCount(key) > 0">({{ getStatusCount(key) }})</span>
            </span>
             <!-- 选项卡现在主要用于控制地图过滤 -->
          </el-tab-pane>
        </el-tabs>
        <div style="display: flex; justify-content: center; align-items: center; margin: 5px 0; width: 100%;">
          <el-button
            type="text"
            class="job-list-toggle"
            @click="toggleJobList"
          >
            <i :class="[showJobList ? 'el-icon-arrow-up' : 'el-icon-arrow-down', {'active': showJobList}]"></i>
          </el-button>
        </div>
      </div>
       <!-- 作业列表，只在点击作业列表按钮时显示 -->
       <div v-if="!isLoading && filteredLocations.length > 0 && showJobList" class="job-list">
          <!-- 移除作业列表标题 -->
          <div class="job-list-content">
            <div
              v-for="(job, index) in filteredLocations"
              :key="job.id || index"
              class="job-item"
              :class="{ 'selected': isJobSelected(job) }"
              @click="locateToMarker(job)"
            >
              <div class="job-well">井号: {{ job.name || '' }}</div>
              <div class="job-number">作业号: <a href="javascript:void(0)" @click="(e) => goToJobDetail(job, e)" class="job-link">{{ job.jobNumber || '未知' }}</a></div>
              <div class="job-date">创建于: {{ formatShortDate(job.createTime) }}</div>
            </div>
          </div>
       </div>
        <div v-if="isLoading" class="marker-details">
           <p>正在加载数据...</p>
       </div>
    </el-card>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader';
// 假设 API 工具类已存在并已配置
import { getJobMapInfoList } from '@/api/job.ts'; // 如果需要，调整路径

// 安全配置只需要在全局设置一次，如果已在其他地方设置，可以移除这部分
if (!window._AMapSecurityConfig) {
  window._AMapSecurityConfig = {
    securityJsCode: 'edc32b937a046ae35a87b155942be84a',
  };
}

// 使用组件级别的变量，而不是全局变量
export default {
  name: 'MapComponent',
  data() {
    return {
      map: null,
      allMarkers: [],
      infoWindow: null,
      AMap: null, // AMap 对象
      allJobLocations: [],
      activeTab: 'inProgress', // 默认显示进行中选项卡
      jobStatusMap: {
        inProgress: '进行中', // 状态 0
        completed: '已完工', // 状态 1
      },
      selectedLocationInfo: null,
      isLoading: false,
      currentStatus: 0, // 当前选中的状态，默认为进行中
      showJobList: false, // 是否显示作业列表
    };
  },
  computed: {
    filteredLocations() {
      // 根据当前选中的状态过滤作业
      const targetStatus = this.activeTab === 'inProgress' ? 0 : 1;
      return this.allJobLocations.filter(loc => loc.jobStatus === targetStatus);
    },
  },
  watch: {
     filteredLocations() {
        // 数据/选项卡更改后渲染标记点
        this.$nextTick(() => {
           this.renderMarkers(this.filteredLocations);
        });
     }
  },
  mounted() {
    this.initMap();

    // 添加全局函数供信息窗口使用
    window.goToJobDetail = (id) => {
      if (id) {
        this.$router.push(`/daily/detail?jobid=${id}`);
      } else {
        this.$message.warning('无法跳转：作业ID不存在');
      }
    };

    // 添加窗口大小变化事件监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    this.destroyMap();

    // 清除全局函数
    window.goToJobDetail = null;

    // 移除窗口大小变化事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.map) {
        this.map.resize();
      }
    },
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        // 考虑使用像 dayjs 这样的库来进行更好的格式化/时区处理
        return date.toLocaleString(); // 使用本地化格式
      } catch (e) {
        console.warn("日期格式化失败:", dateString)
        return dateString; // 回退到原始字符串
      }
    },
    formatShortDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString(); // 只返回日期部分，不包含时间
      } catch (e) {
        console.warn("日期格式化失败:", dateString)
        return dateString; // 回退到原始字符串
      }
    },
    locateToMarker(job) {
      // 设置选中的作业信息，以显示详情
      this.selectedLocationInfo = job;

      // 如果没有有效的位置数据，则只显示详情，不进行地图定位
      if (!job || !job.position || !Array.isArray(job.position) || job.position.length !== 2 ||
          typeof job.position[0] !== 'number' || typeof job.position[1] !== 'number' ||
          isNaN(job.position[0]) || isNaN(job.position[1])) {
        console.warn('无法定位到标记点，位置数据无效:', job);
        this.$message.info(`作业 ${job.jobNumber} 没有有效的坐标信息，无法在地图上定位。`);
        return;
      }

      // 在标记点数组中查找对应的标记点，使用位置坐标而不是id
      const targetMarker = this.allMarkers.find(marker => {
        const markerData = marker.getExtData();
        if (!markerData || !markerData.position || !Array.isArray(markerData.position) || markerData.position.length !== 2) return false;

        // 使用容差比较坐标是否相同
        const tolerance = 0.00001; // 约为1米的容差
        return Math.abs(markerData.position[0] - job.position[0]) < tolerance &&
               Math.abs(markerData.position[1] - job.position[1]) < tolerance;
      });

      if (targetMarker) {
        // 如果找到了标记点，则定位到该标记点，但不放大
        this.map.setCenter(targetMarker.getPosition());
        // 不设置缩放级别，保持当前视图

        // 模拟点击标记点，显示信息窗口
        this.showInfoWindow(targetMarker);

        // 更新标记点图标为选中状态
        this.updateMarkerIcon(targetMarker, true);
      } else {
        // 如果没有找到标记点，则直接使用位置数据定位
        this.map.setCenter(job.position);
        // 不设置缩放级别，保持当前视图
      }
    },
    async initMap() {
      this.isLoading = true; // 开始加载指示器
      try {
        // 将加载的 AMap 对象赋值给实例变量
        this.AMap = await AMapLoader.load({
          key: 'e45e326226fffa633f5b14a2b168c9c9', // 使用您的实际密钥
          version: '2.0',
          plugins: ['AMap.Scale', 'AMap.InfoWindow'],
        });

        this.map = new this.AMap.Map('map-container', {
          viewMode: '2D',
          zoom: 4,
          center: [105.0, 35.0], // 中国的大致中心点
          resizeEnable: true, // 允许自动调整大小
          showIndoorMap: false // 关闭室内地图
        });

        this.map.addControl(new this.AMap.Scale());

        this.infoWindow = new this.AMap.InfoWindow({
          offset: new this.AMap.Pixel(10, 100),
          closeWhenClickMap: true,
          autoMove: false, // 禁用自动移动，保持固定偏移
          isCustom: false,
          anchor: 'bottom-center', // 设置锚点位置为底部中心
          size: new this.AMap.Size(350, 300)
        });

        await this.fetchJobLocations(); // 地图设置后获取数据

        console.log('高德地图 JSAPI 加载成功');

      } catch (e) {
        console.error('高德地图加载失败:', e);
        this.$message.error('地图加载失败: ' + e.message);
        this.isLoading = false; // 确保出错时停止加载
      }
    },
    async fetchJobLocations() {
      this.isLoading = true;
      this.selectedLocationInfo = null; // 每次获取时清除详情
      try {
        const response = await getJobMapInfoList(); // 调用 API
        console.log('获取地图数据:', response);

        // 检查响应结构和成功代码（现在使用 200）
        if (response.data.code === 200) {
          const rawData = response.data.data || []; // 访问嵌套的数据数组

          // 处理原始数据，只保留有效的位置数据
          this.allJobLocations = rawData.map(loc => {
            // 如果已经有有效的position数组，直接使用
            if (loc.position && Array.isArray(loc.position) && loc.position.length === 2 &&
                typeof loc.position[0] === 'number' && typeof loc.position[1] === 'number') {
              return loc;
            }

            // 如果没有有效的position，但有description，尝试从中解析坐标
            if (loc.description) {
              try {
                // 替换中文逗号为英文逗号并分割
                const parts = loc.description.replace("，", ",").split(",");
                if (parts.length === 2) {
                  const lat = parseFloat(parts[0].trim());
                  const lng = parseFloat(parts[1].trim());
                  if (!isNaN(lat) && !isNaN(lng)) {
                    // 创建新的位置数组 [经度, 纬度]
                    loc.position = [lng, lat];
                    return loc;
                  }
                }
                // 如果解析失败，确保position为null
                loc.position = null;
              } catch (e) {
                console.warn(`无法解析坐标 for job ${loc.jobNumber}:`, loc.description);
                loc.position = null;
              }
            } else {
              // 如果description为null，确保position也为null
              loc.position = null;
              console.log(`作业 ${loc.jobNumber} 没有坐标信息（description为null）`);
            }

            return loc;
          });

          console.log('处理后的位置数据:', this.allJobLocations);
        } else {
          // 处理响应代码指示的 API 错误
          const errorMsg = response?.data?.message || 'Failed to fetch job map data.';
          console.error('API Error:', errorMsg, response);
          this.$message.error(`加载地图数据失败: ${errorMsg}`);
          this.allJobLocations = []; // 出错时清除数据
        }
      } catch (error) {
        console.error('Failed to fetch job map info:', error);
        this.$message.error('加载地图数据时发生网络错误');
        this.allJobLocations = []; // 出错时清除数据
      } finally {
        this.isLoading = false;
        // 首次数据加载尝试后渲染标记点，但保持全国视图
        this.$nextTick(() => {
            // 根据当前（可能为空）的过滤列表渲染标记点
            this.renderMarkers(this.filteredLocations);
            // 保持全国视图，不自动缩放
            this.map.setZoomAndCenter(3.6, [105.0, 35.0]);
            // 确保地图大小正确
            this.handleResize();
        });
      }
    },
    renderMarkers(locationsToRender) {
      if (!this.map) return;

      this.map.remove(this.allMarkers); // 清除之前的标记点
      this.allMarkers = [];

      // 过滤出有效的位置数据
      const validLocations = locationsToRender.filter(loc => {
        const pos = loc.position;
        return pos && Array.isArray(pos) && pos.length === 2 &&
               typeof pos[0] === 'number' && typeof pos[1] === 'number' &&
               !isNaN(pos[0]) && !isNaN(pos[1]);
      });

      console.log(`有效位置数据: ${validLocations.length}/${locationsToRender.length}`);

      validLocations.forEach(loc => {
        // 根据作业状态设置不同的标记点样式
        const isCompleted = loc.jobStatus === 1; // 1表示已完工

        // 检查是否选中 - 基于位置坐标而不是作业ID
        let isSelected = false;
        if (this.selectedLocationInfo && this.selectedLocationInfo.position &&
            loc.position && Array.isArray(loc.position) && loc.position.length === 2 &&
            Array.isArray(this.selectedLocationInfo.position) && this.selectedLocationInfo.position.length === 2) {
          // 使用容差比较坐标是否相同
          const tolerance = 0.00001; // 约为1米的容差
          isSelected = Math.abs(loc.position[0] - this.selectedLocationInfo.position[0]) < tolerance &&
                       Math.abs(loc.position[1] - this.selectedLocationInfo.position[1]) < tolerance;
        }

        // 创建标记点配置
        const markerOptions = {
          position: loc.position,
          title: `作业号: ${loc.jobNumber || '未知'}`,
          map: this.map,
          extData: loc, // 存储完整的数据对象
        };

        // 根据作业状态和选中状态设置不同的标记点样式
        if (isSelected) {
          // 选中状态使用选中.svg图标
          markerOptions.icon = new this.AMap.Icon({
            // 使用项目中的SVG图片
            image: require('@/assets/images/map/选中.svg'),
            size: new this.AMap.Size(24, 24),    // 控制图标尺寸
            imageSize: new this.AMap.Size(24, 24)   // 控制图片尺寸
          });
          // 设置选中标记点的 zIndex 为较高值，确保显示在其他标记点上方
          markerOptions.zIndex = 110;
        } else if (isCompleted) {
          // 已完工作业使用已完井.svg图标
          markerOptions.icon = new this.AMap.Icon({
            // 使用项目中的SVG图片
            image: require('@/assets/images/map/已完井.svg'),
            size: new this.AMap.Size(24, 24),    // 控制图标尺寸
            imageSize: new this.AMap.Size(24, 24)   // 控制图片尺寸
          });
          // 设置默认 zIndex
          markerOptions.zIndex = 100;
        } else {
          // 进行中作业使用进行中.svg图标
          markerOptions.icon = new this.AMap.Icon({
            // 使用项目中的SVG图片
            image: require('@/assets/images/map/进行中.svg'),
            size: new this.AMap.Size(24, 24),    // 控制图标尺寸
            imageSize: new this.AMap.Size(24, 24)   // 控制图片尺寸
          });
          // 设置默认 zIndex
          markerOptions.zIndex = 100;
        }

        // 清除content属性，防止影响图标显示
        markerOptions.content = null;
        markerOptions.offset = new this.AMap.Pixel(-12, -12); // 调整偏移量使标记点居中

        const marker = new this.AMap.Marker(markerOptions);

        marker.on('click', (e) => {
          this.showInfoWindow(e.target);
        });

        this.allMarkers.push(marker);
      });
       // 视图调整现在在 fetchJobLocations 完成后处理
    },

    updateMarkerIcon(marker, isSelected) {
      if (!marker || !this.AMap) return;

      const locationData = marker.getExtData();
      if (!locationData) return;

      const isCompleted = locationData.jobStatus === 1;

      let iconImage;
      if (isSelected) {
        // 选中状态
        iconImage = require('@/assets/images/map/选中.svg');
      } else if (isCompleted) {
        // 已完工状态
        iconImage = require('@/assets/images/map/已完井.svg');
      } else {
        // 进行中状态
        iconImage = require('@/assets/images/map/进行中.svg');
      }

      // 更新图标
      marker.setIcon(new this.AMap.Icon({
        image: iconImage,
        size: new this.AMap.Size(24, 24),
        imageSize: new this.AMap.Size(24, 24)
      }));
    },
    showInfoWindow(marker) {
      if (!this.map || !this.infoWindow || !marker) return;

      const locationData = marker.getExtData();
      this.selectedLocationInfo = locationData; // 更新侧边栏

      // 获取当前标记点的位置
      const markerPosition = marker.getPosition();
      const markerLng = markerPosition.getLng();
      const markerLat = markerPosition.getLat();

      // 找出所有相同位置的标记点
      const markersToUpdate = [];
      const markersToRemove = [];

      this.allMarkers.forEach(m => {
        const mPosition = m.getPosition();
        const mLng = mPosition.getLng();
        const mLat = mPosition.getLat();

        // 使用容差比较坐标是否相同
        const tolerance = 0.00001; // 约为1米的容差
        const isSamePosition = Math.abs(mLng - markerLng) < tolerance &&
                              Math.abs(mLat - markerLat) < tolerance;

        if (isSamePosition) {
          // 标记为需要移除的标记点
          markersToRemove.push(m);
          // 保存标记点数据用于重新创建
          markersToUpdate.push({
            data: m.getExtData(),
            position: m.getPosition()
          });
        } else {
          // 更新非选中标记点的图标
          this.updateMarkerIcon(m, false);
        }
      });

      // 移除所有相同位置的标记点
      markersToRemove.forEach(m => {
        m.setMap(null);
        const index = this.allMarkers.indexOf(m);
        if (index > -1) {
          this.allMarkers.splice(index, 1);
        }
      });

      // 创建新的选中状态标记点
      markersToUpdate.forEach(item => {
        // 创建新的标记点配置
        const newMarkerOptions = {
          position: item.position,
          title: `作业号: ${item.data.jobNumber || '未知'}`,
          map: this.map,
          extData: item.data,
          zIndex: 110, // 设置较高的 zIndex
          icon: new this.AMap.Icon({
            image: require('@/assets/images/map/选中.svg'),
            size: new this.AMap.Size(24, 24),
            imageSize: new this.AMap.Size(24, 24)
          }),
          offset: new this.AMap.Pixel(-12, -12)
        };

        // 创建新的标记点
        const newMarker = new this.AMap.Marker(newMarkerOptions);

        // 添加点击事件
        newMarker.on('click', (e) => {
          this.showInfoWindow(e.target);
        });

        // 添加到标记点数组
        this.allMarkers.push(newMarker);
      });

      // 只将标记点位置设置为地图中心点，不放大
      this.map.setCenter(marker.getPosition());

      // 从filteredLocations中找出所有相同坐标的位置
      const locationsAtSamePosition = this.filteredLocations.filter(loc => {
        if (!loc.position || !Array.isArray(loc.position) || loc.position.length !== 2) return false;
        // 对比坐标是否相同，使用一定的容差
        const tolerance = 0.00001; // 约为1米的容差
        return Math.abs(loc.position[0] - markerLng) < tolerance &&
               Math.abs(loc.position[1] - markerLat) < tolerance;
      });

      // 根据点击的是单个位置还是多个位置来调整信息窗口的偏移量和创建内容
      let content;

      // 调整信息窗口的偏移量，单个位置时距离标记更近
      if (locationsAtSamePosition.length <= 1) {
        // 单个位置时使用较小的偏移量，让信息窗口更接近标记
        this.infoWindow.setOffset(new this.AMap.Pixel(10, 165));
        // 单个位置的情况，保持原来的样式
        content = `
          <div style="padding: 10px; font-size: 13px; min-width: 300px; max-height: 300px; overflow-y: auto;">
          <div style="margin-bottom: 5px;">
              <span style="font-weight: bold;">共发现 <span style="color: #67C23A;">${locationsAtSamePosition.length}</span> 个作业位于此处</span>
            </div>
            <hr style="margin: 5px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
              <div>
                <span style="font-weight: bold; margin-right: 5px;">${locationData.name || ''}</span>
                <span><a href="javascript:void(0)" onclick="window.goToJobDetail && window.goToJobDetail('${locationData.id}')" style="color: #409EFF; text-decoration: underline;">${locationData.jobNumber || '未知'}</a></span>
                <span style="margin-left: 5px;">${locationData.serveType || ''}</span>
              </div>
              <div>
                <span style="font-weight: bold;">${this.jobStatusMap[locationData.jobStatus === 0 ? 'inProgress' : 'completed'] || '未知'}</span>
              </div>
            </div>
            <div style="display: flex; flex-wrap: wrap;">
              <div style="margin-right: 15px;">
                <span style="font-weight: bold;">趟数: </span>
                <span>${locationData.totalRun || ''}</span>
              </div>
              <div>
                <span>${locationData.dateIn || ''} 至 ${locationData.dateOut || ''}</span>
              </div>
            </div>
          </div>
        `;
      } else {
        // 多个位置的情况，使用原来的偏移量设置，防止遮挡标记
        this.infoWindow.setOffset(new this.AMap.Pixel(10, 102));

        // 多个位置的情况，创建列表视图
        let locationListHtml = '';

        // 对位置列表进行排序，先显示进行中的作业
        const sortedLocations = [...locationsAtSamePosition].sort((a, b) => {
          // 优先级：1. 作业状态(进行中优先) 2. 作业号
          if (a.jobStatus !== b.jobStatus) {
            return a.jobStatus === 0 ? -1 : 1; // 0是进行中，1是已完成
          }
          return (a.jobNumber || '').localeCompare(b.jobNumber || '');
        });

        // 为每个位置创建一个列表项
        sortedLocations.forEach((loc, index) => {
          const isCompleted = loc.jobStatus === 1;
          const statusText = this.jobStatusMap[isCompleted ? 'completed' : 'inProgress'] || '未知';

          locationListHtml += `
            <div style="${index > 0 ? 'margin-top: 10px; padding-top: 10px; border-top: 1px dashed #e0e0e0;' : ''}">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                <div>
                  <span style="font-weight: bold; margin-right: 5px;">${loc.name || ''}</span>
                  <span><a href="javascript:void(0)" onclick="window.goToJobDetail && window.goToJobDetail('${loc.id}')" style="color: #409EFF; text-decoration: underline;">${loc.jobNumber || '未知'}</a></span>
                  <span style="margin-left: 5px;">${loc.serveType || ''}</span>
                </div>
                <div>
                  <span style="font-weight: bold;">${statusText}</span>
                </div>
              </div>
              <div style="display: flex; flex-wrap: wrap;">
                <div style="margin-right: 15px;">
                  <span style="font-weight: bold;">趟数: </span>
                  <span>${loc.totalRun || ''}</span>
                </div>
                <div>
                  <span>${loc.dateIn || ''} 至 ${loc.dateOut || ''}</span>
                </div>
              </div>
            </div>
          `;
        });

        // 创建包含列表的完整内容
        content = `
          <div style="padding: 10px; font-size: 13px; min-width: 300px; max-height: 300px;">
            <div style="margin-bottom: 5px;">
              <span style="font-weight: bold;">共发现 <span style="color: #67C23A;">${locationsAtSamePosition.length}</span> 个作业位于此处</span>
            </div>
            <hr style="margin: 5px 0;">
            <div style="max-height: 108px; overflow-y: auto; padding-right: 5px;">
              ${locationListHtml}
            </div>
          </div>
        `;
      }

      this.infoWindow.setContent(content);

      // 打开信息窗口
      this.infoWindow.open(this.map, marker.getPosition());
    },
    handleTabClick() {
      // 选项卡更改触发计算属性，计算属性触发观察者，观察者调用 renderMarkers
      console.log('Tab changed to:', this.activeTab);

      // 如果是切换到进行中或已完工，更新当前状态
      if (this.activeTab === 'inProgress') {
        this.currentStatus = 0;
      } else if (this.activeTab === 'completed') {
        this.currentStatus = 1;
      }

      // 关闭信息窗口
      if (this.infoWindow) {
        this.infoWindow.close();
      }

      // 清除选中的位置信息
      this.selectedLocationInfo = null;

      // 清除地图上的所有标记点
      this.map.remove(this.allMarkers);
      this.allMarkers = [];

      // 根据当前选中的选项卡渲染新的标记点
      this.$nextTick(() => {
        // 渲染过滤后的标记点
        this.renderMarkers(this.filteredLocations);

        // 保持全国视图，不自动缩放
        this.map.setZoomAndCenter(3.6, [105.0, 35.0]);
        // 确保地图大小正确
        this.handleResize();

        // 根据选项卡显示不同的日志
        if (this.activeTab === 'jobList') {
          console.log(`切换到作业列表，显示 ${this.allMarkers.length} 个标记点`);
        } else {
          console.log(`切换到 ${this.jobStatusMap[this.activeTab]} 状态，显示 ${this.allMarkers.length} 个标记点`);
        }
      });
    },
    destroyMap() {
      if (this.map) {
        this.map.destroy();
        this.map = null;
        this.allMarkers = [];
        this.infoWindow = null;
      }
    },
    goToJobDetail(job, e) {
      // 阻止事件冒泡，防止触发父元素的点击事件
      if (e) e.stopPropagation();

      if (job && job.id) {
        this.$router.push(`/daily/detail?jobid=${job.id}`);
      } else {
        this.$message.warning('无法跳转：作业ID不存在');
      }
    },
    toggleJobList() {
      // 切换作业列表的显示/隐藏
      this.showJobList = !this.showJobList;
      console.log(`${this.showJobList ? '显示' : '隐藏'}作业列表`);
    },
    getStatusCount(status) {
      // 获取特定状态的作业数量
      const statusValue = status === 'inProgress' ? 0 : 1;
      return this.allJobLocations.filter(loc => loc.jobStatus === statusValue).length;
    },

    isJobSelected(job) {
      // 检查作业是否被选中 - 基于位置坐标而不是作业ID
      if (!this.selectedLocationInfo || !this.selectedLocationInfo.position ||
          !job || !job.position || !Array.isArray(job.position) || job.position.length !== 2 ||
          !Array.isArray(this.selectedLocationInfo.position) || this.selectedLocationInfo.position.length !== 2) {
        return false;
      }

      // 使用容差比较坐标是否相同
      const tolerance = 0.00001; // 约为1米的容差
      return Math.abs(job.position[0] - this.selectedLocationInfo.position[0]) < tolerance &&
             Math.abs(job.position[1] - this.selectedLocationInfo.position[1]) < tolerance;
    },
  },
};
</script>

<style scoped>
.map-component {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

#map-container {
  position: absolute;
  top: 0; right: 0; bottom: 0; left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.map-controls {
  position: absolute;
  top: 10px; left: 20px; /* 增加左侧边距，避免贴边 */
  z-index: 10;
  width: 260px; /* 控制宽度 */
  background-color: rgba(255, 255, 255, 0.9); /* 稍微透明 */
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0,0,0,.3);
  max-height: calc(100% - 20px);
  overflow-y: auto;
  border: none;
}

.job-link {
  color: #409EFF;
  text-decoration: none;
}

.job-link:hover {
  text-decoration: underline;
}

.job-well {
  font-weight: 500;
  margin-bottom: 2px;
  font-size: 13px;
}

.job-number {
  font-weight: 600;
  margin-bottom: 3px;
  font-size: 14px;
}

/* 深度选择器，确保能够覆盖 Element UI 的样式 */
.map-controls >>> .el-card__body {
    padding: 0; /* 移除卡片主体内边距 */
}

/* 选项卡容器样式 */
.map-tabs-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 5px;
  border-bottom: 1px solid #E4E7ED;
}

/* Tab 样式 */
.map-controls >>> .el-tabs--card > .el-tabs__header {
   margin: 0;
   border-bottom: none;
   flex: 1;
}
.map-controls >>> .el-tabs__nav {
    float: none;
    text-align: center;
    border: none;
}
.map-controls >>> .el-tabs__item {
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
    border: none !important;
}
.map-controls >>> .el-tabs__item.is-active {
    color: #409EFF;
    background-color: #ecf5ff;
}

/* 作业列表按钮样式 */
.job-list-toggle {
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
  color: #606266;
  transition: all 0.3s;
}

.job-list-toggle:hover {
  color: #409EFF;
}

.job-list-toggle .active {
  color: #409EFF;
}

/* 作业列表样式 */
.job-list {
  margin-bottom: 10px;
}

.job-list-header {
  padding: 5px 15px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.job-list-title {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
}

.job-list-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}



.job-list-content {
  max-height: 250px;
  overflow-y: auto;
}

.job-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.job-item:hover {
  background-color: #f5f7fa;
}

.job-item.selected {
  background-color: #ecf5ff;
  border-left: 3px solid #409EFF;
}

.job-date {
  font-size: 12px;
  color: #909399;
}

/* 详情部分样式 */
.marker-details {
    padding: 10px 15px;
    font-size: 13px;
    line-height: 1.7;
    border-top: 1px solid #ebeef5;
}
.marker-details h4 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
}
.marker-details p {
    margin: 4px 0;
    color: #606266;
}
.marker-details p strong {
    color: #303133;
    margin-right: 5px;
    display: inline-block;
    min-width: 80px; /* 对齐标签 */
}


</style>
