<template>
    <div>
        <div class="file-list">
            <div class="file-item" v-for="item in attachments">
                <span class="file-title">
                    {{ getFileName(item) }}
                </span>
                <span class="operators">
                    <a :href="item" target="_blank">
                        <i class="el-icon-view oper-icon"></i>
                    </a>
                    <i @click="onDeleteFile(item)" class="el-icon-delete oper-icon"></i>
                </span>
            </div>
            <el-button @click="onUpload" type="text" :loading="uploadLoading">上传</el-button>
        </div>
        <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadFile"
            >
            <div ref="ghostFileUploader" style="width:0;height:0"></div>
        </el-upload>
    </div>
</template>
<script>
import { apiUploadFile } from "@/api/file";
export default {
    name: "FileManage",
    props: {
        attachments: {
            type: Array,
            default: () => []
        }
    },
    data(){
        return {
            uploadLoading: false
        }
    },
    methods: {
        getFileName(filePath){
            const fileName = filePath.split('/').pop();
            return fileName;
        },
        onDeleteFile(filePath){
            const idx = this.attachments.indexOf(filePath);
            if(idx!== -1){
                this.attachments.splice(idx, 1);
            }
        },
        onUploadFile(file){
            this.uploadLoading = true;
            const form = new FormData();
            form.append('file', file);
            form.append('type', 'email');
            apiUploadFile(form).then((res)=>{
                const filePath = res.data.data;
                this.attachments.push(filePath);
            }).finally(()=>{
                this.uploadLoading = false;
            })
        },
        onUpload(){
            this.$refs.ghostFileUploader.click();
        }
    }
    
}
</script>

<style lang="scss" scoped>
.file-list{
    
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 380px;
        margin-bottom: 10px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .operators{
            float: right;
            .oper-icon{
                display: none;
                vertical-align:super;
                margin-right:6px;
                cursor: pointer;
                line-height: 26px;
            }
        }
        &:hover{
            .operators .oper-icon{
                display: inline-block;
            } 
        }
    }
}
</style>