<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        append-to-body
        title="邮件内容"
        width="800px"
    >
        <el-form :model="emailForm" :rules="emailFormRules" ref="emailFormRef" label-width="70px" style="margin-top:-10px">
            <el-form-item label="发送给 " prop="sendIdList">
                <FuzzySelect allow-create create-field="email" placeholder="搜索或者直接输入邮件地址并回车" :initList="sendInitList" @change="onSendChange" multiple type="USER_ALL" v-model="emailForm.sendIdList"></FuzzySelect>
            </el-form-item>
            <el-form-item label="抄送给 " prop="ccIdList">
                <FuzzySelect allow-create create-field="email" placeholder="搜索或者直接输入邮件地址并回车" :initList="ccInitList" @change="onCcChange" multiple type="USER_ALL" v-model="emailForm.ccIdList"></FuzzySelect>
            </el-form-item>
            <el-form-item label="主题 " prop="emailTitle">
                <el-input v-model="emailForm.emailTitle"></el-input>
            </el-form-item>
            <el-form-item label="正文">
                <el-input v-model="emailForm.emailContent1" type="textarea" :autosize="{ minRows: 6 }"></el-input>
                <div style="white-space: pre-line;line-height: 1.5;background: #eee;" v-html="emailForm.emailContent2">
                </div>
            </el-form-item>
            <el-form-item label="附件" prop="attachments">
                <Attachments style="margin-top: 2px" :attachments="emailForm.attachments" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="isDialogVisible = false">取消</el-button>
            <el-button @click="onConfirm" type="primary" :loading="isBtnLoading">确认发送</el-button>
        </template>
    </el-dialog>
</template>
<script>
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import Attachments from "./file.vue"
import { apiGetEmailTemplate, apiSendEmail } from "@/api/email";
export default {
    name: "EmailDialog",
    components: { FuzzySelect, Attachments },
    data(){
        return {
            isDialogVisible: false,
            isBtnLoading: false,
            sendInitList: [],
            ccInitList: [],
            emailForm: {
                sendIdList: [],
                sendList: [],
                ccIdList: [],
                ccList: [],
                emailTitle: '',
                emailBusinessType: '',
                emailContent: '',
                emailContent1: '',
                emailContent2: '',
                attachments: []
            },
            emailFormRules: {
                sendIdList: [
                    {
                        required: true,
                        validator: (_,__,callback) => {
                            if(!this.emailForm.sendIdList||!this.emailForm.sendIdList.length){
                                callback(new Error('请填写发送人'));
                            }
                            callback();
                        }
                    }
                ]
            }
        }
    },
    props: {
        emailBusinessType: {
            type: String,
            required: true
        },
        emailBusinessId: {
            type: String | Number,
            default: 0,
        }
    },
    methods: {
        async showDialog(attachments = []) {
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            const form = new FormData();
            form.append('type', this.emailBusinessType);
            form.append('id', this.emailBusinessId);
            apiGetEmailTemplate(form).then(res=>{
                const emailTemplate = res.data.data?.[0];
                if(emailTemplate){
                    this.emailForm = { ...this.emailForm, ...emailTemplate };
                    const emailTemplateAttachments = emailTemplate.attachments || [];
                    this.emailForm.attachments = [...emailTemplateAttachments, ...attachments];
                    this.emailForm.sendIdList = emailTemplate.sendList.map(item=>item.memberId);
                    this.emailForm.ccIdList = emailTemplate.ccList.map(item=>item.memberId);
                    this.emailForm.emailContent1 = '';
                    this.emailForm.emailContent2 = emailTemplate.emailContent.replaceAll('<br/>', '\n');
                    this.sendInitList = [...emailTemplate.sendList];
                    this.ccInitList = [...emailTemplate.ccList];
                    this.isDialogVisible = true;
                }
            }).finally(()=>{
                loading.close();
            })
        },
        async onConfirm(){
            const valid = await this.$refs.emailFormRef.validate();
            if(!valid){
                return;
            }
            this.isBtnLoading = true;
            const form = { ...this.emailForm };
            form.emailContent = (form.emailContent1 + '\n' + form.emailContent2).replaceAll('\n', '<br/>');
            apiSendEmail(form).then(()=>{
                this.$message.success('邮件已发送!');
                this.isDialogVisible = false;
            }).finally(()=>{
                this.isBtnLoading = false;
            })
        },
        onSendChange(e){
            this.emailForm.sendList = e;
        },
        onCcChange(e){
            this.emailForm.ccList = e;
        }
    }
}
</script>
<style lang="scss">
</style>
