 <template>
    <el-autocomplete
        v-model="modelValue"
        :fetch-suggestions="querySearch"
        :trigger-on-focus="true"
        @select="onSelect"
        style="width: 100%;"
        @change="onChange"
        v-bind="$attrs"
    ></el-autocomplete>
 </template>
  <script>
    export default {
      props: {
        suggestList: {
          type: Array,
          default: () => []
        },
        value: {
        },
      },
      data() {
        return {
          modelValue: null
        };
      },
      watch: {
        modelValue:{
          handler(n){
            // this.$emit("input",n);
          }
        },
        value: {
          immediate: true,
          handler(n){
            this.modelValue = n;
            // this.$emit("change", this.modelValue)
          }
        }
      },
      methods: {
        querySearch(qs, cb) {
          var suggestList = this.suggestList;
          var results = qs ? suggestList.filter(this.createFilter(qs)) : suggestList;
          cb(results);
        },
        createFilter(qs) {
          return (s) => {
            const lowerCaseTarget = s.value.toLowerCase();
            return lowerCaseTarget.includes(qs.toLowerCase());
          };
        },
        onSelect(item) {
          this.modelValue = item.value;
          
          this.$emit("input",item.value);
          this.$emit("change", item.value)
        },
        onChange(n){
          this.$emit("input",n);
          this.$emit("change", n)
        }
      },
    }
  </script>