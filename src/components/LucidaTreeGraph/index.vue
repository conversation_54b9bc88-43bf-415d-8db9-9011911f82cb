<template>
    <div style="height: 100%;">
        <el-form class="treegraph-focus-form" :model="focusForm" inline>
            <el-form-item label="快速定位:">
                <FuzzySelect
                    v-model="focusForm.invCode"
                    type="NORMAL"
                    placeholder="输入部件品号快速定位"
                    :normalDataList="currentInvCodeList"
                    style="width: 200px;"
                    @change="onFoucsFormChange"
                />
                <span v-if="searchStates.matchList.length > 0" style="margin-left: 10px;">
                    <span>
                        {{ searchStates.currentIndex }}/{{ searchStates.matchList.length }}
                    </span>
                    <i style="margin-left: 4px;cursor: pointer;" class="el-icon-arrow-up" @click="onSearchPrev"></i>
                    <i style="margin-left: 4px;cursor: pointer;" class="el-icon-arrow-down" @click="onSearchNext"></i>
                </span>
            </el-form-item>
        </el-form>
        <div id="container" style="height:calc(100% - 100px)"></div>
        <el-dialog :title="invName" :visible.sync="isSelectPartDialogVisible" width="400px">
            <template #title>
                <div style="width: 300px;word-wrap: break-word;font-size: 18px;">
                    {{ invName }}
                </div>
            </template>
            <div style="display: flex;align-items: center;">
                <FuzzySelect
                    v-model="selectedSerialNumber"
                    @change="onConfirmSelectSerialNumber"
                    v-if="isSelectPartDialogVisible"
                    type="LUCIDA_PART_INFO"
                    :restParams="{invCode: invCode}"
                >
                    <template v-slot="{item}">
                        <div>
                            <span style="margin-right: 10px;">
                                {{ item.serialNumber }}
                            </span>
                            <span v-if="item.parentComponentSerialNumber" style="margin-right: 10px;">
                                上级：{{ item.parentComponentSerialNumber }}
                            </span>
                            <span v-if="item.deviceSerialNumber">
                                总成：{{ item.deviceSerialNumber }}
                            </span>
                        </div>
                    </template>
                </FuzzySelect>
                <router-link
                    tag="a"
                    style="color: blue;margin-left: 20px;width: 70px;"
                    :to="`/mwd-tool-maintain/partservice?event=add&invName=${invName}&invCode=${invCode}`"
                    target="_blank" 
                    v-if="p_PartListView"
                >
                    +去新增
                </router-link>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import G6 from "@antv/g6";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { deepCopyTree, findFirstNode, findNodesWithParents } from "@/utils/tree"
import { 
    registerCustomNodes, ANTV_TREE_COLLAPSED_FLAG, ASSEMBLE_NODE, DISASSEMBLE_NODE, DEFAULT_NODE,
    GHOST_NODE, expandNode, collapseNode, expandNodeAll, mergeCompleteTemplateWithPartData, getNextGreen, getNextRed
} from "./utils/nodes";
import { apiGetLucidaTreeByNode } from "@/api/lucida";
import insertCss from "insert-css";
insertCss(`
    .g6-component-contextmenu {
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            font-size: 12px;
            padding: 0;
    }
`)
registerCustomNodes('tree-default-node');

// NOTE: 所有关于树的数据处理，原则上不应该允许原地操作，也不应该允许使用引用，只能使用深拷贝
export default {
    name: "LucidaTreeGraph",
    components: {
        FuzzySelect,
    },
    props: {
        template: {
            type: Object,
            default: null
        },
        actualData: {
            type: Object,
            default: null
        },
    },
    data(){
        return {
            graph: null,
            matrix: null, // 如果有值，则保持视图位置
            treeTemplate: null,
            actualTreeData: null,
            count: 0,
            status: 'READ',
            invName: null,
            invCode: null,
            isSelectPartDialogVisible: false,
            selectedSerialNumber: null,
            focusForm: {
                invCode: null,
            },
            currentInvCodeList: [],
            searchStates: {
                matchList: [], // 选中节点列表
                currentIndex: 0, // 当前选中节点索引
            }

        }
    },
    computed:{
        p_PartListView(){
            return this.$checkBtnPermission('sys:partservice:view');
        }
    },
    methods: {
        initData(status='READ'){
            this.searchStates.matchList = [];
            this.focusForm.invCode = null;
            if(this.graph){
                this.graph.destroy();
            }
            this.status = status;
            if(!this.treeTemplate){
                // NOTE: 不要多次调用！或者可以在执行完还原count
                this.treeTemplate = this.handleTreeData(this.template, { operation: "DEFAULT", nodeType: GHOST_NODE, generateId: true });
            }
            this.actualTreeData = deepCopyTree(this.actualData);
            switch(this.status){
                case 'ASSEMBLE':
                    this.assemble();
                    break;
                case 'DISASSEMBLE':
                    this.disassemble();
                    break;
                case 'READ':
                    this.read();
                    break;
            }
        },
        getDeduplicatedInvCodeList(){
            // 调用时机: 初始化, 折叠/展开后
            // 递归查询模板数据，获取所有的invCode
            // const getInvCode = (data) => {
            //     if (!data || !data.children) {
            //         return [];
            //     }
            //     let invCodeList = data.children.map(item => item.invCode);
            //     data.children.forEach(item => {
            //         invCodeList = [...invCodeList, ...getInvCode(item)];
            //     });
            //     return invCodeList;
            // };
            // const invCodeList = getInvCode(this.treeTemplate);
            const invCodeList = this.graph?.getNodes().map(node => node.getModel().invCode).filter(item=>!!item) || [];
            // 去重
            this.currentInvCodeList = [...new Set(invCodeList)];
        },
        onFoucsFormChange(){
            const allNodes = this.graph.getNodes();
            const filterNods = allNodes.filter(node=>node.getModel().invCode === this.focusForm.invCode);
            allNodes.forEach(node=>{
                this.graph.setItemState(node, 'highlight', '');
            })
            if(!filterNods.length || !this.focusForm.invCode){
                this.searchStates.matchList = [];
                return;
            }
            this.searchStates.matchList = filterNods;
            this.searchStates.currentIndex = 1;
            const targetNode = filterNods[0];
            this.graph.focusItem(targetNode, true, {
                easing: 'easeCubic',
                duration: 500,
            });
            this.searchStates.matchList.forEach(node=>{
                if(node===targetNode){
                    this.graph.setItemState(node, 'highlight', 'focus');
                }else{
                    this.graph.setItemState(node, 'highlight', 'normal');
                }
            })
            
        },
        onSearchNext(){
            if(this.searchStates.currentIndex >= this.searchStates.matchList.length){
                this.searchStates.currentIndex = 1;
            }else{
                this.searchStates.currentIndex++;
            }
            const targetNode = this.searchStates.matchList[this.searchStates.currentIndex - 1];
            this.graph.focusItem(targetNode, true, {
                easing: 'easeCubic',
                duration: 500,
            });
            this.searchStates.matchList.forEach(node=>{
                if(node===targetNode){
                    this.graph.setItemState(node, 'highlight', 'focus');
                }else{
                    this.graph.setItemState(node, 'highlight', 'normal');
                }
            })
        },
        onSearchPrev(){
            if(this.searchStates.currentIndex <= 1){
                this.searchStates.currentIndex = this.searchStates.matchList.length;
            }else{
                this.searchStates.currentIndex--;
            }
            const targetNode = this.searchStates.matchList[this.searchStates.currentIndex - 1];
            this.graph.focusItem(targetNode, true, {
                easing: 'easeCubic',
                duration: 500,
            });
            
            this.searchStates.matchList.forEach(node=>{
                if(node===targetNode){
                    this.graph.setItemState(node, 'highlight', 'focus');
                }else{
                    this.graph.setItemState(node, 'highlight', 'normal');
                }
            })
        },
        read(){
            // 处理目标数据，标记nodeType
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 业务要求下的预处理逻辑 - 无论是读、拆、装，都要显示可能没有的节点
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, false);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        assemble(){
            // 处理目标数据，标记nodeType，并设置head
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 只有组装时会有isGhostHead标记
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, true);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        disassemble(){
            // 处理目标数据，标记nodeType
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 业务要求下的预处理逻辑 - 无论是读、拆、装，都要显示可能没有的节点
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, false);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        // 统一的数据处理函数
        handleTreeData(data, options = {}) {
            const {
                operation = 'DEFAULT', // 'DEFAULT', 'ASSEMBLE', 'DISASSEMBLE', 'CANCEL_DISASSEMBLE', 'CANCEL_ASSEMBLE'
                nodeType,
                collapsed = false,
                generateId = false,
                color = null,
                parentNode = null, // 父节点类型，用于某些操作
            } = options;

            const handleFunc = (item, parent = null) => {
                // 根据不同操作类型进行处理
                switch (operation) {
                    case 'DEFAULT':
                        generateId && (item.id = 'nodeid' + this.count++);
                        item.nodeType = nodeType;
                        break;
                    case 'ASSEMBLE':
                        /* 
                            组装
                            预处理目标数据，标记nodeType，并设置head
                            合并该节点和对应完整的基础模板数据，并设置残缺部分的HEAD标记
                        */
                        item.nodeType = ASSEMBLE_NODE;
                        item.ASSEMBLE_NODE_COLOR = color;
                        break;
                    case 'DISASSEMBLE':
                        /* 
                            拆卸
                            忽略GHOST_NODE和isDisassembleHead为true的节点
                            该节点及其子节点中所有nodeType为DEFAULT_NODE的节点置为DISASSEMBLE_NODE
                            添加DISASSEMBLE_NODE_COLOR字段，值为getNextRed()
                            将该节点的isDisassembleHead置为true
                        */
                        if (item.isDisassembleHead) {
                            return item;
                        }
                        switch (item.nodeType) {
                            case DEFAULT_NODE:
                                item.nodeType = DISASSEMBLE_NODE;
                                item.DISASSEMBLE_NODE_COLOR = color;
                                break;
                            case DISASSEMBLE_NODE:
                                item.DISASSEMBLE_NODE_COLOR = color;
                                break;
                            case GHOST_NODE:
                                return item;
                        }
                        break;
                    case 'CANCEL_DISASSEMBLE':
                        /* 
                            取消拆卸
                            如果是GHOST_NODE或isDisassembleHead为true的节点，直接返回
                            如果是DISASSEMBLE_NODE，查看父节点是否是DISASSEMBLE_NODE
                                是，颜色保持和父节点一致
                                否，将节点类型置为DEFAULT_NODE，并删除DISASSEMBLE_NODE_COLOR
                        */
                        switch (item.nodeType) {
                            case DISASSEMBLE_NODE:
                                if (item.isDisassembleHead) {
                                    return item;
                                }
                                if (parentNode.nodeType === DISASSEMBLE_NODE) {
                                    item.DISASSEMBLE_NODE_COLOR = parentNode.DISASSEMBLE_NODE_COLOR;
                                } else {
                                    item.nodeType = DEFAULT_NODE;
                                    delete item.DISASSEMBLE_NODE_COLOR;
                                }
                                break;
                            case GHOST_NODE:
                                return item;
                        }
                        break;
                }
                // 这个逻辑会保持组装拆卸中GHOST_NODE的折叠状态
                item[ANTV_TREE_COLLAPSED_FLAG] = collapsed;
                if (item.children) {
                    item.children.forEach((child) => {
                        handleFunc(child, item);
                    });
                }
                return item;
            };
            return handleFunc(data);
        },
        initContainer() {
            this.container = document.getElementById("container");
            this.width = this.container.scrollWidth;
            this.height = this.container.scrollHeight || 500;
        },

        initGraph(data) {
            if (!data) {
                return;
            }
            const tooltip = new G6.Tooltip({
                offsetX: 10,
                offsetY: 0,
                // 允许出现 tooltip 的 item 类型
                itemTypes: ["node"],
                // 自定义 tooltip 内容
                getContent: (e) => {
                    const model = e.item.getModel();
                    const { maxBht, reviseTotalHours, riskType } = model;
                    return `
                        <div class="tooltip-item">
                            <div class="tooltip-label">最高温度</div>
                            <div class="tooltip-content">${maxBht || ""}</div>
                        </div>
                        <div class="tooltip-item">
                            <div class="tooltip-label">总时间</div>
                            <div class="tooltip-content">${reviseTotalHours || ""}</div>
                        </div>
                        <div class="tooltip-item">
                            <div class="tooltip-label">风险类型</div>
                            <div class="tooltip-content">${riskType || ""}</div>
                        </div>
                    `;
                },
                shouldBegin: (e) => {
                    return e.target.get("name") === "serialNumber";
                },
            });
            const contextmenu = new G6.Menu({
                getContent: (e) => {
                    return e._contextMenuContent;
                },
                handleMenuClick: (target, item) => {
                    const name = target.getAttribute("name");
                    const model = item.getModel();
                    const [{node: templateCurrentNode, parent: templateParentNode}] = findNodesWithParents(this.treeTemplate, data => data.id === model.id);
                    switch(name) {
                        case "EXPAND":
                            expandNode(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                        case "COLLAPSE":
                            collapseNode(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                        case "ALL_EXPAND":
                            expandNodeAll(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                        case "DISASSEMBLE":
                            /* 
                                拆卸
                                忽略GHOST_NODE和isDisassembleHead为true的节点
                                该节点及其子节点中所有nodeType为DEFAULT_NODE的节点置为DISASSEMBLE_NODE
                                添加DISASSEMBLE_NODE_COLOR字段，值为getNextRed()
                                将该节点的isDisassembleHead置为true
                            */
                            const disassembleData = this.handleTreeData(model, { operation: 'DISASSEMBLE', color: getNextRed() });
                            disassembleData.isDisassembleHead = true;
                            this.graph.updateChild(disassembleData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                        case "CANCEL_DISASSEMBLE":
                            /* 
                                取消拆卸
                                先删除目标节点isDisassembleHead标记
                                如果是GHOST_NODE或isDisassembleHead为true的节点，直接返回
                                如果是DISASSEMBLE_NODE，查看父节点是否是DISASSEMBLE_NODE
                                    是，颜色保持和父节点一致
                                    否，将节点类型置为DEFAULT_NODE，并删除DISASSEMBLE_NODE_COLOR
                            */
                            const graphParentNode = this.graph.findDataById(templateParentNode.id);
                            delete model.isDisassembleHead;
                            const cancelDisassembleData = this.handleTreeData(model, { operation: 'CANCEL_DISASSEMBLE', parentNode: graphParentNode });
                            this.graph.updateChild(cancelDisassembleData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                        case "CANCEL_ASSEMBLE":
                            // 取消组装 - 这个逻辑反倒异常简单，原因是组装在真实业务中不能出现断点
                            // 将该节点及其子节点全部置为模板节点的GHOST
                            // 将该节点的isAssembleHead置为true
                            const ghostData = deepCopyTree(templateCurrentNode);
                            ghostData.isGhostHead = true;
                            this.graph.updateChild(ghostData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                    }
                },
                offsetX: 0,
                offsetY: 0,
                // 在哪些类型的元素上响应
                itemTypes: ['node'],
                // 在这里判断要不要显示contextmenu
                shouldBegin: (e) => {
                    // 将显示的内容计算逻辑放在这里, 存到e._contextMenuContent中, 否则getContent可能要重复这个逻辑
                    // 核心原因是getContent不能返回null或undefined, 而且就算返回空串也会显示一个空的contextmenu
                    const model = e.item.getModel();
                    // 是否有子节点
                    const hasChildren = !!(model.children && model.children.length > 0);
                    // 是否折叠状态
                    const isCollapsed = !!model[ANTV_TREE_COLLAPSED_FLAG];
                    // 递归判断是否是全部展开的状态
                    const checkAllExpanded = (m) => {
                        if (!m.children || m.children.length === 0) {
                            return true;
                        }
                        return m.children.every((child) => {
                            return !child[ANTV_TREE_COLLAPSED_FLAG] && checkAllExpanded(child);
                        });
                    };
                    const isHeadNode = model.id === 'nodeid0'
                    const allExpandDiv = `<div class="menu-item" name="ALL_EXPAND">全部展开</div>`;
                    const expandDiv = `<div class="menu-item" name="EXPAND">展开</div>`;
                    const collapseDiv = `<div class="menu-item" name="COLLAPSE">折叠</div>`;
                    const disassembleDiv = `<div class="menu-item" name="DISASSEMBLE">拆卸</div>`;
                    const cancelDisassembleDiv = `<div class="menu-item" name="CANCEL_DISASSEMBLE">取消拆卸</div>`;
                    const cancelAssembleDiv = `<div class="menu-item" name="CANCEL_ASSEMBLE">取消组装</div>`;
                    let content = "";
                    if (hasChildren) {
                        if (isCollapsed) {
                            content = expandDiv + allExpandDiv;
                        } else {
                            // TODO: 全部展开孙节点会有问题, 先不展示
                            // if (checkAllExpanded(model)) {
                            //     content = collapseDiv;
                            // } else {
                            //     content = collapseDiv + allExpandDiv;
                            // }
                            content = collapseDiv;
                        }
                    }
                    if(!isHeadNode){
                        if(this.status === 'ASSEMBLE'){
                            if(model.isAssembleHead){
                                content += cancelAssembleDiv;
                            }
                        }
                        if(this.status === 'DISASSEMBLE'){
                            if(model.nodeType === DISASSEMBLE_NODE){
                                if(model.isDisassembleHead){
                                    content += cancelDisassembleDiv;
                                }else{
                                    content += disassembleDiv;
                                }
                            }
                            if(model.nodeType === DEFAULT_NODE){
                                content += disassembleDiv;
                            }
                        }
                    }
                    e._contextMenuContent = content;
                    return content;
                    
                },
            });
            this.graph = new G6.TreeGraph({
                container: "container",
                ...this.defaultConfig,
                padding: [20, 20],
                defaultLevel: 3,
                defaultZoom: 0.7,
                animate: false,
                plugins: [
                    tooltip,
                    contextmenu,
                    // TODO: 场景，位置，大小，样式
                    // new G6.Minimap({
                    //     size: [150, 100],
                    // }),
                ],
            });
            this.graph.data(data);
            this.graph.render();
            
            // 给+-号添加点击事件
            const handleCollapse = (e) => {
                e.preventDefault();
                const item = e.item;
                const model = item.getModel();
                model[ANTV_TREE_COLLAPSED_FLAG] ? expandNode(item, this.graph) : collapseNode(item, this.graph);
                this.$nextTick(()=>{
                    this.getDeduplicatedInvCodeList();
                })
            };
            this.graph.on("collapse-text:click", handleCollapse);
            this.graph.on("collapse-rect:click", handleCollapse);
            this.graph.on("serialNumber:click", this.expandToNode);
            this.graph.on("assemble:click", (e)=>{
                const item = e.item;
                const model = item.getModel();
                this.showSelectPartDialog(model);
            });
            this.graph.on("viewportchange", () => {
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            });
            // 监听鼠标滚动，改为拖拽画布
            this.graph.on("wheel", (e) => {
                e.preventDefault();
                // 获取滚动的距离
                const deltaX = e.deltaX;
                const deltaY = e.deltaY;
                // 模拟拖拽画布的行为, TODO: 不能超出边界，但边界条件不应该在这里计算
                this.graph.translate(-deltaX, -deltaY);
            });
            this.graph.on('afterrender', () => {
                // 保持视图位置
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            })
            
            // 以保证第一个节点高度为40为标准, 设置整个画布的初始zoom
            const firstNode = this.graph.getNodes()[0];
            if (firstNode) {
                const bbox = firstNode.getBBox();
                const height = bbox.maxY - bbox.minY;
                this.graph.zoom(70 / height);
            }
            // 因为没有配置fitView, 这里手动平移一下, 否则默认的左上角是应该是第一个节点的中心
            this.graph.translate(120, 40);
        },
        drawChart() {
            if (!this.container) {
                this.initContainer();
            }
            this.defaultConfig = {
                width: this.width,
                height: this.height,
                nodeStateStyles: {
                    focus: {
                        fill: 'red',
                    },
                },
                modes: {
                    default: [
                        // TODO: 重定义折叠标志, 但可能会有bug, 暂时使用默认的字段collapsed
                        // {
                        //     type: "collapse-expand",
                        //     onChange: function onChange(item, collapsed) {
                        //         const data = item.get("model");
                        //         data[ANTV_TREE_COLLAPSED_FLAG] = collapsed;
                        //         return true;
                        //     },
                        // },
                        "drag-canvas",
                        // "zoom-canvas",
                    ]
                },
                // fitView: true, // 因为主动初始化了zoom, 这个不能开启
                animate: true,
                defaultNode: {
                    type: "tree-default-node",
                },
                defaultEdge: {
                    type: 'cubic-horizontal',
                    style: {
                        stroke: '#CED4D9',
                    },
                },
                layout: {
                    type: "indented",
                    direction: "LR",
                    dropCap: false,
                    indent: 300,
                    getHeight: () => {
                        return 80;
                    },
                    // 重定义折叠标志
                    // getChildren: (model) => {
                    //     return model[ANTV_TREE_COLLAPSED_FLAG] ? [] : model.children;
                    // },
                },
            };
            // this.registerFun();
            if (this.graph) {
                this.graph.destroy();
            }
            this.initGraph(this.graphData);
            this.$nextTick(()=>{
                this.getDeduplicatedInvCodeList();
            })
            window.onresize = () => {
                if (!this.graph || this.graph.get("destroyed")){
                    return;
                }
                if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight){
                    return;
                }
                this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight);
            };
            
        },
        rerenderGraph(){
            // TODO: 这里是hack，这个逻辑本不应该有
            this.matrix = this.graph.getGroup().getMatrix();
            this.graph.changeData(this.graph.save());
        },
        showSelectPartDialog(model){
            this.currentModel = model;
            this.selectedSerialNumber = null;
            this.invName = model.invName;
            this.invCode = model.invCode;
            this.isSelectPartDialogVisible = true;
        },
        onConfirmSelectSerialNumber(item){
            if(!this.selectedSerialNumber){
                return
            }
            const graphData = this.graph.save();
            const inAssembly = !!item.deviceId;
            const inPart = !!item.parentComponentId;
            const duplicatedSn = !!findFirstNode(graphData, data => data.serialNumber === this.selectedSerialNumber&&data.invCode === this.invCode);
            // 在总成中 || 在其他部件内部 || 已在图中被安装
            if(inAssembly||inPart||duplicatedSn){
                this.$message.error("该部件已经被组装，请选择其他部件");
                return;
            }
             
            const componentId = item.componentId;
            apiGetLucidaTreeByNode({componentId}).then(res=>{
                const tree = res.data.data;
                const node = findFirstNode(tree, data => data.componentId === componentId);
                const [{node: templateCurrentNode, parent: templateParentNode}] = findNodesWithParents(this.treeTemplate, data => data.id === this.currentModel.id);
                const partData = this.handleTreeData(node, { operation: 'ASSEMBLE', color: getNextGreen() });
                partData.isAssembleHead = true;
                // 预处理sort字段
                partData.sort = templateCurrentNode.sort;
                // 这里取巧了，直接合并了图数据，正常逻辑应该是合并模板数据后覆盖图数据
                const mergeNode = mergeCompleteTemplateWithPartData(this.currentModel, partData, true);
                this.graph.updateChild(mergeNode, templateParentNode.id);
                this.isSelectPartDialogVisible = false;
                this.rerenderGraph();
            })
            // apiGetComponentItem({serialNumber: this.selectedSerialNumber, invName: this.currentModel.invName}).then((res)=>{
            //     const data = res.data.data || [];
            //     const componentId = data[0]?.componentId;
            //     if(!componentId){
            //         this.$message.error("没有找到对应的组件信息，请检查SN是否正确");
            //         return;
            //     }
            //     apiGetLucidaTreeByNode({componentId}).then(res=>{
            //         const tree = res.data.data;
            //         const node = findFirstNode(tree, data => data.componentId === componentId);
            //         const [{node: templateCurrentNode, parent: templateParentNode}] = findNodesWithParents(this.treeTemplate, data => data.id === this.currentModel.id);
            //         const partData = this.handleTreeData(node, { operation: 'ASSEMBLE', color: getNextGreen() });
            //         partData.isAssembleHead = true;
            //         // 预处理sort字段
            //         partData.sort = templateCurrentNode.sort;
            //         // 这里取巧了，直接合并了图数据，正常逻辑应该是合并模板数据后覆盖图数据
            //         const mergeNode = mergeCompleteTemplateWithPartData(this.currentModel, partData, true);
            //         this.graph.updateChild(mergeNode, templateParentNode.id);
            //         this.isSelectPartDialogVisible = false;
            //         this.rerenderGraph();
            //     })
            // })
        },
        expandToNode(){
            const serialNumber = "16305432";
            const targetNodeId = findFirstNode(this.graph.save(), data => data.serialNumber === serialNumber).id;
            const model = this.graph.findById(targetNodeId)?.getModel();
            if (!model) return;

            let node = model;
            while (node && node.id) {
                const parentNode = this.graph.findById(node.parentId);
                if (parentNode) {
                    this.graph.updateItem(parentNode, { collapsed: false });
                }
                node = parentNode ? parentNode.getModel() : null;
            }
            this.rerenderGraph();
            
            // 等待动画完成后聚焦
            this.$nextTick(()=>{
                this.graph.focusItem(targetNodeId, true, {
                    easing: 'easeCubic',
                    duration: 500,
                });
            })
        },
        // 聚焦节点
        handleNodeClick(e) {
            const item = e.item;
            this.graph.focusItem(item, true, {
                easing: 'easeCubic',
                duration: 500,
            });
        },
        onAddPart(){
        },
    }
}
</script>
<style lang="scss">
    .g6-minimap {
        position: absolute;
        right: 100px;
        top: 100px;
        background-color: #fff;
    }
    .g6-component-tooltip {
        background-color: rgba(0,0,0, 0.65);
        padding: 10px;
        box-shadow: rgb(174, 174, 174) 0px 0px 10px;
        width: fit-content;
        color: #fff;
        display: flex;
        .tooltip-item{
            display: flex;
            .tooltip-label{
                width: 100px;
            }
            .tooltip-content{
                flex: 80px;
            }
        }
    };
    .g6-component-contextmenu {
        .menu-item {
            padding: 6px 10px;
            cursor: pointer;
            transition: background 0.2s;
            white-space: nowrap;
            &:hover {
                background: #f5f5f5;
            }
            &:active {
                background: #e0e0e0;
            }
        }
        .menu-divider {
            height: 1px;
            background: #ddd;
            margin: 4px 0;
        }
    }
</style>
<style lang="scss">
.treegraph-focus-form{
    .el-form-item{
        margin-bottom: 4px;
    }
}
</style>