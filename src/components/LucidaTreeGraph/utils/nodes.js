// NOTE: 全局默认树的子节点字段为children
// 炫技: 为了防止collapsed字段被占用, 设置了ANTV_TREE_COLLPSED_FLAG为折叠标志字段
// TODO: 展开层级(1/all)
import G6 from "@antv/g6";
import { deepCopyTree } from "@/utils/tree";
const icon = require("./icon.png")
export const ANTV_TREE_COLLAPSED_FLAG = "collapsed"
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 240;
const BOX_HEIGHT = 70;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#ccc';
const BOX_RADIUS = 4;
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "red"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;

export const NODE_COLLAPSE_STATUS = 'NODE_COLLAPSED';
export const DISASSEMBLE_NODE = 'DISASSEMBLE_NODE';
export const ASSEMBLE_NODE = 'ASSEMBLE_NODE';
export const GHOST_NODE = 'GHOST_NODE';
export const DEFAULT_NODE = 'DEFAULT_NODE';
export const NODE_TYPE = 'NODE_TYPE';

// 生成节点唯一id的一个方法

function splitTextByWidth(text, fontSize, maxWidth) {
    const parts = [];
    let currentPart = '';
    let currentWidth = 0;
    
    const segments = text.split(/(,)/); // 保留逗号
    
    for (let segment of segments) {
        let segmentWidth = [...segment].reduce((acc, char) => acc + (/[A-Za-z0-9]/.test(char) ? fontSize : fontSize / 3), 0);
        
        if (currentWidth + segmentWidth > maxWidth && segment !== ',') {
            if (currentPart) parts.push(currentPart);
            currentPart = segment;
            currentWidth = segmentWidth;
        } else {
            currentPart += segment;
            currentWidth += segmentWidth;
        }
    }
    
    if (currentPart) {
        parts.push(currentPart);
    }
    
    return parts;
}

const nodes = {
    "tree-default-node": {
        options: {
            style: {
                'main-rect': {
                    rect: {
                        [DEFAULT_NODE]: {
                            width: BOX_WIDTH,
                            height: BOX_HEIGHT,
                            lineWidth: BOX_BORDER_WIDTH,
                            fontSize: 12,
                            radius: BOX_RADIUS,
                            stroke: BOX_BORDER_COLOR,
                            fill: BOX_FILL_COLOR
                        },
                        [GHOST_NODE]: {
                            lineDash: [5, 5],
                            fill: "#f0f0f0"
                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        },
                        [ASSEMBLE_NODE]: {
                            fill: "#A8CD89"
                        }
                    },
                    text: {
                        [DEFAULT_NODE]: {
                            textAlign: "left",
                            textBaseline: "bottom",
                        },
                        [GHOST_NODE]: {

                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        },
                        [ASSEMBLE_NODE]: {
                            fill: "#A8CD89"
                        }
                    }
                },
                'collapse-rect': {
                    rect: {
                        [DEFAULT_NODE]: {
                            width: 16,
                            height: 16,
                            stroke: "rgba(0, 0, 0, 0.25)",
                            cursor: "pointer",
                            fill: "#fff",
                        },
                        [GHOST_NODE]: {
                            lineDash: [5, 5]
                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        }
                    },
                    text: {
                        [DEFAULT_NODE]: {
                            textAlign: "center",
                            textBaseline: "middle",
                            fontSize: 16,
                            cursor: "pointer",
                            fill: "rgba(0, 0, 0, 0.25)",
                        },
                        [GHOST_NODE]: {},
                        [DISASSEMBLE_NODE]: {
                        }
                    }
                }
            }
        },
        draw(cfg, group) {
            // 主节点配置
            const invNameText = splitTextByWidth(cfg.invName, 8, 210);
            const mainRectDefaultStyle = this.options.style['main-rect'].rect[DEFAULT_NODE];
            let nodeType = cfg.nodeType;
            const mainRectCustomStyle = nodeType ? this.options.style['main-rect'].rect[nodeType] : {};
            // 声明主节点中心在长方形的中心
            const nodeOrigin = {
                x: -mainRectDefaultStyle.width / 2,
                y: -mainRectDefaultStyle.height / 2,
            };
            const rect = group.addShape("rect", {
                attrs: {
                    x: nodeOrigin.x,
                    y: nodeOrigin.y,
                    ...mainRectDefaultStyle,
                    ...mainRectCustomStyle,
                    ...(nodeType === ASSEMBLE_NODE && cfg.ASSEMBLE_NODE_COLOR ? { fill: cfg.ASSEMBLE_NODE_COLOR } : {}),
                    ...(nodeType === DISASSEMBLE_NODE && cfg.DISASSEMBLE_NODE_COLOR ? { fill: cfg.DISASSEMBLE_NODE_COLOR } : {})
                },
                name: "main-rect"
            });
            // 文字基本样式
            const mainRectDefaultTextStyle = this.options.style['main-rect'].text[DEFAULT_NODE];
            const mainRectCustomTextStyle = cfg.nodeType ? this.options.style['main-rect'].text[cfg.nodeType] : {};
            // label title
            group.addShape("text", {
                attrs: {
                    ...mainRectDefaultTextStyle,
                    x: 12 + nodeOrigin.x,
                    y: 20 + nodeOrigin.y,
                    text: cfg.invCode,
                    fontSize: 12,
                    opacity: 0.85,
                    fill: "#000",
                },
                // 用于在e.target中作区分
                name: "invCode",
            });
            invNameText.forEach((text, index) => {
                group.addShape("text", {
                    attrs: {
                        ...mainRectDefaultTextStyle,
                        x: 12 + nodeOrigin.x,
                        y: 20 + nodeOrigin.y + 14 * (index+1),
                        text,
                        fontSize: 12,
                        opacity: 0.85,
                        fill: "#000",
                    },
                    // 用于在e.target中作区分
                    name: "invName",
                });
            });
            if(nodeType === GHOST_NODE){
                group.addShape("text", {
                    attrs: {
                        ...mainRectDefaultTextStyle,
                        x: -10,
                        y: 40,
                        text: cfg.isGhostHead ? '+' : '',
                        fontSize: 34,
                        opacity: 0.5,
                        fill: "blue",
                        cursor: "pointer",
                    },
                    // 用于在e.target中作区分
                    name: "assemble",
                });
            }else{
                group.addShape("text", {
                    attrs: {
                        ...mainRectDefaultTextStyle,
                        x: 12 + nodeOrigin.x,
                        y: 30,
                        text: cfg.serialNumber,
                        fontSize: 14,
                        opacity: 0.85,
                        fill: "blue",
                        cursor: "pointer",
                    },
                    // 用于在e.target中作区分
                    name: "serialNumber",
                });
            }
            if(cfg.isAssembleHead || cfg.isDisassembleHead){
                group.addShape('image', {
                    attrs: {
                      x: -nodeOrigin.x - 24,
                      y: nodeOrigin.y + 6,
                      height: 16,
                      width: 16,
                      img: icon,
                    },
                    name: 'node-icon',
                  });
            }
            // collapse图标
            const collapseRectDefaultStyle = this.options.style['collapse-rect'].rect[DEFAULT_NODE];
            // collapse文字基本样式
            const collapseRectDefaultTextStyle = this.options.style['collapse-rect'].text[DEFAULT_NODE];
            if (
                cfg.children &&
                cfg.children.length
            ) {
                group.addShape("rect", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2 - 8,
                        y: -10,
                        ...collapseRectDefaultStyle
                    },
                    name: "collapse-rect",
                });
                group.addShape("text", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2,
                        y: -2,
                        text: cfg[ANTV_TREE_COLLAPSED_FLAG] ? "+" : "-",
                        ...collapseRectDefaultTextStyle
                    },
                    name: "collapse-text",
                });
            }
            return rect;
        },
        setState(name, value, item){
            const group = item.getContainer();
            if (name === NODE_COLLAPSE_STATUS) {
                const collapseText = group.find(
                    (e) => e.get("name") === "collapse-text"
                );
                if (collapseText) {
                    if (!value) {
                        collapseText.attr({ text: "-", });
                    } else {
                        collapseText.attr({ text: "+", });
                    }
                }
            }
            if(name === 'highlight'){
                const mainRect = group.find(
                    (e) => e.get("name") === "main-rect"
                );
                let fill = mainRect.attrs.fill;
                let originFill = mainRect.attrs._originFill || fill;
                if(value==='focus'){
                    mainRect.attr({ _originFill: originFill, fill: "#ff9632" })
                }else if(value==='normal'){
                    mainRect.attr({ _originFill: originFill, fill: "#ffff00" })
                }else{
                    mainRect.attr({ _originFill: originFill, fill: originFill })
                }
            }
        },
    }
}

// string, string[], {name: alias}[]
export function registerCustomNodes(nodeList) {
    if(typeof nodeList==="string"){
        nodeList = [nodeList];
    }
    nodeList.forEach(item => {
        let name, alias;

        if (typeof item === 'string') {
            // 直接传递的节点名
            name = item;
            alias = item;
        } else if (typeof item === 'object' && item !== null) {
            [[name, alias]] = Object.entries(item);
        }

        if (nodes[name]) {
            // 这里默认了是rect, 可能需要维护
            G6.registerNode(alias, nodes[name], 'rect');
        } else {
            console.warn(`Node type "${name}" is not defined.`);
        }
    });
}

export const collapseNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLAPSED_FLAG] = true;
    graph.setItemState(item, NODE_COLLAPSE_STATUS, true);
    graph.layout();
}

export const expandNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLAPSED_FLAG] = false;
    if (model.children) {
        model.children.forEach((child) => {
            child[ANTV_TREE_COLLAPSED_FLAG] = true;
        });
    }
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);
    graph.layout();
}

export const expandNodeAll = (item, graph) => {
    const model = item.getModel();
    const expandAll = (m) => {
        m[ANTV_TREE_COLLAPSED_FLAG] = false;
        if (m.children) {
            m.children.forEach((child) => {
                expandAll(child);
            });
        }
    };
    expandAll(model);
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);
    graph.layout();

}


// 第一棵树是完整的树，将第二颗树中的数据映射到第一棵树，并将残缺部分的所有根节点标记为isGhostHead
// NOTE: 这个方法没有修改源数据，而且返回的是一个深拷贝
export const mergeCompleteTemplateWithPartData = (templateData, data, head) => {
    let res = {};
    Object.keys(templateData).forEach(key => {
        if (key !== 'children') {
            res[key] = templateData[key];
        }
    });
    Object.keys(data).forEach(key => {
        // 排除children和level字段
        if (key !== 'children' && key !== 'level') {
            res[key] = data[key];
        }
    });
    
    if(templateData.children){
        if(!data.children || data.children.length===0){
            // Create deep copy of template children
            res.children = templateData.children.map((templateChild) => {
                const copiedChild = deepCopyTree(templateChild);
                if (head) {
                    copiedChild.isGhostHead = true;
                }
                return copiedChild;
            });
        }else{
            res.children = templateData.children.map((templateChild) => {
                const target = data.children.find(child => child.sort === templateChild.sort);
                if(target){
                    return mergeCompleteTemplateWithPartData(templateChild, target, head);
                }else{
                    const copiedChild = deepCopyTree(templateChild);
                    if (head) {
                        copiedChild.isGhostHead = true;
                    }
                    return copiedChild;
                }
            });
        }
    }
    return res;
}

// 组装时的颜色查询
const greenColors = [
    // "#CCFFCC", // 非常浅的绿色
    // "#80FF80", // 轻盈的春绿
    // "#33FF33", // 纯粹的嫩绿
    // "#ADFF2F", // 绿黄色
    // "#98FB98", // 苍绿
    // "#7CFC00", // 草坪绿
    "#A8CD89"
]
const redColors = [
    // "#FFCCCC", // 非常浅的红色
    // "#FF8080", // 轻盈的粉红
    // "#FF3333", // 纯粹的红色
    // "#FF6347", // 番茄
    // "#FF4500", // 橙红
    // "#FF0000", // 红色
    "#FF8989"
]
function createColorCycler(colors) {
    let index = 0;
    return function () {
      const color = colors[index];
      index = (index + 1) % colors.length; // 循环索引
      return color;
    };
  }
  
export const getNextGreen = createColorCycler(greenColors);
export const getNextRed = createColorCycler(redColors);


export const defaultFieldsToRemove = [
    'id',
    'type',
    'collapsed',
    'isGhostHead',
    'isAssembleHead',
    'isDisassembleHead',
    'nodeType',
    'DISASSEMBLE_NODE_COLOR',
    'ASSEMBLE_NODE_COLOR',
    'style',
    'x',
    'y',
]


export const pruneTree = (tree, fieldsToRemove, shouldRemoveNode) => {
    if (shouldRemoveNode(tree)) {
        return null;
    }

    const cleanedNode = {};

    for (const key in tree) {
        if (!fieldsToRemove.includes(key) && key !== 'children') {
        cleanedNode[key] = tree[key];
        }
    }

    if (Array.isArray(tree.children)) {
        const cleanedChildren = tree.children
            .map(child => pruneTree(child, fieldsToRemove, shouldRemoveNode))
            .filter(Boolean); // 过滤被删掉的子节点

        if (cleanedChildren.length > 0) {
            cleanedNode.children = cleanedChildren;
        }
    }

    return cleanedNode;
}