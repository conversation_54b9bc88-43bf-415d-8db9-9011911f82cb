import momentjs from "moment";
// moment的week周默认起始是周日, isoWeek为周一
export const getThisWeekDaterange = (format='YYYY-MM-DD') => {
    return [momentjs().startOf('isoWeek').format(format), momentjs().format(format)];
}
const getLastWeekDaterange = (format='YYYY-MM-DD') => {
    return [momentjs().subtract(1, 'isoWeek').startOf('isoWeek').format(format), momentjs().subtract(1, 'isoWeek').endOf('isoWeek').format(format)];
}
export const getThisMonthDaterange = (format='YYYY-MM-DD') => {
    return [momentjs().startOf('month').format(format), momentjs().format(format)];
}
const getLastMonthDaterange = (format='YYYY-MM-DD') => {
    return [momentjs().subtract(1,'month').startOf('month').format(format), momentjs().subtract(1,'month').endOf('month').format(format)];
}
export const getAvailableRadioBtnList = (format='YYYY-MM-DD') => {
    // 缓存可能有风险, 但问题不大
    return [
        { label: "THIS_WEEK", text: "本周", daterange: getThisWeekDaterange(format) },
        { label: "LAST_WEEK", text: "上周", daterange: getLastWeekDaterange(format) },
        { label: "THIS_MONTH", text: "本月", daterange: getThisMonthDaterange(format) },
        { label: "LAST_MONTH", text: "上月", daterange: getLastMonthDaterange(format) },
    ]
}

export const getLabelByDaterange = (daterange, labels) => {
    if(Array.isArray(daterange)){
        return labels.find(item=>{
            const itemDaterange = item.daterange;
            return itemDaterange[0] === daterange[0] && itemDaterange[1] === daterange[1];
        })?.label || null;
    }
    return null
}

