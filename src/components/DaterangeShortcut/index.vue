<!-- 这个组件暴露的绑定值是一个daterange, 不是枚举！ -->
<!-- 可以考虑和datepicker组合 -->
<!-- $listeners重写会有问题, 这里只暴露重写的change事件(它也只有这一个事件) 可能有帮助: https://github.com/vuejs/vue/issues/7042 -->
<template>
    <el-radio-group v-model="modelValue" v-bind="$attrs" @change="onDaterangeTypeChange">
        <el-radio-button v-for="item in filterLabels" :key="item.label" :label="item.label">{{ item.text }}</el-radio-button>
    </el-radio-group>
</template>

<script>
import { getAvailableRadioBtnList, getThisWeekDaterange, getLabelByDaterange, getThisMonthDaterange } from "./utils"
export { getThisWeekDaterange, getThisMonthDaterange }
export default {
    name: "DaterangeShortcut",
    props: {
        value:{},
        labels: {
            default: () => ["THIS_WEEK", "LAST_WEEK", "THIS_MONTH", "LAST_MONTH"]
        },
        // 这个format是moment的format, 可能有区别于datepicker
        format: {
            default: "YYYY-MM-DD"
        }, 
    },
    data(){
        return {
            modelValue: null,
        }
    },
    computed: {
        filterLabels(){
            if(Array.isArray(this.labels)){
                const availableRadioBtnList = getAvailableRadioBtnList(this.format)
                return this.labels.reduce((acc, label) => {
                    const item = availableRadioBtnList.find(item => item.label === label);
                    if (item) {
                        acc.push(item);
                    }
                    return acc;
                }, []);
            } else {
                throw new Error("labels must be an array")
            }
        }
    },
    watch:{
        value: {
            deep: true,
            immediate: true,
            handler(n){
                this.modelValue = getLabelByDaterange(n, this.filterLabels);
            }
        }
    },
    methods: {
        onDaterangeTypeChange(value){
            let daterange = this.filterLabels.find(item => item.label === value)?.daterange;
            this.$emit("input", daterange);
            this.$emit("change", daterange);
        }
    }
}
</script>