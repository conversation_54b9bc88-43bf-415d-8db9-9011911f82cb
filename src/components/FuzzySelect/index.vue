<!-- 重写了change事件, emit的数据是全量行数据对象(列表) -->
<!-- NOTE: 可以通过$ref访问query属性来获取qs, 这个qs和modelValue有本质上的不同 -->
<template>
    <el-select
        v-model="modelValue"
        :remote-method="remoteMethod"
        v-bind="computedAttrs"
        @change="onChange"
        @clear="onClear"
        @focus="onFocus"
        style="width: 100%;"
    >
        <el-option
            v-for="item in dataList"
            :key="item[uniqueKey]"
            :label="item[labelKey]"
            :value="item[valueKey]"
        >
            <slot :item="item"></slot>
        </el-option>
  </el-select>
</template>
<script>
import { apiGetMemberList } from '@/api/users';
import { apiGetMemberListByPM } from '@/api/workhours';
import { apiGetProjectList, apiGetProjectWriteableList } from '@/api/project';
import { apiGetInvNameFuzzyList, apiGetSnFuzzyList } from '@/api/tools';
import { apiWellListFuzzy } from '@/api/wellInfo';
import getDict from '@/utils/getDict';
import { apiGetFuzzySerialNumberList } from '@/api/tool-mantain';
import { apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import { apiGetLucidaPartFuzzyList } from '@/api/lucida';
const defaultProps = {
    filterable: true,
    remote: true,
    'reserve-keyword': true,
    clearable: true,
    placeholder:"请输入关键词"
}
export default {
    name: "FuzzySelect",
    props: {
        // 如果有初始化的数据, 这里必须要有对应的列表数据, 否则显示的只有value, 而不是label
        initList: {
            type: Array,
            default: ()=>[]
        },
        value: {
        },
        type:{
            required: true
        },
        restParams: {
            type: Object,
            default: ()=>({})
        },
        // TODO: restParams重新赋值，即使没有改变也会触发watch
        // hack: 临时解决方案
        restLazy: {
            type: Boolean,
            default: false
        },
        // 如果是type是NORMAL, 则需要传入这个参数, 作为数据源
        normalDataList: {
            type: Array,
            default: ()=>[]
        },
    },
    data(){
        return {
            dataList: [],
            modelValue: null,
            restParamsFirstChangeFlag: true,
        }
    },
    computed: {
        computedAttrs() {
            return {
                ...defaultProps,
                ...this.$attrs
            }
        },
        valueKey(){
            return this.searchApiMap[this.type].valueKey
        },
        labelKey(){
            return this.searchApiMap[this.type].labelKey
        },
        uniqueKey(){
            return this.searchApiMap[this.type].key
        },
        allowCreate() {
            const allowCreate = this.$attrs[`allow-create`];
            return allowCreate!==undefined && allowCreate!=="false" && allowCreate!==false
        },
        isMultiple() {
            const multiple = this.$attrs.multiple;
            return multiple!==undefined && multiple!=="false" && multiple!==false
        },
        /* 
            {
                searchApi: 调用的api
                valueKey: 值键
                labelKey: 标签键
                key: 用来作为key的键
            }

        */
        searchApiMap() {
            return {
                PROJECT_NAME: {
                    searchApi: qs => apiGetProjectList({ projectName: qs }, {current:1, size: 9999}).then(res => res.data.data.records),
                    valueKey: "projectName",
                    labelKey: "projectName",
                    key: 'id',
                },
                PROJECT_NUMBER: {
                    searchApi: qs => apiGetProjectList({ projectNumber: qs }, {current:1, size: 9999}).then(res => res.data.data.records),
                    valueKey: "projectNumber",
                    labelKey: "projectNumber",
                    key: 'id',
                },
                WRITEABLE_PROJECT_NUMBER: {
                    searchApi: qs => apiGetProjectWriteableList({ projectNumber: qs }, {current:1, size: 9999}).then(res => res.data.data.records),
                    valueKey: "projectNumber",
                    labelKey: "projectNumber",
                    key: 'id',
                },
                USER_ALL: {
                    searchApi: qs => apiGetMemberList({ name: qs }).then(res => res.data.data.memberInfoList),
                    valueKey: "memberId",
                    labelKey: "name",
                    key: 'memberId',
                },
                USER_FOR_PM: {
                    searchApi: qs => apiGetMemberListByPM({ memberName: qs }).then(res => res.data.data),
                    valueKey: "memberId",
                    labelKey: "name",
                    key: 'memberId',
                },
                MWD_INVNAME: {
                    searchApi: qs => apiGetInvNameFuzzyList({ invName: qs, toolType: "MWD" }).then(res => res.data.data),
                    valueKey: "invName",
                    labelKey: "invName",
                    key: 'invCode',
                },
                CORE_SN: {
                    searchApi: qs => apiGetSnFuzzyList({ serialNumber: qs || undefined, ...this.restParams }).then(res => res.data.data.map(item=>({value: item, label: item}))),
                    valueKey: "value",
                    labelKey: "label",
                    key: 'value',
                },
                LUCIDA_PART_INFO: {
                    searchApi: qs => apiGetLucidaPartFuzzyList({ serialNumber: qs || undefined, ...this.restParams }).then(res => res.data.data?.records || []),
                    valueKey: "serialNumber",
                    labelKey: "serialNumber",
                    key: 'componentId',
                },
                DEVICE_SN: {
                    searchApi: qs => apiGetFuzzySerialNumberList({ serialNumber: qs || null, ...this.restParams }).then(res => res.data.data.map(item=>({value: item, label: item}))),
                    valueKey: "value",
                    labelKey: "label",
                    key: 'value',
                },
                DEVICE_INFO: {
                    searchApi: qs => apiGetWarehouseDeviceBatchInfo({ serialNumber: qs || null, ...this.restParams }).then(res => res.data.data),
                    valueKey: "serialNumber",
                    labelKey: "serialNumber",
                    key: 'deviceId',
                },
                WELL_NUMBER: {
                    searchApi: qs => {
                        const form = new FormData();
                        form.append("wellNumberKey", qs || "");
                        return apiWellListFuzzy(form).then(res => res.data.data)
                    },
                    valueKey: "wellNumber",
                    labelKey: "wellNumber",
                    key: 'wellId',
                },
                DEVICE_TYPE: {
                    searchApi: qs => {
                        return getDict([17]).then(res=>{
                            // TODO: 抽出一个公共模糊匹配方法
                            return qs ? res[0].filter(item => item.name.toLowerCase().includes(qs.toLowerCase())) : res[0]
                        })
                    },
                    valueKey: "id",
                    labelKey: "name",
                    key: 'id',
                },
                NORMAL: {
                    searchApi: qs => {
                        return Promise.resolve(this.normalDataList).then(res=>{
                            const data = qs ? res.filter(item => item.toLowerCase().includes(qs.toLowerCase())) : res;
                            return data.map(item=>({value: item, label: item}))
                        })
                    },
                    valueKey: "value",
                    labelKey: "label",
                    key: 'value',
                }
            }
        }
    },
    watch:{
        modelValue:{
            deep: true,
            handler(n){
                this.$emit("input",n);
            }
        },
        initList:{
            deep: true,
            immediate: true,
            handler(n){
                this.dataList = n;
                if(this.isMultiple){
                    this.emitList = n;
                }
            }
        },
        value: {
            deep: true,
            handler(n){
                this.modelValue = n
            }
        },
        restParams: {
            handler(){
                if(this.restLazy){
                    return
                }
                // 在第一次改变时禁止触发
                if(this.restParamsFirstChangeFlag){
                    this.restParamsFirstChangeFlag = false;
                    return
                }
                this.remoteMethod("")
            }
        },
        normalDataList(){
            this.remoteMethod("");
        }
    },
    mounted(){
        this.modelValue = this.value;
    },
    methods: {
        remoteMethod(qs){
            const { searchApi } = this.searchApiMap[this.type]
            Promise.resolve(searchApi(qs)).then(res=>{
                // const data = res.data.data || [];
                this.dataList = res || []
            })
        },
        onChange(n){
            this.$emit("input",n);
            if(this.isMultiple){
                this.emitList = this.getEmitList()
                this.$emit("change", this.emitList)
            }else{
                this.$emit("change", this.dataList.find(item=>item[this.valueKey]===n) || {})
            }
        },
        getEmitList(){
            if(!this.modelValue.length){
                return []
            };
            return this.modelValue.map(value=>{
                // 先从emitList中找, 能找到说明是本身就存在的, 然后在dataList中找, 能找到说明是新增的
                // 如果allow-create, 则有可能是新建的, 则返回相应键值对
                const emitItem = this.emitList.find(item=>item[this.valueKey]===value);
                const dataItem = this.dataList.find(item=>item[this.valueKey]===value);
                let createFieldItem;
                if(this.allowCreate){
                    const createField = this.$attrs[`create-field`] || 'defaultCreateField';
                    createFieldItem = { [createField]: value }
                }
                return emitItem || dataItem || createFieldItem;
            })
        },
        onClear(){
            this.remoteMethod("")
        },
        onFocus(){
            // hack: 解决初始化时, qs为空值不触发remoteMethod的问题
            if(!this.dataList?.length){
                this.remoteMethod("")
            }
        }
    }
}
</script>