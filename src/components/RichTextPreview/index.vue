<template>
  <div class="rich-text-preview-container">
    <!-- 预览模式 -->
    <div
      v-if="!expanded"
      class="preview-mode"
      @click="handlePreviewClick"
    >
      <div class="preview-content" :class="{ 'has-content': hasContent, 'size-large': size === 'large' }">
        <div v-if="hasContent" class="preview-text" v-html="previewContent"></div>
        <div v-else class="no-content">{{ emptyText }}</div>
        <div v-if="showExpandButton" class="preview-actions">
          <el-button type="text" size="mini" @click.stop="toggleExpand">
            <i class="el-icon-view"></i> {{ expandButtonText }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 展开模式 - 弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      :close-on-click-modal="false"
      class="rich-text-dialog"
      :append-to-body="true"
    >
      <div class="rich-text-dialog-content">
        <image-viewer>
          <div v-html="sanitizedContent" class="rich-text-content"></div>
        </image-viewer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DOMPurify from 'dompurify';
import ImageViewer from '@/components/ImageViewer/index.vue';

export default {
  name: 'RichTextPreview',
  components: {
    ImageViewer
  },
  props: {
    // 富文本内容
    content: {
      type: String,
      default: ''
    },
    // 尺寸大小：'small' 表格中使用，'large' 展开详情中使用
    size: {
      type: String,
      default: 'small',
      validator: value => ['small', 'large'].includes(value)
    },
    // 标题
    title: {
      type: String,
      default: '内容详情'
    },
    // 预览内容最大长度
    previewMaxLength: {
      type: Number,
      default: 150  // 增加默认长度以显示更多内容
    },
    // 空内容显示文本
    emptyText: {
      type: String,
      default: '无内容'
    },
    // 展开按钮文本
    expandButtonText: {
      type: String,
      default: '查看完整内容'
    },
    // 是否显示展开按钮
    showExpandButton: {
      type: Boolean,
      default: true
    },
    // 弹窗宽度
    dialogWidth: {
      type: String,
      default: '70%'
    }
  },
  data() {
    return {
      expanded: false,
      dialogVisible: false
    };
  },
  computed: {
    // 是否有内容
    hasContent() {
      return !!this.content && this.content.trim() !== '';
    },
    // 净化后的内容
    sanitizedContent() {
      return this.sanitizeHtml(this.content);
    },
    // 预览内容
    previewContent() {
      if (!this.hasContent) return '';

      // 先净化HTML
      const sanitizedHtml = this.sanitizeHtml(this.content);

      // 创建临时DOM元素以提取纯文本
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = sanitizedHtml;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';

      // 优化的文本截取逻辑
      if (textContent.length > this.previewMaxLength) {
        // 智能截取：尽量在句号、逗号等标点符号处截断
        let cutText = textContent.substring(0, this.previewMaxLength);
        const punctuationIndex = Math.max(
          cutText.lastIndexOf('。'),
          cutText.lastIndexOf('，'),
          cutText.lastIndexOf('；'),
          cutText.lastIndexOf('：'),
          cutText.lastIndexOf('！'),
          cutText.lastIndexOf('？')
        );

        // 如果找到合适的标点符号位置，且不会截取太多内容
        if (punctuationIndex > this.previewMaxLength * 0.7) {
          cutText = textContent.substring(0, punctuationIndex + 1);
        }

        return this.sanitizeHtml(cutText + (cutText.endsWith('。') || cutText.endsWith('！') || cutText.endsWith('？') ? '' : '...'));
      }

      return sanitizedHtml;
    }
  },
  methods: {
    // 切换展开状态
    toggleExpand() {
      this.dialogVisible = true;
    },
    // 处理预览点击
    handlePreviewClick() {
      if (this.hasContent) {
        // 无论是否显示展开按钮，点击预览内容都可以查看完整内容
        this.toggleExpand();
      }
    },
    // HTML内容净化，防止XSS攻击
    sanitizeHtml(html) {
      if (!html) return '';

      try {
        // 使用DOMPurify库进行HTML内容净化
        // 配置DOMPurify允许保留图片但限制大小
        const sanitizedHtml = DOMPurify.sanitize(html, {
          ADD_TAGS: ['img'],
          ADD_ATTR: ['src', 'alt', 'width', 'height', 'style', 'class', 'loading', 'data-original'],
          FORBID_TAGS: ['script', 'iframe', 'object', 'embed'],
          FORBID_ATTR: ['onerror', 'onload', 'onclick']
        });

        // 处理HTML中的图片，限制最大宽度
        const parser = new DOMParser();
        const doc = parser.parseFromString(sanitizedHtml, 'text/html');
        const images = doc.querySelectorAll('img');

        images.forEach(img => {
          // 保存原始图片URL
          const originalSrc = img.getAttribute('src');
          if (originalSrc) {
            img.setAttribute('data-original', originalSrc);
          }

          // 设置样式
          img.style.maxWidth = '100%';
          img.style.maxHeight = '300px';
          img.style.cursor = 'pointer';
          img.style.transition = 'transform 0.3s';
          img.style.borderRadius = '4px';
          img.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
          img.style.margin = '8px 0';

          // 添加懒加载属性
          img.setAttribute('loading', 'lazy');

          // 添加类名，方便CSS样式控制
          img.classList.add('rich-content-image');
        });

        // 将处理后的HTML转换回字符串
        return doc.body.innerHTML;
      } catch (error) {
        console.error('HTML净化失败:', error);
        // 如果处理失败，返回原始HTML（不推荐，但作为降级处理）
        return html;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.rich-text-preview-container {
  width: 100%;
}

.preview-mode {
  cursor: pointer;
  position: relative; /* 确保绝对定位的按钮能正确定位 */
}

.preview-content {
  background-color: #f9f9f9;
  padding: 4px 8px; /* 默认紧凑的内边距 */
  border-radius: 4px;
  border-left: 3px solid #dcdfe6;
  font-size: 13px;
  line-height: 1.5;
  transition: all 0.3s;
  min-height: 28px; /* 默认紧凑的高度 */
  display: flex;
  flex-direction: column;
  justify-content: center; /* 默认垂直居中 */

  /* 大尺寸样式 - 用于展开详情 */
  &.size-large {
    padding: 8px 10px; /* 增加内边距 */
    min-height: 60px; /* 增加高度，显示更多内容 */
    justify-content: flex-start; /* 内容从顶部开始 */
  }

  &.has-content {
    border-left-color: #409EFF;

    &:hover {
      background-color: #ecf5ff;
      border-left-color: #66b1ff;
    }
  }
}

.preview-text {
  max-height: 36px;  /* 默认紧凑的最大高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  line-height: 1.2;  /* 默认紧凑的行高 */
  flex: 1; /* 占据可用空间 */
  padding-right: 80px; /* 为右侧按钮留出空间 */

  /* 大尺寸样式 - 用于展开详情 */
  .size-large & {
    max-height: 80px;  /* 增加最大高度，显示更多内容 */
    line-height: 1.4;  /* 稍微增加行高，提高可读性 */
  }

  ::v-deep img {
    display: none; /* 在预览中隐藏图片 */
  }

  /* 优化段落间距以显示更多内容 */
  ::v-deep p {
    margin: 0; /* 移除段落间距以最大化利用空间 */
    line-height: 1.3; /* 与外层保持一致的适中行高 */
  }

  /* 移除多余的空白字符 */
  ::v-deep * {
    white-space: normal;
  }
}

.no-content {
  color: #909399;
  font-style: italic;
}

.preview-actions {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%); /* 垂直居中 */
  display: flex;
  justify-content: flex-end;
  z-index: 10; /* 确保按钮在文字上方 */

  /* 如果没有内容则不显示 */
  &:empty {
    display: none;
  }

  /* 给按钮添加背景以确保可见性 */
  ::v-deep .el-button {
    font-size: 11px;
    padding: 2px 6px;

    &:hover {
      background-color: rgba(236, 245, 255, 0.95);
    }
  }
}

/* 富文本对话框样式 */
::v-deep .rich-text-dialog .el-dialog__body {
  padding: 10px 20px;
}

.rich-text-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.rich-text-content {
  line-height: 1.6;
  font-size: 14px;

  ::v-deep p {
    margin: 8px 0;
    word-break: break-word;
  }

  ::v-deep img {
    max-width: 100%;
    height: auto;
    max-height: 500px;
    margin: 12px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: block;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
