<!-- 在这个container中左右滑动能抛出相应事件 -->
<template>
    <div class="swipe-container" @touchstart.passive="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
      <slot></slot>
    </div>
  </template>
  
  <script>
  export default {
    name: "SwipeContainer",
    data() {
      return {
        touchStartX: 0,
        touchEndX: 0,
      };
    },
    methods: {
      onTouchStart(event) {
        event.preventDefault();  // Prevent scrolling on touchmove
        this.touchStartX = event.touches[0].clientX;
      },
      onTouchMove(event) {
        this.touchEndX = event.touches[0].clientX;
      },
      onTouchEnd() {
        const diff = this.touchEndX - this.touchStartX;
        if (diff > 50) {
          this.$emit("swipe-right");
        } else if (diff < -50) {
          this.$emit("swipe-left");
        }
        this.touchStartX = 0;
        this.touchEndX = 0;
      },
    },
  };
  </script>
  
  <style scoped>
  .swipe-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  </style>
  