<template>
    <van-cell style="padding: 0">
        <van-field
            v-model="modelValue"
            v-bind="computedAttrs"
            readonly
            clickable
            @click="showCalendar = true">
        </van-field>
        <van-calendar :min-date="minDate" :max-date="maxDate" :default-date="defaultDate" v-model="showCalendar" @confirm="onConfirm" />
    </van-cell>
</template>
<script>
const defaultProps = {
    readonly: true,
    clickable: true,
}
export default {
    name: "VanDatePicker",
    props: {
        value: {
        },
    },
    data() {
        return {
            modelValue: null,
            showCalendar: false,
            minDate: new Date(),
            maxDate: new Date(),
            defaultDate: new Date(),
        }
    },
    
    computed: {
        computedAttrs() {
            return {
                ...defaultProps,
                ...this.$attrs
            }
        },
    },
    watch:{
        modelValue(n){
            this.$emit("input",n);
        },
        value: {
            immediate: true,
            handler(n){
                this.modelValue = n;
            }
        }
    },
    mounted(){
        const year = new Date().getFullYear();
        this.minDate = new Date(`${year - 3}`);
        this.maxDate = new Date(`${year + 3}`);
        if(this.value){
            this.defaultDate = new Date(this.value);
        }
    },
    methods: {
        onConfirm(date){
            this.modelValue = date.Format('yyyy-MM-dd');
            this.showCalendar = false;
        },
    }
}
</script>