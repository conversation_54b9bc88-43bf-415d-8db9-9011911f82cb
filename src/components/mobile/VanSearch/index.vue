<template>
    <van-cell style="padding: 0;">
        <van-field
            v-model="modelValue"
            v-bind="computedAttrs"
            @click="onFieldClick"
        >
        </van-field>
        <van-popup @opened="onPopupOpened" position="right" get-container="body" style="height: 100%;width: 100%;" v-model="showPopup">
            <SwipeContainer @swipe-right="onHideSearch">
                <div class="top-container">
                    <van-icon class="arrow-icon" name="arrow-left" @click="onHideSearch" />
                    <div class="search-container">
                        <van-field clearable class="search-field" :class="{ hideCaret: !isPopupOpened }" @input="onQueryChange" ref="searchFieldRef" v-model="searchValue" placeholder="请输入搜索关键词">
                            <template #left-icon>
                                <van-icon name="search" />
                            </template>
                        </van-field>
                    </div>
                    <span v-if="searchType==='INPUT'" style="color:#1989fa;font-size: 15px;margin-left: 6px;" @click="onConfirm">确定</span>
                </div>
                <!-- 最大8行 -->
                <template v-if="!isSearching">
                    <div class="list-container" v-if="searchResults.length">
                        <van-cell-group v-for="item in searchResults" :key="item[uniqueKey]">
                            <van-cell
                                :title="item[valueKey]"
                                :value="item[labelKey]"
                                @click="onSearchItemClick(item)"
                            />
                        </van-cell-group>
                    </div>
                    <div class="empty-container" v-else>
                        <van-empty description="暂无数据" />
                    </div>
                </template>
            
            </SwipeContainer>
        </van-popup>
    </van-cell>
</template>
<script>
const defaultProps = {
    clickable: true,
    readonly: true,
}
import { apiGetWarehouseDeviceList } from "@/api/warehouse";
import { apiGetWellListWithJobs } from "@/api/wellInfo";
import SwipeContainer from "@/components/mobile/SwiperContainer";
export default {
    name: "VanSearch",
    components: { SwipeContainer },
    props: {
        // 如果有初始化的数据, 这里必须要有对应的列表数据, 否则显示的只有value, 而不是label
        initList: {
            type: Array,
            default: ()=>[]
        },
        value: {
        },
        searchType: {
            type: String,
            default: "SELECT"
        },
        type:{
            required: true
        },
        restParams: {
            type: Object,
            default: ()=>({})
        },
    },
    data() {
        return {
            modelValue: null,
            showPopup: false,
            searchValue: "",
            searchResults: [],
            isSearching: true, // 当接口在调用时, 不显示任何结果, 类似isLoading
            isPopupOpened: false, // 用于控制是否显示光标, 修复光标闪烁的bug
        }
    },
    
    computed: {
        computedAttrs() {
            return {
                ...defaultProps,
                ...this.$attrs
            }
        },
        valueKey(){
            return this.searchApiMap[this.type].valueKey
        },
        labelKey(){
            return this.searchApiMap[this.type].labelKey
        },
        uniqueKey(){
            return this.searchApiMap[this.type].key
        },
        searchApiMap(){
            return {
                WELL_LIST_WITH_JOBS: {
                    searchApi: qs => apiGetWellListWithJobs({wellNumberKey: qs || ""}).then((res) => res.data?.data || [] ),
                    valueKey: "wellNumber",
                    labelKey: "wellNumber",
                    key: 'wellId',
                },
                SN_IN_STOCK: {
                    searchApi: qs => apiGetWarehouseDeviceList(
                        { serialNumber: qs || undefined, ...this.restParams }, 
                        { current: 1, size: 20 }
                    ).then(res => { const data = res.data.data || {}; return data.records || []; }),
                    valueKey: "serialNumber",
                    labelKey: "serialNumber",
                    key: 'deviceId',
                },
            }
        }
    },
    watch:{
        modelValue(n){
            this.$emit("input",n);
        },
        value: {
            immediate: true,
            handler(n){
                this.modelValue = n;
            },
        }
    },
    mounted(){
    },
    methods: {
        onFieldClick(){
            // 初始化数据, 打开popup, 聚焦搜索框
            this.isSearching = true;
            this.searchResults = [];
            this.searchValue = this.modelValue;
            this.onQueryChange(this.searchValue);
            this.showPopup = true;
            this.$nextTick(()=>{
                // 在这里聚焦搜索框, ios没有问题, 但是因为popup动画的问题, 聚焦的光标会移动并出现闪烁, 这里使用isPopupOpened来控制是否将光标变成透明的
                this.$refs.searchFieldRef.focus();
            })
        },
        onPopupOpened(){
            this.isPopupOpened = true;
        },
        // NOTE: 直接搜索和选择搜索的emit结果不一样
        onSearchItemClick(item){
            this.modelValue = item[this.valueKey];
            this.$emit("change", item);
            this.onHideSearch();
        },
        onConfirm(){
            this.modelValue = this.searchValue;
            this.$emit("change", this.searchValue);
            this.onHideSearch();
        },
        onHideSearch(){
            // 失焦, 隐藏popup
            this.$refs.searchFieldRef.blur();
            this.showPopup = false;
            this.isPopupOpened = false;
        },
        onQueryChange(){
            this.isSearching = true;
            const { searchApi } = this.searchApiMap[this.type]
            Promise.resolve(searchApi(this.searchValue)).then(res=>{
                // const data = res.data.data || [];
                this.searchResults = res || []
            }).finally(() => {
                this.isSearching = false;
            });
        }
    }
}
</script>
<style lang="scss">
.top-container{
    display: flex;
    align-items: center;
    padding: 10px;
    .arrow-icon{
        font-size: 18px;
    }
    .search-container {
        background-color: #ffffff;
        flex: 1;
        margin-left: 6px;
        .search-field{
            padding: 5px 10px;
            background-color: #f7f8fa;
            border-radius: 16px;
            .van-field__value{
            }
            .van-field__control{
                caret-color: auto;
            }
            &.hideCaret{
                .van-field__control{
                    caret-color: transparent;
                }
            }
        }
    }
}
.list-container{
    height: 352px; // 8行
    overflow-y: auto;
}
</style>