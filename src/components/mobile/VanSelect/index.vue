<!-- 二次封装的表单组件都使用了van-cell容器, 为了保留cell下面的横线 -->
<template>
    <van-cell style="padding: 0">
        <van-field            
            v-bind="computedAttrs"
            :value="modelValue"
            @click="onClickField"
        />
        <van-popup v-model="showPicker" round position="bottom">
            <van-picker
                show-toolbar
                :columns="options"
                :value-key="labelKey"
                :default-index="defaultIndex"
                @cancel="showPicker = false"
                @confirm="onConfirm"
            />
        </van-popup>
    </van-cell>
</template>
<script>
const defaultProps = {
    readonly: true,
    clickable: true,
    placeholder: null
}
export default {
    name: "VanSelect",
    props: {
        value: {
        },
        options: {
            default: () => [],
            type: Array
        },
        valueKey: {
            required: true,
            type: String,
        },
        labelKey: {
            required: true,
            type: String,
        }
    },
    data() {
        return {
            modelValue: null,
            showPicker: false,
            defaultIndex: null,
        }
    },
    
    computed: {
        computedAttrs() {
            return {
                ...defaultProps,
                ...this.$attrs
            }
        },
    },
    watch:{
        value: {
            immediate: true,
            handler(n){
                this.modelValue = this.options.find(item=>item[this.valueKey] === n)?.[this.labelKey] || null;
            }
        }
    },
    mounted(){
        // 初始化输入框显示
        // this.modelValue = this.options.find(item=>item[this.valueKey] === this.value)?.[this.labelKey] || null;
    },
    methods: {
        onClickField(){
            // 初始化picker选择
            // TODO: 这里只有第一次是正确的, 用户滑动后不选择然后关闭, 下次再打开仍然会在上次滑动的位置, 虽然不太准确, 但很难说不是feature
            this.defaultIndex = this.options.findIndex(item=>item[this.valueKey] === this.value) ?? null;
            this.showPicker = true;
        },
        onConfirm(item){
            this.modelValue = item[this.labelKey];
            this.showPicker = false;
            // NOTE: change事件emit的是整个item, 而不是item[this.valueKey]
            if(this.value != item[this.valueKey]){
                this.$emit("change", item);
            }
            this.$emit("input", item[this.valueKey]);
        },
    }
}
</script>