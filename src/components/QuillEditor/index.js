import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import resizeImage from "quill-image-resize-module";
require("vue-quill-editor").Quill.register("modules/resizeImage", resizeImage);
import { quillEditor } from "vue-quill-editor";
const QuillEditor = {
  install: function (Vue) {
      // 注册并获取组件，然后在main.js中引用，在Vue.use()就可以了
      Vue.component('QuillEditor', quillEditor)
  }
}
export default QuillEditor