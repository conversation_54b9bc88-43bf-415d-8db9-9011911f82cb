/* 
    node    group   -> shape
                    -> keyShape
    edge    group   -> shape
*/
// enum
import { $checkBtnPermission } from "@/utils/common"
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 200;
const BOX_HEIGHT = 50;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#ccc';
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "rgb(215, 215, 215)"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;
export const openLink = (type, model) => {
    const getLink = linkMap[type]?.link;
    const permissionCode = linkMap[type]?.permissionCode;
    if(permissionCode && !$checkBtnPermission(permissionCode)){
        return;
    }
    if(getLink){
        const link = getLink(model);
        if(link){
            window.open(link, '_blank');
        }
    }
}
const linkMap = {
    AT_RIG: {
        permissionCode: `sys:mwd:info`,
        link: ({jobId}) => jobId ? `/daily/detail?jobid=${jobId}` : null
    },
    WORK_ORDER_MWD: {
        permissionCode: `sys:dailydetail:view`,
        link: ({businessId}) => businessId ? `/mwd-tool-maintain/maintain?mwdId=${businessId}` : null
    },
    WAREHOUSE: {
        permissionCode: `sys:warehouse:kit:load:detail`, // 只写了一个, 但通常这两个是一起的
        link: ({businessId, boxType}) => {
            if(businessId && boxType){
                const loadType = boxType == 16035 ? 'twelllink' : 'kit';
                return `/warehouse/${loadType}/load/detail?id=${businessId}`;
            }
            return null;
        }
    },
    REPAIR: {
        permissionCode: `sys:mwd:repair:detail`,
        link: ({businessId}) => businessId ? `/mwd/repair/detail?id=${businessId}` : null
    }
}
export const circulateMap = {
    TRANSFER_ORDER: {
        label: '调拨单',
    },
    DEMAND_ORDER: {
        label: '需求单',
    },
    HANDOVER: {
        label: '交接单',
    },
    WAREHOUSE: {
        label: '出入库',
    },
    REPAIR: {
        label: '返修单',
    },
    OUT_BOUND_ORDER: {
        label: '出库单',
    },
    WORK_ORDER_MWD: {
        label: 'MWD维修工单',
    },
}
// TODO: edge方向
export const dataGenerator = (data, deviceStatusList) => {
    const stdData = handleData1(data);
    const nodes = [];
    const edges = [];
    const rowLen = stdData.length;

    let curNodeIndex = 0;

    for (let i = 0; i < rowLen; i++) {
        const rowData = stdData[i];
        for (let j = 0; j < rowData.length; j++) {
            const curNodeId = `node-${curNodeIndex}`;
            const prevNodeId = `node-${curNodeIndex-1}`
            const rowItem = rowData[j];
            nodes.push({ 
                ...rowItem,
                statusStr: deviceStatusList.find(item => item.id === rowItem.status)?.name || "",
                id: curNodeId,
                x: INIT_X + (BOX_WIDTH + COL_GUTTER) * j,
                y: INIT_Y + (BOX_HEIGHT + ROW_GUTTER) * (rowLen - i),
                anchorPoints: [ANCHOR.LEFT, ANCHOR.RIGHT]
            });
            if(!rowItem.isStart){
                let label = "";
                if(rowItem && circulateMap[rowItem.circulateType]){
                    label = circulateMap[rowItem.circulateType].label || "";
                }
                edges.push({ 
                    ...rowItem,
                    label,
                    source: prevNodeId,
                    target: curNodeId,
                    sourceAnchor: 1,
                    targetAnchor: 0,
                });
            }
            curNodeIndex++;
        }
    }
    return { nodes, edges }
}

export const defaultNodeGenerator = (cfg, group) => {
    // const { isStart, isEnd } = cfg;
    // 从内到外add
    // const point_offset = 2 * BOX_BORDER_WIDTH - POINT_RADIUS / 2;
    // const point_cfg = {
    //     r: POINT_RADIUS,
    //     fill: POINT_COLOR,
    //     stroke: POINT_COLOR,
    // }
    // if(!isStart){
    //     // 左侧
    //     group.addShape('circle', {
    //         attrs: {
    //             x: -point_offset,
    //             y: BOX_HEIGHT / 2,
    //             ...point_cfg
    //         },
    //         name: 'default-start-point'
    //     })
    // }
    // if(!isEnd){
    //     // 右侧
    //     group.addShape('circle', {
    //         attrs: {
    //             x: BOX_WIDTH + point_offset,
    //             y: BOX_HEIGHT / 2,
    //             ...point_cfg
    //         },
    //         name: 'default-end-point'
    //     })
    // }
    /* 
        keyshape
            anchorPoints(边的两端)相对于这个
            group中的其它shape的位置都基于它, 所以它的(x, y)应该取原点
    */ 
    const node = group.addShape('rect', {
        attrs: {
            x: 0,
            y: 0,
            width: BOX_WIDTH,
            height: BOX_HEIGHT,
            fill: BOX_FILL_COLOR,
            lineWidth: BOX_BORDER_WIDTH,
            stroke: BOX_BORDER_COLOR,
            ...(cfg.style || {}),
        },
        name: 'key-rect-shape'
    })
    
    group.addShape('text', {
        attrs: {
            text: cfg.statusStr,
            x: BOX_WIDTH / 2,
            y: BOX_HEIGHT / 2,
            fontSize: 14,
            textAlign: 'center',
            textBaseline: 'middle',
            cursor: 'pointer',
            fill: '#333',
        },
        name: 'default-status-text'
    })
    if(cfg.status === 19001){
        const text = cfg.jobId && cfg.jobNumber ? `${cfg.location}-${cfg.jobNumber}` : cfg.location;
        group.addShape('text', {
            attrs: {
                text: text,
                x: BOX_WIDTH / 2,
                y: BOX_HEIGHT + 20,
                fontSize: 14,
                textAlign: 'center',
                textBaseline: 'middle',
                cursor: 'pointer',
                fill: '#333',
            },
            name: 'default-location-text'
        })
    }
    // if(cfg.status === 19002){
    //     group.addShape('text', {
    //         attrs: {
    //             text: cfg.businessNumber,
    //             x: BOX_WIDTH / 2,
    //             y: BOX_HEIGHT + 20,
    //             fontSize: 14,
    //             textAlign: 'center',
    //             textBaseline: 'middle',
    //             cursor: 'pointer',
    //             fill: '#409eff',
    //         },
    //         name: 'default-business-text'
    //     })
    // }
    return node
}

/* 
    TODO:

    highlight   ✔
    arrow       ✔
    cursor      ✔
    click       ✔
    hover       ✔
    long text
    contextmenu ✔     
    animation   ✔
    要关联的cfg
*/
export const defaultEdgeGenerator = (cfg, group) => {
    const { startPoint, endPoint } = cfg;
    const { x: startX, y: startY } = startPoint;
    const { x: endX, y: endY } = endPoint;
    // label
    const label_font_size = 14;
    const label_offset_y = label_font_size / 2 + 8; // 4 是label和线的间距
    group.addShape('text', {
        attrs: {
            text: cfg.label,
            x: (startX + endX) / 2,
            y: (startY + endY) / 2 - label_offset_y, // 在线中间的上方
            fontSize: label_font_size,
            textAlign: 'center',
            textBaseline: 'middle',
            cursor: 'pointer',
            fill: '#333',
            position: 'middle',
        },
        name: 'default-edge-text-shape',
    });
    if(cfg.businessNumber){
        group.addShape('text', {
            attrs: {
                text: cfg.businessNumber,
                x: (startX + endX) / 2,
                y: (startY + endY) / 2 + label_offset_y, // 在线中间的下方
                fontSize: label_font_size,
                textAlign: 'center',
                textBaseline: 'middle',
                cursor: 'pointer',
                fill: '#333',
                position: 'middle',
            },
            name: 'default-edge-business-text-shape',
        });
    }
    // line
    let edge;
    const edge_cfg = {
        lineWidth: LINE_WIDTH,
        stroke: LINE_COLOR,
        endArrow: {
            path: 'M 0,0 L 12,6 L 9,0 L 12,-6 Z',
            d: 0,
            fill: LINE_COLOR,
        },
    }
    if(cfg.isCycleStart){
        // s形线
        const sPath = [
            ['M', startX, startY],
            ['C', startX + 200, startY - 100, endX - 200, endY + 100, endX, endY], // TODO: 贝塞尔曲线通用策略
        ];
        edge = group.addShape('path', {
            attrs: {
                path: sPath,
                lineDash: [10, 5],
                ...edge_cfg,
            },
            name: 's-path-shape',
        });
    }else{
        // 直线
        edge = group.addShape('path', {
            attrs: {
                path: [['M', startX, startY], ['L', endX, endY]],
                ...edge_cfg,
            },
            name: 'l-path-shape',
        });
    }
    return edge;
}

/* 
    [{status}] -> [[{status, isStart, isEnd, isCycleStart, isCycleEnd}]]
*/

function handleData1(arr) {
  if (!arr || arr.length === 0) return [];

  const reversedArr = [...arr].reverse();
  const result = [];
  let currentGroup = [];
  let cycleStarted = false;

  const len = reversedArr.length;
  const first = reversedArr[0];
  const last = reversedArr[len - 1];

  // 标记起点与终点
  first.isStart = true;
  first.isCycleStart = (first.status === 19003);
  last.isEnd = true;

  for (let i = 0; i < len; i++) {
    const current = reversedArr[i];
    const next = reversedArr[i + 1];

    currentGroup.push(current);

    const nextIsCycleStart = next && next.status === 19003;

    // 判断是否是一个循环段的结束
    if (nextIsCycleStart) {
      current.isCycleEnd = true;
      next.isCycleStart = true;

      result.push(currentGroup);
      currentGroup = [];
      cycleStarted = true;
    }

    // 处理最后一个元素
    if (!next) {
      current.isEnd = true;
      if (cycleStarted || current.status === 19003) {
        current.isCycleEnd = true;
      }
    }
  }

  // 添加最后一组
  if (currentGroup.length > 0) {
    result.push(currentGroup);
  }

  // 特殊情况处理
  if (result.length === 1 && first.status === 19003) {
    first.isCycleStart = true;
  }

  if (!cycleStarted && last.status === 19003) {
    last.isCycleStart = true;
  }

  return result;
}
