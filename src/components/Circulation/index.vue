<template>
    <div id="container" style="height: 100%;">

    </div>
</template>
<script>
import G6 from '@antv/g6';
import { defaultNodeGenerator, defaultEdgeGenerator, dataGenerator, openLink } from './utils'
import getDict from '@/utils/getDict';
export default {
    name: 'Circulation',
    data(){
        return {}
    },
    methods: {
      async initData(data){
        if(this.graph){
          this.graph.destroy();
          await this.$nextTick();
        }
        [this.deviceStatusList] = await getDict([20]);
        const container = document.getElementById('container');
        const width = container.scrollWidth;
        const height = container.scrollHeight || 800;
        G6.registerNode('default-node', {
          draw: defaultNodeGenerator,
          // setState: (stateName, stateValue, node)=>{
          //   const keyShape = node.getKeyShape();
          //   switch (stateName) {
          //     case 'selected':
          //       if (stateValue) {
          //         keyShape.attr({fill:'#3182ce'});
          //       } else {
          //         keyShape.attr({fill:'#f5f5f5'});
          //       }
          //       break;
          //     default:
          //   }
          // }
        }, 'rect'); // 指定要扩展的内置节点类型, 类似继承, 否则要实现其他需要的方法
        G6.registerEdge('default-edge', {
          draw: defaultEdgeGenerator,
          // animation
          // afterDraw(cfg, group) {
          //   const shape = group.get('children')[1];
          //   let index = 0;
          //   shape.animate(
          //     () => {
          //       index++;
          //       if (index > 9) {
          //         index = 0;
          //       }
          //       const res = {
          //         lineDash: [10, 5],
          //         lineDashOffset: -index,
          //       };
          //       // returns the modified configurations here, lineDash and lineDashOffset here
          //       return res;
          //     },
          //     {
          //       repeat: true, // whether executes the animation repeatly
          //       duration: 10000, // the duration for executing once
          //     },
          //   );
          // },
        }, 'line');
        // tooltip
        const tooltip = new G6.Tooltip({
          offsetX: 10,
          offsetY: 10,
          itemTypes: ['edge'],
          getContent: e => {
            const dom = document.createElement('div');
            dom.style.width = 'fit-content';
            const model = e.item.getModel();
            dom.innerText = model.label;
            return dom;
          },
          // 在这里判断要不要显示tooltip
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        })
        // contextmenu
        const contextmenu = new G6.Menu({
          getContent(evt) {
            return `<div style="cursor: pointer">${evt.item.getType()}</div>`;
          },
          handleMenuClick: (target, item) => {
            console.log(target, item.getModel());
          },
          offsetX: 16 + 10,
          offsetY: 0,
          // 在哪些类型的元素上响应
          itemTypes: ['edge'],
          // 在这里判断要不要显示contextmenu
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        });
        this.graph = new G6.Graph({
          container: 'container',
          width,
          height,
          // plugins: [tooltip, contextmenu],
          modes: {
            default: ['drag-canvas'],
          },
          defaultNode: {
            type: 'default-node',
          },
          defaultEdge: {
            type: 'default-edge',
          },
        });
        const generatedData = dataGenerator(data, this.deviceStatusList);
        this.graph.data(generatedData);
        this.graph.render();// 监听鼠标滚动，改为拖拽画布
        this.graph.on("wheel", (e) => {
          e.preventDefault();
          // 获取滚动的距离
          const deltaX = e.deltaX;
          const deltaY = e.deltaY;
          // 模拟拖拽画布的行为, TODO: 不能超出边界，但边界条件不应该在这里计算
          this.graph.translate(-deltaX, -deltaY);
        });
        this.graph.on('default-edge-text-shape:click', (e)=>{
          const model = e.item.getModel();
          openLink(model.circulateType, model);
        })
        this.graph.on('default-edge-business-text-shape:click', (e)=>{
          const model = e.item.getModel();
          openLink(model.circulateType, model);
        })
        this.graph.on('default-business-text:click', (e) => {
          const model = e.item.getModel();
          openLink(model.circulateType, model);
        })
        this.graph.on('default-location-text:click', (e) => {
          const model = e.item.getModel();
          openLink('AT_RIG', model)
        })
        // 以保证第一个节点高度为40为标准, 设置整个画布的初始zoom
          const firstNode = this.graph.getNodes()[0];
          if (firstNode) {
              const bbox = firstNode.getBBox();
              const height = bbox.maxY - bbox.minY;
              this.graph.zoom(50 / height);
          }
          // 因为没有配置fitView, 这里手动平移一下, 否则默认的左上角是应该是第一个节点的中心
          this.graph.translate(-40, -240);
        if (typeof window !== 'undefined')
          window.onresize = () => {
            if (!this.graph || this.graph.get('destroyed')) return;
            if (!container || !container.scrollWidth || !container.scrollHeight) return;
            this.graph.changeSize(container.scrollWidth, container.scrollHeight);
          };

      },
    }
}
</script>