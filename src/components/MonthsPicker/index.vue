<template>
  <div id="boxArea" ref="selectMonths" class="selectMonthBoxSquare rel clearFixed">
    <el-popover
      v-model="visible"
      placement="bottom"
      width="250"
      trigger="click"
    >
      <el-input slot="reference" v-model="inputValue" class="inputStyle" type="text" :placeholder="placeholder" readonly @focus="showBox = true">
        <i slot="prefix" class="el-input__icon el-icon-date"></i>
        <i v-if="showClear" slot="suffix" class="el-input__icon el-icon-circle-close clearIconStyle" @click="resetMonth"></i>
      </el-input>

      <div class="selectContentBox">
        <div class="contentArea">
          <div class="conterList">
            <el-checkbox-group v-model="selectedMonths" class="flex flex-wrap">
              <el-row class="monthRow">
                <el-col v-for="month in 12" :key="month" :span="6" class="monthCol">
                  <el-checkbox :class="[{'today': false}, 'onSelect flex-x-center']" :label="month">
                    {{ monthArr[month-1] }}月
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
        </div>

        <div class="buttonBox t-r">
          <el-button class="buttonStyle" size="mini" plain @click.stop="clearMonth">清空</el-button>
          <el-button class="buttonStyle" size="mini" plain @click.stop="invertMonth">反选</el-button>
          <el-button class="buttonStyle" size="mini" type="primary" plain @click.stop="handleSubmit">确定</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
name: 'MonthsPicker',
props: {
    placeholder: {
        type: String,
        default: '请选择查询月份'
    }
},
data() {
    return {
        selectedMonths: [],
        resultMonths: [],
        currentM: '', // 当前月份
        showBox: false, // 是否显示月份选择弹框
        visible: false,
        monthNumberArray: new Array(12).fill(null).map((_,index)=>index+1),
        monthArr:['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二']
    }
},
mounted() {
    // 点击弹框外的任意位置关闭区域弹窗
    document.addEventListener('click', (e) => {
        // 获取弹窗对象
        const boxArea = document.getElementById('boxArea')
        if (boxArea && !boxArea.contains(e.target)) {
            const equalArr = this.resultMonths.sort().toString() === this.selectedMonths.sort().toString()
            if (!equalArr) {
                this.selectedMonths = [...this.resultMonths]
            }
            // 关闭弹框
            this.showBox = false
        }
    })
},
computed:{
    showClear() {
      return this.inputValue !== "";
    },
    inputValue: {
        set(){},
        get(){
            return this.selectedMonths.map(sm=>this.monthArr[sm-1]).join('月,') + (this.selectedMonths.length ? '月' : '')
        }
    }
},
methods: {
    // 确定
    handleSubmit() {
        const _this = this;
        _this.resultMonths = _this.selectedMonths;
        _this.showBox = false;
        _this.visible = false;
        _this.$emit('change', _this.resultMonths);
    },
    // 反选
    invertMonth() {
        const _this = this
        _this.selectedMonths = new Array(12).fill(null).map((_,index)=>index+1).filter(item=>!_this.selectedMonths.includes(item))
    },
    // x清空
    clearMonth() {
      const _this = this;
      _this.selectedMonths = [];
    },
    // 盒子清空
    resetMonth() {
        const _this = this;
        _this.selectedMonths = [];
        _this.resultMonths = [];
        _this.$emit('change', _this.resultMonths);
    },
}
}
</script>
<style lang="scss" scoped>
  .flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .flex-around {
    justify-content: space-around;
  }
  .flex-x-center {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .selectMonthBoxSquare {
    display: inline-block;
    // width: 250px;
    .selectContentBox {
      position: absolute;
      top: 35px;
      left: 0;
      z-index: 2021;
      background: #ffffff;
      border: 1px solid #e5e5e5;
      border-radius: 3px;
      .contentArea {
        width: 330px;
      }
    }
  }

    .inputStyle {
      width: 200px;
      display: inline-block;
    }
    .clearIconStyle {
      display: none;
    }
    .inputStyle:hover .clearIconStyle{
      display: block;
    }
    .conterList{
      .monthCol{
        .today {
          color: #1890ff;
        }
      }
      .onSelect{
        // width: 25% !important;
        padding:3px 0;
        margin: 20px 0 !important;
      }
    }
    ::v-deep .el-checkbox__input {
      display: none !important;
    }
    ::v-deep .el-checkbox__label {
      padding-left: 0px !important;
    }
    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #fff;
    }
    label.el-checkbox.onSelect.flex-x-center.is-checked {
      background: #409EFF;
      width: 50px;
      border-radius: 15px;
    }
    .lableStyle {
      font-size: 14px;
    }
    .el-button--mini {
      padding: 5px 15px;
      font-size: 12px;
      border-radius: 3px;
    }
    .buttonBox {
      border-top: 1px solid #e5e5e5;
      padding: 10px;
      padding-bottom: 0px;
      display: flex;
      justify-content: flex-end;
    }
</style>