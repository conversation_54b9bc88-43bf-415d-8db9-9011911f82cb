<template>
    <el-input-number
        class="custom-input-number"
        :align="align"
        :class="`align-${align}`"
        :controls="false"
        v-model="numval"
        :placeholder="placeholder"
        :controls-position="controlsPosition"
        @input="transferValue"
        :min="min"
        :max="max"
        :step="step"
        :stepStrictly="stepStrictly"
        :precision="precision"
        :size="size"
        :disabled="disabled"
        :style="autoStyle"
        @blur="blur"
        @change="change"
    ></el-input-number>
</template>
<script>
export default {
    props: {
        align: { default: "center" },
        value: { default: undefined },
        min: {},
        max: {},
        controlsPosition: { type: String, default: "right" },
        placeholder: { type: String },
        decimalPlaces: {},
        step: { default: 1 },
        stepStrictly: { default: false },
        precision: {},
        size: {},
        autoStyle: { type: String },
        disabled: { default: false },
    },
    data() {
        return {
            numval: undefined,
        };
    },
    watch: {
        value: {
            handler(newName) {
                this.$nextTick(() => {
                    this.numval =
                        newName === "" ||
                        newName === null ||
                        newName == undefined
                            ? undefined
                            : newName;
                });
            },
            immediate: true,
        },
    },
    mounted() {
        this.numval =
            this.value === "" || this.value === null || this.value === undefined
                ? undefined
                : this.value;
    },
    methods: {
        transferValue(newValue) {
            this.$emit("input", newValue === undefined ? null : newValue);
        },
        blur() {
            this.$emit("blur");
        },
        change() {
            this.$emit("change");
        },
    },
};
</script>
<style lang="scss">
.custom-input-number.align-center.el-input-number {
    .el-input__inner {
        text-align: center !important;
    }
}
.custom-input-number.align-left.el-input-number {
    .el-input__inner {
        text-align: left !important;
    }
}
</style>
