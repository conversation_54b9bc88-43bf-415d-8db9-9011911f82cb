<!-- TODO: 合并到CustomAutocomplete -->
<template>
    <el-autocomplete
        v-model="modelValue"
        :fetch-suggestions="querySearch"
        :trigger-on-focus="true"
        @select="onSelect"
        style="width: 100%;"
        @change="onChange"
        v-bind="$attrs"
    ></el-autocomplete>
 </template>
  <script>
    import { apiGetWellListWithJobs, apiWellListFuzzy } from "@/api/wellInfo";
    export default {
        name: "FuzzyWellInput",
      props: {
        suggestList: {
          type: Array,
          default: () => []
        },
        value: {
        },
      },
      data() {
        return {
          modelValue: null
        };
      },
      watch: {
        modelValue:{
          handler(n){
            // this.$emit("input",n);
          }
        },
        value: {
          immediate: true,
          handler(n){
            this.modelValue = n;
            // this.$emit("change", this.modelValue)
          }
        }
      },
      methods: {
        querySearch(wellNumberKey = "", cb) {
            const form = new FormData();
            form.append("wellNumberKey", wellNumberKey || "");
            apiWellListFuzzy(form).then((res) => {
                console.log((res.data?.data || []).map(item=>item.wellNumber))
                cb((res.data?.data || []).map(item=>({value:item.wellNumber})));
            });
        },
        onSelect(item) {
          this.modelValue = item.value;
          
          this.$emit("input",item.value);
          this.$emit("change", item.value)
        },
        onChange(n){
          this.$emit("input",n);
          this.$emit("change", n)
        }
      },
    }
  </script>