// TODO: get label by constant
export const defaultOptions: any ={
    RISK_TYPE: {
        RISK: {
            label: '风险',
            type: 'danger'
        },
        PRODUCE: {
            label: '生产',
            type: 'success'
        },
        TEST: {
            label: '试验',
            type: 'warning'
        },
        SCRAP: {
            label: '报废',
            type:'info'
        }
    },
    MAINTAIN_TYPE: {
        MAINTAIN: {
            label: '常规保养',
            type: 'success'
        },
        REPAIR: {
            label: '失效维修',
            type: 'danger'
        },
        DISCARD: {
            label: '报废处理',
            type: 'info'
        }
    },
    TRUE_FALSE: {
        true: {
            label: '是',
            type: 'success'
        },
        false: {
            label: '否',
            type: 'danger'
        }
    },
    NEGATIVE_TRUE_FALSE: {
        true: {
            label: '是',
            type: 'danger'
        },
        false: {
            label: '否',
            type: 'success'
        }
    },
    FINISH: {
        '1': {
            label: '完成',
            type: 'success'
        },
        '0': {
            label: '未完成',
            type: 'danger'
        },
        '-1': {
            label: '滞留',
            type: 'warning'
        }
    },
    TEST_RESULT: {
        '1': {
            label: '通过',
            type: 'success'
        },
        '0': {
            label: '未通过',
            type: 'danger'
        },
    },
    RECEIVE_STATUS: {
        'RECEIVED': {
            label: '已接收',
            type: 'success'
        },
        'SUBMITTED': {
            label: '未接收',
            type: 'danger'
        },
        'IGNORE': {
            label: '已忽略',
            type: 'info'
        }
    },
    TEST_SERVICE_STATUS: {
        'START_SERVICE': {
            label: '开始服役',
            type: 'warning'
        },
        'END_SERVICE': {
            label: '结束服役',
            type: 'danger'
        },
        'SERVICING': {
            label: '正在服役',
            type:'success'
        }
    },
    PROJECT_STATUS: {
        '0': {
            label: '进行中',
            type: 'success'
        },
        '1': {
            label: '已完结',
            type: 'danger'
        },
    },
    HOURS_APPROVE_STATUS: {
        'PENDING': {
            label: '待审核',
            type: 'warning'
        },
        'ONGOING': {
            label: '审核中',
            type: ''
        },
        'APPROVED': {
            label: '已通过',
            type: 'success'
        },
        'REJECTED': {
            label: '已驳回',
            type: 'danger'
        },
        'REVOKED': {
            label: '已撤回',
            type: 'info'
        }
    },
    
    STOCK_DEVICE_STATUS: {
        '19001': {
            label: 'At Rig',
            color: 'rgb(0, 176, 240)'
        },
        '19002': {
            label: 'Repair',
            color: 'rgb(255 ,255 ,0)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19003': {
            label: 'Ready',
            color: 'rgb(0, 176, 80)'
        },
        '19004': {
            label: 'New',
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19005': {
            label: "Sent Back Canada",
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19006': {
            label: "Hand Over To Client",
            color: 'rgb(0, 112, 192)'
        },
        '19007': {
            label: 'Scrapped',
            color: 'rgb(0, 32, 96)'
        },
        '19008': {
            label: "Lost In Hole",
            color: 'rgb(112, 48, 160)'
        },
        '19009': {
            label: 'Test',
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19010': {
            label: "Sent Back Overseas",
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19011': {
            label: "Pending",
            color: 'rgb(112, 48, 160)'
        },
        '19012': {
            label: 'Production',
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
        '19013': {
            label: 'NA',
            color: 'rgba(0,0,0,0.1)',
            font_color: 'rgb(0, 0, 0)'
        },
    },
} 