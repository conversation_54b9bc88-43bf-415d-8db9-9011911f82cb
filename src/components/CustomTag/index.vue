<!-- 如果传入了tagType, 则通过tagValue使用规则判定tag的type, 否则和 el-tag 一样 -->
<!-- 禁用了动画 -->
<!-- enhancement: 如果slot和tagLabel都为空, 则不显示 -->
<template>
    <el-tag v-if="showTag" :disable-transitions="true" effect="dark" v-bind="computedAttrs" :style="`color:${computedAttrs.font_color}`">
        <template v-if="tagType">
            {{ tagLabel }}
        </template>
        <template v-else>
            <slot />
        </template>
    </el-tag>
</template>

<script>
import { defaultOptions } from "./options"
export default {
    name: "CustomTag",
    props: ['tagType', 'tagValue'],
    computed: {
        computedAttrs(){
            const { type, ...restAttrs } = this.$attrs;
            return { ...restAttrs, type: this.computedType || type, color: this.computedColor || null, font_color: this.computedFontColor || null }
        },
        computedType(){
            if(this.defaultOption){
                return this.defaultOption[this.tagValue]?.type || null
            }
            return null
        },
        computedColor(){
            if(this.defaultOption){
                return this.defaultOption[this.tagValue]?.color || null
            }
            return null
        },
        computedFontColor(){
            if(this.defaultOption){
                return this.defaultOption[this.tagValue]?.font_color || null
            }
            return null
        },
        defaultOption(){
            if(this.tagType){
                const option = defaultOptions[this.tagType]
                return option || null
            }
            return null
        },
        tagLabel(){
            if(this.defaultOption){
                return this.defaultOption[this.tagValue]?.label || null
            }
            return null
        },
        showTag(){
            return this.tagType ? !!this.tagLabel : !!this.$slots.default
        }
    }
}
</script>
<style lang="scss" scoped>
.hasColor{
    // color: black;
}
.el-tag--dark{
    border-color: #fafafa;
}
</style>