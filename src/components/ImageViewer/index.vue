<!-- 插槽内的所有图片可以通过点击进行列表式预览 -->
<!-- disabled属性默认为false, 为true时不触发预览事件 -->
<template>
    <div ref="imageViewerContainer" @click="handleImage($event)">
        <slot />
        <!-- click事件不会绑定到模态窗 -->
        <el-image-viewer
            :initialIndex="imageViewerInitialIndex"
            v-if="showViewer"
            :on-close="() => (showViewer = false)"
            :url-list="srcList"
            style="z-index: 99999"
        />
    </div>
</template>
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
    name: 'ImageViewer',
    components: { ElImageViewer },
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data(){
        return {
            showViewer: false,
            imageViewerInitialIndex: 0,
            srcList: [],
        }
    },
    methods: {
        getImageSrcList() {
            const imgList = this.$refs.imageViewerContainer.querySelectorAll('img');
            this.srcList = [].map.call(imgList, (img) =>
                img.getAttribute("src")
            ).filter(item=>!!item);
        },
        handleImage(event) {
            if(this.disabled){
                return
            }
            if (event.target.nodeName == "IMG" || event.target.nodeName == "img") {
                this.getImageSrcList();
                const img = event.target.currentSrc;
                this.imageViewerInitialIndex = this.srcList.findIndex(
                    (item) => decodeURI(img).includes(item)
                );
                this.showViewer = true;
            }
        }
    }
}
</script>