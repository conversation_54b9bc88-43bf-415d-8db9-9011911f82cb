<template>
    <el-autocomplete
      v-model="inputValue"
      :fetch-suggestions="fetchSuggestions"
      @select="onSelect"
      @blur="onBlur"
      @clear="onClear"
      v-bind="$attrs"
    />
  </template>
  
  <script>
import { apiGetComponentFuzzyList } from '@/api/mwd'

  export default {
    name: 'CustomAutocomplete',
    props: {
      value: {
        type: String,
        default: ''
      },
      type: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        inputValue: null,
        hasSelected: false
      }
    },
    computed: {
      searchApiMap() {
        return {
          COMPONENT_INFO: {
              searchApi: qs => apiGetComponentFuzzyList({serialNumber: qs}).then(res => res.data.data)
          },
        }
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          this.inputValue = newVal // 同步到 inputValue
        }
      },
      inputValue(val) {
        this.$emit('input', val) // 支持 v-model
      }
    },
    methods: {
      onSelect(item) {
        this.hasSelected = true
        this.$emit('select', item)
      },
      onBlur() {
        // 简单版本：如果没有选择就清空
        if (!this.hasSelected) {
          this.$emit('change', this.inputValue) // 同步到父组件
        }
        this.hasSelected = false // 重置
      },
      onClear(){
        this.$emit('change', null) // 同步到父组件
      },
      fetchSuggestions(qs, cb){
        const { searchApi } = this.searchApiMap[this.type];
        Promise.resolve(searchApi(qs)).then(res=>{
            cb(res || [])
        })
      },
    }
  }
  </script>
  