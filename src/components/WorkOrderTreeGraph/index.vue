<template>
    <div style="height: 100%;">
        <div id="work-order-tree-container" style="height:calc(100% - 20px)"></div>
    </div>
</template>
<script>
import G6 from "@antv/g6";
import { 
    registerCustomNodes, ANTV_TREE_COLLAPSED_FLAG, DEFAULT_NODE,
    GHOST_NODE, expandNode, collapseNode, expandNodeAll, 
} from "./utils/nodes";
import insertCss from "insert-css";
import { apiGetEaOrderTree } from "@/api/ea";
insertCss(`
    .g6-component-contextmenu {
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            font-size: 12px;
            padding: 0;
    }
`)
registerCustomNodes('work-order-tree-default-node');
export default {
    name: "WorkOrderTreeGraph",
    props: {
        jobType: String,
        id: Number
    },
    data(){
        return {
            graph: null,
            matrix: null, // 如果有值，则保持视图位置
            treeTemplate: null,
            actualTreeData: null,

        }
    },
    computed:{
        p_EaWorkOrderInfo(){
            return this.$checkBtnPermission('sys:ea:detail:all');
        },
        p_MwdInfoView(){
            return this.$checkBtnPermission('sys:mwd:info');
        }
    },
    mounted(){
        if(!this.id){
            this.$message.error('无目标数据id，请重试');
            return;
        }
        const infoForm = {
            [this.jobType==='EA'?'eaId':'mwdId']: this.id,
        }
        apiGetEaOrderTree(infoForm).then(res=>{
            const data = res.data.data;
            this.treeData = this.handleTreeData(data);
            this.$nextTick(()=>{
                this.drawChart();
            })
        })
    },
    methods: {
        // 统一的数据处理函数
        handleTreeData(data) {
            const handleFunc = (item, parent = null) => {
                item.nodeType = (item.jobType === this.jobType && item.id===this.id) ? GHOST_NODE : DEFAULT_NODE;
                item.id = item.jobType + ',' + item.id;
                if (item.children) {
                    item.children.forEach((child) => {
                        handleFunc(child, item);
                    });
                }
                return item;
            };
            return handleFunc(data);
        },
        initContainer() {
            this.container = document.getElementById("work-order-tree-container");
            this.width = this.container.scrollWidth;
            this.height = this.container.scrollHeight || 500;
        },

        initGraph(data) {
            if (!data) {
                return;
            }
            const contextmenu = new G6.Menu({
                getContent: (e) => {
                    return e._contextMenuContent;
                },
                handleMenuClick: (target, item) => {
                    const name = target.getAttribute("name");
                    switch(name) {
                        case "EXPAND":
                            expandNode(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                        case "COLLAPSE":
                            collapseNode(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                        case "ALL_EXPAND":
                            expandNodeAll(item, this.graph);
                            this.$nextTick(()=>{
                                this.getDeduplicatedInvCodeList();
                            })
                            break;
                    }
                },
                offsetX: 0,
                offsetY: 0,
                // 在哪些类型的元素上响应
                itemTypes: ['node'],
                // 在这里判断要不要显示contextmenu
                shouldBegin: (e) => {
                    // 将显示的内容计算逻辑放在这里, 存到e._contextMenuContent中, 否则getContent可能要重复这个逻辑
                    // 核心原因是getContent不能返回null或undefined, 而且就算返回空串也会显示一个空的contextmenu
                    const model = e.item.getModel();
                    // 是否有子节点
                    const hasChildren = !!(model.children && model.children.length > 0);
                    // 是否折叠状态
                    const isCollapsed = !!model[ANTV_TREE_COLLAPSED_FLAG];
                    // 递归判断是否是全部展开的状态
                    const checkAllExpanded = (m) => {
                        if (!m.children || m.children.length === 0) {
                            return true;
                        }
                        return m.children.every((child) => {
                            return !child[ANTV_TREE_COLLAPSED_FLAG] && checkAllExpanded(child);
                        });
                    };
                    const allExpandDiv = `<div class="menu-item" name="ALL_EXPAND">全部展开</div>`;
                    const expandDiv = `<div class="menu-item" name="EXPAND">展开</div>`;
                    const collapseDiv = `<div class="menu-item" name="COLLAPSE">折叠</div>`;;
                    let content = "";
                    if (hasChildren) {
                        if (isCollapsed) {
                            content = expandDiv + allExpandDiv;
                        } else {
                            // TODO: 全部展开孙节点会有问题, 先不展示
                            // if (checkAllExpanded(model)) {
                            //     content = collapseDiv;
                            // } else {
                            //     content = collapseDiv + allExpandDiv;
                            // }
                            content = collapseDiv;
                        }
                    }
                    e._contextMenuContent = content;
                    return content;
                    
                },
            });
            this.graph = new G6.TreeGraph({
                container: "work-order-tree-container",
                ...this.defaultConfig,
                padding: [20, 20],
                defaultLevel: 3,
                defaultZoom: 0.7,
                animate: false,
                plugins: [
                    contextmenu,
                ],
            });
            this.graph.data(data);
            this.graph.render();
            
            // 给+-号添加点击事件
            const handleCollapse = (e) => {
                e.preventDefault();
                const item = e.item;
                const model = item.getModel();
                model[ANTV_TREE_COLLAPSED_FLAG] ? expandNode(item, this.graph) : collapseNode(item, this.graph);
                this.$nextTick(()=>{
                    this.getDeduplicatedInvCodeList();
                })
            };
            this.graph.on("collapse-text:click", handleCollapse);
            this.graph.on("collapse-rect:click", handleCollapse);
            this.graph.on("jobType:click", (e)=>{
                const item = e.item;
                const model = item.getModel();
                let { jobType, id } = model;
                id = Number(id.split(',')[1]);
                if(!id){
                    this.$message.error('无目标工单数据！');
                    return;
                }
                if(this.jobType===jobType&&id===this.id){
                    return;
                }
                let link;
                if(jobType==='EA'){
                    if(!this.p_EaWorkOrderInfo){
                        return;
                    }
                    link = '/ea/detail?eaId=' + id;
                }else{
                    if(!this.p_MwdInfoView){
                        return;
                    }
                    link = '/mwd-tool-maintain/maintain?mwdId=' + id;
                }
                window.open(link, '_blank');
            });
            this.graph.on("viewportchange", () => {
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            });
            // 监听鼠标滚动，改为拖拽画布
            this.graph.on("wheel", (e) => {
                e.preventDefault();
                // 获取滚动的距离
                const deltaX = e.deltaX;
                const deltaY = e.deltaY;
                // 模拟拖拽画布的行为, TODO: 不能超出边界，但边界条件不应该在这里计算
                this.graph.translate(-deltaX, -deltaY);
            });
            this.graph.on('afterrender', () => {
                // 保持视图位置
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            })
            
            // 以保证第一个节点高度为40为标准, 设置整个画布的初始zoom
            const firstNode = this.graph.getNodes()[0];
            if (firstNode) {
                const bbox = firstNode.getBBox();
                const height = bbox.maxY - bbox.minY;
                this.graph.zoom(70 / height);
            }
            // 因为没有配置fitView, 这里手动平移一下, 否则默认的左上角是应该是第一个节点的中心
            this.graph.translate(120, 40);
        },
        drawChart() {
            if (!this.container) {
                this.initContainer();
            }
            this.defaultConfig = {
                width: this.width,
                height: this.height,
                nodeStateStyles: {
                    focus: {
                        fill: 'red',
                    },
                },
                modes: {
                    default: [
                        "drag-canvas",
                    ]
                },
                // fitView: true, // 因为主动初始化了zoom, 这个不能开启
                animate: true,
                defaultNode: {
                    type: "work-order-tree-default-node",
                },
                defaultEdge: {
                    type: 'cubic-horizontal',
                    style: {
                        stroke: '#CED4D9',
                    },
                },
                layout: {
                    type: "indented",
                    direction: "LR",
                    dropCap: false,
                    indent: 300,
                    getHeight: () => {
                        return 80;
                    },
                },
            };
            if (this.graph) {
                this.graph.destroy();
            }
            this.initGraph(this.treeData);
            window.onresize = () => {
                if (!this.graph || this.graph.get("destroyed")){
                    return;
                }
                if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight){
                    return;
                }
                this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight);
            };
            
        },
        rerenderGraph(){
            // TODO: 这里是hack，这个逻辑本不应该有
            this.matrix = this.graph.getGroup().getMatrix();
            this.graph.changeData(this.graph.save());
        },
    }
}
</script>
<style lang="scss">
    .g6-minimap {
        position: absolute;
        right: 100px;
        top: 100px;
        background-color: #fff;
    }
    .g6-component-tooltip {
        background-color: rgba(0,0,0, 0.65);
        padding: 10px;
        box-shadow: rgb(174, 174, 174) 0px 0px 10px;
        width: fit-content;
        color: #fff;
        display: flex;
        .tooltip-item{
            display: flex;
            .tooltip-label{
                width: 100px;
            }
            .tooltip-content{
                flex: 80px;
            }
        }
    };
    .g6-component-contextmenu {
        .menu-item {
            padding: 6px 10px;
            cursor: pointer;
            transition: background 0.2s;
            white-space: nowrap;
            &:hover {
                background: #f5f5f5;
            }
            &:active {
                background: #e0e0e0;
            }
        }
        .menu-divider {
            height: 1px;
            background: #ddd;
            margin: 4px 0;
        }
    }
</style>
<style lang="scss">
.treegraph-focus-form{
    .el-form-item{
        margin-bottom: 4px;
    }
}
</style>