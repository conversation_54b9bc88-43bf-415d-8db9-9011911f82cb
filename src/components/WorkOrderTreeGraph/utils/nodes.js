import G6 from "@antv/g6";
const icon = require("./icon.png")
export const ANTV_TREE_COLLAPSED_FLAG = "collapsed"
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 240;
const BOX_HEIGHT = 70;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#ccc';
const BOX_RADIUS = 4;
const BOX_FILL_COLOR = '#dee9ff';

export const NODE_COLLAPSE_STATUS = 'NODE_COLLAPSED';
export const DISASSEMBLE_NODE = 'DISASSEMBLE_NODE';
export const ASSEMBLE_NODE = 'ASSEMBLE_NODE';
export const GHOST_NODE = 'GHOST_NODE';
export const DEFAULT_NODE = 'DEFAULT_NODE';
export const NODE_TYPE = 'NODE_TYPE';

function splitTextByWidth(text, fontSize, maxWidth) {
    const parts = [];
    let currentPart = '';
    let currentWidth = 0;
    
    const segments = text.split(/(,)/); // 保留逗号
    
    for (let segment of segments) {
        let segmentWidth = [...segment].reduce((acc, char) => acc + (/[A-Za-z0-9]/.test(char) ? fontSize : fontSize / 3), 0);
        
        if (currentWidth + segmentWidth > maxWidth && segment !== ',') {
            if (currentPart) parts.push(currentPart);
            currentPart = segment;
            currentWidth = segmentWidth;
        } else {
            currentPart += segment;
            currentWidth += segmentWidth;
        }
    }
    
    if (currentPart) {
        parts.push(currentPart);
    }
    
    return parts;
}
const lucidaDeviceTypeMap = {
    16027: 'LCP',
    16028: 'MWD',
    16029: 'SU',
}
function getOrderType(cfg){
    const jobType = cfg.jobType;
    if(jobType==='MWD'){
        const deviceType = cfg.deviceType;
        return lucidaDeviceTypeMap[deviceType] || jobType;
    }
    return jobType;
}
const nodes = {
    "work-order-tree-default-node": {
        options: {
            style: {
                'main-rect': {
                    rect: {
                        [DEFAULT_NODE]: {
                            width: BOX_WIDTH,
                            height: BOX_HEIGHT,
                            lineWidth: BOX_BORDER_WIDTH,
                            fontSize: 12,
                            radius: BOX_RADIUS,
                            stroke: BOX_BORDER_COLOR,
                            fill: BOX_FILL_COLOR
                        },
                        [GHOST_NODE]: {
                            fill: "#ff9632"
                        },
                    },
                    text: {
                        [DEFAULT_NODE]: {
                            textAlign: "left",
                            textBaseline: "bottom",
                        },
                        [GHOST_NODE]: {
                        },
                    }
                },
                'collapse-rect': {
                    rect: {
                        [DEFAULT_NODE]: {
                            width: 16,
                            height: 16,
                            stroke: "rgba(0, 0, 0, 0.25)",
                            cursor: "pointer",
                            fill: "#fff",
                        },
                        [GHOST_NODE]: {
                        },
                    },
                    text: {
                        [DEFAULT_NODE]: {
                            textAlign: "center",
                            textBaseline: "middle",
                            fontSize: 16,
                            cursor: "pointer",
                            fill: "rgba(0, 0, 0, 0.25)",
                        },
                        [GHOST_NODE]: {},
                    }
                }
            }
        },
        draw(cfg, group) {
            // 主节点配置
            const invNameText = splitTextByWidth(cfg.invName, 8, 210);
            const mainRectDefaultStyle = this.options.style['main-rect'].rect[DEFAULT_NODE];
            let nodeType = cfg.nodeType;
            const mainRectCustomStyle = nodeType ? this.options.style['main-rect'].rect[nodeType] : {};
            // 声明主节点中心在长方形的中心
            const nodeOrigin = {
                x: -mainRectDefaultStyle.width / 2,
                y: -mainRectDefaultStyle.height / 2,
            };
            const rect = group.addShape("rect", {
                attrs: {
                    x: nodeOrigin.x,
                    y: nodeOrigin.y,
                    ...mainRectDefaultStyle,
                    ...mainRectCustomStyle,
                    ...(nodeType === ASSEMBLE_NODE && cfg.ASSEMBLE_NODE_COLOR ? { fill: cfg.ASSEMBLE_NODE_COLOR } : {}),
                    ...(nodeType === DISASSEMBLE_NODE && cfg.DISASSEMBLE_NODE_COLOR ? { fill: cfg.DISASSEMBLE_NODE_COLOR } : {})
                },
                name: "main-rect"
            });
            // 文字基本样式
            const mainRectDefaultTextStyle = this.options.style['main-rect'].text[DEFAULT_NODE];
            const mainRectCustomTextStyle = cfg.nodeType ? this.options.style['main-rect'].text[cfg.nodeType] : {};
            // label title
            group.addShape("text", {
                attrs: {
                    ...mainRectDefaultTextStyle,
                    x: 12 + nodeOrigin.x,
                    y: 18 + nodeOrigin.y,
                    text: getOrderType(cfg)+ ": " + cfg.workNumber,
                    fontSize: 14,
                    opacity: 0.85,
                    cursor: "pointer",
                    fill: "blue",
                },
                // 用于在e.target中作区分
                name: "jobType",
            });
            invNameText.forEach((text, index) => {
                group.addShape("text", {
                    attrs: {
                        ...mainRectDefaultTextStyle,
                        x: 12 + nodeOrigin.x,
                        y: 20 + nodeOrigin.y + 14 * (index+1),
                        text,
                        fontSize: 12,
                        opacity: 0.85,
                        fill: "#000",
                    },
                    // 用于在e.target中作区分
                    name: "invName",
                });
            });
            group.addShape("text", {
                attrs: {
                    ...mainRectDefaultTextStyle,
                    x: 12 + nodeOrigin.x,
                    y: 64 + nodeOrigin.y,
                    text: cfg.serialNumber,
                    fontSize: 12,
                    opacity: 0.85,
                    fill: "#000",
                },
                // 用于在e.target中作区分
                name: "serialNumber",
            });
            // collapse图标
            const collapseRectDefaultStyle = this.options.style['collapse-rect'].rect[DEFAULT_NODE];
            // collapse文字基本样式
            const collapseRectDefaultTextStyle = this.options.style['collapse-rect'].text[DEFAULT_NODE];
            if (
                cfg.children &&
                cfg.children.length
            ) {
                group.addShape("rect", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2 - 8,
                        y: -10,
                        ...collapseRectDefaultStyle
                    },
                    name: "collapse-rect",
                });
                group.addShape("text", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2,
                        y: -2,
                        text: cfg[ANTV_TREE_COLLAPSED_FLAG] ? "+" : "-",
                        ...collapseRectDefaultTextStyle
                    },
                    name: "collapse-text",
                });
            }
            return rect;
        },
        setState(name, value, item){
            const group = item.getContainer();
            if (name === NODE_COLLAPSE_STATUS) {
                const collapseText = group.find(
                    (e) => e.get("name") === "collapse-text"
                );
                if (collapseText) {
                    if (!value) {
                        collapseText.attr({ text: "-", });
                    } else {
                        collapseText.attr({ text: "+", });
                    }
                }
            }
        },
    }
}

// string, string[], {name: alias}[]
export function registerCustomNodes(nodeList) {
    if(typeof nodeList==="string"){
        nodeList = [nodeList];
    }
    nodeList.forEach(item => {
        let name, alias;

        if (typeof item === 'string') {
            // 直接传递的节点名
            name = item;
            alias = item;
        } else if (typeof item === 'object' && item !== null) {
            [[name, alias]] = Object.entries(item);
        }

        if (nodes[name]) {
            // 这里默认了是rect, 可能需要维护
            G6.registerNode(alias, nodes[name], 'rect');
        } else {
            console.warn(`Node type "${name}" is not defined.`);
        }
    });
}

export const collapseNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLAPSED_FLAG] = true;
    graph.setItemState(item, NODE_COLLAPSE_STATUS, true);
    graph.layout();
}

export const expandNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLAPSED_FLAG] = false;
    if (model.children) {
        model.children.forEach((child) => {
            child[ANTV_TREE_COLLAPSED_FLAG] = true;
        });
    }
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);
    graph.layout();
}

export const expandNodeAll = (item, graph) => {
    const model = item.getModel();
    const expandAll = (m) => {
        m[ANTV_TREE_COLLAPSED_FLAG] = false;
        if (m.children) {
            m.children.forEach((child) => {
                expandAll(child);
            });
        }
    };
    expandAll(model);
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);
    graph.layout();

}

