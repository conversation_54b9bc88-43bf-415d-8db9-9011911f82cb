<template>
  <el-input
    @focus="focus"
    v-model="numval"
    :placeholder="placeholder"
    @input="transferValue"
    :size="size"
    :disabled="disabled"
    :style="autoStyle"
    @blur="blur"
    @change="change"
  ></el-input>
</template>
<script>
export default {
  props: {
    value: { default: undefined },
    placeholder: { type: String },
    size: {},
    autoStyle: { type: String },
    disabled: { default: false },
  },
  data() {
    return {
      numval: undefined,
    };
  },
  watch: {
    value: {
      handler(newName) {
        this.$nextTick(() => {
          this.numval =
            newName === "" || newName === null || newName == undefined
              ? undefined
              : newName;
        });
      },
      immediate: true,
    },
  },
  mounted() {
    this.numval =
      this.value === "" || this.value === null || this.value === undefined
        ? undefined
        : this.value;
  },
  methods: {
  // 原则：绑定前转换，blur或change后转换，input时预编辑
    cutNumber(str){
      const strArr = str.split('');
      let idx = strArr.findIndex(item=>isNaN(Number(item))&&item!=='.')
      return str.substring(0,idx)
    },
    // 预编辑
    // Number('2.') === 2
    // Number('.2') === 2
    validateNumber(value){
      if(isNaN(Number(value))&&value!='.'){
        
        const len = value.length
        let str = value.substring(0,len-1)
        if(isNaN(Number(str))){
          return null
        }{
          console.log(value,str)
          return str
        }
      }else{
        return value
      }
      // this.$nextTick()
      // if(value){
      //   // TODO:截取
      //   if(!isNaN(Number(value))){
      //     return value
      //   }else{
      //     const v0 = value.charAt(0)
      //     // 首位不是数字
      //     if(isNaN(Number(v0))){
      //       // 首位是'.'
      //       if(v0=='.'){
      //         // TODO:长度是否大于二
      //         // 补零后不是数字
      //         if(isNaN(Number(0+value))){
      //           return this.cutNumber((0+value))
      //         }else{
      //           return 0+value
      //         }
      //       }else{
      //         return null
      //       }
      //     }else{
      //       return 
      //     }
      //   }
      // }else{
      //   return null
      // }
      
    },
    transferValue(newValue) {
      // TODO: trim
      let value = this.validateNumber(newValue)
      this.$emit("input", value === undefined ? null : value);
    },
    blur() {
      // TODO: 转换Number
      this.$emit("blur");
    },
    focus(){
      this.$emit("focus");
    },
    change() {
      // TODO: 转换Number 
      this.$emit("change");
    },
  },
};
</script>
<style lang="css" scoped></style>
