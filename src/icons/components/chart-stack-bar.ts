/* eslint-disable */
/* tslint:disable */
// @ts-ignore
import icon from 'vue-svgicon'
icon.register({
  'chart-stack-bar': {
    width: 200,
    height: 200,
    viewBox: '0 0 1024 1024',
    data: '<path pid="0" d="M63.983 890.418h895.375v69.95H63.983v-69.95z" _fill="#515151"/><path pid="1" d="M63.983 64.994h69.951V960.37h-69.95V64.994zm417.083 326.147v456.432h167.597V391.14H481.066zm139.855 428.452H507.347V597.497h113.574v222.096zm115.468-615.087v643.066h166.133V204.506H736.39zM876.243 821.34H762.668V514.43h113.574v306.91zM233.178 287.963v559.61h164.385v-559.61H233.178zm138.979 533.378H258.583V599.246h113.574v222.095z" _fill="#515151"/>'
  }
})
