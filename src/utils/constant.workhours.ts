import momentjs from "moment";
export const noonStart = "12:00";
export const noonEnd = "13:00";
export const dayStart = "08:30"
// 要是改这个, 变动会比较大
export const timeFormat = "HH:mm"
// NOTE: isAfter 和 isBefore 不包含比较对象, 用取反来表示 >= 和 <=
// 获取时间区间包含的小时数, 会忽略午休和8:30之前的时间
export const getHoursByTimerange = (timerange) =>{
    if(!timerange){
        return 0;
    }
    if(!timerange[0] || !timerange[1]){
        return 0
    }
    let minutes = 0;
    const noonStartMoment = momentjs(noonStart, timeFormat);
    const noonEndMoment = momentjs(noonEnd, timeFormat);
    const dayStartMoment = momentjs(dayStart, timeFormat);
    const [startTime, endTime] = timerange;
    let startTimeMoment = momentjs(startTime, timeFormat);
    let endTimeMoment = momentjs(endTime, timeFormat);
    // 保证前小后大
    if(startTimeMoment.isAfter(endTimeMoment)){
        let tmp = startTimeMoment;
        startTimeMoment = endTimeMoment;
        endTimeMoment = tmp;
    }
    // 保证都在8:30之后
    if(startTimeMoment.isBefore(dayStartMoment)){
        startTimeMoment = dayStartMoment
    }
    if(endTimeMoment.isBefore(dayStartMoment)){
        endTimeMoment = dayStartMoment
    }
    // 处理午休
    if(startTimeMoment.isBetween(noonStartMoment, noonEndMoment)){
        startTimeMoment = noonEndMoment
    }
    if(endTimeMoment.isBetween(noonStartMoment, noonEndMoment)){
        endTimeMoment = noonStartMoment
    }
    // 如果前后都在午休会出现这种情况
    if(startTimeMoment.isAfter(endTimeMoment)){
        return 0;
    }
    // 前在午休起始之前有可能会切分 用取反来 hack >=和<=
    if(!startTimeMoment.isAfter(noonStartMoment)){
        if(!endTimeMoment.isBefore(noonEndMoment)){
            minutes = noonStartMoment.diff(startTimeMoment,'minute') + endTimeMoment.diff(noonEndMoment,'minute')
        }else{
            minutes = endTimeMoment.diff(startTimeMoment,'minute')
        }
    }
    // 前在午休起始之后, 不可能出现切分情况了
    if(!startTimeMoment.isBefore(noonEndMoment)){
        minutes = endTimeMoment.diff(startTimeMoment,'minute')
    }
    // 
    const hours = Number((minutes/60).toFixed(2))
    if(isNaN(hours)){
        return 0
    }else{
        if(hours<=0){
            return 0
        }
        return hours
    }
}
// 基于HH:mm:ss为timeFormat时的修正时间区间方法, 修正完, 时间必定在08:30之后, 秒数会变成零
export const reviseTimerange = (timerange) => {
    if(!timerange){
        return timerange
    }
    const [startTime, endTime] = timerange;
    const dayStartMoment = momentjs(dayStart, 'HH:mm');
    let startTimeMoment = momentjs(startTime, 'HH:mm');
    let endTimeMoment = momentjs(endTime, 'HH:mm');
    if(startTimeMoment.isAfter(endTimeMoment)){
        let tmp = startTimeMoment;
        startTimeMoment = endTimeMoment;
        endTimeMoment = tmp;
    }
    if(startTimeMoment.isBefore(dayStartMoment)){
        startTimeMoment = dayStartMoment
    }
    if(endTimeMoment.isBefore(dayStartMoment)){
        endTimeMoment = dayStartMoment
    }
    return [startTimeMoment.format(timeFormat), endTimeMoment.format(timeFormat)]
}

const weekList = ['日', '一', '二', '三', '四', '五', '六']
export const getWeek = (date?:string) => {
    return weekList[momentjs(date).day()];
}

export const getTimerange = ({startTime, endTime}) => {
    return startTime ? `${momentjs(startTime).format(timeFormat)} - ${momentjs(endTime).format(timeFormat)}` : '';
}

export const sectorList = [
    { value: 'SC', label: '四川' },
    { value: 'SH', label: '上海' },
]