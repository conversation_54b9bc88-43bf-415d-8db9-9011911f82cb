import momentjs from "moment";
export const calcuateStockTime = (row) => {
    return row.status === 'READY' && row.receiveDate ? momentjs().diff(momentjs(row.receiveDate), 'days') : '';
}

export const loadTypeMap = {
    19001: {
        loadType: 'IN',
        loadTypeStr: '入库',
        targetStatus: 19002,
        needCreate: true, // 意思是19001 -> 19002时需要创建
    },
    19002: {
        loadType: 'SET',
        loadTypeStr: '配置',
        targetStatus: 19003
    },
    19003: {
        loadType: 'OUT',
        loadTypeStr: '出库',
        targetStatus: 19001
    }
}

export const CIRCULATE_TYPE = {
    OUT_BOUND_ORDER: "OUT_BOUND_ORDER",
    HANDOVER: "HANDOVER",
    TRANSFER_ORDER: "TRANSFER_ORDER",
    DEMAND_ORDER: "DEMAND_ORDER",
    REPAIR: "REPAIR",
    WAREHOUSE: "WAREHOUSE",
    WORK_ORDER_MWD: "WORK_ORDER_MWD",
}