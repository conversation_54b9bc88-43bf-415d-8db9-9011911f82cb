export const BUSINESS_TYPE = {
    1: '工况',
    2: '井型',
    // 3: '井别',
    // 4: '工具大类',
    // 5: '工具类型',
    // 6: 'kit箱模板类型',
    7: '流动类型',
    8: '工具状态',
    9: '作业类型',
    10: '失效部件',
    11: '钻进方式',
    12: '维修状态',
    13: '故障类型',
    14: '失效部件分类',
    17: 'MWD工具',
    18: '井下工具'
}

export enum ENUM_BUSINESS_TYPE {
    TYPE_1 = 1,
    TYPE_2 = 2,
    TYPE_3 = 3,
    TYPE_4 = 4,
    TYPE_5 = 5,
    TYPE_6 = 6,
    TYPE_7 = 7,
    TYPE_8 = 8,
    TYPE_9 = 9,
    TYPE_10 = 10,
    TYPE_11 = 11,
    TYPE_12 = 12,
    TYPE_13 = 13,
    TYPE_14 = 14
}

export interface IBusinessType {
    id?: number,
    name?: string,
    businessType?: number
}

export const CAMBER_TYPE = {
    SINGLE_CAMBER: "单弯",
    ADJUSTABLE_CAMBER: "可调"
}


export const riskTypeList = [
    { value: "RISK", label: "风险" },
    { value: "PRODUCE", label: "生产" },
    { value: "TEST", label: "试验" },
    { value: "SCRAP", label: "报废" },
]
export const getRiskTypeStr = (key) => {
    const item = riskTypeList.find(item => item.value === key);
    return item ? item.label : "";
}

export const rssFailedPartsList = [
  {
    deviceType: "ROTART_LCP",
    name: "旋导LCP",
    failed: false
  },
  {
    deviceType: "ROTART_MWD",
    name: "旋导MWD",
    failed: false
  },
  {
    deviceType: "ROTART_SU",
    name: "旋导SU",
    failed: false
  }
]