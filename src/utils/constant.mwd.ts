// 试用件
export enum TEST_SERVICE_STATUS {
    START_SERVICE = "START_SERVICE",
    END_SERVICE = "END_SERVICE",
    SERVICING = "SERVICING"
}

export const getTestServiceStatusStr = status => {
    switch (status) {
        case TEST_SERVICE_STATUS.START_SERVICE:
            return "开始服役"
        case TEST_SERVICE_STATUS.END_SERVICE:
            return "结束服役"
        case TEST_SERVICE_STATUS.SERVICING:
            return "正在服役"
        default:
            return ""
    }
}

export const getTestWarnStrategyStr = ({ warnType, standardRun, standardHour }) => {
    switch (warnType) {
        case 'NO_WARN':
            return '无'
        case 'HOUR_WARN':
            return `正常作业${standardHour}小时以上`;
        case 'RUN_WARN':
            return `正常使用至少${standardRun}趟钻`;
        case 'RUN_AND_HOUR_WARN':
            return `正常作业${standardHour}小时以上 \n正常使用至少${standardRun}趟钻`;
        default:
            return '无'
    }
}

export enum SHARE_STATUS {
    SHARED = "SHARED",
    UNSHARED = "UNSHARED"
}

export const splitRun = (run) => {
    if (run) {
        return String(run).split(',')
    }
    return null
}
export const mergeRun = (runList) => {
    if (Array.isArray(runList)) {
        return runList.join(',')
    }
    return null
}

export const repairTypeList = [
    { value: "MAINTAIN", label: "常规保养" },
    { value: "REPAIR", label: "失效维修" },
    { value: "DISCARD", label: "报废处理" }
];

export const LUCIDA_DEVICE_TYPE_LIST = [16027, 16028, 16029]
export const lucidaDeviceTypeMap = {
    16027: {
        template: 'LCP'
    },
    16028: {
        template: 'MWD'
    },
    16029: {
        template: 'SU'
    },
}