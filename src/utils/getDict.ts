import { apiGetDictList } from "@/api/dict";
import { IBusinessType } from "./constant";
const dictCache = {}
export default function getDict(dictType: number | number[]) {
    let dictTypeList: number[];
    if (typeof dictType === 'number') {
        dictTypeList = [dictType];
    } else {
        dictTypeList = dictType;
    }
    return Promise.all((dictTypeList).map(type => {
        return new Promise<IBusinessType[]>((resolve, _) => {
            if (dictCache[type] && dictCache[type].length) {
                resolve(dictCache[type])
            } else {
                apiGetDictList({ businessType: type }).then(res => {
                    const data = res.data.data || [];
                    dictCache[type] = data;
                    resolve(data)
                })
            }
        })
    }))
}

export const getNameById = ( list: IBusinessType[], id: number) => {
    return list.find(item=>item.id == id)?.name || ""
}