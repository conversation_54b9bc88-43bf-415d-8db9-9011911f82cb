import { saveAs } from 'file-saver'
import XLSX, { WorkBook, WorkSheet } from 'xlsx-style'
interface ICell {
  v: Date | number | boolean | string; // 原始值
  t?: string; // 单元格内容格式 b: Boolean | n: Number | e: Error | s: String | d: Date
  z?: string; // 与单元格关联的数字格式字符串
  s?: any; // 单元格样式
}

/* 
  单元格样式

  | Style Attribute | Sub Attributes | Values |
  | :-------------- | :------------- | :------------- |
  | fill            | patternType    | "solid" or "none" |
  |                 | fgColor        | COLOR_SPEC |
  |                 | bgColor        | COLOR_SPEC |
  | font            | name           | "Calibri" // default |
  |                 | sz             | "11" // font size in points |
  |                 | color          | COLOR_SPEC |
  |                 | bold           | true || false |
  |                 | underline      | true || false |
  |                 | italic         | true || false |
  |                 | strike         | true || false |
  |                 | outline        | true || false |
  |                 | shadow         | true || false |
  |                 | vertAlign      | true || false |
  | numFmt          |                | "0" // integer index to built-in formats, see StyleBuilder.SSF property |
  |                 |                | "0.00%" // string matching a built-in format, see StyleBuilder.SSF |
  |                 |                | "0.0%" // string specifying a custom format |
  |                 |                | "0.00%;\\(0.00%\\);\\-;@" // string specifying a custom format, escaping special characters |
  |                 |                | "m/dd/yy" // string a date format using Excel's format notation |
  | alignment       | vertical       | "bottom"||"center"||"top" |
  |                 | horizontal     | "bottom"||"center"||"top" |
  |                 | wrapText       | true || false |
  |                 | readingOrder   | 2 // for right-to-left |
  |                 | textRotation   | Number from 0 to 180 or 255 (default is 0) |
  |                 |                | 90 is rotated up 90 degrees |
  |                 |                | 45 is rotated up 45 degrees |
  |                 |                | 135 is rotated down 45 degrees |
  |                 |                | 180 is rotated down 180 degrees |
  |                 |                | 255 is special, aligned vertically |
  | border          | top            | { style: BORDER_STYLE, color: COLOR_SPEC } |
  |                 | bottom         | { style: BORDER_STYLE, color: COLOR_SPEC } |
  |                 | left           | { style: BORDER_STYLE, color: COLOR_SPEC } |
  |                 | right          | { style: BORDER_STYLE, color: COLOR_SPEC } |
  |                 | diagonal       | { style: BORDER_STYLE, color: COLOR_SPEC } |
  |                 | diagonalUp     | true||false |
  |                 | diagonalDown   | true||false |

*/
class Workbook implements WorkBook {
  SheetNames: string[] = []
  Sheets: { [sheet: string]: WorkSheet } = {}
}

function datenum(date: Date) {
  return (+date - +new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000)
}

// 默认单元格样式
const defaultCellStyleOption = {
  font: {
    name: '宋体',
  }
}
// 合并单元格样式
function mergeCellStyleOption(customCellStyleOption, mergeOption = defaultCellStyleOption) {
  const result = { ...mergeOption };
  for (const key in customCellStyleOption) {
      if (result.hasOwnProperty(key)) {
          if (typeof result[key] === 'object' && result[key] !== null && !Array.isArray(result[key])) {
              result[key] = mergeCellStyleOption(customCellStyleOption[key], result[key]);
          } else {
              result[key] = customCellStyleOption[key];
          }
      } else {
          result[key] = customCellStyleOption[key];
      }
  }
  return result;
}
// 将二维数组转换为WorkSheet
/* 
  Sheet
  {
    [cell: string]: CellObject | SheetKeys | any;
    '!type'?: SheetType;
    '!ref'?: string; // 范围
    '!margins'?: MarginInfo;
  }
  WorkSheet extends Sheet
  {
    [cell: string]: CellObject | WSKeys | any;
    '!cols'?: ColInfo[]; // { hidden?: boolean, width?: number, wpx?: number, wch?: number, MDW?: number}
    '!rows'?: RowInfo[]; // { hidden?: boolean, hpx?: number, hpt?: number, level?: number }
    '!merges'?: Range[]; // { s: {r, c}, e: {r, c} }[]
    '!protect'?: ProtectInfo;
    '!autofilter'?: AutoFilterInfo;
  }
*/
const sheetFromDataArray = (data: any[][], styleCb: any = {}) => {
  const ws: WorkSheet = {}
  const range = {
    s: {
      c: 10000000,
      r: 10000000
    },
    e: {
      c: 0,
      r: 0
    }
  }
  for (let R = 0; R !== data.length; ++R) {
    for (let C = 0; C !== data[R].length; ++C) {
      if (range.s.r > R) range.s.r = R
      if (range.s.c > C) range.s.c = C
      if (range.e.r < R) range.e.r = R
      if (range.e.c < C) range.e.c = C
      const cell: ICell = {
        v: data[R][C],
        s: typeof styleCb === 'function' ? mergeCellStyleOption(styleCb({ R, C })) : mergeCellStyleOption(styleCb)
      }
      if (cell.v == null) continue
      /* 
        // 将数值行列转换为excel的行列坐标 {c:1,r:1} -> B2
        function encode_cell(cell) {
          var col = cell.c + 1;
          var s="";
          for(; col; col=((col-1)/26)|0) s = String.fromCharCode(((col-1)%26) + 65) + s;
          return s + (cell.r + 1);
        }
      */
      const cellRef = XLSX.utils.encode_cell({
        c: C,
        r: R
      })
      // 赋予单元格数据格式 - 感觉完全没必要, 直接字符串解百忧
      if(typeof cell.v === 'number'){
        cell.t = 'n'
      }else if(typeof cell.v === 'boolean'){
        cell.t = 'b'
      }else if(cell.v instanceof Date) {
        cell.t = 'n'
        cell.z = XLSX.SSF.get_table()[14]
        cell.v = datenum(cell.v)
      }else{
        cell.t = 's'
      }
      ws[cellRef] = cell
    }
  }
  if (range.s.c < 10000000) {
    /* 
      // 将对角线两点的数值行列坐标转换为excel的范围 {s:{c:1,r:1},e:{c:10,r:10}} -> B2:K11
      function encode_range(cs,ce) {
        if(typeof ce === 'undefined' || typeof ce === 'number') {
          return encode_range(cs.s, cs.e);
        }
        if(typeof cs !== 'string'){
          cs = encode_cell((cs));
        }
        if(typeof ce !== 'string'){
          ce = encode_cell((ce));
        }
        return cs == ce ? cs : cs + ":" + ce;
      }
    */
    ws['!ref'] = XLSX.utils.encode_range(range)
  }
  return ws
}

const s2ab = (s: string) => {
  const buf = new ArrayBuffer(s.length)
  const view = new Uint8Array(buf)
  for (let i = 0; i !== s.length; ++i) {
    view[i] = s.charCodeAt(i) & 0xFF
  }
  return buf
}

// 根据单元格内容计算cell宽度, 中文宽度为2, 其它为1
function getCellWidth(cell: any) {
  if (!cell) {
    return 10;
  }
  let ret = 0;
  for (let i = 0; i < cell.length; i++) {
    if (cell.toString().charCodeAt(i) > 255) {
      ret += 2;
    } else {
      ret += 1;
    }
  }
  return ret
}
// 获取计算列最大宽度数组
function getColMaxWidthArray(matrix: any[][]): number[] {
  if (matrix.length === 0 || matrix[0].length === 0) {
    return [];
  }
  const columnLen = matrix[0].length;
  const ret = new Array<number>(columnLen).fill(10); // 取默认宽度为10
  for (let row of matrix) {
    for (let i = 0; i < columnLen; i++) {
      const cellWidth = getCellWidth(row[i])
      if (cellWidth > ret[i]) {
        ret[i] = cellWidth;
      }
    }
  }
  return ret;
}
interface IOption {
  header?: string[]; // 表头
  data?: any[][]; // 数据
  filename?: string; // 文件名
  multiHeader?: string[][]; // 多重表头
  merges?: any[]; // 单元格合并 - 这里的格式是类似A1:K11的范围字符串, 调用decode_range
  autoWidth?: boolean; // 是否自动计算列宽
  bookType?: 'xlsx' | 'chart'; // 
  styleCb?: any; // 单元格样式回调：要么是样式对象, 要么是类型为 ({R, C})=>样式对象 的方法
}
export const exportJson2Excel = ({ header = [], data = [], filename = 'excel-list', multiHeader = [], merges = [], autoWidth = true, bookType = 'xlsx', styleCb = {} }: IOption) => {

  // 创建WorkBook
  const wb = new Workbook()

  // ?
  data = [...data]
  // 添加表头
  data.unshift(header)
  // 倒序添加多重表头 - 这玩意儿没什么意思, 这里没有对表头单独处理的逻辑, 它和data是一个类型
  for (let i = multiHeader.length - 1; i > -1; i--) {
    data.unshift(multiHeader[i])
  }
  // 根据数据和样式生成WorkSheet
  const ws = sheetFromDataArray(data, styleCb)
  
  // 处理合并单元格逻辑 - 合并完显示的是左上角数据, 也就是{s:{r,c}}的数据
  if (merges.length > 0) {
    if (!ws['!merges']) {
      ws['!merges'] = []
    }
    merges.forEach(item => {
      /* 
        // 将excel的范围转换为对角线两点的数值行列坐标 B2:K11 -> {s:{c:1,r:1},e:{c:10,r:10}}
        function decode_range(range) {
          var idx = range.indexOf(":");
          if(idx == -1) {
            return { s: decode_cell(range), e: decode_cell(range) };
          }
          return { s: decode_cell(range.slice(0, idx)), e: decode_cell(range.slice(idx + 1)) };
        }
        // 将excel的单元格坐标转换为数值行列坐标 B2 -> {c:1,r:1}
        function decode_cell(cstr) {
          var R = 0, C = 0;
          for(var i = 0; i < cstr.length; ++i) {
            var cc = cstr.charCodeAt(i);
            if(cc >= 48 && cc <= 57) R = 10 * R + (cc - 48);
            else if(cc >= 65 && cc <= 90) C = 26 * C + (cc - 64);
          }
          return { c: C - 1, r:R - 1 };
        }
      */
      ws['!merges'].push(XLSX.utils.decode_range(item))
    })
  }

  // 自动计算列宽度
  if (autoWidth) {
    ws['!cols'] = getColMaxWidthArray(data).map(item => ({ wch: item }))
  }

  const wsName = 'SheetJS'
  // 更新WorkBook的sheet名称列表
  wb.SheetNames.push(wsName)
  // 将WorkSheet添加至WorkBook
  wb.Sheets[wsName] = ws

  // 将WorkBook转为二进制数据
  const wbout = XLSX.write(wb, {
    bookType,
    bookSST: false,
    type: 'binary'
  })

  // 执行保存
  saveAs(new Blob([s2ab(wbout)], {
    type: 'application/octet-stream'
  }), `${filename}.${bookType}`)
}
