import { UserModule } from '@/store/modules/user'
export function commmonTableHeaderCellStyle(item: any) {
    if (item.rowIndex === 0) {
        return "background:#004e7e;color:white"
    }
}
export const $REPORT_TYPE_MAP = {
    COVER: "封面",
    TOOLSONSITE: "现场工具列表",
    RIPROCEDURES: "钻前流程",
    MWD: "仪器日报",
    RUN: "趟钻",
    SURVEYS: "测斜数据",
    BHA: "钻具组合",
    FAILURE: "失效报告",
    ROPROCEDURES: "完钻流程",
    PROJECTTOOLSONSITE: "井下工具列表",
    PROJECT: "工程日报",
    RSS_COVER: "封面",
    RSS_TOOLSONSITE: "现场工具列表",
    RSS_RIPROCEDURES: "钻前流程",
    RSS_ROPROCEDURES: "完钻流程",
    RSS_DAILY: "仪器日报",
    RSS_RUN: "趟钻",
    RSS_BHA: "钻具组合",
    RSS_FAILURE: "失效报告",
}

export const $checkBtnPermission = (value: string): boolean => {
    return UserModule.authFlagList.includes(value)
}

// admin 和 wanglianjie
const SUPER_ADMIN_ID_LIST = [1, 2];
export function $isSuperAdmin(id = UserModule.userId) {
    return SUPER_ADMIN_ID_LIST.includes(id);
}