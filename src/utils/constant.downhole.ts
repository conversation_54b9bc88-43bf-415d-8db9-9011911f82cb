export const DEVICE_TYPE_SCREW = 17001;
export const rubberTypeList = [
    { value: "NORMAL_TEMP_WATER", label: "常温水基" },
    { value: "NORMAL_TEMP_RUBBER", label: "常温橡胶" },
    { value: "NORMAL_TEMP_OIL", label: "常温油基" },
    { value: "HIGH_TEMP_RUBBER", label: "高温橡胶" },
    { value: "HIGH_TEMP_OIL", label: "高温油基" },
    { value: "WATER_RUBBER", label: "水基橡胶" },
    { value: "OIL_RUBBER", label: "油基橡胶" },
    { value: "UNIVERSAL_RUBBER", label: "万能胶橡胶" },
    { value: "NORMAL_OIL_RUBBER", label: "常油橡胶" },
    { value: "SPIN_HIGH_OIL", label: "旋导高油" },
    { value: "HIGH_OIL_RUBBER", label: "高油橡胶" },
    { value: "OIL", label: "油基" },
]
export const getRubberTypeStr = (value) => {
    const target = rubberTypeList.find(item => item.value == value)
    return target ? target.label : ""
}

export enum MaintainTypeEnum {
    DISASSEMBLE = "DISASSEMBLE",
    ASSEMBLE = "ASSEMBLE",
}
export const MaintainTypeEnumMap = {
    DISASSEMBLE: "拆卸",
    ASSEMBLE: "组装"
}
export const MaintainTypeList = Object.keys(MaintainTypeEnumMap).map(key => ({
    value: key,
    label: MaintainTypeEnumMap[key]
}))

export const repairTypeList = [
    { value: "MAINTAIN", label: "常规保养" },
    { value: "REPAIR", label: "失效维修" },
    { value: "ASSEMBLE", label: "组装" },
    { value: "DISASSEMBLE", label: "拆卸" },
]
// TODO: 这个玩意儿需要整合
export const getRepairType = (value: string) => {
    return repairTypeList.find((item) => item.value === value)?.label || "";
}

export const useStatusList = [
    { value: "UNUSED", label: "未使用" },
    { value: "INUSE", label: "在使用" },
]
export const getUseStatusStr = (value: string) => {
    return useStatusList.find((item) => item.value === value)?.label || "";
}

export const curveTypeList = [
    { value: 1, label: "单弯" },
    { value: 0, label: "可调" },
    { value: 2, label: "直" },
    { value: 3, label: "旋导" },
]

export const getCurveTypeStr = (curve) => {
    return curveTypeList.find((item) => item.value === curve)?.label || "";
}

export const mudTypeList = [
    { value: '水基', label: "水基" },
    { value: "油基", label: "油基" },
    { value: "有机盐聚磺", label: "有机盐聚磺" },
]

export const getMudTypeStr = (curve) => {
    return curveTypeList.find((item) => item.value === curve)?.label || "";
}

export const brokeList = [
    { value: 1, label: "是" },
    { value: 0, label: "否" },
]

export const getBrokeStr = (curve) => {
    return brokeList.find((item) => item.value === curve)?.label || "";
}