import Vue from 'vue'
import Vuex from 'vuex'
import { IUserState } from './modules/user'
import { ITagsViewState } from './modules/tags-view'
import { IPermissionState } from './modules/permission'
import { AppModule } from './modules/app'
import { IConstantState } from './modules/constant'
Vue.use(Vuex)

export interface IRootState {
  user: IUserState
  tagsView: ITagsViewState
  permission: IPermissionState
  constant: IConstantState
}

// Declare empty store first, dynamically register all modules later.
export default new Vuex.Store<IRootState>({})
