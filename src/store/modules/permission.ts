import { VuexModule, Module, Mutation, Action, getModule } from 'vuex-module-decorators'
import { RouteConfig } from 'vue-router'
import store from '@/store'
import { tailerConstantRoutes } from '@/router'
import { AppModule, DeviceType } from './app'

export const filterAsyncRoutes = (routes: RouteConfig[], roles: number[]) => {
  const res: RouteConfig[] = []
  routes.forEach(route => {
    const r = { ...route }
    if (hasPermission(roles, r)) {
      if (r.children) {
        r.children = filterAsyncRoutes(r.children, roles)
      }
      res.push(r)
    }
  })
  return res
}

const hasPermission = (roles: number[], route: RouteConfig) => {
  if (route.meta && route.meta.id) {
    return roles.includes(route.meta.id)
  } else {
    return true
  }
}

// export const loadComponent = (view: any) => {
//   return (resolve: any) => require([`@/views/${view}.vue`], resolve)
// }

// enhancement: dynamic import 
/* 
  import 无法引入编译期间路径不明确的依赖
  require.context 会主动遍历所有符合规则的文件, 保证这些文件能够被打包, lazy mode可以实现懒加载, 但是会产生很多不必要的包碎片
    可以有一个解决方案 - 限定router的component指向的文件必须以index.vue命名, 可以在配置菜单列表的组件路径中添加固定suffix来做强限制 [待实现]
      const context = require.context('@/views', true, /index\.vue$/, 'lazy');
  类似的在vite中可以用import.meta.glob()
*/
const context = require.context('@/views', true, /(^|\/)index\.vue$/, 'lazy'); // 将符合规则(在views文件夹内, 且文件名为index.vue)的组件进行打包, 并获取对应文件上下文
export const loadComponent = (view: string) => {
  // TODO: 自定义chunk name
  return () => context(`./${view}/index.vue`) // 从上下文中动态引入的组件
};
// TODO: 通过设备类型区分路由
export const convertRoutes = (menuInfo: MenuInfo) => {
    let component = menuInfo.component;
    let routeObj: any = menuInfo;
    //如果component为Layout
    if (component == "Layout") {
      routeObj.component = (resolve: any) => require([`@/layout/index2.vue`], resolve)
    } else {
      routeObj.component = loadComponent(component)
    }
    //如果存在
    if (menuInfo.children) {
      let routeList:any = []
      menuInfo.children.forEach(menu=>{
        let route = convertRoutes(menu)
        routeList.push(route);
      })
      routeObj.children = routeList
    }
  return routeObj
}

export interface Meta{
  hidden:boolean
  icon:string
  id:number,
  noCache:boolean,
  title:string
}

export interface MenuInfo{
  component:string
  name:string
  path:string
  redirect:string
  meta:Meta
  children:MenuInfo[]
}

export interface IPermissionState {
  routes: RouteConfig[]
  dynamicRoutes: RouteConfig[]
  defaultPath: string
}

@Module({ dynamic: true, store, name: 'permission' })
class Permission extends VuexModule implements IPermissionState {
  public routes: RouteConfig[] = []
  public dynamicRoutes: RouteConfig[] = []
  public defaultPath: string = ''

  @Mutation
  private SET_ROUTES(routes: RouteConfig[]) {
    this.routes = routes
    this.dynamicRoutes = routes
  }
  @Mutation
  private SET_DEFAULT_PATH(path: string) {
    this.defaultPath = path
  }
  // @Action
  // public GenerateRoutes(roles: string[]) {
  //   let accessedRoutes
  //   if (roles.includes('admin')) {
  //     accessedRoutes = asyncRoutes
  //   } else {
  //     accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
  //   }
  //   this.SET_ROUTES(accessedRoutes)
  // }
  @Action
  public GenerateRoutes(menuList: MenuInfo[]) {
    let dynamicRoutes:any[] = []
    menuList.forEach(menu=>{
      let route = convertRoutes(menu)
      dynamicRoutes.push(route);
    })
    this.SET_ROUTES(dynamicRoutes.concat(tailerConstantRoutes))
    this.SET_DEFAULT_PATH(findDefaultPath(dynamicRoutes))
  }
}

function findDefaultPath(dynamicRoutes: any[], cur = '') {
  if(cur){
    return cur
  };

  for(const route of dynamicRoutes){
    if(route.children){
      const path = findDefaultPath(route.children);
      if(path){
        return path
      };
    }else if(!route.meta?.hidden) {
      return route.path;
    }
  }
  return '';
}

export const PermissionModule = getModule(Permission)
