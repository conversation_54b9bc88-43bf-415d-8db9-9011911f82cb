import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'
import store from '@/store'
import { apiGetListByOrgTree } from "@/api/users";
import { apiWellList } from "@/api/wellInfo";
export interface IConstantState {
    userTreeList: any[],
    wellList: any[]
}

@Module({ dynamic: true, store, name: 'constant' })
class Constant extends VuexModule implements IConstantState {
    public userTreeList: any[] = []
    public wellList: any[] = []

    @Mutation
    private SET_USER_TREE_LIST(userTreeList: any[]) {
        this.userTreeList = userTreeList
    }

    @Mutation
    private SET_WELL_LIST(wellList: any[]) {
        this.wellList = wellList
    }

    @Action
    public async getUserTreeList(noCache = false) {
        if (!this.userTreeList.length || noCache) {
            await apiGetListByOrgTree({}).then(res => {
                console.log(res.data.data)
                this.SET_USER_TREE_LIST(res.data.data || [])
            })
        }
        return this.userTreeList
    }

    @Action
    public async getWellList(noCache = false) {
        if (!this.wellList.length || noCache) {
            await apiWellList({}).then(res => {
                console.log(res.data.data)
                this.SET_WELL_LIST(res.data.data || [])
            })
        }
        return this.wellList
    }
}

export const ConstantModule = getModule(Constant)
