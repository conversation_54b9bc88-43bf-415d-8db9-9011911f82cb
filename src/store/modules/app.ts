import {
    VuexModule,
    Module,
    Mutation,
    Action,
    getModule
  } from 'vuex-module-decorators'
  import {
    getSidebarStatus,
    setSidebarStatus,

  } from '@/utils/cookies'
  import store from '@/store'
  //import { getLocale } from '@/lang'
  
  export enum DeviceType {
    Mobile,
    Desktop
  }
  
  export interface IAppState {
    device: DeviceType
    sidebar: {
      opened: boolean
      withoutAnimation: boolean
    }
    language: string
  }
  function getDefaultDevice(){
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobile = /iphone|ipad|ipod|android|blackberry|mini|windows\sce|palm/i.test(userAgent);
    return isMobile ? DeviceType.Mobile : DeviceType.Desktop;
  }
  @Module({ dynamic: true, store, name: 'app' })
  class App extends VuexModule implements IAppState {
    public sidebar = {
      opened: getSidebarStatus() !== 'closed',
      withoutAnimation: false
    }
    public device = getDefaultDevice();
    public language = "zh"//getLocale()
  
    @Mutation
    private TOGGLE_SIDEBAR(withoutAnimation: boolean) {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = withoutAnimation
      if (this.sidebar.opened) {
        setSidebarStatus('opened')
      } else {
        setSidebarStatus('closed')
      }
    }
  
    @Mutation
    private CLOSE_SIDEBAR(withoutAnimation: boolean) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
      setSidebarStatus('closed')
    }
  
    @Mutation
    private TOGGLE_DEVICE(device: DeviceType) {
      this.device = device
    }
  
    @Mutation
    private SET_LANGUAGE(language: string) {
      this.language = language
      //setLanguage(this.language)
    }
  
    @Action
    public ToggleSideBar(withoutAnimation: boolean) {
      this.TOGGLE_SIDEBAR(withoutAnimation)
    }
  
    @Action
    public CloseSideBar(withoutAnimation: boolean) {
      this.CLOSE_SIDEBAR(withoutAnimation)
    }
  
    @Action
    public ToggleDevice(device: DeviceType) {
      this.TOGGLE_DEVICE(device)
    }
  
    @Action
    public SetLanguage(language: string) {
      //this.SET_LANGUAGE(language)
    }
  }
  
  export const AppModule = getModule(App)
  