import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'
import { login, getAuthUserInfo } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/cookies'
import router, { resetRouter } from '@/router'
import { PermissionModule } from './permission'
import { TagsViewModule } from './tags-view'
import store from '@/store'

export interface IUserState {
  token: string
  name: string
  realName: string
  department: string
  avatar: string
  introduction: string
  roles: number[]
  email: string,
  userId: number,
  authFlagList: string[]
}

@Module({ dynamic: true, store, name: 'user' })
class User extends VuexModule implements IUserState {
  public token = getToken() || ''
  public name = ''
  public realName = ''
  public department = ''
  public avatar = ''
  public introduction = ''
  public roles: number[] = []
  public email = ''
  public userId = -1
  public menuList: [] = []
  public authFlagList: string[] = []
  @Mutation
  private SET_TOKEN(token: string) {
    this.token = token
  }

  @Mutation
  private SET_NAME(name: string) {
    this.name = name
  }

  @Mutation
  private SET_REAL_NAME(realName: string) {
    this.realName = realName
  }
  @Mutation
  private SET_DEPARTMENT(department: string) {
    this.department = department
  }

  @Mutation
  private SET_AVATAR(avatar: string) {
    this.avatar = avatar
  }

  @Mutation
  private SET_INTRODUCTION(introduction: string) {
    this.introduction = introduction
  }

  @Mutation
  private SET_ROLES(roles: number[]) {
    this.roles = roles
  }

  @Mutation
  private SET_EMAIL(email: string) {
    this.email = email
  }

  @Mutation
  private SET_USER_ID(userId: number) {
    this.userId = userId
  }

  @Mutation
  private SET_MENU_LIST(menuList: []) {
    this.menuList = menuList
  }

  @Mutation
  private SET_AUTH_FLAG_LIST(btnPermissionList: []) {
    this.authFlagList = btnPermissionList
  }

  @Action
  public async Login(form: FormData) {
    // let { username, password } = userInfo
    // username = username.trim()
    const { data } = await login(form)//TODO: type:AxiosResponse
    setToken(data.data.token)
    this.SET_TOKEN(data.data.token)
  }

  @Action
  public ResetToken() {
    removeToken()
    this.SET_TOKEN('')
    this.SET_ROLES([])
  }
  // 添加rawError, 避免其他莫名其妙的error 
  @Action({ rawError: true })
  public GetUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.token === '') {
        reject('GetUserInfo: token is undefined!')
      }
      getAuthUserInfo({}).then(res => {
        const data = res.data;
        const { menuList, accountInfo, memberInfo, btnPermissionList } = data.data;
        let roles: number[] = []
        menuList.forEach((menu: any) => {
          if (!roles.includes(menu.id)) {
            roles.push(menu.id)
          }
        })
        this.SET_MENU_LIST(menuList)
        this.SET_ROLES(roles)
        this.SET_NAME(memberInfo.name)
        this.SET_REAL_NAME(memberInfo.name)
        this.SET_EMAIL(memberInfo.email)
        this.SET_USER_ID(accountInfo.userId)
        this.SET_AUTH_FLAG_LIST(btnPermissionList || [])
        resolve(data);
      }).catch(() => {
        reject('权限认证失败，请重新登陆！')
      })
    })
    // try {
    //   const { data } = await getAuthUserInfo({ /* Your params here */ })
    //   console.log(data)
    //   const { menuList, accountInfo, memberInfo, btnPermissionList } = data.data
    //   // // role must be a non-empty array
    //   // if (!role || role.length <= 0) {
    //   //   throw Error('GetUserInfo: role must be a non-null array!')
    //   // }
    //   //TODO: roles
    //   let roles: number[] = []
    //   menuList.forEach((menu: any) => {
    //     if (!roles.includes(menu.id)) {
    //       roles.push(menu.id)
    //     }
    //   })
    //   this.SET_MENU_LIST(menuList)
    //   this.SET_ROLES(roles)
    //   this.SET_NAME(memberInfo.name)
    //   this.SET_REAL_NAME(memberInfo.name)
    //   this.SET_EMAIL(memberInfo.email)
    //   this.SET_USER_ID(accountInfo.userId)
    //   this.SET_AUTH_FLAG_LIST(btnPermissionList || [])
    // } catch (err) {
    //   throw Error('Verification failed, please Login again.')
    // }

  }

  @Action
  public async ChangeRoles(role: string) {
    // Dynamically modify permissions
    const token = role + '-token'
    this.SET_TOKEN(token)
    setToken(token)
    await this.GetUserInfo()
    resetRouter()
    // Generate dynamic accessible routes based on roles
    PermissionModule.GenerateRoutes(this.menuList)
    // Add generated routes
    router.addRoutes(PermissionModule.dynamicRoutes)
    // Reset visited views and cached views
    TagsViewModule.delAllViews()
  }

  @Action
  public async LogOut() {
    if (this.token === '') {
      throw Error('LogOut: token is undefined!')
    }
    // await logout()
    removeToken()
    resetRouter()

    // Reset visited views and cached views
    TagsViewModule.delAllViews()
    this.SET_TOKEN('')
    this.SET_ROLES([])
    this.SET_MENU_LIST([])
    this.SET_AUTH_FLAG_LIST([])
  }
}

export const UserModule = getModule(User)
