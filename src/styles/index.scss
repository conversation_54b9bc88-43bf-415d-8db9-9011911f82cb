// @import './variables.scss'; // Already imported in style-resources-loader
// @import './mixins.scss'; // Already imported in style-resources-loader
@import './transition.scss';
@import './svgicon.scss';
@import './common.scss';
@import './report.scss';
/* Global scss */

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
  height: 100%;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  @include clearfix;
}

label {
  font-weight: 700;
}

.app-container {
  padding: 12px;
  padding-bottom: 0;
  height: 100%;
  // height: 800px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

// Refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

aside {
  background: #eef1f6;
  color: #2c3e50;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
  }
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.no-padding {
  padding: 0px !important;
}

.simple-line{
  width:100%;
  border-top:1px dashed #DCE1E6;
  margin: 20px 0;
}

.custom-form {
    margin-bottom: 10px;
    .custom-form-head {
        display: inline-block;
        font-size: 16px;
        // font-weight: bold;
        text-align: right;
    }
}

.editable {
  .el-table__row {
      td {
          padding: 0 !important;
          .cell {
              // height: 46px;
              padding: 0 !important;
              overflow: hidden;
              .el-table--border td:first-child .cell {
                  padding: 0 !important;
              }
              .el-input {
                  input {
                      height: 46px !important;
                      border: none !important;
                      box-sizing: border-box;
                      //   border-bottom: 1px solid transparent !important;
                      border-radius: 0 !important;
                  }
                  .el-input__inner {
                      font-family: Arial, "Times New Roman", "Microsoft YaHei",
                          SimHei;
                      font-size: 13px;
                      letter-spacing: 1px;
                      line-height: 46px;
                  }
              }
              .cell-content {
                  font-size: 13px;
                  padding: 0 15px;
                  height: 100%;
                  font-family: Arial, "Times New Roman", "Microsoft YaHei",
                      SimHei;
                  // line-height: 46px;
                  letter-spacing: 1px;
              }
          }
      }
  }
}
.editor .ql-container {
  min-height: 200px;
  max-height: 200px;
  overflow: auto;
}

// 一直显示date-picker中的'至'
.fixed-separator-date-picker .el-range-separator {
  width: 20px !important;
}

.el-year-table td.current:not(.disabled) .cell {
  background: rgba($color: #004e7e, $alpha: 0.1);
  font-weight: bold;
}

// quill
.ql-container.ql-snow {
  height: calc(100% - 60px);
  width: 100%;
}
.editor.ql-editor {
  width: 100%;
  height: calc(100% + 20px);
  margin-top: -20px;
  padding: 0;
}


.thin-margin-form{
  .el-form-item{
      margin-bottom: 6px;
  }
}

.textarea-ofh .el-textarea__inner{
  overflow: hidden;
  min-height: 26px !important;
}

.ghost-uploader{
  width: 0;
  height: 0;
}