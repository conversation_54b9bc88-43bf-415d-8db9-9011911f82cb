.app-card {
  box-sizing: border-box;
  margin: 0;
  background: #fff;
  color: rgba(0,0,0,.65);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  position: relative;
  padding-top: 16px;
  padding-left: 40px;
  padding-right: 40px;
  padding-bottom: 0;
  overflow: auto;
  zoom: 1;
  .app-card-title{
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #333333;
    margin-bottom: 10px;
  }
}
//表格多选对齐
.el-table-column--selection .cell {
  padding-left: 10px;
  padding-right: 10px;
}
.el-table--medium .el-table__cell{
  padding: 9px 0
}

.el-table .cell{
  line-height: 18px;
}

.el-table .el-button{
  padding: 0
}
.el-table .caret-wrapper{
  justify-content: space-between;
  height: 19px;
  .sort-caret{
    position: relative;
    left: 0;
    top: 0;
    bottom: 0;
    border-width: 4px;
  }
}

.el-select-dropdown.el-popper {
  .el-select-dropdown__wrap {
    max-height: 500px;
  }
}