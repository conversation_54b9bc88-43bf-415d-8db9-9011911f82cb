.mwd-container {

  // min-width: 1200px;
  //   height: calc(100% - 20px);
  //   padding-right: 5px;
  //   // padding-left: 1px;
  //   overflow: auto;
  //   //height: calc(3000px);
  //   &::-webkit-scrollbar {
  //     width: 5px;
  //     height: 8px;
  //     z-index: 999;
  //     position: fixed;
  //   }
  //   &::-webkit-scrollbar-thumb {
  //     border-radius: 3px;
  //     background: #666666;
  //     width: 6px;
  //   }
  //   &::-webkit-scrollbar-track {
  //     background: white;
  //   }
  .el-textarea.is-disabled .el-textarea__inner {
    background-color: transparent;
    color: inherit;
    cursor: default;
  }

  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: transparent;
    color: inherit;
    cursor: default
  }

  .el-input.is-disabled .el-input__inner {
    background-color: transparent;
    color: inherit;
    cursor: default
  }

  .mwd-header {
    display: flex;
    border-bottom: 1px solid #cccccc;

    .trademark-container {
      border: 1px solid #cccccc;
      border-bottom: none;
      box-sizing: border-box;
      width: 140px;
      min-width: 140px;
      height: 100px;
      background-size: contain;
      background-position: center;
    }

    .header-content {
      flex: 1 1 auto;

      .header {
        width: 100%;
        height: 60px;
        line-height: 60px;
        font-size: 28px;
        font-weight: 600;
        text-align: center;
        background: #cccccc;
      }

      .basic-info {
        display: flex;
        justify-content: space-between;
        // line-height: 3.6;
        margin-left: 10px;
        height: 40px;

        .info-container {
          display: flex;
          align-items: center;

          .info-title {
            flex: 150px 0 0;
          }

          .basic-info-input {
            border-bottom: 1px solid black;
            max-width: 200px;
          }
        }

        div {
          flex: 1;
          text-align: right;
        }
      }
    }
  }

  .operator-line {}

  .content-container {
    width: 100%;

    .line {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .title {
        font-size: 13px;
        box-sizing: border-box;
        background-color: #eee;
        height: 30px;
        line-height: 30px;
        border: 1px black solid;
        font-weight: 600;

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
          padding: 0 5px;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }

      .text {
        box-sizing: border-box;
        background-color: white;
        height: 30px;
        line-height: 30px;
        border: 1px black solid;
        font-weight: 400;
        text-align: center;

        .mwd-input {
          width: 100%;
          height: 99%;
        }

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }
    }

    .line-2 {
      width: 100%;
      display: flex;

      .line-2-left {
        flex: 50% 0 0;
        display: flex;
        flex-wrap: wrap;

        .title-2 {
          box-sizing: border-box;
          background-color: #eee;
          height: 30px;
          line-height: 30px;
          border: 1px black solid;
          font-weight: 600;

          &.center {
            text-align: center;
          }

          &.right {
            text-align: right;
            padding: 0 10px;
          }
        }

        .text-2 {
          box-sizing: border-box;
          background-color: white;
          height: 30px;
          line-height: 30px;
          border: 1px black solid;
          font-weight: 400;
          text-align: center;

          .mwd-input {
            width: 100%;
          }

          &.center {
            text-align: center;
          }

          &.right {
            text-align: right;
          }
        }
      }

      .line-2-right {
        flex: 1;
      }
    }

    .report-header {
      font-size: 24px;
      font-weight: 400;
      box-sizing: border-box;
      border: 1px solid black;
      text-align: center;
      height: 50px;
      line-height: 50px;
    }

    .report-title {
      font-size: 20px;
      font-weight: 600;
      box-sizing: border-box;
      border: 1px solid black;
      text-align: center;
      height: 50px;
      line-height: 50px;
    }
  }

  .content-container2 {
    // user-select: none;
    width: 100%;

    .line {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      .title {
        font-size: 13px;
        box-sizing: border-box;
        background-color: #F9F9F9;
        height: 34px;
        line-height: 34px;
        border: 1px #E8E8E8 solid;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #7195AB;

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
          padding: 0 5px;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }

      .text {
        box-sizing: border-box;
        background-color: white;
        height: 34px;
        line-height: 34px;
        // border: 1px #E8E8E8 solid;
        border-top: 1px #E8E8E8 solid;
        border-right: 1px #E8E8E8 solid;
        border-left: 1px #E8E8E8 solid;
        font-weight: 400;
        text-align: center;

        .mwd-input {
          width: 100%;
          height: 99%;
        }

        &.not-input {
          background: #FaFaFa;
        }

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }

      .text--last {
        box-sizing: border-box;
        background-color: white;
        height: 34px;
        line-height: 34px;
        border: 1px #E8E8E8 solid;

        font-weight: 400;
        text-align: center;

        .mwd-input {
          width: 100%;
          height: 99%;
        }

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }

      .title2 {
        box-sizing: border-box;
        background-color: white;
        height: 34px;
        line-height: 34px;
        // border: 1px #E8E8E8 solid;
        border-top: 1px #E8E8E8 solid;
        border-right: 1px #E8E8E8 solid;
        border-left: 1px #E8E8E8 solid;
        font-weight: 400;
        padding-left: 5px;
        background-color: #F9F9F9;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #7195AB;

        text-align: center;

        .mwd-input {
          width: 100%;
          height: 99%;
        }

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }

      .title2--last {
        box-sizing: border-box;
        background-color: white;
        height: 34px;
        line-height: 34px;
        border: 1px #E8E8E8 solid;

        background-color: #F9F9F9;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        padding-left: 20px;

        .mwd-input {
          width: 100%;
          height: 99%;
        }

        &.center {
          text-align: center;
        }

        &.right {
          text-align: right;
        }

        &.left {
          text-align: left;
        }

        &.px10 {
          font-size: 10px;
        }

        &.px12 {
          font-size: 12px;
        }

        &.px14 {
          font-size: 14px;
        }
      }
    }

    .line-2 {
      width: 100%;
      display: flex;

      .line-2-left {
        flex: 50% 0 0;
        display: flex;
        flex-wrap: wrap;

        .title-2 {
          box-sizing: border-box;
          background-color: #eee;
          height: 30px;
          line-height: 30px;
          border: 1px #E8E8E8 solid;
          font-weight: 600;

          &.center {
            text-align: center;
          }

          &.right {
            text-align: right;
            padding: 0 10px;
          }
        }

        .text-2 {
          box-sizing: border-box;
          background-color: white;
          height: 30px;
          line-height: 30px;
          border: 1px #E8E8E8 solid;
          font-weight: 400;
          text-align: center;

          .mwd-input {
            width: 100%;
          }

          &.center {
            text-align: center;
          }

          &.right {
            text-align: right;
          }
        }
      }

      .line-2-right {
        flex: 1;
      }
    }

    .report-header {
      font-size: 24px;
      font-weight: 400;
      box-sizing: border-box;
      border: 1px solid #E8E8E8;
      text-align: center;
      height: 50px;
      line-height: 50px;
    }

    .report-title {
      font-size: 20px;
      font-weight: 600;
      box-sizing: border-box;
      border: 1px solid #E8E8E8;
      text-align: center;
      height: 50px;
      line-height: 50px;
    }

    .hover-line {
      position: relative;

      .hover-icon {
        display: none;
        position: absolute;
        right: 2px;
        z-index: 999999;
        font-size: 6px;
      }

      .plus {
        bottom: 2px;
        color: green;
      }

      .delete {
        top: 2px;
        color: red;
      }

      &:hover .hover-icon {
        display: block;
      }
    }

    .box-border {
      border: 2px solid #BAD0DC
    }
  }
}

.mwd-input {
  &.el-select .el-input .el-select__caret.el-icon-arrow-up {
    display: none !important;

  }

  &.el-input.is-disabled .el-input__inner {
    background-color: transparent;
    border-color: none;
    color: inherit;
    cursor: inherit;
  }

  .el-textarea__inner {
    border: none;
    resize: none;
    color: #333333;
    font-size: 16px;
    line-height: 1 !important;
    min-height: 58px !important;
  }

  &.el-input-number {
    line-height: 14px !important;
  }

  .el-input__inner {
    font-size: 16px;
    color: #000000;
    height: 100% !important;
    line-height: 1 !important;
    padding: 0 !important;
    text-align: center;
    border: none;
    min-width: 0px;
  }

  &.left .el-input__inner {
    text-align: left;
    padding-left: 5px !important;
  }

  .el-input__prefix {
    display: none;
  }

  .el-input__suffix {
    // display: none;
    right: -3px !important;

    .el-icon-circle-close {
      line-height: 1 !important;
    }
  }
}

.report-container {
  padding-left: 0px;
  // display: none;
  .report {
      height: calc(100vh - 242px);

      display: flex;
      // justify-content: space-around;
      .key-list {
          flex: 160px 0 0;
          padding-right: 10px;
          .search {
              padding: 16px 40px;
          }
          .list-title {
              height: 28px;
              line-height: 28px;
              padding-left: 20px;
              margin-bottom: 10px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #737677;
          }
          .list-content {
              overflow-y: overlay;
              max-height: calc(100vh - 260px);
              &::-webkit-scrollbar {
                  width: 5px;
                  height: 8px;
                  z-index: 999;
                  position: fixed;
              }
              &::-webkit-scrollbar-thumb {
                  border-radius: 3px;
                  background: #666666;
                  width: 6px;
              }
              &::-webkit-scrollbar-track {
                  background: white;
              }
              .report-list-item {
                  height: 36px;
                  font-size: 14px;
                  line-height: 36px;
                  padding: 0 20px;
                  color: #818899;
                  position: relative;
                  cursor: pointer;
                  user-select: none;
                  .el-icon-date {
                      margin-right: 10px;
                  }
                  &:hover {
                      background: rgba(228, 242, 253, 0.5);
                  }
                  .list-more-icon {
                      display: none;
                  }
                  &.checked {
                      background: #e4f2fd;
                  }
                  &:hover {
                      .list-more-icon {
                          position: absolute;
                          float: right;
                          top: 6px;
                          right: 6px;
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          width: 24px;
                          height: 24px;
                          border-radius: 50%;
                          &:hover {
                              background: rgba(3, 14, 44, 0.04);
                          }
                          i {
                              transform: rotate(90deg);
                              font-weight: 200;
                          }
                      }
                  }
              }
          }
      }
      .content {
          // overflow: auto;
          flex: 1;
          // padding: 10px 20px;
         // min-width: 1390px;
          height: calc(100% - 20px);
          width: 100%;
          padding-right: 5px;
          // padding-left: 1px;
          // overflow: auto;
          //height: calc(3000px);
          &::-webkit-scrollbar {
              width: 5px;
              height: 8px;
              z-index: 999;
              position: fixed;
          }
          &::-webkit-scrollbar-thumb {
              border-radius: 3px;
              background: #666666;
              width: 6px;
          }
          &::-webkit-scrollbar-track {
              background: white;
          }
          .mwd-container{
              height: calc(100%);
              overflow: auto;
              padding-right: 4px;
              padding-bottom:10px;
              padding-top: 10px;
              &::-webkit-scrollbar {
                  width: 5px;
                  height: 8px;
                  z-index: 999;
                  position: fixed;
              }
              &::-webkit-scrollbar-thumb {
                  border-radius: 3px;
                  background: #666666;
                  width: 6px;
              }
              &::-webkit-scrollbar-track {
                  background: white;
              }
          }
      }
  }
}