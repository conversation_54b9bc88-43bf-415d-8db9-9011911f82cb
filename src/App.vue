<template>
    <div id="app">
        <router-view />
        <!-- <service-worker-update-popup /> -->
    </div>
</template>

<script lang="ts">
import axios from "axios";
import { Component, Vue } from "vue-property-decorator";
@Component({
    name: "App",
    components: {},
})
export default class extends Vue {
    created() {
        // this.onCheckVersion()
    }
    private intervalTime:any;
    private onCheckVersion(){
        const time = 5000;
        this.checkVerison(time);
        this.$on('hook:beforeDestroy',()=>{
            clearInterval(this.intervalTime);
            this.intervalTime = null;
        })
    }
    private checkVerison(time){
        this.intervalTime = setInterval(()=>{
            axios({
                url:`${window.location.origin}/version.json`,
                method:'get',
                timeout:60000,
            }).then(({status, data})=>{
                if(status===200){
                    console.log('最新版本', data.version )
                    console.log('当前版本', this.$version)
                }
            })
        }, time)
    }
}
</script>
