import { RouteConfig } from "vue-router";
import Layout from "@/layout/index2.vue";

export const constantRoutes: RouteConfig[] = [
    {
        path: "/",
        component: Layout,
        redirect: "/dashboard",
        meta: {},
        children: [
            {
                path: "dashboard",
                component: () => import("@/views/dashboard/index.vue"),
                name: "dashboard",
                meta: {
                    title: "工作台",
                    icon: "gongzuotai"
                },
            },
        ]
    },
    {
        path: "/daily",
        component: Layout,
        redirect: "/daily/index",
        meta: { title: "日报管理", icon: "gongzuoribao" },
        children: [
            {
                path: "index",
                component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/index.vue"),
                name: "daily",
                meta: {
                    title: "作业查询",
                    icon: "dashboard",
                },
            },
            // {
            //     path: "senseroffset",
            //     component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/sensorOffset/index.vue"),
            //     name: "senseroffset",
            //     meta: {
            //         title: "零长计算",
            //         icon: "dashboard",
            //     },
            // },
            // {
            //     path: "workjob",
            //     component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/statistics/workjob.vue"),
            //     name: "workjob",
            //     meta: {
            //         title: "仪器施工统计",
            //         icon: "dashboard",
            //     },
            // },
        ],
    },
    {
        path: "/tooltrack",
        component: Layout,
        redirect: "/tooltrack/toolstatus",
        meta: { title: "仪器追踪", icon: "gongjuyiqi" },
        children: [
            {
                path: "toolstatus",
                component: () => import("@/views/tool-track/tool-status/index.vue"),
                name: "toolstatus",
                meta: {
                    title: "仪器状态查询",
                    icon: "dashboard",
                },
            },
            {
                path: "tooltrack",
                component: () => import("@/views/tool-track/tool-track/index.vue"),
                name: "tooltrack",
                meta: {
                    title: "仪器流转历史",
                    icon: "dashboard",
                },
            },
            {
                path: "partservice",
                component: () => import("@/views/tool-track/part-service/index.vue"),
                name: "partservice",
                meta: {
                    title: "部件服役历史",
                    icon: "dashboard",
                },
            },
            {
                path: "toolservice",
                component: () => import("@/views/tool-track/tool-service/index.vue"),
                name: "toolservice",
                meta: {
                    title: "仪器使用统计",
                    icon: "dashboard",
                },
            },
        ],
    },
    {
        path: "/base",
        component: Layout,
        redirect: "/base/well",
        meta: { title: "基础信息", icon: "dashboard" },
        children: [
            {
                path: "well",
                component: () => import(/* webpackChunkName: "dashboard" */ "@/views/base/well.vue"),
                name: "well-Manage",
                meta: {
                    title: "井信息管理",
                    icon: "dashboard",
                },
            },
            {
                path: "kitModel",
                component: () => import(/* webpackChunkName: "dashboard" */ "@/views/base/kitTemplate.vue"),
                name: "kitModel",
                meta: {
                    title: "kit模板管理",
                    icon: "dashboard",
                },
            },
            {
                path: "dictionary",
                component: () => import(/* webpackChunkName: "dashboard" */ "@/views/base/dictionary.vue"),
                name: "dictionary",
                meta: {
                    title: "字典管理",
                    icon: "dashboard",
                },
            },
        ],
    },
    {
        path: "/mwd-tool-maintain",
        component: Layout,
        redirect: "/workorder",
        meta: { title: "MWD维修", icon: "dashboard" },
        children: [
            {
                path: "workorder",
                component: () => import("@/views/mwd-tool-maintain/work-order/index.vue"),
                name: "mwd-workorder",
                meta: {
                    title: "工单查询",
                    icon: "dashboard",
                },
            },
            {
                path: "maintain",
                component: () => import("@/views/mwd-tool-maintain/maintain/index.vue"),
                name: "mwd-maintain",
                meta: {
                    title: "仪器维修",
                    icon: "dashboard",
                },
            },
            {
                path: "statistics",
                component: () => import("@/views/mwd-tool-maintain/statistics/index.vue"),
                name: "mwd-statistics",
                meta: {
                    title: "维修统计",
                    icon: "dashboard",
                },
            },
        ],
    },
    {
        path: "/downhole-tool-maintain",
        component: Layout,
        redirect: "/workorder",
        meta: { title: "井下工具维修", icon: "dashboard" },
        children: [
            {
                path: "workorder",
                component: () => import("@/views/downhole-tool-maintain/work-order/index.vue"),
                name: "downhole-workorder",
                meta: {
                    title: "工单查询",
                    icon: "dashboard",
                },
            },
            {
                path: "maintain",
                component: () => import("@/views/downhole-tool-maintain/maintain/index.vue"),
                name: "downhole-maintain",
                meta: {
                    title: "工具维修",
                    icon: "dashboard",
                },
            },
            {
                path: "statistics",
                component: () => import("@/views/downhole-tool-maintain/statistics/index.vue"),
                name: "downhole-statistics",
                meta: {
                    title: "统计分析",
                    icon: "dashboard",
                },
            },
        ],
    },
    {
        path: '/permission',
        component: Layout,
        redirect: '/permission/department',
        meta: {
            title: '组织架构',
            id: 21,
            icon: 'lock',
            alwaysShow: true // will always show the root menu
        },
        children: [
            {
                path: 'department',
                component: () => import('@/views/permission/department.vue'),
                name: 'department',
                meta: {
                    title: '部门管理',
                }
            },
            {
                path: 'user',
                component: () => import('@/views/permission/user.vue'),
                name: 'user',
                meta: {
                    title: '用户管理'
                }
            },
            {
                path: ' ',
                component: () => import('@/views/permission/role.vue'),
                name: 'role',
                meta: {
                    title: '角色管理',
                },
            },
            {
                path: 'assign',
                component: () => import('@/views/permission/assign.vue'),
                name: 'assign',
                meta: {
                    title: '分配权限',
                    hidden: true
                },
            },
            {
                path: 'menu',
                component: () => import('@/views/permission/menu.vue'),
                name: 'menu',
                meta: {
                    title: '菜单列表'
                }
            },
            {
                path: 'resource',
                component: () => import('@/views/permission/resource.vue'),
                name: 'resource',
                meta: {
                    title: '资源列表'
                }
            },
        ]
    },
]