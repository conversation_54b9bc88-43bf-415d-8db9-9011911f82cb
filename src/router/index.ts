import Vue from "vue";
import VueRouter, { RouteConfig } from "vue-router";

/* Layout */
//import Layout from '@/layout/index.vue'
import Layout from "@/layout/index2.vue";

Vue.use(VueRouter);

/*
  Note: sub-menu only appear when children.length>=1
  Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
*/

/*
  name:'router-name'             the name field is required when using <keep-alive>, it should also match its component's name property
                                 detail see : https://vuejs.org/v2/guide/components-dynamic-async.html#keep-alive-with-Dynamic-Components
  redirect:                      if set to 'noredirect', no redirect action will be trigger when clicking the breadcrumb
  meta: {
    roles: ['admin', 'editor']   will control the page roles (allow setting multiple roles)
    title: 'title'               the name showed in subMenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon showed in the sidebar
    hidden: true                 if true, this route will not show in the sidebar (default is false)
    alwaysShow: true             if true, will always show the root menu (default is false)
                                 if false, hide the root menu when has less or equal than one children route
    breadcrumb: false            if false, the item will be hidden in breadcrumb (default is true)
    noCache: true                if true, the page will not be cached (default is false)
    affix: true                  if true, the tag will affix in the tags-view
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
*/

/**
 * NOTE:多层级菜单中，第二层(相当于左侧菜单栏的顶层)的redirect一定要到最深的一层，不能逐级重定向，否则tagsview会有bug
 */

/**
 ConstantRoutes
 a base page that does not have permission requirements
 all roles can be accessed
 */
export const headerConstantRoutes: RouteConfig[] = [
    
    {
        path: "/repair",
        component: Layout,
        redirect: "/repair/add",
        meta: { title: "库房", icon: "kufang" },
        children: [
            {
                path: "/repair/add",
                component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/repair/addRepair/index.vue"),
                name: "repair_add",
                meta: {
                    title: "新增返修单",
                    icon: "dashboard",
                },
            },
        ]
    },
    {
        path: "/redirect",
        component: Layout,
        meta: { hidden: true, noCache: true },
        children: [
            {
                path: "/redirect/:path(.*)",
                component: () => import(/* webpackChunkName: "redirect" */ "@/views/redirect/index.vue"),
                meta: { hidden: true, noCache: true },
            },
        ],
    },
    {
        path: "/processprint",
        component: () => import(/* webpackChunkName: "redirect" */ "@/views/taskprocess/procedure/print.vue"),
        meta: { hidden: true },
    },
    {
        path: "/login",
        component: () => import(/* webpackChunkName: "login" */ "@/views/login/index.vue"),
        meta: { hidden: true },
    },
    {
        path: "/404",
        component: () => import(/* webpackChunkName: "404" */ "@/views/error-page/404.vue"),
        meta: { hidden: true },
    },
    {
        path: "/401",
        component: () => import(/* webpackChunkName: "401" */ "@/views/error-page/401.vue"),
        meta: { hidden: true },
    },
];
// some route like 404
export const tailerConstantRoutes: any[] = [
    // {
    //     path: "/repair",
    //     component: Layout,
    //     redirect: "/repair/add",
    //     meta: { title: "库房", icon: "kufang" },
    //     children: [
    //         {
    //             path: "/repair/add",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/repair/addRepair/index.vue"),
    //             name: "repair_add",
    //             meta: {
    //                 title: "新增返修单",
    //                 icon: "dashboard",
    //             },
    //         },
    //     ]
    // },
    // {
    //     path: "/warehouse",
    //     component: Layout,
    //     redirect: "/daily/index",
    //     meta: { title: "库房", icon: "kufang" },
    //     children: [
    //         {
    //             path: "/warehouse/request",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/request/index.vue"),
    //             name: "warehouse_request",
    //             meta: {
    //                 noCache: true,
    //                 title: "需求单接收",
    //                 icon: "dashboard",
    //             },
    //         },
    //         {
    //             path: "/warehouse/transfer",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/transfer/index.vue"),
    //             name: "warehouse_transfer",
    //             meta: {
    //                 noCache: true,
    //                 title: "调拨单接收",
    //                 icon: "dashboard",
    //             },
    //         },
    //         {
    //             path: "/warehouse/kit/history",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/kit_history/index.vue"),
    //             name: "warehouse_kit_history",
    //             meta: {
    //                 noCache: true,
    //                 title: "kit箱历史查询",
    //                 icon: "dashboard",
    //             },
    //         },
    //         {
    //             path: "/warehouse/kit/detail",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/kit_detail/index.vue"),
    //             name: "warehouse_kit_detail",
    //             meta: {
    //                 noCache: true,
    //                 title: "kit箱出入库详情",
    //                 icon: "dashboard",
    //             },
    //         },
    //         // {
    //         //     path: "/warehouse/kit/in",
    //         //     component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/kit_load/index.vue"),
    //         //     name: "warehouse_kit_in",
    //         //     meta: {
    //         //         noCache: true,
    //         //         title: "kit箱入库",
    //         //         icon: "dashboard",
    //         //     },
    //         // },
    //         {
    //             path: "/warehouse/stock",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/stock/index.vue"),
    //             name: "warehouse_stock",
    //             meta: {
    //                 noCache: true,
    //                 title: "库存盘点",
    //                 icon: "dashboard",
    //             },
    //         },
    //         {
    //             path: "/warehouse/kit/load",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/kit_load/index.vue"),
    //             name: "warehouse_kit_load",
    //             meta: {
    //                 noCache: true,
    //                 title: "kit箱出入库",
    //                 icon: "dashboard",
    //             },
    //         },
    //         // {
    //         //     path: "/warehouse/kit/out",
    //         //     component: () => import(/* webpackChunkName: "dashboard" */ "@/views/warehouse/kit_load/index.vue"),
    //         //     name: "warehouse_out",
    //         //     meta: {
    //         //         noCache: true,
    //         //         title: "kit箱出库",
    //         //         icon: "dashboard",
    //         //     },
    //         // },
    //         {
    //             path: "/warehouse/device/history",
    //             component: () => import(/* webpackChunkName: "dashboard" */ "@/views/daily/index.vue"),
    //             name: "warehouse_device_history",
    //             meta: {
    //                 noCache: true,
    //                 title: "仪器流转历史",
    //                 icon: "dashboard",
    //             },
    //         },
    //     ]
    // },
    {
        path: "*",
        redirect: "/404",
        meta: { hidden: true },
    },
];

const createRouter = () => new VueRouter({
    mode: "history",  // Disabled due to Github Pages doesn't support this, enable this if you need.
    scrollBehavior: (to, from, savedPosition) => {
        if (savedPosition) {
            return savedPosition;
        } else {
            return { x: 0, y: 0 };
        }
    },
    base: process.env.BASE_URL as string,
    routes: headerConstantRoutes,
});

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter();
    (router as any).matcher = (newRouter as any).matcher; // reset router
}

export default router;
