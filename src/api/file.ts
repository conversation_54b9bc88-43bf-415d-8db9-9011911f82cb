import request from '@/utils/request'

export const getSensorOffsets = (params: any) =>
  request({
    url: '/api/file/sensorOffsets/stream',
    method: 'get',
    params,
    responseType: 'blob',
    headers: {}
  })

export const saveSensorOffset = (data: any) =>
    request({
        url: '/api/file/sensorOffsets/save',
        method: 'post',
        data
    })
  export const apiUploadFile = (data: any) =>
    request({
        url: '/api/file/uploadFile',
        method: 'post',
        data,
        timeout: 500000
    })

export const apiDownloadFile = (params: any) =>
    request({
        url: "/api/file/download",
        method: "get",
        params,
        responseType: 'blob',
        timeout: 500000
    });

    export const apiUploadMassFile = (data:any) =>
      request({
          url: `/api/massFile/upload`,
          method: "post",
          data,
          timeout: 500000
      });
  export const apiGetMassFileList = (data:any, { current, size }:any) =>
      request({
          url: `/api/massFile/list/${current}/${size}`,
          method: "post",
          data
      });
  export const apiDeleteMassFile = (data:any) =>
      request({
          url: '/api/massFile/delete',
          method: 'post',
          data,
      })
  export const apiPreviewMassFile = (data:any) =>
      request({
          url: '/api/massFile/preview',
          method: 'post',
          data,
      })
  export const apiDownloadMassFile = (data:any) =>
      request({
          url: '/api/massFile/download',
          method: 'post',
          data,
          responseType: 'blob',
          timeout: 500000
      })
  