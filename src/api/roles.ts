import request from '@/utils/request'

export const getRoleList = (params: any) =>
    request({
        url: '/api/role/list',
        method: 'get',
        params
    })

export const assignUserRole = (data: any) =>
    request({
        url: '/api/user/role/assign',
        method: 'post',
        data
    })
//批量增加用户角色
export const apiBatchAssignUserRole = (data: any) =>
    request({
        url: '/api/user/role/batchAssign',
        method: 'post',
        data
    })
//删除用户角色
export const apiBatchDeleteUserRole = (data: any) =>
    request({
        url: '/api/user/role/batchDelete',
        method: 'post',
        data
    })


export const getDepartmentList = (params: any) =>
    request({
        url: '/api/department/list',
        method: 'get',
        params
    })


export const addRole = (data: any) =>
    request({
        url: '/api/role/add',
        method: 'post',
        data
    })
export const deleteRole = (params: any) =>
    request({
        url: '/api/role/delete',
        method: 'get',
        params
    })
export const updateRole = (data: any) =>
    request({
        url: '/api/role/update',
        method: 'post',
        data
    })
export const assignMenu = (data: any) =>
    request({
        url: '/api/role/menu/assign',
        method: 'post',
        data
    })
export const assignResource = (data: any) =>
    request({
        url: '/api/menu/resource/assign',
        method: 'post',
        data
    })

