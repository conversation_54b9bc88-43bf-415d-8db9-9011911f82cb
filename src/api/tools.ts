// 工具历史
import request from "@/utils/request";

export const apiGetDeviceHistoryList = ({ current, size }: any) =>
    request({
        url: `/api/deviceHistory/list/${current}/${size}`,
        method: "get",
    });
export const apiGetDeviceHistoryListBy = ({ current, size }: any, data: any) =>
    request({
        url: `/api/deviceHistory/listByCondition/${current}/${size}`,
        method: "post",
        data,
    });

export const apiAddDeviceHistory = (data: any) =>
    request({
        url: "/api/deviceHistory/add",
        method: "post",
        data,
    });

export const apiUpdateDeviceHistory = (data: any) =>
    request({
        url: "/api/deviceHistory/update",
        method: "post",
        data,
    });
    
export const apiGetDeviceInfo = (data: any) =>
    request({
        url: "/api/device/info",
        method: "post",
        data,
    });

export const apiGetDeviceList = (data: any) =>
    request({
        url: "/api/device/list/1/10",
        method: "post",
        data,
    });

// 外联仪器列表
export const apiGetCustomToolList = (data: any, { current, size }: any) =>
    request({
        url: `/api/component/list/${current}/${size}`,
        method: "post",
        data,
    });

export const apiGetInvNameFuzzyList = (data: any) => 
    request({
        url: `/api/component/nameList`,
        method: "post",
        data,
    });
export const apiGetSnFuzzyList = (data: any) => 
    request({
        url: `/api/component/getSerialNumberList`,
        method: "post",
        data,
    });