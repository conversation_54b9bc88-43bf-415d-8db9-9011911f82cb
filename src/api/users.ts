import request from '@/utils/request'

export const apiGetUserList = (data: any) =>
    request({
        url: '/api/user/list',
        method: 'post',
        data
    })
export const apiGetMemberList = (data: any) =>
    request({
        url: '/api/member/list',
        method: 'post',
        data
    })

export const apiGetUserListByRole = (params: any) =>
    request({
        url: '/api/user/listByRole',
        method: 'get',
        params
    })

export const apiDeleteUser = (params: any) =>
    request({
        url: '/api/user/delete',
        method: 'get',
        params
    })


export const apiAddUser = (data: any) =>
    request({
        url: '/api/user/add',
        method: 'post',
        data
    })

export const apiUpdateUser = (data: any) =>
    request({
        url: '/api/user/update',
        method: 'post',
        data
    })

export const apiUpdateUserDepartment = (data: any) =>
    request({
        url: '/api/user/batchUpdateDepartment',
        method: 'post',
        data
    })

export const apiUpdateUserStatus = (data: any) =>
    request({
        url: '/api/user/batchUpdateStatus',
        method: 'post',
        data
    })

export const apiGetListByMenuId = (params: any) =>
    request({
        url: '/api/user/listByMenu',
        method: 'get',
        params
    })

// 组织树
export const apiGetListByOrgTree = (params: any) =>
    request({
        url: '/api/user/listByOrgTree',
        method: 'get',
        params
    })

// 修改密码

export const apiChangePsw = (data: any) =>
    request({
        url: '/api/auth/update/pass',
        method: 'post',
        data
    })