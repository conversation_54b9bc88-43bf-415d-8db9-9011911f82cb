import request from '@/utils/request'

export const getAuthUserInfo = (params: any) =>
  request({
    url: '/api/auth/info',
    method: 'get',
    params
  })

export const login = (data: any) =>
    request({
        url: '/api/login',
        method: 'post',
        data
    })

// export const getUserInfo = (params: any) =>
//     request({
//         url: '/api/auth/info',
//         method: 'get',
//         params
//     })
