import request from '@/utils/request'
export const apiGetUnderholeToolsByWell = (data: any) =>
    request({
        url: '/api/underWellTool/list',
        method: 'post',
        data
    })
// export const apiGetDeviceHistoryListBy = (params: any) =>
//     request({
//         url: '/api/deviceHistory/listByCondition',
//         method: 'get',
//         params
//     })

// 
export const apiGetTransferOrderList = (data, { current, size }) =>
    request({
        url: `/api/transferOrder/page/${current}/${size}`,
        method: "post",
        data
    });
export const apiAddTransfer = (data: any) =>
    request({
        url: '/api/transferOrder/insert',
        method: 'post',
        data
    })
    
export const apiUpdateTransfer = (data: any) =>
    request({
        url: '/api/transferOrder/updateInfo',
        method: 'post',
        data
    })
    
export const apiGetTransferInfo = (data: any) =>
    request({
        url: '/api/transferOrder/info',
        method: 'post',
        data
    })
    
export const apiReceiveTransfer = (data: any) =>
    request({
        url: '/api/transferOrder/update/receiveStatus',
        method: 'post',
        data
    })    
    
export const apiReceiveTransferItem = (data: any) =>
    request({
        url: '/api/transferOrder/update/fieldReceiveStatus',
        method: 'post',
        data
    })
export const apiExportTransfer = (data: any) =>
    request({
        url: '/api/transferOrder/export',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })