import request from '@/utils/request'

export const apiGetLucidaTemplate = (data) =>
    request({
        url: `/api/rotary/getTemplate`,
        method: "post",
        data
    });
export const apiGetLucidaTree = (data) =>
    request({
        url: `/api/rotary/tree`,
        method: "post",
        data
    });

export const apiGetLucidaTreeByNode = (params) =>
    request({
        url: `/api/rotary/treeByAnyNode`,
        method: "get",
        params
    });

export const apiAssembleLucida = (data) =>
    request({
        url: `/api/rotary/assemble`,
        method: "post",
        data
    });

export const apiGetLucidaPartFuzzyList = (data, { current, size } = {current:1, size:20}) =>
    request({
        url: `/api/rotary/selectComponentsByPN/${current}/${size}`,
        method: "post",
        data
    });