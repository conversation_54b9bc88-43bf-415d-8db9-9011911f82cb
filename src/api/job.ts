import request from '@/utils/request'; // Assuming your request utility is here
import { AxiosResponse } from 'axios'; // Import AxiosResponse

// --- Generic API Response Wrapper (Matches Backend CommonResult) ---
export interface ApiResponse<T> {
    code: number;
    msg: string | null;
    data: T; // The actual data payload
}

// --- Interfaces ---

// Based on JobMapInfoVo.java
export interface JobMapInfo {
    jobId: number | null;
    jobNumber: string | null;
    wellId: number | string | null; // Assuming well_id can be string or number
    name: string | null; // well_number
    serveType: string | null;
    totalRun: number | null;
    failureRun: number | null;
    createTime: string | null; // Assuming ISO date string
    dateIn: string | null; // Assuming ISO date string
    dateOut: string | null; // Assuming ISO date string
    jobStatus: number | null; // 0: In Progress, 1: Completed
    originalDescription: string | null;
    position?: [number, number]; // Added by frontend after processing originalDescription
}

// Placeholder for pagination parameters
export interface PageParams {
    current: number;
    size: number;
}

// Placeholder based on JobInfoDetailVo used as RequestBody
export interface JobInfoDetailFilter {
    // Add fields from JobInfoDetailVo used for filtering
    // Example:
    // wellName?: string;
    // status?: number;
    [key: string]: any; // Allow arbitrary filter fields
}

// Placeholder for JobInfoDetailVo in the response list
export interface JobInfoDetail {
    // Define fields returned by listWellWithJobs_v2
    // Example:
    // id: number;
    // jobNumber: string;
    // wellName: string;
    // ... other fields
    [key: string]: any;
}

// UPDATED: Represents the structure within the 'data' field of ApiResponse for paginated results
// Matches backend IPage structure
export interface PaginatedResponseData<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
    // ... other pagination fields if any
}


// --- API Functions ---

/**
 * 获取用于地图展示的作业和井信息列表
 * Corresponds to: JobController.listJobMapInfo()
 * GET /api/job/listJobMapInfo
 */
export const getJobMapInfoList = (): Promise<AxiosResponse<ApiResponse<JobMapInfo[]>>> =>
    request({
        url: '/api/job/listJobMapInfo',
        method: 'get',
    });

/**
 * 分页查询作业列表 (带井信息) - V2
 * Corresponds to: JobController.listWellWithJobs_v2()
 * POST /api/integration/job/listWellWithJobs_v2/{current}/{size}
 * @param pageParams Pagination info { current, size }
 * @param filter Filter criteria (based on JobInfoDetailVo)
 */
export const listWellWithJobsV2 = (pageParams: PageParams, filter?: JobInfoDetailFilter): Promise<AxiosResponse<ApiResponse<PaginatedResponseData<JobInfoDetail>>>> =>
    request({
        url: `/api/integration/job/listWellWithJobs_v2/${pageParams.current}/${pageParams.size}`,
        method: 'post',
        data: filter || {}, // Send empty object if no filter provided
    });

// You can add other job-related API functions here later
