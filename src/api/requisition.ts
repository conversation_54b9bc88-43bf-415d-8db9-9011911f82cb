import request from '@/utils/request'

export const apiGetTransferList = (params: any) =>
    request({
        url: '/api/form/transfer/list',
        method: 'get',
        params
    })
export const apiGetTransferInfo = (params: any) =>
    request({
        url: '/api/form/transfer/info',
        method: 'get',
        params
    })
export const apiAddTransfer = (data: any) =>
    request({
        url: '/api/form/transfer/add',
        method: 'post',
        data
    })
export const apiUpdateTransfer = (data: any) =>
    request({
        url: '/api/form/transfer/update',
        method: 'post',
        data
    })
export const apiDeleteTransfer = (data: any) =>
    request({
        url: '/api/form/transfer/delete',
        method: 'post',
        data
    })
export const apiConfirmTransfer = (data: any) =>
    request({
        url: '/api/form/transfer/confirm',
        method: 'post',
        data
    })