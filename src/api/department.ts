import request from '@/utils/request'
export const apiAddDept = (data: any) =>
request({
    url: '/api/department/add',
    method: 'post',
    data
})
export const apiDeleteDept = (params: any) =>
request({
    url: '/api/department/delete',
    method: 'get',
    params
})
export const apiUpdateDept = (data: any) =>
request({
    url: '/api/department/update',
    method: 'post',
    data
})
export const apiGetDeptList = (params: any) =>
request({
    url: '/api/department/list',
    method: 'get',
    params
})
