import request from "@/utils/request";

export const apiGetMwdWorkOderInfo = (params: any) =>
    request({
        url: "/api/mwdWorkOrder/info",
        method: "get",
        params,
    });
export const apiSaveMwdWorkOderInfo = (data: any) =>
    request({
        url: "/api/mwdWorkOrder/save",
        method: "post",
        data,
    });
export const apiDeleteMwdWorkOder = (data: any) =>
    request({
        url: "/api/mwdWorkOrder/delete",
        method: "post",
        data,
    });
export const getMwdStatisticsInfo = (data: any) =>
    request({
        url: "/api/mwdWorkStatistics/getMwdStatisticsInfo",
        method: "post",
        data,
    });
export const getInventoryClassList = (params: any) =>
    request({
        url: "/api/mwdWorkOrder/inventoryClassList",
        method: "get",
        params,
    });

// 机加明细
export const apiGetProcessCraftList = (data: any, { current, size }: any) =>
    request({
        url: `/api/process/list/${current}/${size}`,
        method: "post",
        data,
    });
// export const  apiGetProccessCraftInfo=(data:any)=>
//     request({
//         url: `/api/process/info`,
//         method: "post",
//         data,
//     });

export const apiGetProccessCraftInfo = (params: any) =>
    request({
        url: '/api/process/info',
        method: "get",
        params,
    });

export const apiGetProcessOperateList = (data: any, { current, size }: any) =>
    request({
        url: `/api/process/operate/queryList/${current}/${size}`,
        method: "post",
        data,
    });
export const apiGetProcessOperateInfo = (params: any) =>
    request({
        url: "/api/process/operate/info",
        method: "get",
        params,
    });
export const apiAddProcessOperate = (data: any) =>
    request({
        url: `/api/process/operate/add`,
        method: "post",
        data,
    });
export const apiUpdateProcessOperate = (data: any) =>
    request({
        url: `/api/process/operate/update`,
        method: "post",
        data,
    });
export const apiDeleteProcessOperate = (data: any) =>
    request({
        url: `/api/process/operate/delete`,
        method: "post",
        data,
    });

//机加汇总
export const apiGetProcessOperateStatistics = (data: any) =>
    request({
        url: `/api/process/statistics`,
        method: "post",
        data,
    });

export const apiGetDeviceList = (params: any) =>
    request({
        url: "/api/process/deviceNumber/list",
        method: "get",
        params,
    });

export const apiGetProcessOverAll = (data: any) =>
    request({
        url: `/api/process/statisticsOverview`,
        method: "post",
        data,
    });
// 工具台账
export const apiGetToolStandingBookList = (data: any, { current, size }: any) =>
    request({
        url: `/api/toolStandingBook/list/${current}/${size}`,
        method: "post",
        data,
    });
export const apiDeleteToolStandingBook = (data: any) =>
    request({
        url: "/api/toolStandingBook/delete",
        method: "post",
        data,
    });
export const apiGetToolStandingBookInfo = (params: any) =>
    request({
        url: "/api/toolStandingBook/info",
        method: "get",
        params,
    });
export const apiSaveToolStandingBook = (data: any) =>
    request({
        url: "/api/toolStandingBook/save",
        method: "post",
        data,
    });


export const apiGetToolStandingBookStatisticsByMonth = (params: any) =>
    request({
        url: "/api/toolStandingBook/statisticsByMonth",
        method: "get",
        params,
    });
export const apiGetToolStandingBookStatisticsByHours = (params: any) =>
    request({
        url: "/api/toolStandingBook/statisticsByHours",
        method: "get",
        params,
    });