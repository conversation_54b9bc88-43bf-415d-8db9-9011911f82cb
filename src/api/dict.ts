import request from '@/utils/request'
export const apiGetDictList = (data: any) =>
    request({
        url: '/api/dataDictionary/list',
        method: 'post',
        data
    })
export const apiAddDict = (data: any) =>
    request({
        url: '/api/dataDictionary/add',
        method: 'post',
        data
    })
export const apiUpdateDict = (data: any) =>
    request({
        url: '/api/dataDictionary/update',
        method: 'post',
        data
    })
export const apiDeleteDict = (data: any) =>
    request({
        url: '/api/dataDictionary/delete',
        method: 'post',
        data
    })
export const apiGetDictRelationList = (data: any) =>
    request({
        url: '/api/dataDictionary/relationList',
        method: 'post',
        data
    })