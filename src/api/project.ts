import request from '@/utils/request'
export const apiGetProjectList = (data:any, { current, size }:any) =>
    request({
        url: `/api/project/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetProjectWriteableList = (data:any, { current, size }:any) =>
    request({
        url: `/api/project/listWriteable/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetProjectInfo = (params: any) =>
    request({
        url: '/api/project/info',
        method: 'get',
        params
    })
export const apiAddProject = (data: any) =>
    request({
        url: '/api/project/add',
        method: 'post',
        data
    })
export const apiUpdateProject = (data: any) =>
    request({
        url: '/api/project/update',
        method: 'post',
        data
    })
export const apiDeleteProject = (data: any) =>
    request({
        url: '/api/project/delete',
        method: 'post',
        data
    })
export const apiFinishProject = (data: any) =>
    request({
        url: '/api/project/finish',
        method: 'post',
        data
    })