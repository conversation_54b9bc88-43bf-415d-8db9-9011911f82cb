import request from '@/utils/request'

export const apiGetDemandList = (data, { current, size }) =>
    request({
        url: `/api/demandOrder/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiGetDemandInfo = (data) =>
    request({
        url: `/api/demandOrder/info`,
        method: "post",
        data
    });
export const apiAddDemand = (data) =>
    request({
        url: `/api/demandOrder/insert`,
        method: "post",
        data
    });
export const apiUpdateDemand = (data) =>
    request({
        url: `/api/demandOrder/update`,
        method: "post",
        data
    });
export const apiReceiveDemand = (data) =>
    request({
        url: `/api/demandOrder/updateStatus`,
        method: "post",
        data
    });

export const apiExportDemand = (data) =>
    request({
        url: '/api/demandOrder/exportDetail',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })