import request from '@/utils/request'
// 模板
export const apiGetTaskTemplateList = (data: any) =>
    request({
        url: '/api/produceTemplate/list',
        method: 'post',
        data
    })
export const apiGetTaskTemplateInfo = (data: any) =>
    request({
        url: '/api/produceTemplate/info',
        method: 'post',
        data
    })
export const apiGetTaskTemplateChildrenList = (data: any) =>
    request({
        url: '/api/produceTemplate/childList',
        method: 'post',
        data
    })
export const apiAddTaskTemplate = (data: any) =>
    request({
        url: '/api/produceTemplate/add',
        method: 'post',
        data
    })
export const apiUpdateTaskTemplate = (data: any) =>
    request({
        url: '/api/produceTemplate/update',
        method: 'post',
        data
    })
export const apiDeleteTaskTemplate = (data: any) =>
    request({
        url: '/api/produceTemplate/delete',
        method: 'post',
        data
    })
//生产管理
export const apiGetProductionTaskInfo = (data: any) =>
    request({
        url: '/api/produce/info',
        method: 'post',
        data
    })
export const apiGetProductionTaskFuzzyList = (data: any) =>
    request({
        url: '/api/produce/fuzzyList',
        method: 'post',
        data
    })
export const apiGetProductionTaskListByCondition = (data: any, { current, size }: any) =>
    request({
        url: `/api/produce/list/${current}/${size}`,
        method: 'post',
        data
    })
export const apiAddProductionTask = (data: any) =>
    request({
        url: '/api/produce/add',
        method: 'post',
        data
    })
export const apiBatchAddProductionTask = (data: any) =>
    request({
        url: '/api/produce/addBatch',
        method: 'post',
        data
    })
export const apiUpdateProductionTask = (data: any) =>
    request({
        url: '/api/produce/update',
        method: 'post',
        data
    })
export const apiDeleteProductionTask = (data: any) =>
    request({
        url: '/api/produce/delete',
        method: 'post',
        data
    })
// 日志
export const apiGetCommentList = (data: any) =>
    request({
        url: `/api/produce/comment/list`,
        method: 'post',
        data
    })
export const apiAddComment = (data: any) =>
    request({
        url: `/api/produce/comment/add`,
        method: 'post',
        data
    })
// 附件
export const apiGetFileList = (data: any) =>
    request({
        url: `/api/produce/file/list`,
        method: 'post',
        data
    })
export const apiDeleteFile = (data: any) =>
    request({
        url: `/api/produce/file/delete`,
        method: 'post',
        data
    })
export const apiAddFile = (data: any) =>
    request({
        url: `/api/produce/file/upload`,
        method: 'post',
        data,
        timeout: 500000
    })
// 人员分配
export const apiAddProduceUser = (data: any) =>
    request({
        url: `/api/producerUser/add`,
        method: 'post',
        data
    })
export const apiDeleteProduceUser = (data: any) =>
    request({
        url: `/api/producerUser/delete`,
        method: 'post',
        data
    })
export const apiGetProduceUser = (params: any) =>
    request({
        url: `/api/producerUser/query`,
        method: 'get',
        params
    })
export const apiUpdateProduceUser = (data: any) =>
    request({
        url: `/api/producerUser/hours/update`,
        method: 'post',
        data
    })
// 单据列表
export const apiGetReceiptList = (data: any) =>
    request({
        url: `/api/produce/receipt/list`,
        method: 'post',
        data
    })
export const apiGetMyTask = ({ current, size }: { current: number, size: number }, data: any,) =>
    request({
        url: `/api/produce/myTask/${current}/${size}`,
        method: 'post',
        data
    })
export const apiUpdateMyTaskStatus = (data: any,) =>
    request({
        url: `/api/produce/updateStatus`,
        method: 'post',
        data
    })
export const apiGetProjectInfo = (params: any,) =>
    request({
        url: `/api/produce/projectInfo`,
        method: 'get',
        params
    })
// 工艺流程
export const apiGetProcessList = (data: any, { current, size }: any) =>
    request({
        url: `/api/process/list/${current}/${size}`,
        method: 'post',
        data
    })
export const apiGetProcessFuzzyList = (data: any) =>
    request({
        url: `/api/process/fuzzyList`,
        method: 'post',
        data
    })
export const apiGetProcessInfo = (params: any) =>
    request({
        url: `/api/process/info`,
        method: 'get',
        params
    })
export const apiAddProcess = (data: any) =>
    request({
        url: `/api/process/add`,
        method: 'post',
        data
    })
export const apiUpdateProcess = (data: any) =>
    request({
        url: `/api/process/update`,
        method: 'post',
        data
    })
export const apiDeleteProcess = (data: any) =>
    request({
        url: `/api/process/delete`,
        method: 'post',
        data
    })
export const apiGetProcessAllocatedUserInfo = (params: any) =>
    request({
        url: `/api/process/flow/list`,
        method: 'get',
        params
    })
export const apiGetProcessFlowList = (params: any) =>
    request({
        url: `/api/process/flow/list`,
        method: 'get',
        params
    })
export const apiAddProcessFlow = (data: any) =>
    request({
        url: `/api/process/flow/add`,
        method: 'post',
        data
    })
export const apiUpdateProcessFlow = (data: any) =>
    request({
        url: `/api/process/flow/update`,
        method: 'post',
        data
    })
export const apiDeleteProcessFlow = (data: any) =>
    request({
        url: `/api/process/flow/delete`,
        method: 'post',
        data
    })
export const apiGetFlowDetailList = (params: any) =>
    request({
        url: `/api/process/operate/list`,
        method: 'get',
        params
    })
export const apiAddBatchAllocateUser = (data: any) =>
    request({
        url: `/api/process/operate/addBatch`,
        method: 'post',
        data
    })
export const apiUpdateAllocateUser = (data: any) =>
    request({
        url: `/api/process/operate/update`,
        method: 'post',
        data
    })
export const apiDeleteAllocateUser = (data: any) =>
    request({
        url: `/api/process/operate/delete`,
        method: 'post',
        data
    })