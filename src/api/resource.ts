import request from '@/utils/request'
export const addResource = (data: any) =>
request({
    url: '/api/resource/add',
    method: 'post',
    data
})
export const deleteResource = (params: any) =>
request({
    url: '/api/resource/delete',
    method: 'get',
    params
})
export const updateResource = (data: any) =>
request({
    url: '/api/resource/update',
    method: 'post',
    data
})
export const getResourceList = (params: any) =>
request({
    url: '/api/resource/list',
    method: 'get',
    params
})
export const getCategoryList = (params: any) =>
request({
    url: '/api/resource/category/list',
    method: 'get',
    params
})
export const getResourceListByMenu = (params: any) =>
    request({
        url: '/api/resource/listByMenu',
        method: 'get',
        params
    })
export const getResourceListWithCategory = (params: any) =>
request({
    url: '/api/resource/listWithCategory',
    method: 'get',
    params
})
export const getResourceTree = (params: any) =>
    request({
        url: '/api/resource/tree',
        method: 'get',
        params
    })
// export const getResourceListByRole = (params: any) =>
// request({
//     url: '/api/resource/listByRole',
//     method: 'get',
//     params
// })
