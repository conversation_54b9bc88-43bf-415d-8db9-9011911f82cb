import request from "@/utils/request";

export const apiGetCurrentStockList = (data: any, { current, size }: any) =>
    request({
        url: `/api/currentStock/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiGetMwdWorkOrderList = (data: any, { current, size }: any) =>
    request({
        url: `/api/workOrderMwd/list/${current}/${size}`,
        method: "post",
        data,
        timeout: 10000
    });
export const apiGetMwdWorkOrderInfo = (params: any) =>
    request({
        url: `/api/workOrderMwd/info`,
        method: "get",
        params,
        timeout: 20000
    });
export const apiAddMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/add`,
        method: "post",
        data
    });
export const apiBatchAddMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/add/batch`,
        method: "post",
        data
    });
export const apiUpdateMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/update`,
        method: "post",
        data
    });
export const apiUpdateMwdWorkOrderShareStatus = (data: any) =>
    request({
        url: `/api/workOrderMwd/update/shareStatus`,
        method: "post",
        data
    });
export const apiMwdAssemble = (data: any) =>
    request({
        url: `/api/workOrderMwd/assemble`,
        method: "post",
        data
    });
export const apiMwdComponentRepair = (data: any) =>
    request({
        url: `/api/workOrderMwd/component/repair`,
        method: "post",
        data
    });
export const apiFinishMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/finish`,
        method: "post",
        data
    });

export const apiGetDeviceTemplateList = (data: any) =>
    request({
        url: `/api/device/template/list`,
        method: "post",
        data
    });

//仪器使用统计
export const apiGetDeviceUsage = (data: any) =>
    request({
        url: `/api/dailyReport/usage`,
        method: "post",
        data
    })

export const apiGetMwdWorkOrderStatistics = (data: any) =>
    request({
        url: `/api/workOrderMwd/statistics`,
        method: "post",
        data
    })

// 新建工单, 序列号校验
export const apiCheckSerialNumber = (data: any) =>
    request({
        url: `/api/device/serialNumber/check`,
        method: "post",
        data
    })
// 模糊查询序列号
export const apiGetFuzzySerialNumberList = (data: any) =>
    request({
        url: `/api/device/getSerialNumberList`,
        method: "post",
        data
    })
// 导出维修报告
export const apiGetMwdMaintainReport = ({ mwdId }: any) =>
    request({
        url: `/api/workOrderMwd/repair/report/${mwdId}`,
        method: "get",
        responseType: 'blob'
    })
// 导出工单列表
export const apiExportMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/order/export`,
        method: "post",
        data,
        responseType: 'blob',
        timeout: 500000
    })
// 工时统计
export const apiGetMwdLabourHour = (data: any) =>
    request({
        url: `/api/workOrderMwd/labourHour`,
        method: "post",
        data
    })

// 工时统计
export const apiGetMwdWorkloadMonth = (data: any) =>
    request({
        url: `/api/workOrderMwd/mwdWorkAmount`,
        method: "post",
        data
    })

export const apiGetMwdWorkloadWeek = (data:any) =>
    request({
        url: '/api/workOrderMwd/mwdWorkAmount/week',
        method: 'post',
        data,
    })
// 删除工单
export const apiDeleteMwdWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderMwd/delete`,
        method: "post",
        data
    })

export const apiCreateMwdOrderByOutbound = (data: any) =>
    request({
        url: '/api/outboundOrder/batchInsertMwdOrder',
        method: 'post',
        data
    });


export const apiGetMwdWorkOrderOwnerList = (params: any) =>
    request({
        url: `/api/workOrderMwd/owner/select`,
        method: "get",
        params,
    });

// 旋导维保记录导出

export const apiExportRssMaintainHistory = (data: any) =>
    request({
        url: `/api/device/rssMaintenance/export`,
        method: "post",
        data,
        responseType: 'blob',
        timeout: 500000
    })