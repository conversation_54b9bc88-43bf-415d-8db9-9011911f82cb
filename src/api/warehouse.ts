import request from '@/utils/request'
export const apiGetDeviceTypes = (params: any) =>
    request({
        url: '/api/current_stock/inventoryClassList',
        method: 'get',
        params
    })

//current_stock/list
export const apiGetDeviceList = (data: any) =>
    request({
        url: '/api/current_stock/list',
        method: 'post',
        data
    })

export const apiGetDeviceFuzzyList = (data: any) =>
    request({
        url: '/api/current_stock/fuzzyList',
        method: 'post',
        data
    })

export const apiGetDeviceInfo = (params: any) =>
    request({
        url: '/api/current_stock/info',
        method: 'get',
        params
    })

export const apiAddDevice = (data: any) =>
    request({
        url: '/api/current_stock/add',
        method: 'post',
        data
    })
export const apiUpdateDevice = (data: any) =>
    request({
        url: '/api/current_stock/update',
        method: 'post',
        data
    })
export const apiDeleteDevice = (data: any) =>
    request({
        url: '/api/current_stock/delete',
        method: 'post',
        data
    })
// export const apiGetDeviceFuzzyList = (data: any) =>
//     request({
//         url: '/api/current_stock/fuzzyList',
//         method: 'post', data
//     })

export const apiGetInvNameFuzzyList = (data: any) =>
    request({
        url: '/api/inventory/invName',
        method: 'post',
        data
    })

export const apiGetWarehouseToolList = (data: any, { current, size }: any) =>
    request({
        url: `/api/warehouse/list/${current}/${size}`,
        method: "post",
        data,
    });
export const apiAddWarehouseToolList = (data: any) =>
    request({
        url: '/api/warehouse/add',
        method: 'post',
        data
    });
export const apiUpdateWarehouseToolList = (data: any) =>
    request({
        url: '/api/warehouse/update',
        method: 'post',
        data
    });
export const apiDeleteWarehouseToolList = (data: any) =>
    request({
        url: '/api/warehouse/delete',
        method: 'post',
        data
    });

// new 
export const apiGetWarehouseDeviceList = (data: any, { current, size }: any) =>
    request({
        url: `/api/device/listByType/${current}/${size}`,
        method: "post",
        data,
    });
export const apiAddWarehouseDeviceList = (data: any) =>
    request({
        url: '/api/device/insert',
        method: 'post',
        data
    });
export const apiUpdateWarehouseDeviceList = (data: any) =>
    request({
        url: '/api/device/update',
        method: 'post',
        data
    });
export const apiBatchUpdateWarehouseDeviceList = (data: any) =>
    request({
        url: '/api/device/batchUpdate',
        method: 'post',
        data
    });
export const apiGetWarehouseDeviceInfo = (data: any) =>
    request({
        url: '/api/device/infoNew',
        method: 'post',
        data
    });

export const apiGetWarehouseDeviceBatchInfo = (data: any) =>
    request({
        url: '/api/device/info/batch',
        method: 'post',
        data
    });
export const apiGetWarehouseDeviceStats = (data: any) =>
    request({
        url: '/api/device/statisticsByType',
        method: 'post',
        data
    });

export const apiGetWarehouseDeviceStatsCondition = (data: any) =>
    request({
        url: '/api/device/getValueList',
        method: 'post',
        data
    });
export const apiExportWarehouseDevice = (data: any) =>
    request({
        url: '/api/device/export',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    });
export const apiCheckSerialNumberList = (data: any) =>
    request({
        url: `/api/device/serialNumber/listCheck`,
        method: "post",
        data
    })
export const apiGetLoadOutListTemplate = (params: any) =>
    request({
        url: '/api/mwdLoadOut/getTemplate',
        method: 'get',
        params
    });
export const apiKitIn = (data: any) =>
    request({
        url: '/api/mwdLoadOut/loadIn',
        method: 'post',
        data
    });
export const apiKitOut = (data: any) =>
    request({
        url: '/api/mwdLoadOut/loadOut',
        method: 'post',
        data
    });
export const apiKitHistory = (data: any) =>
    request({
        url: '/api/mwdLoadOut/serviceHistory',
        method: 'post',
        data
    });
export const apiUpdateLoadOut = (data: any) =>
    request({
        url: '/api/mwdLoadOut/update',
        method: 'post',
        data
    });
export const apiGetLoadOutInfo = (data: any) =>
    request({
        url: '/api/mwdLoadOut/info',
        method: 'post',
        data
    });
export const apiExportLoadOutInfo = (data: any) => 
    request({
        url: `/api/mwdLoadOut/exportDetail`,
        method: "post",
        data,
        responseType: 'blob'
    })
export const apiExportEvaluation = (data: any) => 
    request({
        url: `/api/mwdLoadOut/exportRiskAssessment`,
        method: "post",
        data,
        responseType: 'blob'
    })

// 领料

export const apiGetFreeMaterialList = (data: any, { current, size }: any) =>
    request({
        url: `/api/outboundOrder/queryList/${current}/${size}`,
        method: "post",
        data,
    });

export const apiGetOutboundList = (data: any, { current, size }: any) =>
    request({
        url: `/api/outboundOrder/list/${current}/${size}`,
        method: "post",
        data,
    });
export const apiCreateOutboundOrder = (data: any) =>
    request({
        url: '/api/outboundOrder/insert',
        method: 'post',
        data
    });

export const apiCheckOutboundOrder = (data: any) =>
    request({
        url: '/api/outboundOrder/updateStatus',
        method: 'post',
        data
    });

export const apiGetStockStats = (params: any) =>
    request({
        url: '/api/device/inventory',
        method: 'get',
        params
    })

// 旋导出入库
export const apiGetLucidaLoadOutListTemplate = (params: any) =>
    request({
        url: '/api/mwdLoadOut/getTemplate',
        method: 'get',
        params
    });
export const apiLucidaLoadOutHistory = (data: any) =>
    request({
        url: '/api/mwdLoadOut/serviceHistory',
        method: 'post',
        data
    });
export const apiUpdateLucidaLoadOut = (data: any) =>
    request({
        url: '/api/loadout/updateLucida',
        method: 'post',
        data
    });
export const apiAddLucidaLoadOut = (data: any) =>
    request({
        url: `/api/loadout/createLucida`,
        method: 'post',
        data
    });
export const apiGetLucidaLoadOutInfo = (params: any) =>
    request({
        url: '/api/loadout/getLucidaDetail',
        method: 'get',
        params
    });
export const apiExportLucidaLoadOutInfo = (data: any) => 
    request({
        url: `/api/loadout/exportLucidaExcel`,
        method: "post",
        data,
        responseType: 'blob'
    })
export const apiExportLucidaEvaluation = (data: any) => 
    request({
        url: `/api/mwdLoadOut/exportRiskAssessment`,
        method: "post",
        data,
        responseType: 'blob'
    })

export const apiLucidaHistory = (params: any) =>
    request({
        url: '/api/loadout/getLucidaList',
        method: 'get',
        params
    });