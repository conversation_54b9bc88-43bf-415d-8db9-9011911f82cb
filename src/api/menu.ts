import request from '@/utils/request'
export const addMenu = (data: any) =>
request({
    url: '/api/menu/add',
    method: 'post',
    data
})
export const deleteMenu = (params: any) =>
request({
    url: '/api/menu/delete',
    method: 'get',
    params
})
export const getMenuInfo = (params: any) =>
    request({
        url: '/api/menu/info',
        method: 'get',
        params
})
export const updateMenu = (data: any) =>
request({
    url: '/api/menu/update',
    method: 'post',
    data
})
export const getMenuTree = (data: any) =>
request({
    url: '/api/menu/tree',
    method: 'post',
    data
})

export const getTreeByRole = (params: any) =>
request({
    url: '/api/menu/getTreeByRole',
    method: 'get',
    params
})

export const getDefaultMenuIdListByRole = (params: any) =>
request({
    url: '/api/menu/queryIdsByRole',
    method: 'get',
    params
})
export const getTreeByUser = (params: any) =>
request({
    url: '/api/menu/getTreeByUser',
    method: 'get',
    params
})
