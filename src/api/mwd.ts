import request from "@/utils/request";

export const apiJobList = (params: any) =>
    request({
        url: "/api/job/list",
        method: "get",
        params,
    });
export const apiGetJobList = (data: any, { current, size }: any) =>
    request({
        url: `/api/job/listWellWithJobs_v2/${current}/${size}`,
        method: "post",
        data
    });
// export const apiGetJobList = (params: any) =>
//     request({
//         url: "/api/job/listWellWithJobs_v2",
//         method: "get",
//         params,
//     });
export const apiJobInfo = (params: any) =>
    request({
        url: "/api/job/info",
        method: "get",
        params,
    });
export const apiJobListByWell = (params: any) =>
    request({
        url: "/api/job/listByWell",
        method: "get",
        params,
    });
export const apiAddJob = (data: any) =>
    request({
        url: "/api/job/add",
        method: "post",
        data,
    });
export const apiUpdateJobStatus = (data: any) =>
    request({
        url: "/api/job/updateJobStatus",
        method: "post",
        data,
    });

export const apiDeleteJob = (params: any) =>
    request({
        url: "/api/job/delete",
        method: "post",
        params,
    });

export const apiAddJobOperator = (data: any) =>
    request({
        url: "/api/job/operator/add",
        method: "post",
        data,
    });

export const apiBatchAddJobOperator = (data: any) =>
    request({
        url: "/api/job/operator/batchAdd",
        method: "post",
        data,
    });

export const apiGetJobOperator = (params: any) =>
    request({
        url: "/api/job/operator/list",
        method: "get",
        params,
    });

export const apiGetDailyReportInfo = (params: any) =>
    request({
        url: "/api/dailyReport/info",
        method: "get",
        params,
        timeout: 50000
    });

export const apiGetDailyReportBaseinfo = (params: any) =>
    request({
        url: "/api/dailyReport/baseInfoList",
        method: "get",
        params,
    });

export const apiUpdateDailyReport = (data: any) =>
    request({
        url: "/api/dailyReport/update",
        method: "post",
        data,
    });

export const apiAddDailyReport = (data: any) =>
    request({
        url: "/api/dailyReport/add",
        method: "post",
        data,
    });
export const apiDeleteDailyReport = (data: any) =>
    request({
        url: "/api/dailyReport/delete",
        method: "post",
        data,
    });

export const getToolRunHours = (params: any) =>
    request({
        url: "/api/dailyReport/toolRunHours",
        method: "get",
        params,
    });
export const getTooData = (data: any) =>
    request({
        url: "/api/dailyReport/toolData",
        method: "post",
        data,
    });
export const getToolRunToolList = (params: any) =>
    request({
        url: "/api/dailyReport/runToolList",
        method: "get",
        params,
    });
export const getProjectData = (data: any) =>
    request({
        url: "/api/dailyReport/projectData",
        method: "post",
        data,
    });
export const getBaseData = (data: any) =>
    request({
        url: "/api/dailyReport/baseData",
        method: "post",
        data,
    });

export const dailyReportRefresh = (data: any) =>
    request({
        url: "/api/dailyReport/dataRefresh",
        method: "post",
        data,
    });

export const apiMwdOrderList = ({ current, size }: any, data: any) =>
    request({
        url: `/api/mwdWorkOrder/list/${current}/${size}`,
        method: "post",
        data
    });
// export const apiMwdOrderListBy = ({ current, size }: any, data: any) =>
//     request({
//         url: `/api/mwdWorkOrder/listByCondition/{current}/{size}`,
//         method: "post",
//         data,
//     });

export const apiMwdOrderDelete = (data: any) =>
    request({
        url: "/api//mwdWorkOrder/delete",
        method: "post",
        data,
    });

export const apiMwdStandingBook = (params: any) =>
    request({
        url: "/api/mwdWorkOrder/info",
        method: "get",
        params,
    });
// 完井报告
export const apiGetCompltedWellReportTemplate = ({reportType, jobId}:any) =>
    request({
        url: `/api/dailyReport/complete/download/${reportType}/${jobId}`,
        method: "get",
        responseType: 'blob',
        timeout: 500000
    });
export const apiUploadCompltedWellReport = (data:any) =>
    request({
        url: `/api/dailyReport/complete/upload`,
        method: "post",
        data,
        timeout: 500000
    });

// 日报导出Excel
export const apiExportDailyReportExcel = (data:any) =>
    request({
        url: `/api/dailyReport/export`,
        method: "post",
        data,
        responseType: 'blob',
        timeout: 500000
    });
export const apiUploadCompleteWellFile = (data:any, onUploadProgress) =>
    request({
        url: '/api/massFile/upload_v2',
        method: 'post',
        data,
        timeout: 1000000,
        onUploadProgress
    })

export const apiGetCompleteFileList = ({ current, size }:any, data:any) =>
    request({
        url: `/api/massFile/listByJob/${current}/${size}`,
        method: "post",
        data
    });

export const apiDeleteCompleteWellFile = (data:any) =>
    request({
        url: '/api/massFile/delete',
        method: 'post',
        data,
    })

export const apiGetYearFailureStats = (data:any) =>
    request({
        url: '/api/workOrderMwd/statistics/yearly',
        method: 'post',
        data,
    })
export const apiExportRepairExcel = (data:any) =>
    request({
        url: '/api/repair/export',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })
export const apiGetSNFuzzyList = (data:any) =>
    request({
        url: '/api/workOrderMwd/getSerialNumber',
        method: 'post',
        data,
    })

export const apiUpdateFailureShop = (data:any) =>
    request({
        url: '/api/failureShopUpdate/update',
        method: 'post',
        data,
    })
export const apiGetFailureReportList = (data:any, { current, size }:any) =>
    request({
        url: `/api/failureShopUpdate/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiUpdateFailedComponent = (data:any) => {
    request({
        url: '/api/failureShopUpdate/update/component',
        method: 'post',
        data,
    })
}
export const apiUploadFailureShopFile = (data:any) => 
    request({
        url: '/api/massFile/upload/failureShop',
        method: 'post',
        data,
    })

export const apiDeleteFailureShopFile = (data:any) => 
    request({
        url: '/api/massFile/delete/failureFile',
        method: 'post',
        data,
    })

export const apiGetFailureShopFile = (data:any, { current, size }:any) => 
    request({
        url: `/api/massFile/listByReport/${current}/${size}`,
        method: 'post',
        data,
    })

export const apiUploadMwdMassFile = (data:any) =>
    request({
        url: `/api/massFile/upload/mwdWorkOrder`,
        method: "post",
        data,
        timeout: 500000
    });

export const apiGetMwdMassFileList = (data:any, { current, size }:any) =>
    request({
        url: `/api/massFile/listByMwdId/${current}/${size}`,
        method: "post",
        data
    });
export const apiDeleteMwdMassFile = (data:any) =>
    request({
        url: '/api/massFile/delete/mwdWorkOrder',
        method: 'post',
        data,
    })

export const apiGetMwdRiskInfo = (data:any) =>
    request({
        url: '/api/device/riskType',
        method: 'post',
        data,
    })

export const apiGetMwdRunDeviceInfo = (data:any) =>
    request({
        url: '/api/dailyReport/run/device/info',
        method: 'post',
        data,
    })
export const apiUpdateMwdRunDeviceSummary = (data:any) =>
    request({
        url: '/api/dailyReport/run/summary/save',
        method: 'post',
        data,
    })
export const apiGetMwdRepairFailedComponentStats = (data:any) =>
    request({
        url: '/api/workOrderMwd/component/repair/statistics',
        method: 'post',
        data,
    })
export const apiGetMwdFailureComponentStats = (data:any) =>
    request({
        url: '/api/workOrderMwd/component/failed/statistics',
        method: 'post',
        data,
    })
export const apiGetTestList = (data:any, { current, size }:any) =>
    request({
        url: `/api/component/test/list/${current}/${size}`,
        method: 'post',
        data,
    })
export const apiAddTest = (data:any) =>
    request({
        url: '/api/component/test/add',
        method: 'post',
        data,
    })
export const apiUpdateTestInfo = (data:any) =>
    request({
        url: '/api/component/test/update',
        method: 'post',
        data,
    })
export const apiExportTest = (data:any) =>
    request({
        url: '/api/component/test/export',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })
export const apiGetTestServiceHistoryList = (data:any) =>
    request({
        url: '/api/component/test/history/list',
        method: 'post',
        data,
    })
export const apiGetTestFuzzyList = (data:any) =>
    request({
        url: '/api/component/test/sn/fuzzyList',
        method: 'post',
        data,
    })
export const apiGetMwdDeviceStats = (data:any) =>
    request({
        url: '/api/workOrderMwd/history/detail',
        method: 'post',
        data,
    })
// 模糊查询bom
export const apiGetBomFuzzylist = (data:any) =>
    request({
        url: '/api/bom/list',
        method: 'post',
        data,
    })
// bom内容
export const apiGetBomDetail = (data:any) =>
    request({
        url: '/api/bom/item/list',
        method: 'post',
        data,
    })

// 工单文件
export const apiGetMaintainFileList = (data:any, { current, size }:any) =>
    request({
        url: `/api/massFile/mwdFileList/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetMaintainFileTypeList = (params:any) =>
    request({
        url: '/api/massFile/typeList?massFileType=MWD_WORK_ORDER_FILE',
        method: 'get',
        params,
    })
export const apiDownloadMaintainFile = (data:any) =>
    request({
        url: '/api/massFile/download',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })
export const apiPreviewFile = (data:any) =>
    request({
        url: '/api/massFile/preview',
        method: 'post',
        data,
    })
// 合格证
export const apiGetCertInfo = (data:any) =>
    request({
        url: '/api/cert/select',
        method: 'post',
        data,
    })
export const apiUpdateCertInfo = (data:any) =>
    request({
        url: '/api/cert/update',
        method: 'post',
        data,
    })
export const apiExportCert = (data:any) =>
    request({
        url: '/api/cert/exportCert',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })
// 核心部件出厂列表
export const apiGetCoreOutList = (data:any, { current, size }:any) =>
    request({
        url: `/api/component/out/list/${current}/${size}`,
        method: "post",
        data
    });

// 仪器列表
export const apiGetDeviceStatisticList = (data:any, { current, size }:any) =>
    request({
        url: `/api/device/statistic/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiValidateMwdAssemble = (data: any) => 
    request({
        url: '/api/workOrderMwd/assembleCheck',
        method: 'post',
        data,
    })

// 根据条件查询component表
export const apiGetComponentItem = (data: any) => 
    request({
        url: '/api/component/getByQuery',
        method: 'post',
        data,
    })

export const apiAddComponent = (data: any) => 
    request({
        url: '/api/component/add',
        method: 'post',
        data,
    })
export const apiUpdateComponent = (data: any) => 
    request({
        url: '/api/component/update',
        method: 'post',
        data,
    })
export const apiGetTempTypeList = (data: any) => 
    request({
        url: '/api/component/tempType',
        method: 'post',
        data,
    })
export const apiGetComponentFuzzyList = (data: any) => 
    request({
        url: '/api/component/infoList',
        method: 'post',
        data,
    })