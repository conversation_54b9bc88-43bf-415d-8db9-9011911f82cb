import request from '@/utils/request'

export const apiGetEaWorkOrderList = (data, { current, size }) =>
    request({
        url: `/api/workOrderEa/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiAddEaWorkOrder = (data) =>
    request({
        url: `/api/workOrderEa/add`,
        method: "post",
        data
    });

export const apiGetEaOrderInfo = (params) =>
    request({
        url: `/api/workOrderEa/info`,
        method: "get",
        params
    });
export const apiGetPartTemplate = (data) =>
    request({
        url: `/api/inventory/template`,
        method: "post",
        data
    });

export const apiDeleteEaWorkOrder = (data) =>
    request({
        url: `/api/workOrderEa/delete`,
        method: "post",
        data
    });

export const apiAssembleEa = (data) =>
    request({
        url: `/api/workOrderEa/assemble`,
        method: "post",
        data
    });

export const apiUpdateEaWorkOrder = (data) =>
    request({
        url: `/api/workOrderEa/update`,
        method: "post",
        data
    });

export const apiFinishEaWorkOrder = (data) =>
    request({
        url: `/api/workOrderEa/finish`,
        method: "post",
        data
    });

export const apiGetEaOrderTree = (data) =>
    request({
        url: `/api/workOrderEa/getEaTree`,
        method: "post",
        data
    });