import request from '@/utils/request'

export const apiGetRmaWorkOrderList = (data, { current, size }) =>
    request({
        url: `/api/workOrderRma/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetRmaWorkOrderInfo = (params) =>
    request({
        url: `/api/workOrderRma/info`,
        method: "get",
        params
    });
export const apiAddRmaWorkOrder = (data) =>
    request({
        url: `/api/workOrderRma/add`,
        method: "post",
        data
    });
export const apiDeleteRmaWorkOrder = (data) =>
    request({
        url: `/api/workOrderRma/delete`,
        method: "post",
        data
    });
export const apiUpdateRmaWorkOrder = (data) =>
    request({
        url: `/api/workOrderRma/update`,
        method: "post",
        data
    });
export const apiFinishRmaWorkOrder = (data) =>
    request({
        url: `/api/workOrderRma/finish`,
        method: "post",
        data
    });
// rma返修单
export const apiGetRmaRepairList = (data, { current, size }) =>
    request({
        url: `/api/repairRma/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiAddRmaRepair = (data) =>
    request({
        url: `/api/repairRma/add`,
        method: "post",
        data
    });
export const apiDeleteRmaRepair = (data) =>
    request({
        url: `/api/repairRma/deleteRepair`,
        method: "post",
        data
    });
export const apiUpdateRmaRepair = (data) =>
    request({
        url: `/api/repairRma/update`,
        method: "post",
        data
    });
export const apiGetRmaRepairInfo = (data) =>
    request({
        url: `/api/repairRma/info`,
        method: "post",
        data
    });
export const apiReceiveRmaRepair = (data) =>
    request({
        url: `/api/repairRma/receive`,
        method: "post",
        data
    });
export const apiGetRmaRepairDataByCore = (data) =>
    request({
        url: `/api/repairRma/selectFromMwd`,
        method: "post",
        data
    });
export const apiCreateRmaWorkOrderByRepair = (params) =>
    request({
        url: `/api/repairRma/addFromRepair`,
        method: "get",
        params
    });
    
export const apiGetRmaWorkOrderDptNameList = (data) =>
    request({
        url: `/api/workOrderRma/getDepartmentName`,
        method: "post",
        data
    });