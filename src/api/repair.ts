import request from '@/utils/request'
// export const apiGetDictList = (params: any) =>
// request({
//     url: '/api/dataDictionary/list',
//     method: 'get',
//     params
// })

export const apiGetRepairMWDList = (data: any) =>
    request({
        url: '/api/form/repair/mwd/list',
        method: 'post',
        data
    })
export const apiGetRepairUnderList = (data: any) =>
    request({
        url: '/api/form/repair/under/list',
        method: 'post',
        data
    })
export const apiGetRepairDetail = (data: any) =>
    request({
        url: '/api/form/repair/mwd/info',
        method: 'post',
        data
    })
export const apiUpdateRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/mwd/update',
        method: 'post',
        data
    })
export const apiAddRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/mwd/add',
        method: 'post',
        data
    })
export const apiConfirmRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/confirm',
        method: 'post',
        data
    })
export const apiDeleteRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/mwd/delete',
        method: 'post',
        data
    })



export const apiGetUnderRepairOrderInfo = (data: any) =>
    request({
        url: '/api/form/repair/under/info',
        method: 'post',
        data
    })
export const apiUpdateUnderRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/under/update',
        method: 'post',
        data
    })
export const apiAddUnderRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/under/add',
        method: 'post',
        data
    })
export const apiDeleteUnderRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/under/delete',
        method: 'post',
        data
    })
export const apiConfirmUnderRepairOrder = (data: any) =>
    request({
        url: '/api/form/repair/confirm',
        method: 'post',
        data
    })

export const apiGetRepairList = (data: any, { current, size }: any) =>
    request({
        url: `/api/repair/list/${current}/${size}`,
        method: 'post',
        data
    })

export const apiGetRepairInfo = (params: any) =>
    request({
        url: `/api/repair/info`,
        method: 'get',
        params
    })

export const apiGetRepairInfoByCode = (params: any) =>
    request({
        url: `/api/repair/infoByCode`,
        method: 'get',
        params
    })

export const apiAddRepair = (data: any) =>
    request({
        url: '/api/repair/add',
        method: 'post',
        data
    })

export const apiUpdateRepair = (data: any) =>
    request({
        url: '/api/repair/update',
        method: 'post',
        data
    })

export const apiDeleteRepair = (data: any) =>
    request({
        url: '/api/repair/delete',
        method: 'post',
        data
    })

export const apiGetToolInfoFromReport = (params: any) =>
    request({
        url: '/api/dailyReport/common/data',
        method: 'get',
        params
    })
export const apiReceiveRepair = (data: any) =>
    request({
        url: '/api/repair/receive',
        method: 'post',
        data
    })
export const apiReceiveRepairItem = (data: any) =>
    request({
        url: '/api/repair/receiveDetail',
        method: 'post',
        data
    })