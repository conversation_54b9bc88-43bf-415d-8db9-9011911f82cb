import request from "@/utils/request";

export const apiWellList = (data: any) =>
    request({
        url: "/api/wellInfo/list",
        method: "get",
        data,
    });
// 井 - 井列表分页
export const apiWellListPage = (data: any, { current, size }: any) =>
    request({
        url: `/api/wellInfo/list/${current}/${size}`,
        method: "post",
        data,
    });
// 井 - 井列表模糊查询
export const apiWellListFuzzy = (data: any) =>
    request({
        url: `/api/wellInfo/fuzzyList`,
        method: "post",
        data,
    });
export const apiWellAdd = (data: any) =>
    request({
        url: "/api/wellInfo/add",
        method: "post",
        data,
    });
export const apiGetWellInfo = (data: any) =>
    request({
        url: "/api/wellInfo/info",
        method: "post",
        data,
    });
export const apiWellDelete = (data: any) =>
    request({
        url: "/api/wellInfo/delete",
        method: "post",
        data,
    });
export const apiWellDelete_v2 = (data: any) =>
    request({
        url: "/api/wellInfo/delete_v2",
        method: "post",
        data,
    });
export const apiWellUpdate = (data: any) =>
    request({
        url: "/api/wellInfo/update",
        method: "post",
        data,
    });

export const apiWellShipTo = (params: any) =>
    request({
        url: `/api/wellInfo/getAllShips`,
        method: "get",
        params,
    });

export const apiGetWellListWithJobs = (params: any) =>
    request({
        url: `/api/job/listWellWithJobs`,
        method: "get",
        params,
    });
