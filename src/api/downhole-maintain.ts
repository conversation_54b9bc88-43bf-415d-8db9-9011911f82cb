import request from "@/utils/request";

export const apiGetCurrentStockList = (data: any, { current, size }: any) =>
    request({
        url: `/api/currentStock/list/${current}/${size}`,
        method: "post",
        data
    });


export const apiGetPitToolsWorkOrderList = (data: any, { current, size }: any) =>
    request({
        url: `/api/workOrderPitTools/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetPitToolsWorkOrderInfo = (params: any) =>
    request({
        url: `/api/workOrderPitTools/info`,
        method: "get",
        params
    });
export const apiAddPitToolsWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderPitTools/add`,
        method: "post",
        data
    });
export const apiUpdatePitToolsWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderPitTools/update`,
        method: "post",
        data
    });

export const apiPitToolsAssemble = (type:string, data: any) =>
    request({
        url: `/api/workOrderPitTools/assemble/${type}`,
        method: "post",
        data
    });
export const apiPitToolsComponentRepair = (data: any) =>
    request({
        url: `/api/workOrderPitTools/component/repair`,
        method: "post",
        data
    });
export const apiFinishPitToolsWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderPitTools/finish`,
        method: "post",
        data
    });

export const apiGetDeviceTemplateList = (data: any) =>
    request({
        url: `/api/device/template/list`,
        method: "post",
        data
    });

//仪器使用统计
export const apiGetDeviceUsage = (data: any) =>
    request({
        url: `/api/dailyReport/usage`,
        method: "post",
        data
    })

// export const apiGetMwdWorkOrderStatistics = (data: any) =>
//     request({
//         url: `/api//workOrderMwd/statistics`,
//         method: "post",
//         data
//     })

// 删除工单
export const apiDeletePitToolsWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderPitTools/delete`,
        method: "post",
        data
    })

export const apiGetOaDeviceList = (data: any) =>
    request({
        url: `/api/workOrderPitTools/oa/getDeviceList`,
        method: "post",
        data
    })
export const apiGetOaBaseInfoList = (data: any) =>
    request({
        url: `/api/workOrderPitTools/oa/getBaseInfoList`,
        method: "post",
        data
    })

export const apiUploadPitToolsMassFile = (data:any) =>
    request({
        url: `/api/massFile/upload/pitToolsWorkOrder`,
        method: "post",
        data,
        timeout: 500000
    });

export const apiGetPitToolsMassFileList = (data:any, { current, size }:any) =>
    request({
        url: `/api/massFile/listByPitToolsId/${current}/${size}`,
        method: "post",
        data
    });
export const apiDeleteFile = (data:any) =>
    request({
        url: '/api/massFile/delete',
        method: 'post',
        data,
    })

export const apiValidatePittoolsAssemble = (data: any) => 
    request({
        url: '/api/workOrderPitTools/assembleCheck',
        method: 'post',
        data,
    })

// 关联工时
export const apiGetLaborHours = (data: any) => 
    request({
        url: '/api/erpWorkHour/detailInfoList',
        method: 'post',
        data,
    })
export const apiGetLaborHoursBaseInfoList = (data: any) => 
    request({
        url: '/api/erpWorkHour/baseInfoList',
        method: 'post',
        data,
    })

// 获取各部件维修类型
export const apiGetRepairTypeList = (params: any) => 
    request({
        url: '/api/workOrderPitTools/component/repairType',
        method: 'get',
        params,
    })
// 导出工单列表
export const apiExportPittoolsWorkOrder = (data: any) =>
    request({
        url: `/api/workOrderPitTools/order/export`,
        method: "post",
        data,
        responseType: 'blob'
    })