import request from '@/utils/request'
export const apiGetWorkHourList = (data:any, { current, size }:any) =>
    request({
        url: `/api/projectLaborHour/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetWorkHourInfo = (params: any) =>
    request({
        url: '/api/projectLaborHour/info',
        method: 'get',
        params
    })
export const apiAddWorkHour = (data: any) =>
    request({
        url: '/api/projectLaborHour/add',
        method: 'post',
        data
    })
export const apiUpdateWorkHour = (data: any) =>
    request({
        url: '/api/projectLaborHour/update',
        method: 'post',
        data
    })
export const apiDeleteWorkHour = (data: any) =>
    request({
        url: '/api/projectLaborHour/delete',
        method: 'post',
        data
    })
export const apiGetSimpleList = (data: any) => 
    request({
        url: '/api/projectLaborHour/listByDays',
        method: 'post',
        data
    })

// 工时申请
export const apiAddHoursApply = (data: any) => 
    request({
        url: '/api/projectLaborHour/addReview',
        method: 'post',
        data
    })
export const apiGetHoursApplyList = (data: any) => 
    request({
        url: '/api/projectLaborHour/listReview',
        method: 'post',
        data
    })
export const apiWithdrawHoursApply = (data: any) => 
    request({
        url: '/api/projectLaborHour/revokeReview',
        method: 'post',
        data
    })

// 工时审批
export const apiGetHoursApproveList = (data: any) => 
    request({
        url: '/api/projectLaborHour/getReviewTodoList',
        method: 'post',
        data
    })
export const apiHoursApprove = (data: any) => 
    request({
        url: '/api/projectLaborHour/review',
        method: 'post',
        data
    })
// 工时详情
export const apiGetWorkHourDetailList = (data:any, { current, size }:any) =>
    request({
        url: `/api/projectLaborHour/detailList/${current}/${size}`,
        method: "post",
        data
    });
// 工时统计
export const apiGetTotalStats = (data: any) => 
    request({
        url: '/api/projectLaborHour/detailListByDay',
        method: 'post',
        data
    })
export const apiGetSubmitInfoByDay = (data: any) => 
    request({
        url: '/api/projectLaborHour/getFillStatus',
        method: 'post',
        data
    })
export const apiGetProjectStats = (data: any) => 
    request({
        url: '/api/projectLaborHour/statistics',
        method: 'post',
        data
    })
export const apiGetHoursStats = (data:any, { current, size }:any) =>
    request({
        url: `/api/projectLaborHour/memberStatistics/${current}/${size}`,
        method: 'post',
        data
    })
// 按项目经理身份查询员工列表
export const apiGetMemberListByPM = (data: any) =>
    request({
        url: '/api/projectLaborHour/getMember',
        method: 'post',
        data
    })
// 获取总工时
export const apiGetTotalWorkHours = (data: any) =>
    request({
        url: '/api/projectLaborHour/getTotal',
        method: 'post',
        data
    })