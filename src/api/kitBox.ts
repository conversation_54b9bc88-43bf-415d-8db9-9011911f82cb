import request from "@/utils/request";

export const apiKitBoxPackage = (data: any) =>
    request({
        url: "/api/kit_box/package",
        method: "post",
        data,
    });

export const apiGetKitBoxInfo = (params: any) =>
    request({
        url: "/api/kit_box/info",
        method: "get",
        params,
    });
export const apiGetKitBoxInfo2 = (data: any) =>
    request({
        url: "/api/kit_box/kitBoxInfo",
        method: "post",
        data,
    });

export const apiGetKitBoxList = (data: any) =>
    request({
        url: "/api/kit_box/list",
        method: "post",
        data,
    });

export const apiDeleteKitBox = (params: any) =>
    request({
        url: "/api/kit_box/delete",
        method: "post",
        params,
    });

export const apiConfirmStatus = (data: any) =>
    request({
        url: "/api/kit_box/updateStatus",
        method: "post",
        data,
    });

export const apiRemoveKitBox = (data: any) =>
    request({
        url: "/api/kit_box/remove",
        method: "post",
        data,
    });
