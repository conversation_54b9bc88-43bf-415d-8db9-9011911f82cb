import request from "@/utils/request";

export const apiGetRequestList = (data: any) =>
    request({
        url: "/api/form/materialrequest/list",
        method: "post",
        data,
    });

export const apiDetailedRequestList = (data: any) =>
    request({
        url: "/api/form/materialrequest/info",
        method: "post",
        data,
    });

export const apiAddRequest = (data: any) =>
    request({
        url: "/api/form/materialrequest/add",
        method: "post",
        data,
    });

export const apiUpdateRequest = (data: any) =>
    request({
        url: "/api/form/materialrequest/update",
        method: "post",
        data,
    });

export const apiDeleteRequest = (data: any) =>
    request({
        url: "/api/form/materialrequest/delete",
        method: "post",
        data,
    });

export const apiConfirmRequest = (data: any) =>
    request({
         url: "/api/form/materialrequest/confirm",
        method: "post",
        data,
    });
