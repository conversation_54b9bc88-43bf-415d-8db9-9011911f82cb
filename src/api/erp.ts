import request from '@/utils/request'
export const apiSyncData = (data: any) =>
    request({
        url: '/api/task/data/sync',
        method: 'post',
        data
    })

export const apiSyncAllData = (data: any) =>
    request({
        url: '/api/task/data/syncAll',
        method: 'get',
        data
    })

export const apiSyncGroupData = (data: any) =>
    request({
        url: '/api/task/data/syncGroup',
        method: 'post',
        data
    })
// export const apiAddDict = (data: any) =>
//     request({
//         url: '/api/dataDictionary/add',
//         method: 'post',
//         data
//     })

export const apiGetMoList = (data: any) =>
    request({
        url: '/api/mo/list',
        method: 'post',
        data
    })
export const apiGetMoInfo = (data: any) =>
    request({
        url: '/api/mo/info',
        method: 'post',
        data
    })
export const apiGetPickOrderList = (data: any) =>
    request({
        url: '/api/pick/list',
        method: 'post',
        data
    })

// 领料单模糊查询
export const apiGetFuzzyPickOrderList = (data: any) =>
    request({
        url: '/api/pick/docNoList',
        method: 'post',
        data
    })