// 工具历史
import request from "@/utils/request";

export const apiGetCurrentStockList = (data: any, { current, size }: any) =>
    request({
        url: `/api/currentStock/list/${current}/${size}`,
        method: "post",
        data
    });


export const apiGetInventoryClassList = (params: any) =>
    request({
        url: `/api/currentStock/inventoryClassList`,
        method: "get",
        params
    });

//获取部件服役历史
export const apiGetServiceHistoryList = (data: any, { current, size }: any) =>
    request({
        url: `/api/component/history/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetServiceHistoryDetailList = (data: any, { current, size }: any) =>
    request({
        url: `/api/component/historyDetail/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiGetServiceHistoryDetail = (data: any) =>
    request({
        url: `/api/component/historyDetail`,
        method: "post",
        data
    });
// 部件统计详情
export const apiGetPartStats = (data: any) =>
    request({
        url: `/api/component/history/detail`,
        method: "post",
        data
    });

//获取部件维修历史
export const apiGetMaintainHistoryList = (data: any, { current, size }: any) =>
    request({
        url: `/api/component/repair/list/${current}/${size}`,
        method: "post",
        data
    });

//仪器流转历史查询
export const apiGetDeviceHistoryList = (data: any, { current, size }: any) =>
    request({
        url: `/api/device/transfer/list/${current}/${size}`,
        method: "post",
        data
    });

export const apiGetDetail = (data: any) =>
    request({
        url: `/api/device/info`,
        method: "post",
        data
    });

export const apiGetInventoryList = (data: any) =>
    request({
        url: `/api/inventory/selectInvName`,
        method: "post",
        data
    });

export const apiGetSerialNumberList = (data: any) =>
    request({
        url: `/api/currentStock/serialNumberList`,
        method: "post",
        data
    });

/*

 */

export const apiGetWarehouseAllName = (data: any) =>
    request({
        url: `/api/warehouse/getAllName`,
        method: "post",
        data
    });

export const apiGetToolByDeviceTypeList = (data: any) =>
    request({
        url: `/api/warehouse/queryByDeviceTypeList`,
        method: "post",
        data
    });

export const apiGetToolByDeviceType = (data: any, { current, size }: any) =>
    request({
        url: `/api/warehouse/queryByDeviceType/${current}/${size}`,
        method: "post",
        data
    });
// 仪器盘点导出
export const apiExportWarehouse = (params: any) =>
    request({
        url: `/api/warehouse/device/export`,
        method: "get",
        params,
        responseType: 'blob'
    });
    
export const apiCoreList = (data: any) =>
    request({
        url: `/api/component/core/list`,
        method: "post",
        data
    });

// 流转历史
    
export const apiGetCirculation = (data: any) =>
    request({
        url: `/api/deviceServiceHistory/selectHistory`,
        method: "post",
        data
    });
