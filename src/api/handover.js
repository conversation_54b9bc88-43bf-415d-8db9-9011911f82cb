import request from '@/utils/request'

export const apiGetHandoverList = (data, { current, size }) =>
    request({
        url: `/api/handover/list/${current}/${size}`,
        method: "post",
        data
    });
export const apiReceiveHandover = (data) =>
    request({
        url: `/api/handover/updateStatus`,
        method: "post",
        data
    });
export const apiCreateHandover = (data) =>
    request({
        url: `/api/handover/add`,
        method: "post",
        data
    });
export const apiExportHandover = (data) =>
    request({
        url: '/api/handover/export',
        method: 'post',
        data,
        responseType: 'blob',
        timeout: 500000
    })