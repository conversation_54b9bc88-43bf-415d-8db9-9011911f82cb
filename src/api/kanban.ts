import request from '@/utils/request'

// board
export const apiGetBoard = (params: any) =>
    request({
        url: '/api/board/list',
        method: 'get',
        params
    })
export const apiAddBoard = (data: any) =>
    request({
        url: '/api/board/add',
        method: 'post',
        data
    })
export const apiUpdateBoard = (data: any) =>
    request({
        url: '/api/board/update',
        method: 'post',
        data
    })
export const apiDeleteBoard = (data: any) =>
    request({
        url: '/api/board/delete',
        method: 'post',
        data
    })

//board file
export const apiGetBoardFileList = (data: any) =>
    request({
        url: '/api/board/file/list',
        method: 'post',
        data
    })

export const apiUploadBoardFile = (formData: any) =>
    request({
        url: '/api/board/file/add',
        headers: { "Content-type": "multipart/form-data" },
        method: 'POST',
        data: formData,
    })

export const apiDownloadBoardFile = (params: any) =>
    request({
        url: '/api/board/file/download',
        method: 'get',
        responseType: 'blob',
        params
    })
export const apiDeleteBoardFile = (data: any) =>
    request({
        url: '/api/board/file/delete',
        method: 'post',
        data: data
    })


// board detail
export const apiGetBoardDetail = (params: any) =>
    request({
        url: '/api/board/detail',
        method: 'get',
        params
    })
export const apiAddBoardDetail = (data: any) =>
    request({
        url: '/api/board/detail/add',
        method: 'post',
        data
    })
export const apiUpdateBoardDetail = (data: any) =>
    request({
        url: '/api/board/detail/update',
        method: 'post',
        data
    })
export const apiDeleteBoardDetail = (data: any) =>
    request({
        url: '/api/board/detail/delete',
        method: 'post',
        data
    })

export const apiSortBoardDetail = (data: any) =>
    request({
        url: '/api/board/detail/sort',
        method: 'post',
        data
    })

// card
export const apiGetCard = (params: any) =>
    request({
        url: '/api/card/detail',
        method: 'get',
        params
    })
export const apiAddCard = (data: any) =>
    request({
        url: '/api/card/add',
        method: 'post',
        data
    })
export const apiUpdateCard = (data: any) =>
    request({
        url: '/api/card/update',
        method: 'post',
        data
    })
export const apiDeleteCard = (data: any) =>
    request({
        url: '/api/card/delete',
        method: 'post',
        data
    })

export const apiSortCard = (data: any) =>
    request({
        url: '/api/card/sort',
        method: 'post',
        data
    })

// member
export const apiGetAllMembers = (params: any) =>
    request({
        url: '/api/user/listByMenu',
        method: 'get',
        params
    })
export const apiAddMembers = (data: any) =>
    request({
        url: '/api/card/member/add',
        method: 'post',
        data
    })
export const apiDeleteMembers = (data: any) =>
    request({
        url: '/api/card/member/delete',
        method: 'post',
        data
    })

// comments
export const apiAddComment = (data: any) =>
    request({
        url: '/api/card/comment/add',
        method: 'post',
        data
    })
export const apiDeleteComment = (data: any) =>
    request({
        url: '/api/card/comment/delete',
        method: 'post',
        data
    })

// upload image
export const apiAddImage = (data: any) =>
    request({
        url: '/api/card/image/add',
        method: 'post',
        data
    })
export const apiDeleteImage = (data: any) =>
    request({
        url: '/api/card/image/delete',
        method: 'post',
        data
    })


