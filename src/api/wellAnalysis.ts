import request from '@/utils/request'

/**
 * 导出井作业分析详情数据到Excel
 * @param params 查询参数，包含井号、趟次、井类别、是否故障等筛选条件
 * @returns 请求Promise，返回二进制数据流
 */
export const apiExportWellAnalysisExcel = (params: any) =>
  request({
    url: '/api/wellAnalysis/export',
    method: 'get',
    params,
    responseType: 'blob',
    timeout: 500000
  })

/**
 * 井作业分析详情查询参数接口
 */
export interface WellAnalysisQueryParams {
  /** 当前页码 */
  current: number;
  /** 每页数量 */
  size: number;
  /** 井号（可选，模糊查询） */
  wellNumber?: string;
  /** 趟次（可选） */
  runNumber?: string | number;
  /** 井类别（可选） */
  serviceWellCategory?: string;
  /** 是否故障（可选） */
  isFailure?: string;
  /** 排序字段（可选） */
  orderBy?: string;
  /** 排序方式（可选） */
  orderType?: 'asc' | 'desc';
}

/**
 * 工具信息接口
 */
export interface WellAnalysisInstrument {
  /** 主键ID */
  id?: number;
  /** 分析详情ID */
  analysisDetailIdFk?: number;
  /** 工具角色 */
  invName: string;
  /** 序列号 */
  serialNumber: string;
  /** 风险类型 */
  riskType: 'RISK' | 'PRODUCE' | 'TEST' | 'SCRAP' | string;
  /** 工具类型 */
  toolType?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 井作业分析详情接口
 */
export interface WellAnalysisDetail {
  /** 主键ID */
  id?: number;
  /** 井号 */
  wellNumber: string;
  /** 趟次 */
  run: number | string;
  /** 井类别 */
  serviceWellCategory?: string;
  /** 区块 */
  block?: string;
  /** 是否故障 */
  isFailure?: string;
  /** 故障类型 */
  consolidatedFailureType?: string;
  /** 入井时间 */
  dateIn?: string;
  /** 出井时间 */
  dateOut?: string;
  /** 入井工作时长 */
  tripInHours?: number;
  /** 循环工作时长 */
  circulatingHours?: number;
  /** 施工开始井深(MD) */
  constructionStartDepthMd?: number;
  /** 施工结束井深(MD) */
  constructionEndDepthMd?: number;
  /** 进尺(MD) */
  footageMd?: number;
  /** 泥浆类型 */
  mudType?: string;
  /** 泥浆密度 */
  mudDensity?: number | string;
  /** 排量 */
  flowRate?: number | string;
  /** Orifice配置 */
  orificeConfig?: string;
  /** Poppet配置 */
  poppetConfig?: string;
  /** 信号强度 */
  signalStrength?: number | string;
  /** 温度值 */
  temperatureValue?: number | string;
  /** 振动级别 */
  vibrationLevel?: string;
  /** 振动超标 */
  vibrationOos?: boolean;
  /** 故障原因描述 */
  failureReasonDescription?: string;
  /** 现场端主要情况 */
  fieldIncidentDescription?: string;
  /** 维护/研发端检查发现 */
  workshopShopFinding?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 关联的工具信息 */
  instruments?: WellAnalysisInstrument[];
}

/**
 * 井作业分析详情更新VO接口
 */
export interface WellAnalysisDetailVO extends WellAnalysisDetail {
  /** 需要删除的工具ID列表 */
  instrumentIdsToDelete?: number[];
}

/**
 * 分页响应接口
 */
export interface PageResponse<T> {
  /** 当前页数据 */
  records: T[];
  /** 总记录数 */
  total: number;
  /** 每页大小 */
  size: number;
  /** 当前页码 */
  current: number;
  /** 总页数 */
  pages: number;
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  /** 状态码 */
  code: number;
  /** 消息 */
  message: string;
  /** 数据 */
  data: T;
}

/**
 * 分页查询井作业分析详情，包含关联的工具信息
 * @param params 查询参数
 * @returns 请求Promise，返回ApiResponse<PageResponse<WellAnalysisDetail>>类型的数据
 */
export const apiGetWellAnalysisDetailList = (params: WellAnalysisQueryParams) =>
  request({
    url: '/api/wellAnalysis/page-with-instruments',
    method: 'get',
    params
  }) as unknown as Promise<ApiResponse<PageResponse<WellAnalysisDetail>>>

/**
 * 根据ID获取井作业分析详情，包含关联的工具信息
 * @param id 详情ID
 * @returns 请求Promise，返回ApiResponse<WellAnalysisDetail>类型的数据
 */
export const apiGetWellAnalysisDetailById = (id: number) =>
  request({
    url: `/api/wellAnalysis/detail-with-instruments/${id}`,
    method: 'get'
  }) as unknown as Promise<ApiResponse<WellAnalysisDetail>>

/**
 * 更新井作业分析详情，同时更新关联的工具信息
 * @param data 包含主表和子表数据的VO对象
 * @returns 请求Promise，返回ApiResponse<boolean>类型的数据
 */
export const apiUpdateWellAnalysisDetail = (data: WellAnalysisDetailVO) =>
  request({
    url: '/api/wellAnalysis/update-with-instruments',
    method: 'post',
    data
  }) as unknown as Promise<ApiResponse<boolean>>

/**
 * 删除井作业分析详情
 * @param id 详情ID
 * @returns 请求Promise，返回ApiResponse<boolean>类型的数据
 */
export const apiDeleteWellAnalysisDetail = (id: number) =>
  request({
    url: `/api/wellAnalysis/${id}`,
    method: 'delete'
  }) as unknown as Promise<ApiResponse<boolean>>

/**
 * 井作业分析详情基本信息接口
 */
export interface WellAnalysisDetailBasic {
  /** 主键ID */
  id?: number;
  /** 井号 */
  wellNumber: string;
  /** 趟次 */
  run: number | string;
  /** 井类别 */
  serviceWellCategory?: string;
  /** 区块 */
  block?: string;
  /** 是否故障 */
  isFailure?: string;
  /** 故障类型 */
  consolidatedFailureType?: string;
  /** 入井时间 */
  dateIn?: string;
  /** 出井时间 */
  dateOut?: string;
  /** 入井工作时长 */
  tripInHours?: number;
  /** 循环工作时长 */
  circulatingHours?: number;
  /** 施工开始井深(MD) */
  constructionStartDepthMd?: number;
  /** 施工结束井深(MD) */
  constructionEndDepthMd?: number;
  /** 进尺(MD) */
  footageMd?: number;
  /** 温度值 */
  temperatureValue?: number | string;
  /** 振动级别 */
  vibrationLevel?: string;
  /** 振动超标 */
  vibrationOos?: boolean;
  /** 维护级别 */
  maintenanceLevel?: string;
  /** 使用后维护完成日期 */
  postUseMaintenanceCompletionDate?: string;
  /** 更换主要部件描述 */
  replacedMajorComponentsDesc?: string;
  /** 起钻原因 */
  tripOutReasonType?: string;
  /** 关联的工具信息 */
  instruments?: WellAnalysisInstrument[];
}

/**
 * 井作业分析详情富文本内容接口
 */
export interface WellAnalysisDetailRichContent {
  /** 主键ID */
  id?: number;
  /** 泥浆类型 */
  mudType?: string;
  /** 泥浆密度 */
  mudDensity?: number | string;
  /** 排量 */
  flowRate?: number | string;
  /** Orifice配置 */
  orificeConfig?: string;
  /** Poppet配置 */
  poppetConfig?: string;
  /** 信号强度 */
  signalStrength?: number | string;
  /** 振动级别 */
  vibrationLevel?: string;
  /** 振动超标 */
  vibrationOos?: boolean;
  /** 故障原因描述 */
  failureReasonDescription?: string;
  /** 现场端主要情况 */
  fieldIncidentDescription?: string;
  /** 井下仪器情况描述 */
  downholeInstrumentStatusDesc?: string;
  /** 维护/研发端检查发现 */
  workshopShopFinding?: string;
  /** 改进计划/措施 */
  improvementPlanMeasures?: string;
  /** 维护级别 */
  maintenanceLevel?: string;
  /** 使用后维护完成日期 */
  postUseMaintenanceCompletionDate?: string;
  /** 更换主要部件描述 */
  replacedMajorComponentsDesc?: string;
  /** 关联的工具信息 */
  instruments?: WellAnalysisInstrument[];
}

/**
 * 分页查询井作业分析详情的基本信息（不包含富文本字段）
 * @param params 查询参数
 * @returns 请求Promise，返回ApiResponse<PageResponse<WellAnalysisDetailBasic>>类型的数据
 */
export const apiGetWellAnalysisBasicInfoList = (params: WellAnalysisQueryParams) =>
  request({
    url: '/api/wellAnalysis/page-basic-info',
    method: 'get',
    params
  }) as unknown as Promise<ApiResponse<PageResponse<WellAnalysisDetailBasic>>>

/**
 * 根据ID获取井作业分析详情的富文本内容
 * @param id 详情ID
 * @returns 请求Promise，返回ApiResponse<WellAnalysisDetailRichContent>类型的数据
 */
export const apiGetWellAnalysisRichContent = (id: number) =>
  request({
    url: `/api/wellAnalysis/rich-text-content/${id}`,
    method: 'get',
    timeout: 60000 // 增加超时时间到1分钟
  }) as unknown as Promise<ApiResponse<WellAnalysisDetailRichContent>>

/**
 * 部分更新接口参数
 */
export interface WellAnalysisPartialUpdateParams {
  /** 详情ID */
  id: number;
  /** 字段名称 */
  fieldName: string;
  /** 字段值 */
  fieldValue: string;
}

/**
 * 更新井作业分析详情的单个富文本字段
 * 此接口专门用于处理大型富文本内容，只更新指定的字段
 * @param data 包含ID、字段名和字段值的对象
 * @returns 请求Promise，返回ApiResponse<boolean>类型的数据
 */
export const apiUpdateWellAnalysisRichTextField = (data: WellAnalysisPartialUpdateParams) =>
  request({
    url: '/api/wellAnalysis/update-rich-text-field',
    method: 'post',
    data,
    timeout: 300000 // 增加超时时间到5分钟
  }) as unknown as Promise<ApiResponse<boolean>>

/**
 * 根据井号获取该井所有趟次的汇总信息，用于井汇总视图的展开行显示
 * @param wellNumber 井号
 * @returns 请求Promise，返回ApiResponse<WellAnalysisDetailBasic[]>类型的数据
 */
export const apiGetWellRunsSummary = (wellNumber: string) =>
  request({
    url: `/api/wellAnalysis/well-runs-summary/${encodeURIComponent(wellNumber)}`,
    method: 'get',
    timeout: 30000
  }) as unknown as Promise<ApiResponse<WellAnalysisDetailBasic[]>>

/**
 * 同步新的井作业分析数据
 * 此接口会触发后台同步过程，从MongoDB获取新的作业数据并同步到MySQL
 * @returns 请求Promise，返回ApiResponse<string>类型的数据
 */
export const apiSynchronizeWellAnalysisData = () =>
  request({
    url: '/api/wellAnalysis/synchronize-new',
    method: 'post',
    timeout: 60000 // 增加超时时间到1分钟
  }) as unknown as Promise<ApiResponse<string>>

/**
 * 井汇总数据接口
 */
export interface WellSummary {
  /** 井号 */
  wellNumber: string;
  /** 作业号 */
  jobNumber?: string;
  /** 区块 */
  block?: string;
  /** 作业状态 */
  jobStatus?: number;
  /** 总趟次数 */
  totalRuns: number;
  /** 故障趟次数 */
  failureRuns: number;
  /** 故障率 */
  failureRate: string;
  /** 总入井小时 */
  totalTripInHours?: number;
  /** 总循环小时 */
  totalCirculatingHours?: number;
  /** 总工作时长(小时) */
  totalWorkHours: number;
  /** 温度范围 */
  temperatureRange?: string;
  /** 施工周期 */
  constructionPeriod?: string;
  /** 主要故障类型 */
  mainFailureTypes?: string;
  /** 工具总数 */
  totalInstruments: number;
  /** 最早入井时间 */
  earliestDateIn?: string;
  /** 最晚出井时间 */
  latestDateOut?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 * 井汇总查询参数接口
 */
export interface WellSummaryQueryParams {
  /** 当前页码 */
  current: number;
  /** 每页数量 */
  size: number;
  /** 井号（可选，模糊查询） */
  wellNumber?: string;
  /** 是否故障（可选） */
  isFailure?: string;
  /** 排序字段（可选） */
  orderBy?: string;
  /** 排序方式（可选） */
  orderType?: 'asc' | 'desc';
}

/**
 * 分页查询井汇总数据
 * 按井号聚合数据，返回每个井的统计信息，专门用于井汇总视图
 * @param params 查询参数
 * @returns 请求Promise，返回ApiResponse<PageResponse<WellSummary>>类型的数据
 */
export const apiGetWellSummaryList = (params: WellSummaryQueryParams) =>
  request({
    url: '/api/wellAnalysis/page-well-summary',
    method: 'get',
    params
  }) as unknown as Promise<ApiResponse<PageResponse<WellSummary>>>
