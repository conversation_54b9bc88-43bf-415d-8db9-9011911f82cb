import request from "@/utils/request";

export const apiGetFailureReportList = (data: any) =>
    request({
        url: "/api/form/failureReport/list",
        method: "post",
        data,
    });

export const apiConfirmFailureReportOrder = (data: any) =>
    request({
        url: "/api/form/failureReport/confirm",
        method: "post",
        data,
    });

export const apiGetFailInfo = (data:any, { current, size }:any) =>
    request({
        url: `/api/workOrderMwd/sharedList/${current}/${size}`,
        method: "post",
        data
    });

export const apiGetFailInfoV2 = (data:any, { current, size }:any) =>
    request({
        url: `/api/workOrderMwd/sharedList_v2/${current}/${size}`,
        method: "post",
        data
    });