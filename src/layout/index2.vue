<template>
    <div :class="classObj" class="app-wrapper" >
        <div
           v-if="classObj.mobile && sidebar.opened"
            class="drawer-bg"
            @click="handleClickOutside"
        />
        <Sidebar2 class="sidebar-container"/>
        <div class="main-container">
            <navbar/>
            <!-- <tags-view /> -->
            <app-main/>
        </div>
    </div>
</template>

<script lang="ts">
    // import Driver from 'driver.js'
    // import 'driver.js/dist/driver.min.css'

    import { Component } from 'vue-property-decorator'
    import { mixins } from 'vue-class-component'
    import { DeviceType, AppModule } from '@/store/modules/app'
    //import { AppMain, Navbar, Sidebar } from './components'
    import { AppMain,Sidebar2,Navbar } from './components'
import {TagsView} from './components'
    // import RightPanel from '@/components/RightPanel/index.vue'
    import ResizeMixin from './mixin/resize'

    @Component({
        name: 'Layout',
        components: {
            AppMain,
            Navbar,
            Sidebar2,TagsView
        }
    })
    //export default class extends mixins(ResizeMixin)
    export default class extends mixins(ResizeMixin) {//
        // private driver: Driver | null = null

        get classObj() {
            return {
                hideSidebar:!this.sidebar.opened,
                openSidebar: this.sidebar.opened,
                withoutAnimation:this.sidebar.withoutAnimation,
                mobile: this.device === DeviceType.Mobile
            }
        }

        mounted() {
            // this.driver = new Driver()

            this.$nextTick(() => {
                // this.guide()
            })
        }


        private handleClickOutside() {
            AppModule.CloseSideBar(false)
        }
    }
</script>

<style lang="scss" scoped>
    .app-wrapper {
        @include clearfix;
        position: relative;
        height: 100%;
        width: 100%;
    }

    .drawer-bg {
        background: #000;
        opacity: 0.3;
        width: 100%;
        top: 0;
        height: 100%;
        position: absolute;
        z-index: 999;
    }

    .main-container {
        min-height: 100%;
        transition: margin-left .28s;
        margin-left: $sideBarWidth;
        position: relative;
    }

    .sidebar-container {
        transition: width 0.28s;
        width: $sideBarWidth !important;
        height: 100%;
        position: fixed;
        font-size: 0px;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1001;
        overflow: hidden;  background-color:#001529 !important;
    }

    .fixed-header {
        position: fixed;
        top: 0;
        right: 0;
        z-index: 9;
        width: calc(100% - #{$sideBarWidth});
        transition: width 0.28s;
    }

    .hideSidebar {
        .main-container {
            margin-left: 54px;
        }

        .sidebar-container {
            width: 54px !important;
        }

        .fixed-header {
            width: calc(100% - 54px)
        }
    }

    /* for mobile response 适配移动端 */
    .mobile {
        .main-container {
            margin-left: 0px;
        }

        .sidebar-container {
            transition: transform .28s;
            width: $sideBarWidth !important;
        }

        &.openSidebar {
            position: fixed;
            top: 0;
        }

        &.hideSidebar {
            .sidebar-container {
                pointer-events: none;
                transition-duration: 0.3s;
                transform: translate3d(-$sideBarWidth, 0, 0);
            }
        }

        .fixed-header {
            width: 100%;
        }
    }

    .withoutAnimation {
        .main-container,
        .sidebar-container {
            transition: none;
        }
    }
</style>
