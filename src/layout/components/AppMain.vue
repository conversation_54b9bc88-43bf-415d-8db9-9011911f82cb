<template>
  <section class="app-main" :class="{'is-mobile': isMobile}">
    <!-- <transition
      name="fade-transform"
      mode="out-in"
    > -->
      <keep-alive>
        <router-view v-if="!$route.meta.noCache" :key="key" />
      </keep-alive>
      <router-view v-if="$route.meta.noCache"></router-view>
    <!-- </transition> -->
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { TagsViewModule } from '@/store/modules/tags-view'

  import { DeviceType, AppModule } from '@/store/modules/app'
@Component({
  name: 'AppMain'
})
export default class extends Vue {
  get cachedViews() {
    return TagsViewModule.cachedViews
  }
  get isMobile() {
     return AppModule.device === DeviceType.Mobile
  }
  get key() {
    return this.$route.path
  }
  mounted(){
    this.setVh()
    window.addEventListener('resize', this.setVh)
  }
  private setVh(){
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  // min-height: calc(100vh - #{$topBarHeight} - #{$tagsViewHeight});
  width: 100%;
  position: relative;
  background-color: #f0f2f5;
  height: calc(100vh - 65px);
  overflow-y: auto;
  overflow-x: hidden;
  &.is-mobile{
    height: calc(100vh - 60px);
  }
}

// .fixed-header+.app-main {
//   padding-top: 50px;
//   height: 100vh;
//   overflow: auto;
// }

.hasTagsView {
  .app-main {
    min-height: calc(100vh - #{$topBarHeight} - #{$tagsViewHeight});
  }

  // .fixed-header+.app-main {
  //   padding-top: 84px;
  // }
}
</style>
