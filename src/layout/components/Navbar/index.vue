<template>
    <div class="navbar" >
        <hamburger
            id="hamburger-container"
            :is-active="sidebar.opened"
            class="hamburger-container"
            @toggle-click="toggleSideBar"
        />
        <breadcrumb
            id="breadcrumb-container"
            class="breadcrumb-container"
        />
        <div class="right-menu">
            <el-dropdown
                class="avatar-container right-menu-item hover-effect"
                id="personal-center"
                trigger="click"
            >
                <Avatar :username="avatarName"
                    style="margin-top:7px;background-color:#409eff;color: #FFFFFF;width: 40px;height: 40px;font: 15px / 45px Helvetica, Arial, sans-serif">
                </Avatar>
                <el-dropdown-menu slot="dropdown" class="profile-content">
                    <div class="info" style="text-align:center;">
                        <div style="margin-top:10px;margin-bottom: 10px;color: #1f2329">{{realName}}</div>
                        <div style="font-size: 12px;color: #646a73;margin-bottom: 15px">{{department}}</div>
                        <el-dropdown-item @click.native="onChangePsw">
                            <div style="color: #333">修改密码</div>
                        </el-dropdown-item>
                        <el-dropdown-item divided @click.native="onLogout">
                            <div style="color: #f54a45">退出登录</div>
                        </el-dropdown-item>
                    </div>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <el-dialog :close-on-click-modal="false" title="修改密码" :visible.sync="isChangePswDialogVisible" width="100%" max-width="600px">
            <el-form ref="changePswForm" :rules="rules" :model="form" label-width="130px">
                <el-form-item prop="oldPassword" label="旧密码：" size="normal">
                    <el-input auto-complete="new-password" v-model="form.oldPassword" style="width:90%"></el-input>
                </el-form-item>
                <el-form-item prop="newPassword" label="新密码：" size="normal">
                    <el-input auto-complete="new-password" v-model="form.newPassword" style="width:90%" type="password"></el-input>
                </el-form-item>
                <el-form-item prop="repeatPassword" label="再次输入密码：" size="normal">
                    <el-input auto-complete="new-password" v-model="form.repeatPassword" style="width:90%" type="password"></el-input>
                </el-form-item>
            </el-form>
            <template slot="footer">
                <el-button @click="isChangePswDialogVisible=flase">取消</el-button>
                <el-button type="primary" @click="onConfirmChangePsw">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import {AppModule} from '@/store/modules/app'
import {UserModule} from '@/store/modules/user'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import Avatar from 'vue-avatar';
import { apiChangePsw } from '@/api/users'
import { ElForm } from 'element-ui/types/form'
@Component({
    name: 'Navbar',
    components: {
        Breadcrumb,
        Hamburger,
        Avatar
    }
})
export default class extends Vue {
    private rules:any = {
        oldPassword:[{required:true,trigger:'blur',message:'请输入旧密码'}],
        newPassword:[{required:true,trigger:'blur',message:'请输入新密码'}],
        repeatPassword:[
            {required:true,trigger:'blur',message:'请再次输入新密码'},
            {
                validator:  (rule, value, callback) => {
                    if (value === '') {
                        callback(new Error('请再次输入新密码'));
                    } else if (value !== this.form.newPassword) {
                        callback(new Error('两次输入密码不一致'));
                    } else {
                        callback();
                    }
                },
                trigger: 'blur'
            }
        ]
    }
    private isChangePswDialogVisible = false;
    private form:any = {userId:'',oldPassword:'',newPassword:'',repeatPassword:''}
    get sidebar() {
        return AppModule.sidebar
    }

    get avatarName() {
        let realName = UserModule.realName;
        if (realName.length >= 3) {
            realName = realName.substr(realName.length - 2, realName.length - 1);
        }
        if (realName.length === 2) {
            let first = realName.substr(0, 1);
            let last = realName.substr(1, 2);
            realName = first + ' ' + last;
        }
        return realName;
    }
    private toggleSideBar() {
        AppModule.ToggleSideBar(this.sidebar.opened)
    }

    get realName() {
        return UserModule.realName;
    }

    get department() {
        return UserModule.department;
    }

    private onChangePsw(){
        this.isChangePswDialogVisible = true;
        this.form.newPassword = "";
        this.form.oldPassword = "";
        this.form.repeatPassword = "";
        this.$nextTick(()=>{
            (this.$refs.changePswForm as ElForm).clearValidate();
        })
    }
    private onConfirmChangePsw(){
        (this.$refs.changePswForm as ElForm).validate(valid=>{
            if(valid){
                const form = new FormData();
                form.append('userId', String(UserModule.userId));
                form.append('prePassword', this.form.oldPassword);
                form.append('password', this.form.newPassword);
                apiChangePsw(form).then(()=>{
                    this.isChangePswDialogVisible = false;
                    UserModule.LogOut();
                    this.$message.success("密码修改成功，请重新登陆！");
                    this.$router.push("/login")
                })
            }
        })
        
    }
    private async onLogout() {
        await UserModule.LogOut();
        this.$router.push(`/login?redirect=${this.$route.fullPath}`).catch(err => {
            console.warn(err);
        });
    }
}
</script>

<style lang="scss" scoped>
.navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

    .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        padding: 0 15px;
        cursor: pointer;
        transition: background .3s;
        -webkit-tap-highlight-color: transparent;

        &:hover {
            background: rgba(0, 0, 0, .025)
        }
    }

    .breadcrumb-container {
        float: left;
    }

    .errLog-container {
        display: inline-block;
        vertical-align: top;
    }

    .right-menu {
        float: right;
        height: 100%;
        line-height: 50px;
        display: flex;
        align-items: center;

        .theme-cnt {
            margin: 12px 10px 0 0;
        }

        &:focus {
            outline: none;
        }

        .right-menu-item {
            display: inline-block;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            vertical-align: text-bottom;

            &.hover-effect {
                cursor: pointer;
                transition: background .3s;

                &:hover {
                    background: rgba(0, 0, 0, .025)
                }
            }
        }

        .avatar-container {
            margin-right: 4px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                }

                .el-icon-caret-bottom {
                    cursor: pointer;
                    position: absolute;
                    right: -20px;
                    top: 25px;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
