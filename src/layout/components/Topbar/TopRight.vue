<template>
    <div class="right-menu">
        <el-dropdown
                class="avatar-container right-menu-item hover-effect"
                trigger="click">
            <!--            <div class="avatar-wrapper">-->
            <!--            <img-->
            <!--                :src="avatar+'?imageView2/1/w/80/h/80'"-->
            <!--                class="user-avatar"-->
            <!--            >-->
            <!--            <i class="el-icon-caret-bottom" />-->
            <!--            </div>-->
            <Avatar :username="avatarName"
                    style="margin-top:7px;background-color:#409eff;color: #FFFFFF;width: 40px;height: 40px;font: 15px / 45px Helvetica, Arial, sans-serif">
            </Avatar>
            <el-dropdown-menu slot="dropdown" class="profile-content">
                <!--            <router-link to="/profile/">-->
                <!--                <el-dropdown-item>-->
                <!--                用户信息+{{avatar}}+{{username}}-->
                <!--                </el-dropdown-item>-->
                <!--            </router-link>-->
                <div class="info">
                    <Avatar :username="avatarName"
                            style="margin: 0 auto;background-color:#409eff;color: #FFFFFF;width: 58px;height: 58px">
                    </Avatar>
                    <div style="margin-top:10px;margin-bottom: 10px;color: #1f2329">{{realName}}</div>
                    <div style="font-size: 12px;color: #646a73;margin-bottom: 15px">{{department}}</div>
                    <el-dropdown-item divided @click.native="logout">
                        <div style="color: #f54a45">退出登录</div>
                    </el-dropdown-item>
                </div>

            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Avatar from 'vue-avatar';
import { UserModule } from '@/store/modules/user';

@Component({ name: 'TopRight', components: { Avatar } })
export default class extends Vue {
    get avatarName() {
        let realName = UserModule.realName;
        if (realName.length >= 3) {
            realName = realName.substr(realName.length - 2, realName.length - 1);
        }
        if (realName.length === 2) {
            let first = realName.substr(0, 1);
            let last = realName.substr(1, 2);
            realName = first + ' ' + last;
        }
        console.log('realName', realName);
        return realName;
    }

    get realName() {
        return UserModule.realName;
    }

    get department() {
        return UserModule.department;
    }

    private async logout() {
        await UserModule.LogOut();
        this.$router.push(`/login?redirect=${this.$route.fullPath}`).catch(err => {
            console.warn(err);
        });
    }
}
</script>

<style lang="scss" scoped>
    .profile-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0px;
        width: 200px;
        min-width: 170px;

        .info {
            margin: 0;
            padding-top: 22px !important;
            padding-bottom: 10px !important;
            text-align: center
        }
    }

    .avatar-info {
        margin: 0 auto;
        background-color: #cccccc;
        color: #FFFFFF;
        width: 58px;
        height: 58px
    }

    .right-menu {
        float: right;
        height: 100%;

        &:focus {
            outline: none;
        }

        .right-menu-item {
            display: inline-block;
            padding: 4px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            vertical-align: text-bottom;

            &.hover-effect {
                cursor: pointer;
                transition: background .3s;

                &:hover {
                    background: rgba(0, 0, 0, .025)
                }
            }
        }

        .avatar-container {
            margin-right: 30px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                }

                .el-icon-caret-bottom {
                    cursor: pointer;
                    position: absolute;
                    right: -20px;
                    top: 25px;
                    font-size: 12px;
                }
            }
        }
    }
</style>
