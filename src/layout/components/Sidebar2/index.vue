<template>
     <div>
      <sidebar-logo :collapse="isCollapse" :to="'/'"/>
        <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        background-color="#001529"
        :text-color="variables.menuText"
        active-text-color="white"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
          :is-collapse="isCollapse"
        />
      </el-menu>
    </el-scrollbar>
     </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import variables from '@/styles/_variables.scss'
// import SidebarLogo from './SidebarLogo.vue'
import SidebarLogo from './logo.vue'
import SidebarItem from './SidebarItem.vue'
import { PermissionModule } from '@/store/modules/permission'
  import {AppModule} from '@/store/modules/app'
@Component({
  name: 'SideBar',
  components: {
   SidebarLogo,SidebarItem
  }
})
export default class extends Vue {
    get variables() {
        return variables
    }
   get routes() {
        return PermissionModule.routes.filter(item=>item.children&&item.children.find(child=>!child.meta?.hidden))
    }
    get activeMenu() {
        const route = this.$route
        const { meta, path } = route
        // if set path, the sidebar will highlight the path you set
        if (meta?.activeMenu) {
        return meta.activeMenu
        }
        return path
    }

  get menuActiveTextColor() {
      return variables.menuActiveText
  }
  get sidebar() {
    return AppModule.sidebar
  }
  get isCollapse() {
    return !this.sidebar.opened 
  }
}
</script>
<style lang="scss" scope>
.scrollbar-wrapper{
  margin-bottom: 0 !important;
  margin-right: -19px !important;
}
</style>