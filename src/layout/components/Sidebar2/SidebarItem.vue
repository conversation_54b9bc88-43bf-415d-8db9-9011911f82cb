<template>
    <div
        v-if="!item.meta || !item.meta.hidden"
        :class="[
            'menu-wrapper',
            isCollapse ? 'simple-mode' : 'full-mode',
            { 'first-level': isFirstLevel },
        ]"
    >
        <template
            v-if="
                !alwaysShowRootMenu &&
                    theOnlyOneChild &&
                    !theOnlyOneChild.children
            "
        >
            <sidebar-item-link
                v-if="theOnlyOneChild.meta"
                :to="resolvePath(theOnlyOneChild.path)"
            >
                <el-menu-item
                    :index="resolvePath(theOnlyOneChild.path)"
                    :class="{ 'submenu-title-noDropdown': isFirstLevel }"
                >
                    <svg-icon
                        v-if="theOnlyOneChild.meta.icon && item.children"
                        :name="theOnlyOneChild.meta.icon"
                    />
                    <span v-if="theOnlyOneChild.meta.title" slot="title">{{
                        theOnlyOneChild.meta.title
                    }}</span>
                </el-menu-item>
            </sidebar-item-link>
        </template>
        <el-submenu
            v-else
            :index="resolvePath(item.path)"
            popper-append-to-body
        >
            <template slot="title">
                <svg-icon
                    v-if="item.meta && item.meta.icon"
                    :name="item.meta.icon"
                />
                <span v-if="item.meta && item.meta.title" slot="title">{{
                    item.meta.title
                }}</span>
            </template>
            <template v-if="item.children">
                <sidebar-item
                    v-for="child in item.children"
                    :key="child.path"
                    :item="child"
                    :is-collapse="isCollapse"
                    :is-first-level="false"
                    :base-path="resolvePath(child.path)"
                    class="nest-menu"
                />
            </template>
        </el-submenu>
    </div>
</template>

<script lang="ts">
import path from "path";
import { Component, Prop, Vue } from "vue-property-decorator";
import { Route, RouteConfig } from "vue-router";
import { isExternal } from "@/utils/validate";
import SidebarItemLink from "./SidebarItemLink.vue";

@Component({
    // Set 'name' here to prevent uglifyjs from causing recursive component not work
    // See https://medium.com/haiiro-io/element-component-name-with-vue-class-component-f3b435656561 for detail
    name: "SidebarItem",
    components: {
        SidebarItemLink,
    },
})
export default class extends Vue {
    @Prop({ required: true }) private item!: RouteConfig;
    @Prop({ default: false }) private isCollapse!: boolean;
    @Prop({ default: true }) private isFirstLevel!: boolean;
    @Prop({ default: "" }) private basePath!: string;

    get alwaysShowRootMenu() {
        // NOTE: 权限系统定制化判断逻辑
        if (this.item.meta && (this.item as any).type==="DIRECTORY" && this.item.name!=="dashboard" && this.item.name!=="首页") {
            return true;
        }
        return false;
    }

    get showingChildNumber() {
        if (this.item.children) {
            const showingChildren = this.item.children.filter((item) => {
                if (item.meta && item.meta.hidden) {
                    return false;
                } else {
                    return true;
                }
            });
            return showingChildren.length;
        }
        return 0;
    }

    get theOnlyOneChild() {
        if (this.showingChildNumber > 1) {
            return null;
        }
        if (this.item.children) {
            for (let child of this.item.children) {
                if (!child.meta || !child.meta.hidden) {
                    return child;
                }
            }
        }
        // If there is no children, return itself with path removed,
        // because this.basePath already conatins item's path information
        return { ...this.item, path: "" };
    }

    private resolvePath(routePath: string) {
        if (isExternal(routePath)) {
            return routePath;
        }
        if (isExternal(this.basePath)) {
            return this.basePath;
        }
        return path.resolve(this.basePath, routePath);
    }
}
</script>

<style lang="scss">
.el-submenu.is-active > .el-submenu__title {
    color: #fff !important;
}

.full-mode {
    .nest-menu .el-submenu > .el-submenu__title,
    .el-submenu .el-menu-item {
        min-width: $sideBarWidth !important;
        background-color: #000C17  !important;

        &:hover {
            background-color: #000C00 !important;
        }
        &.is-active{
            background-color: #3F78FF !important;
        }
    }
}

.simple-mode {
    &.first-level {
        .submenu-title-noDropdown {
            padding: 0 !important;
            position: relative;

            .el-tooltip {
                padding: 0 !important;
            }
        }
        .el-submenu.is-active {
            & > .el-submenu__title {
                background-color: #3370ff !important;
            }
        }
        .el-submenu {
            overflow: hidden;

            & > .el-submenu__title {
                padding: 0px !important;
                color: white;
                .el-submenu__icon-arrow {
                    display: none;
                }

                & > span {
                    visibility: hidden;
                }
            }
            // & > .is-active{
            //     background-color: #3370ff !important;
            // }
            // & > .is-active div svg{
            // color:white;
            // }
        }
    }
}
</style>

<style lang="scss" scoped>
.svg-icon {
    margin-right: 16px;
}

.simple-mode {
    .svg-icon {
        margin-left: 20px;
    }
    svg {
        color: white;
    }
    span {
        color: white;
    }
}
.is-active {
    background-color: #3370ff !important;
}
.is-active div svg {
    color: white;
}
</style>
