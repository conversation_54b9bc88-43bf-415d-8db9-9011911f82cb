<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :background-color="variables.subMenuBg"
        :text-color="variables.menuText"
        :active-text-color="menuActiveTextColor"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
      <!-- 不同于第一层，如果第二层的key直接取path，是有可能重复的，重复的key会导致菜单栏高亮有bug，所以多加一个name，也可以加随机数或者index -->
        <sidebar-item
          v-for="route in currentChildrenRoutes"
          :key="route.path + route.name"
          :item="route"
          :base-path="basePath"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { PermissionModule } from '@/store/modules/permission'
import SidebarItem from './SidebarItem.vue'
import variables from '@/styles/_variables.scss'

@Component({
  name: 'SideBar',
  components: {
    SidebarItem,
  }
})
export default class extends Vue {
  get currentRootRoute(){
    let idx:number = PermissionModule.routes.findIndex(item=>item.path===this.$route.matched[0].path)
    return PermissionModule.routes[idx]
  }
  get basePath(){
    return this.currentRootRoute.path
  }
  get currentChildrenRoutes() {
    return this.currentRootRoute.children
  }


  get menuActiveTextColor() {
    return variables.menuActiveText
  }

  get variables() {
    return variables
  }

  get activeMenu() {
    const { path } = this.$route
    return path
  }

}
</script>

<style lang="scss">
.sidebar-container {
  height: calc(100% - #{$topBarHeight});
  margin-top: 0px;
  // reset element-ui css
  .horizontal-collapse-transition {
    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
  }
  .sidebar-container {
    &.el-menu-item.is-active {
      background-color: red !important;
    }
  }
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .el-scrollbar__view {
    height: 100%;
    border-right: 1px solid #eee;
  }

  .el-scrollbar__bar {
    &.is-vertical {
      right: 0px;
    }

    &.is-horizontal {
      display: none;
    }
  }
  .el-scrollbar {
    // margin-top: 60px;
    overflow-x: hidden;
    height: calc(100% - #{$topBarHeight})
  }
}
</style>

<style lang="scss" scoped>


.el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}
</style>
