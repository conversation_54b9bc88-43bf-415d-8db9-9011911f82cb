import Vue, { DirectiveOptions } from 'vue'

import 'normalize.css'
import ElementUI from 'element-ui'
import SvgIcon from 'vue-svgicon'

import Vant from 'vant'
import 'vant/lib/index.css'

Vue.use(Vant)

import '@/styles/element-variables.scss'
import '@/styles/index.scss'

import App from '@/App.vue'
import store from '@/store'
import router from '@/router'


import '@/icons/components'
import '@/permission'
import * as directives from '@/directives'
import * as filters from '@/filters'
import * as globalTools from '@/utils/common'
import '@/utils/dateFormat'
declare module "vue/types/vue" {
  interface Vue {
    [key: string]: any;
  }
}
import QuillEditor from '@/components/QuillEditor/index'
Vue.use(QuillEditor);
import InputNumber from '@/components/InputNumber/index'
Vue.use(InputNumber);
import NumberInput from '@/components/NumberInput/index'
Vue.use(NumberInput);
import CustomTag from '@/components/CustomTag/index'
Vue.use(CustomTag);
import locale from 'element-ui/lib/locale/lang/zh-CN'
Vue.use(ElementUI, {
  size: 'medium', // Set element-ui default size
  locale
})

Vue.use(SvgIcon, {
  tagName: 'svg-icon',
  defaultWidth: '1em',
  defaultHeight: '1em'
})

// Register global directives
Object.keys(directives).forEach(key => {
  Vue.directive(key, (directives as { [key: string]: DirectiveOptions })[key])
})

const { version } = process.env;
Vue.prototype.$version = version;
// Register global filter functions
Object.keys(filters).forEach(key => {
  Vue.filter(key, (filters as { [key: string]: Function })[key])
})
Object.keys(globalTools).forEach(key => {
  Vue.prototype[key] = (globalTools as { [key: string]: unknown })[key]
})
Vue.config.productionTip = false



new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
