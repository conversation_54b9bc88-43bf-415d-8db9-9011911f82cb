<template>
    <div class="gpc-container" style="margin-top: 20px">
        <div style="margin: 0px 10px 20px 0px">全部应用</div>
        <el-breadcrumb separator="/">
            <el-breadcrumb-item>
                <a
                    @click="onShowAll"
                    style="font-weight: 500 !important"
                    :class="currentMenu == 0 ? 'gpc-menu-active' : ''"
                >
                    全部
                </a>
            </el-breadcrumb-item>
            <el-breadcrumb-item
                v-for="(item, index) in routes"
                :key="index"
                v-show="(!item.meta || !item.meta.hidden) && item.meta.title"
            >
                <a
                    @click="onDisplayApp(index + 1, item)"
                    style="font-weight: 500 !important"
                    :class="currentMenu == index + 1 ? 'gpc-menu-active' : ''"
                >
                    {{ item.meta.title }}
                </a>
            </el-breadcrumb-item>
        </el-breadcrumb>
        <div class="panel-container">
            <div
                class="panel-item"
                v-for="(item, index) in currRoute.children"
                :key="index"
                v-show="currRoute.children && item.meta && !item.meta.hidden"
                @click="onGoPage(item.basePath, item.path)"
            >
                <div
                    class="panel-icon"
                    :style="'background-color:' + randomRgb()"
                >
                    <svg-icon
                        v-if="item.meta && item.meta.icon"
                        :name="item.meta.icon"
                        :style="'color:white;'"
                    />
                    <div v-else style="color: white">...</div>
                </div>
                <span
                    v-if="item.meta && item.meta.title"
                    slot="title"
                    class="panel-title"
                >
                    {{ item.meta.title }}
                </span>
                <span class="panel-desc">描述信息</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { PermissionModule } from "@/store/modules/permission";
import { isExternal } from "@/utils/validate";
import { RouteConfig } from "node_modules/vue-router/types";
interface IRouteObj {
    children?: RouteConfig[];
}
@Component({
    name: "Dashboard",
})
export default class extends Vue {
    private currentMenu: Number = 0;
    private currRoute: IRouteObj = { children: [] };
    get routes() {
        return PermissionModule.routes;
    }
    mounted() {
        this.onShowAll();
    }
    private onDisplayApp(selectIndex: 0, item: any) {
        this.currentMenu = selectIndex;
        for (let i = 0; i < item.children.length; i++) {
            item.children[i].basePath = item.path;
        }

        this.currRoute = item;
    }
    private onShowAll() {
        this.currentMenu = 0;
        let tmpRoute = this.routes;
        this.currRoute = { children: [] };
        for (let i = 0; i < tmpRoute.length; i++) {
            let baseRoute = tmpRoute[i];
            if (baseRoute.children) {
                for (let j = 0; j < baseRoute.children.length; j++) {
                    let route: any = baseRoute.children[j];
                    route.basePath = baseRoute.path;
                    if (
                        route.path != "dashboard" &&
                        route.path != "/redirect/:path(.*)"
                    )
                        this.currRoute.children?.push(route);
                }
            }
        }
    }
    private onGoPage(basePath: any, routePath: any) {
        if (isExternal(routePath)) {
            window.open(routePath);
        } else {
            this.$router.push(basePath + "/" + routePath);
            //this.$router.push("/product/category")
        }
    }
    randomRgb() {
        let R = Math.floor(Math.random() * 255);
        let G = Math.floor(Math.random() * 255);
        let B = Math.floor(Math.random() * 255);
        return "rgb(" + R + "," + G + "," + B + ")";
    }
}
</script>
<style lang="scss" scoped>
#btnCls {
    transition: width 2s;
}
.gpc-container {
    width: 100%;
    padding: 12px;

    margin-right: auto;
    margin-left: auto;
    max-width: 1285px !important;
}
.el-breadcrumb__item {
    cursor: pointer;
}
.gpc-card {
    width: 180px;
    height: 66px;
    border: 1px solid white;
    margin: 10px;
    background-color: white;
    border-radius: 4px;
    padding: 10px;
    cursor: pointer;
}
.gpc-menu-active {
    border-bottom: 1px solid #3370ff;
    color: #3370ff;
}
// @media (min-width: 1264px)
// .gpc-container {
//     max-width: 1185px;
// }
.panel-container {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    margin-top: 10px;
    .panel-item {
        width: 180px;
        height: 66px;
        border: 1px solid white;
        margin: 10px;
        background-color: white;
        border-radius: 4px;
        padding: 10px;
        position: relative;
        cursor: pointer;
        .panel-icon {
            width: 34px;
            height: 34px;
            border-radius: 20px;
            line-height: 34px;
            text-align: center;
            color: white;
        }
        .panel-title {
            display: block;
            margin-top: -36px;
            margin-left: 50px;
        }
        .panel-desc {
            display: block;
            margin-left: 50px;
            font-size: 13px;
            margin-top: 3px;
            color: #97a8be;
        }
        &.is-viewwarn::after {
            content: "";
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: red;
            top: -5px;
            right: -5px;
        }
    }
}
</style>
