<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井下工具部模块</div>
            </div>
            <div>
                井号：<el-select></el-select> 作业号：<el-select></el-select>
            </div>
            <div class="module-container">
                <div class="module">
                    井下工具
                    <div class="number">12</div>
                </div>
                <div class="module">
                    组装与拆卸
                </div>
                <div class="module">
                    需求单
                </div>
                <div class="module">
                    钻具通知单
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({})
export default class extends Vue {
    private a = 1;
}
</script>
<style lang="scss" scoped>
.module-container {
    margin-top: 20px;
    .module {
        text-align: center;
        line-height: 150px;
        font-size: 20px;
        width: 160px;
        height: 160px;
        margin-right: 20px;
        float: left;
        background: red;
        border-radius: 8px;
        position: relative;
        .number {
            position: absolute;
            top: -5px;
            right: -5px;
            line-height: 1;
            background: blue;
            height: 22px;
            width: 22px;
            font-size: 18px;
            color: white;
            border-radius: 50%;
        }
    }
}
</style>
