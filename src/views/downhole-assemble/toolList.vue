<template>
    <div>
        <div style="margin:10px 0">{{ name }}——列表</div>
        <el-table :data="toolList" border>
            <el-table-column prop="serialNumber" label="序列号">
            </el-table-column>
            <el-table-column prop="serialNumber" label="序列号">
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ components: {} })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    private toolList: any[] = [{ serialNumber: "测试螺杆-1" }];
}
</script>
