<template>
    <el-dialog
        custom-class="part-list-dialog"
        title="零件列表"
        :visible.sync="isDialogVisible"
    >
        <template v-if="!showDetail">
            <el-table :data="partList" border>
                <el-table-column
                    v-for="item in partAttr"
                    :key="item"
                    :label="item"
                >
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" @click="onDetail(scope)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>

        <template v-if="showDetail">
            <template slot="title">
                <el-page-header
                    title="返回零件列表"
                    @back="goBack"
                    content="零件详情"
                ></el-page-header>
            </template>
            <part-detail></part-detail>
        </template>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import PartDetail from "./detail/partDetail.vue";
import { partAttr } from "./field";
@Component({ components: { PartDetail } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    private partAttr = partAttr;
    private showDetail = false;
    private tabs = [
        { label: "自身", value: "self", component: "Self" },
        { label: "服役历史", value: "service", component: "Service" },
        { label: "维修历史", value: "maintain", component: "Maintain" },
    ];
    private partList = [{}];
    private activeTab = "self";
    private isDialogVisible = false;
    showDialog() {
        this.isDialogVisible = true;
    }
    private onDetail(scope: any) {
        this.showDetail = true;
    }
    private goBack() {
        this.showDetail = false;
    }
}
</script>
<style lang="scss">
.part-list-dialog {
    .el-dialog__body {
        padding: 5px 20px;
    }
}
</style>
