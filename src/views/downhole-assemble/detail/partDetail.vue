<template>
    <el-tabs v-model="activeTab" tab-position="top">
        <el-tab-pane
            v-for="item in tabs"
            :key="item.value"
            :label="item.label"
            :name="item.value"
        >
            <component :is="activePaneComponent"></component>
        </el-tab-pane>
    </el-tabs>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import Self from "./self.vue";
import Maintain from "./maintain.vue";
import Service from "./service.vue";
@Component({ components: { Self, Maintain, Service } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    private tabs = [
        { label: "自身", value: "self", component: "Self" },
        { label: "服役历史", value: "service", component: "Service" },
        { label: "维修历史", value: "maintain", component: "Maintain" },
    ];
    private activeTab = "self";
    get activePaneComponent() {
        let map = this.tabs.find((item) => item.value === this.activeTab);
        return map?.component;
    }
}
</script>
<style lang="scss">
.part-detail-dialog {
    .el-dialog__body {
        padding: 5px 20px;
    }
}
</style>
