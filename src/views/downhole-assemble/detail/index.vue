<template>
    <el-dialog
        custom-class="part-detail-dialog"
        title="零件详情"
        :visible.sync="isDialogVisible"
    >
        <part-detail></part-detail>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import PartDetail from "./partDetail.vue";
@Component({ components: { PartDetail } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    private tabs = [
        { label: "自身", value: "self", component: "Self" },
        { label: "服役历史", value: "service", component: "Service" },
        { label: "维修历史", value: "maintain", component: "Maintain" },
    ];
    private activeTab = "self";
    private isDialogVisible = false;
    get activePaneComponent() {
        let map = this.tabs.find((item) => item.value === this.activeTab);
        return map?.component;
    }
    showDialog() {
        this.isDialogVisible = true;
    }
}
</script>
<style lang="scss">
.part-detail-dialog {
    .el-dialog__body {
        padding: 5px 20px;
    }
}
</style>
