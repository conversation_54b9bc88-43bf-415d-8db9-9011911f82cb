<template>
    <div>
        <el-table :data="toolList" border>
            <el-table-column
                v-for="item in toolMaintainHistory"
                :key="item"
                :label="item"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { toolMaintainHistory } from "../field";
@Component({ components: {} })
export default class extends Vue {
    private toolMaintainHistory = toolMaintainHistory;
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private toolList: any[] = [];
}
</script>
