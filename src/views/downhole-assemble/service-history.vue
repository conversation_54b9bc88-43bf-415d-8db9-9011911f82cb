<template>
    <div>
        <div style="margin:10px 0">
            {{ name }}——{{ serialNumber }}——服役历史
        </div>
        <el-table :data="toolList" border>
            <el-table-column
                v-for="item in toolServiceHistory"
                :key="item"
                :label="item"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { toolServiceHistory } from "./field";
@Component({ components: {} })
export default class extends Vue {
    private toolServiceHistory = toolServiceHistory;
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private toolList: any[] = [];
}
</script>
