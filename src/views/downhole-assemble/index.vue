<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井下工具组装</div>
            </div>
            <div>
                工具类型：
                <el-select v-model="toolType" @change="onToolTypeChange">
                    <el-option
                        v-for="toolType in toolTypeList"
                        :key="toolType"
                        :label="toolType"
                        :value="toolType"
                    >
                    </el-option>
                </el-select>
                序列号：
                <el-select
                    v-model="serialNumber"
                    @change="onSerialNumberChange"
                >
                    <el-option
                        v-for="serialNumber in serialNumberList"
                        :key="serialNumber"
                        :label="serialNumber"
                        :value="serialNumber"
                    >
                    </el-option>
                </el-select>
                <template v-if="serialNumber">
                    <el-button
                        :type="operateType === 'LIST' ? 'plain' : 'primary'"
                        @click="
                            () => {
                                operateType = 'LIST';
                            }
                        "
                        style="margin-left:10px"
                    >
                        零件列表
                    </el-button>
                    <el-button
                        :type="operateType === 'ASSEMBLE' ? 'plain' : 'primary'"
                        @click="
                            () => {
                                operateType = 'ASSEMBLE';
                            }
                        "
                        style="margin-left:10px"
                    >
                        去组装
                    </el-button>
                    <el-button
                        :type="
                            operateType === 'DISASSEMBLE' ? 'plain' : 'primary'
                        "
                        @click="
                            () => {
                                operateType = 'DISASSEMBLE';
                            }
                        "
                        >去拆卸</el-button
                    >
                    <el-button
                        :type="
                            operateType === 'ASSEMBLE_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="
                            () => {
                                operateType = 'ASSEMBLE_HISTORY';
                            }
                        "
                        >装配历史</el-button
                    >
                    <el-button
                        :type="
                            operateType === 'SERVICE_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="
                            () => {
                                operateType = 'SERVICE_HISTORY';
                            }
                        "
                        >服役历史</el-button
                    >
                    <el-button
                        :type="
                            operateType === 'MAINTAIN_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="
                            () => {
                                operateType = 'MAINTAIN_HISTORY';
                            }
                        "
                        >维修历史</el-button
                    >
                </template>
            </div>
            <!-- <screw-template></screw-template> -->
            <assemble-module
                v-if="
                    toolType &&
                        (operateType === 'LIST' ||
                            operateType === 'TEMPLATE' ||
                            operateType === 'DISASSEMBLE' ||
                            operateType === 'ASSEMBLE')
                "
                :name="toolType"
                :operateType="operateType"
                :serialNumber="serialNumber"
            ></assemble-module>
            <tool-list-module
                v-if="toolType && !serialNumber"
                :name="toolType"
            ></tool-list-module>
            <tool-assemble-history
                v-if="operateType === 'ASSEMBLE_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-assemble-history>
            <tool-service-history
                v-if="operateType === 'SERVICE_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-service-history>
            <tool-maintain-history
                v-if="operateType === 'MAINTAIN_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-maintain-history>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AssembleModule from "./assemble.vue";
import ScrewTemplate from "./template/screw.vue";
import ToolListModule from "./toolList.vue";
import ToolAssembleHistory from "./assemble-history.vue";
import ToolServiceHistory from "./service-history.vue";
import ToolMaintainHistory from "./maintain-history.vue";
@Component({
    components: {
        ScrewTemplate,
        AssembleModule,
        ToolListModule,
        ToolAssembleHistory,
        ToolServiceHistory,
        ToolMaintainHistory,
    },
})
export default class extends Vue {
    private toolType = "";
    private toolTypeList: any[] = [
        "7LZ172×7.0/5Ⅺ型螺杆钻具",
        "5LZ244×7.0/5Ⅷ型螺杆钻具",
        "7LZ172ZD×7.0/5Ⅺ型振荡螺杆钻具",
        "5LZ244×7.0/5Ⅷ型振荡螺杆钻具",
        "172Ⅱ型水力振荡器",
        "其他仪器",
    ];
    private operateType = "TEMPLATE"; // TEMPLATE | LIST | ASSEMBLE | DISASSEMBLE
    private serialNumber = "";
    private serialNumberList: any[] = ["test_1", "test_2"];
    private onToolTypeChange() {
        this.operateType = "TEMPLATE";
        this.serialNumber = "";
    }
    private onSerialNumberChange() {
        this.operateType = "LIST";
    }
}
</script>
<style lang="scss" scoped>
.module-container {
    margin-top: 20px;
    .module {
        width: 160px;
        height: 160px;
        margin-right: 20px;
        float: left;
        background: red;
        border-radius: 8px;
    }
}
</style>
