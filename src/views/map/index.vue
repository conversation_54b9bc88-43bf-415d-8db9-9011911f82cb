<template>
  <div class="app-container" style="height: 100%; overflow: hidden">
    <div class="app-card" style="height: 100%">
      <!-- 地图容器 -->
      <div id="map-container"></div>

      <!-- 带选项卡的控制面板 -->
      <el-card class="map-controls" shadow="never">
        <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
          <el-tab-pane
            v-for="(label, key) in jobStatusMap"
            :key="key"
            :label="label"
            :name="key"
          >
             <!-- 选项卡现在主要用于控制地图过滤 -->
          </el-tab-pane>
        </el-tabs>
         <!-- 作业列表 -->
         <div v-if="!isLoading && filteredLocations.length > 0" class="job-list">
            <div class="job-list-header">
              <h4>作业列表 ({{ filteredLocations.length }})</h4>
            </div>
            <div class="job-list-content">
              <div
                v-for="(job, index) in filteredLocations"
                :key="job.jobNumber || index"
                class="job-item"
                :class="{ 'selected': selectedLocationInfo && selectedLocationInfo.jobNumber === job.jobNumber }"
                @click="locateToMarker(job)"
              >
                <div class="job-well">井号: {{ job.name || 'N/A' }}</div>
                <div class="job-number">作业号: <a href="javascript:void(0)" @click="(e) => goToJobDetail(job, e)" class="job-link">{{ job.jobNumber || '未知' }}</a></div>
                <div class="job-date">创建于: {{ formatShortDate(job.createTime) }}</div>
              </div>
            </div>
         </div>
          <div v-if="isLoading" class="marker-details">
             <p>正在加载数据...</p>
         </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader';
// 假设 API 工具类已存在并已配置
import { getJobMapInfoList } from '@/api/job.ts'; // 如果需要，调整路径

window._AMapSecurityConfig = {
  securityJsCode: 'edc32b937a046ae35a87b155942be84a',
};

let map = null;
let allMarkers = [];
let infoWindow = null;
let AMap = null; // 全局 AMap 对象

export default {
  name: 'GaodeMap',
  data() {
    return {
      allJobLocations: [],
      activeTab: 'inProgress', // 默认 'inProgress' 对应状态 0
      jobStatusMap: {
        inProgress: '进行中', // 状态 0
        completed: '已完工', // 状态 1
      },
      selectedLocationInfo: null,
      isLoading: false,
    };
  },
  computed: {
    filteredLocations() {
      const targetStatus = this.activeTab === 'inProgress' ? 0 : 1;
      return this.allJobLocations.filter(loc => loc.jobStatus === targetStatus);
    },
  },
  watch: {
     filteredLocations() {
        // 数据/选项卡更改后渲染标记点
        this.$nextTick(() => {
           this.renderMarkers(this.filteredLocations);
        });
     }
  },
  mounted() {
    this.initMap();

    // 添加全局函数供信息窗口使用
    window.goToJobDetail = (id) => {
      if (id) {
        this.$router.push(`/daily/detail?jobid=${id}`);
      } else {
        this.$message.warning('无法跳转：作业ID不存在');
      }
    };
  },
  beforeDestroy() {
    this.destroyMap();

    // 清除全局函数
    window.goToJobDetail = null;
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        // 考虑使用像 dayjs 这样的库来进行更好的格式化/时区处理
        return date.toLocaleString(); // 使用本地化格式
      } catch (e) {
        console.warn("日期格式化失败:", dateString)
        return dateString; // 回退到原始字符串
      }
    },
    formatShortDate(dateString) {
      if (!dateString) return 'N/A';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString(); // 只返回日期部分，不包含时间
      } catch (e) {
        console.warn("日期格式化失败:", dateString)
        return dateString; // 回退到原始字符串
      }
    },
    locateToMarker(job) {
      // 设置选中的作业信息，以显示详情
      this.selectedLocationInfo = job;

      // 如果没有有效的位置数据，则只显示详情，不进行地图定位
      if (!job || !job.position || !Array.isArray(job.position) || job.position.length !== 2 ||
          typeof job.position[0] !== 'number' || typeof job.position[1] !== 'number' ||
          isNaN(job.position[0]) || isNaN(job.position[1])) {
        console.warn('无法定位到标记点，位置数据无效:', job);
        this.$message.info(`作业 ${job.jobNumber} 没有有效的坐标信息，无法在地图上定位。`);
        return;
      }

      // 在标记点数组中查找对应的标记点
      const targetMarker = allMarkers.find(marker => {
        const markerData = marker.getExtData();
        return markerData && markerData.jobNumber === job.jobNumber;
      });

      if (targetMarker) {
        // 如果找到了标记点，则定位到该标记点
        map.setCenter(targetMarker.getPosition());
        map.setZoom(14); // 设置适当的缩放级别

        // 模拟点击标记点，显示信息窗口
        this.showInfoWindow(targetMarker);
      } else {
        // 如果没有找到标记点，则直接使用位置数据定位
        map.setCenter(job.position);
        map.setZoom(14);
      }
    },
    async initMap() {
      this.isLoading = true; // 开始加载指示器
      try {
        // 将加载的 AMap 对象赋值给全局变量
        AMap = await AMapLoader.load({
          key: 'e45e326226fffa633f5b14a2b168c9c9', // 使用您的实际密钥
          version: '2.0',
          plugins: ['AMap.Scale', 'AMap.InfoWindow'],
        });

        map = new AMap.Map('map-container', {
          viewMode: '2D',
          zoom: 4,
          center: [105.0, 35.0], // 中国的大致中心点
        });

        map.addControl(new AMap.Scale());

        infoWindow = new AMap.InfoWindow({
          offset: new AMap.Pixel(10, 100),
          closeWhenClickMap: true,
          autoMove: false, // 禁用自动移动，保持固定偏移
          isCustom: false,
          anchor: 'bottom-center', // 设置锚点位置为底部中心
          size: new AMap.Size(350, 300)
        });

        await this.fetchJobLocations(); // 地图设置后获取数据

        console.log('高德地图 JSAPI 加载成功');

      } catch (e) {
        console.error('高德地图加载失败:', e);
        this.$message.error('地图加载失败: ' + e.message);
        this.isLoading = false; // 确保出错时停止加载
      }
    },
    async fetchJobLocations() {
      this.isLoading = true;
      this.selectedLocationInfo = null; // 每次获取时清除详情
      try {
        const response = await getJobMapInfoList(); // 调用 API
        console.log('获取地图数据:', response);

        // 检查响应结构和成功代码（现在使用 200）
        if (response.data.code === 200) {
          const rawData = response.data.data || []; // 访问嵌套的数据数组

          // 处理原始数据，只保留有效的位置数据
          this.allJobLocations = rawData.map(loc => {
            // 如果已经有有效的position数组，直接使用
            if (loc.position && Array.isArray(loc.position) && loc.position.length === 2 &&
                typeof loc.position[0] === 'number' && typeof loc.position[1] === 'number') {
              return loc;
            }

            // 如果没有有效的position，但有description，尝试从中解析坐标
            if (loc.description) {
              try {
                // 替换中文逗号为英文逗号并分割
                const parts = loc.description.replace("，", ",").split(",");
                if (parts.length === 2) {
                  const lat = parseFloat(parts[0].trim());
                  const lng = parseFloat(parts[1].trim());
                  if (!isNaN(lat) && !isNaN(lng)) {
                    // 创建新的位置数组 [经度, 纬度]
                    loc.position = [lng, lat];
                    return loc;
                  }
                }
                // 如果解析失败，确保position为null
                loc.position = null;
              } catch (e) {
                console.warn(`无法解析坐标 for job ${loc.jobNumber}:`, loc.description);
                loc.position = null;
              }
            } else {
              // 如果description为null，确保position也为null
              loc.position = null;
              console.log(`作业 ${loc.jobNumber} 没有坐标信息（description为null）`);
            }

            return loc;
          });

          console.log('处理后的位置数据:', this.allJobLocations);
        } else {
          // 处理响应代码指示的 API 错误
          const errorMsg = response?.data?.message || 'Failed to fetch job map data.';
          console.error('API Error:', errorMsg, response);
          this.$message.error(`加载地图数据失败: ${errorMsg}`);
          this.allJobLocations = []; // 出错时清除数据
        }
      } catch (error) {
        console.error('Failed to fetch job map info:', error);
        this.$message.error('加载地图数据时发生网络错误');
        this.allJobLocations = []; // 出错时清除数据
      } finally {
        this.isLoading = false;
        // 首次数据加载尝试后渲染标记点，但保持全国视图
        this.$nextTick(() => {
            // 根据当前（可能为空）的过滤列表渲染标记点
            this.renderMarkers(this.filteredLocations);
            // 保持全国视图，不自动缩放
            map.setZoomAndCenter(4.5, [105.0, 35.0]);
        });
      }
    },
    renderMarkers(locationsToRender) {
      if (!map) return;

      map.remove(allMarkers); // 清除之前的标记点
      allMarkers = [];

      // 过滤出有效的位置数据
      const validLocations = locationsToRender.filter(loc => {
        const pos = loc.position;
        return pos && Array.isArray(pos) && pos.length === 2 &&
               typeof pos[0] === 'number' && typeof pos[1] === 'number' &&
               !isNaN(pos[0]) && !isNaN(pos[1]);
      });

      console.log(`有效位置数据: ${validLocations.length}/${locationsToRender.length}`);

      validLocations.forEach(loc => {
        // 根据作业状态设置不同的标记点样式
        const isCompleted = loc.jobStatus === 1; // 1表示已完工

        // 创建标记点配置
        const markerOptions = {
          position: loc.position,
          title: `作业号: ${loc.jobNumber || '未知'}`,
          map: map,
          extData: loc, // 存储完整的数据对象
        };

        // 根据作业状态设置不同的标记点样式
        if (isCompleted) {
          // 已完工作业使用已完井.svg图标
          markerOptions.icon = new AMap.Icon({
            // 使用项目中的SVG图片
            image: require('@/assets/images/map/已完井.svg'),
            size: new AMap.Size(24, 24),    // 控制图标尺寸
            imageSize: new AMap.Size(24, 24)   // 控制图片尺寸
          });
          // 清除content属性，防止影响图标显示
          markerOptions.content = null;
          markerOptions.offset = new AMap.Pixel(-12, -12); // 调整偏移量使标记点居中
        } else {
          // 进行中作业使用进行中.svg图标
          markerOptions.icon = new AMap.Icon({
            // 使用项目中的SVG图片
            image: require('@/assets/images/map/进行中.svg'),
            size: new AMap.Size(24, 24),    // 控制图标尺寸
            imageSize: new AMap.Size(24, 24)   // 控制图片尺寸
          });
          // 清除content属性，防止影响图标显示
          markerOptions.content = null;
          markerOptions.offset = new AMap.Pixel(-12, -12); // 调整偏移量使标记点居中
        }

        const marker = new AMap.Marker(markerOptions);

        marker.on('click', (e) => {
          this.showInfoWindow(e.target);
        });

        allMarkers.push(marker);
      });
       // 视图调整现在在 fetchJobLocations 完成后处理
    },
    showInfoWindow(marker) {
      if (!map || !infoWindow || !marker) return;

      const locationData = marker.getExtData();
      this.selectedLocationInfo = locationData; // 更新侧边栏

      // 先设置缩放级别，再设置中心点
      map.setZoom(14); // 设置适当的缩放级别

      // 将标记点位置设置为地图中心点
      map.setCenter(marker.getPosition());

      // 使用空值合并运算符 (??) 以获得更安全的默认值
      const content = `
        <div style="padding: 10px; font-size: 13px; min-width: 300px; max-height: 300px; overflow-y: auto;">
          <h4 style="margin-top: 0; margin-bottom: 5px;">井号: ${locationData.name || 'N/A'}</h4>
          <h4 style="margin-top: 5px; margin-bottom: 10px;">作业号: <a href="javascript:void(0)" onclick="window.goToJobDetail && window.goToJobDetail('${locationData.id}')" style="color: #409EFF; text-decoration: underline;">${locationData.jobNumber || '未知'}</a></h4>
          <hr style="margin: 5px 0 10px 0;">
          <div style="display: flex; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 140px; margin-right: 10px;">
              <p style="margin: 5px 0;"><strong>服务:</strong> ${locationData.serveType || 'N/A'}</p>
              <p style="margin: 5px 0;"><strong>趟数(总/失效):</strong> ${locationData.totalRun || 'N/A'} / ${locationData.failureRun || 'N/A'}</p>
              <p style="margin: 5px 0;"><strong>状态:</strong> ${this.jobStatusMap[locationData.jobStatus === 0 ? 'inProgress' : 'completed'] || '未知'}</p>
            </div>
            <div style="flex: 1; min-width: 140px;">
              <p style="margin: 5px 0;"><strong>开钻:</strong> ${locationData.dateIn || 'N/A'}</p>
              <p style="margin: 5px 0;"><strong>完钻:</strong> ${locationData.dateOut || 'N/A'}</p>
            </div>
          </div>
        </div>
      `;

      infoWindow.setContent(content);

      // 打开信息窗口
      infoWindow.open(map, marker.getPosition());

      // 确保信息窗口在视图内
      // 注意：这里不使用 setFitView 因为我们已经设置了缩放级别和中心点
    },
    handleTabClick() {
      // 选项卡更改触发计算属性，计算属性触发观察者，观察者调用 renderMarkers
      console.log('Tab changed to:', this.activeTab);

      // 关闭信息窗口
      if (infoWindow) {
        infoWindow.close();
      }

      // 清除选中的位置信息
      this.selectedLocationInfo = null;

      // 清除地图上的所有标记点
      map.remove(allMarkers);
      allMarkers = [];

      // 根据当前选中的选项卡渲染新的标记点
      this.$nextTick(() => {
        // 渲染过滤后的标记点
        this.renderMarkers(this.filteredLocations);

        // 保持全国视图，不自动缩放
        map.setZoomAndCenter(4, [105.0, 35.0]);
        console.log(`切换到 ${this.jobStatusMap[this.activeTab]} 状态，显示 ${allMarkers.length} 个标记点`);
      });
    },
    destroyMap() {
      if (map) {
        map.destroy();
        map = null;
        allMarkers = [];
        infoWindow = null;
      }
    },
    goToJobDetail(job, e) {
      // 阻止事件冒泡，防止触发父元素的点击事件
      if (e) e.stopPropagation();

      if (job && job.id) {
        this.$router.push(`/daily/detail?jobid=${job.id}`);
      } else {
        this.$message.warning('无法跳转：作业ID不存在');
      }
    },
  },
};
</script>

<style scoped>
.app-card {
  height: 100%;
  position: relative;
}

#map-container {
  position: absolute;
  top: 0; right: 0; bottom: 0; left: 0;
}

.map-controls {
  position: absolute;
  top: 10px; left: 10px;
  z-index: 10;
  width: 250px; /* 更宽以显示详情和列表 */
  background-color: rgba(255, 255, 255, 0.95); /* 更不透明 */
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0,0,0,.3);
  max-height: calc(100% - 20px);
  overflow-y: auto;
  border: none;
}

.job-link {
  color: #409EFF;
  text-decoration: none;
}

.job-link:hover {
  text-decoration: underline;
}

.job-well {
  font-weight: 500;
  margin-bottom: 3px;
  font-size: 14px;
}

.job-number {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 15px;
}
.map-controls >>> .el-card__body {
    padding: 0; /* 移除卡片主体内边距 */
}

/* Tab 样式 */
.map-controls >>> .el-tabs--card > .el-tabs__header {
   margin: 0;
   border-bottom: 1px solid #E4E7ED;
}
.map-controls >>> .el-tabs__nav {
    float: none;
    text-align: center;
    border: none;
}
.map-controls >>> .el-tabs__item {
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
    border: none !important;
}
.map-controls >>> .el-tabs__item.is-active {
    color: #409EFF;
    background-color: #ecf5ff;
}

/* 作业列表样式 */
.job-list {
  margin-bottom: 10px;
}

.job-list-header {
  padding: 10px 15px 5px;
  border-bottom: 1px solid #ebeef5;
}

.job-list-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.job-list-content {
  max-height: 300px;
  overflow-y: auto;
}

.job-item {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.job-item:hover {
  background-color: #f5f7fa;
}

.job-item.selected {
  background-color: #ecf5ff;
  border-left: 3px solid #409EFF;
}

.job-title {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 3px;
}

.job-subtitle {
  font-size: 12px;
  color: #606266;
  margin-bottom: 3px;
}

.job-date {
  font-size: 12px;
  color: #909399;
}

/* 详情部分样式 */
.marker-details {
    padding: 10px 15px;
    font-size: 13px;
    line-height: 1.7;
    border-top: 1px solid #ebeef5;
}
.marker-details h4 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
}
.marker-details p {
    margin: 4px 0;
    color: #606266;
}
.marker-details p strong {
    color: #303133;
    margin-right: 5px;
    display: inline-block;
    min-width: 80px; /* 对齐标签 */
}

.app-container {
  height: 100%;
  overflow: hidden;
}
</style>
