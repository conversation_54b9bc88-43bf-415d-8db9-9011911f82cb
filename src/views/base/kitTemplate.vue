<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">kit模板管理</div>
            </div>
            <div style="margin-bottom:12px">
                <span class="select-title">kit箱模板</span>
                <el-select v-model="templateId">
                    <el-option
                        v-for="(item, key) in templateList"
                        :key="key"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
                <el-button
                    icon="el-icon-search"
                    style="margin-left:20px;"
                    type="primary"
                    @click="onInitTemplate"
                    >查询
                </el-button>
                <el-button
                    style="margin-left:20px;"
                    type="primary"
                    @click="onSaveTemplate"
                    >保存模板
                </el-button>
            </div>

            <div class="simple-line"></div>
            <div style="margin-bottom:10px;">
                <span class="select-title">kit箱清单</span>
            </div>
            <el-table
                :data="kitTemplateItemList"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column label="编号" prop="id">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column label="零件名称">
                    <template slot="header">存货编码<span></span></template>
                    <template slot-scope="scope">
                        <span>
                            {{ scope.row.invCode }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header">
                        零件编号<span style="color:red;">*</span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.partNumber }}
                        </span>
                        <el-autocomplete
                            v-else
                            v-model="scope.row.partNumber"
                            :fetch-suggestions="querySearchPartNumber"
                            placeholder="partNumber"
                            value-key="partNumber"
                            @select="
                                onSelectPartNumber(
                                    scope.row.partNumber,
                                    scope.$index
                                )
                            "
                        >
                            <template slot-scope="{ item }">
                                <div class="name" style="line-height:1.2">
                                    {{ item.partNumber }}
                                </div>
                                <span style="font-size:14px">{{
                                    item.invName
                                }}</span>
                            </template>
                        </el-autocomplete>
                    </template>
                </el-table-column>
                <el-table-column label="零件名称">
                    <template slot="header">零件名称<span></span></template>
                    <template slot-scope="scope">
                        <span>
                            {{ scope.row.invName }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            <el-button type="text" @click="onEditRow(scope)">
                                编辑
                            </el-button>
                            <el-button type="text" @click="onDeleteRow(scope)">
                                删除
                            </el-button>
                        </span>
                        <span v-else>
                            <el-button type="text" @click="onSaveRow(scope)">
                                保存
                            </el-button>
                            <el-button type="text" @click="onCancelRow(scope)">
                                取消
                            </el-button>
                        </span>
                    </template>
                </el-table-column>
            </el-table>

            <el-button
                icon="el-icon-plus"
                style="float:right;margin-top:20px;"
                type="primary"
                @click="onAddRow"
                >添加元素
            </el-button>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetKitTemplateList, apiSaveTemplate } from "@/api/kit";
import { apiGetDictList } from "@/api/dict";
import { apiGetDeviceFuzzyList, apiGetDeviceList } from "@/api/warehouse";
import { IBusinessType } from "@/utils/constant";

interface IKitTemplateItem {
    templateItemId?: number;
    invCode?: string;
    partNumber?: string;
    invName?: string;
}

@Component({})
export default class extends Vue {
    private operType = "ADD";
    private templateId?: number = -1;
    private templateList: IBusinessType[] = [];
    private preObject: any = {};
    private editObject = { editRow: false, editRowIndex: -1 };
    private detailForm: IKitTemplateItem = {
        invCode: "",
        partNumber: "",
        invName: "",
    };
    private loading = false;
    private options = [];
    private kitTemplateItemList: IKitTemplateItem[] = [];
    private showDialog = false;

    mounted() {
        apiGetDictList({ businessType: 6 }).then((res) => {
            this.templateList = res.data.data;
            this.templateId = this.templateList[0].id;
        });
    }

    onSelectPartNumber(partNumber: string, index: number) {
        apiGetDeviceList({ partNumber }).then((res) => {
            let list = res.data.data.currentStockList || [{ invName: "" }];
            console.log(list);
            this.kitTemplateItemList[index].invName = list[0]
                ? list[0].invName
                : "";
            this.kitTemplateItemList[index].invCode = list[0]
                ? list[0].invCode
                : "";
        });
    }

    querySearchPartNumber(queryString: string, cb: any) {
        apiGetDeviceFuzzyList({ partNumber: queryString, size: 20 }).then(
            (res) => {
                let list = res.data.data.currentStockList || [];
                cb(list);
            }
        );
    }

    getTempleteItems() {
        apiGetKitTemplateList({})
            .then((res) => {})
            .catch((err) => {});
    }

    // getDeviceList(query) {
    //     if(query == '') return;

    //     let params={current:1,size:100};
    // //    if(this.searchForm.invName!="")
    // //     params.invName=this.searchForm.invName;
    //     this.loading = true;
    //     params.partNumber=query;

    //    apiGetDeviceList(params).then(res=>{
    //        let {code,message,data}=res.data;
    //        this.options=data.currentStockList;
    //        this.loading = false;
    //        console.log(this.options)

    //    }).catch(err=>{})

    // onSaveInfo(param: string, rowIndex: number) {
    //     //console.log(param)
    //     for (let i = 0; i < this.options.length; i++) {
    //         if (this.options[i].partNumber == param) {
    //             this.kitTemplateItemList[rowIndex].invCode = this.options[
    //                 i
    //             ].invCode;
    //             this.kitTemplateItemList[rowIndex].invName = this.options[
    //                 i
    //             ].invName;
    //             this.kitTemplateItemList[rowIndex].partNumber = this.options[
    //                 i
    //             ].partNumber;
    //             break;
    //         }
    //     }
    // }

    onSaveTemplate() {
        if (this.kitTemplateItemList.length == 0) {
            this.$message.error("请添加清单");
            return;
        }
        let params = {
            kitTemplate: {
                templateType: this.templateId,
                templateName: this.templateType,
            },
            kitTemplateItemList: this.kitTemplateItemList,
        };
        apiSaveTemplate(params)
            .then((res) => {
                let { code, message, data } = res.data;
                if (code == 200) {
                    this.$message.success("保存成功");
                }
            })
            .catch((err) => {});
    }

    // }
    private onInitTemplate() {
        apiGetKitTemplateList({ templateType: this.templateId })
            .then((res) => {
                let { data, code, message } = res.data;
                if (data.length > 0)
                    this.kitTemplateItemList = data[0].kitTemplateItemList;
            })
            .catch((err) => {});
    }

    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.kitTemplateItemList.push({
            invCode: undefined,
            partNumber: undefined,
            invName: undefined,
        });
        this.editObject.editRow = true;
        this.editObject.editRowIndex = this.kitTemplateItemList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.kitTemplateItemList.splice(scope.$index, 1);

        //delete to database
    }

    private onSaveRow(scope: any) {
        if (scope.row.partNumber === "" || scope.row.partNumber == undefined) {
            this.$message.error("请填写零件号");
            return;
        }
        let err = false;
        for (let i = 0; i < this.kitTemplateItemList.length; i++) {
            if (i == scope.$index) continue;
            if (this.kitTemplateItemList[i].invCode == scope.row.invCode) {
                err = true;
                break;
            }
        }
        if (err) {
            this.$message.error("有重复项");
            return;
        }
        this.editObject.editRowIndex = -1;
        // 渲染零件名称/存货编码
        apiGetDeviceList({ partNumber: scope.row.partNumber }).then((res) => {
            let list = res.data.data.currentStockList || [{ invName: "" }];
            this.kitTemplateItemList[scope.$index].invName = list[0]
                ? list[0].invName
                : "";
            this.kitTemplateItemList[scope.$index].invCode = list[0]
                ? list[0].invCode
                : "";
        });
        //save to database
        // apiAddKitTemplateItem({templateId:this.templateId,invCode:scope.row.invCode}).then(res=>{
        // }).catch(err=>{})
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.kitTemplateItemList[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.kitTemplateItemList.pop();
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>

<style lang="scss" scoped>
.select-title {
    display: inline-block;
    padding-right: 8px;
    padding-right: 8px;
    font-size: 16px;
}
</style>
