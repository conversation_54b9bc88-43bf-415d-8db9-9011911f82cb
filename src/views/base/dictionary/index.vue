<template>
    <div class="app-container">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">字典管理</div>
                <el-button
                    v-if="$checkBtnPermission('sys:dictionary:add')"
                    icon="el-icon-plus"
                    type="primary"
                    @click="onAdd"
                >
                    新增字典
                </el-button>
            </div>
            <div>
                <span style="font-size: 16px">字典业务类型: </span>
                <el-select
                    v-model="businessType"
                    clearable
                    placeholder="请选择"
                    @change="onBusinessTypeChange"
                    style="margin-left: 20px"
                >
                    <el-option
                        v-for="(value, key) in BUSINESS_TYPE"
                        :key="key"
                        :label="value"
                        :value="Number(key)"
                    ></el-option>
                </el-select>
            </div>
            <div class="simple-line"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <!-- <el-table-column prop="id" label="编号"></el-table-column> -->
                <el-table-column label="字典名称" prop="name"></el-table-column>
                <el-table-column label="业务类型" prop="businessType">
                    <template slot-scope="scope">
                        {{ BUSINESS_TYPE[scope.row.businessType] }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                    <template slot-scope="scope">
                        <el-button
                            v-if="$checkBtnPermission('sys:dictionary:edit')"
                            icon="el-icon-edit"
                            size="medium"
                            style="padding: 6px 6px"
                            type="success"
                            @click="onEdit(scope)"
                            >编辑
                        </el-button>
                        <el-button
                            v-if="$checkBtnPermission('sys:dictionary:delete')"
                            icon="el-icon-delete"
                            size="medium"
                            style="padding: 6px 6px"
                            type="danger"
                            @click="onDelete(scope)"
                            >删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            :title="dialogTitle"
            :close-on-click-modal="false"
            :visible.sync="showDialog"
            width="400px"
        >
            <el-form ref="form" :model="form" label-width="100px">
                <el-form-item label="字典名称：" prop="name" required>
                    <el-input v-model="form.name" style="width: 90%"></el-input>
                </el-form-item>
                <el-form-item label="业务类型：" prop="businessType" required>
                    <el-select v-model="form.businessType" style="width: 90%">
                        <el-option
                            v-for="(value, key) in BUSINESS_TYPE"
                            :key="key"
                            :label="value"
                            :value="Number(key)"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { BUSINESS_TYPE } from "@/utils/constant";
import { Form as ElForm } from "element-ui";
import {
    apiAddDict,
    apiDeleteDict,
    apiGetDictList,
    apiUpdateDict,
} from "@/api/dict";
import { IBusinessType } from "@/utils/constant";

@Component({})
export default class extends Vue {
    private BUSINESS_TYPE = BUSINESS_TYPE;
    private tableData: any[] = [];
    private showDialog = false;
    private businessType: any = null;
    private form: IBusinessType = {
        id: -1,
        name: "",
        businessType: undefined,
    };

    get dialogTitle(): string {
        return this.operType === "ADD" ? "新增" : "编辑";
    }

    mounted() {
        this.getDictList();
    }

    async getDictList() {
        if (this.businessType == undefined) return;
        await apiGetDictList({ businessType: this.businessType }).then(
            (res) => {
                this.tableData = res.data.data;
            }
        );
    }
    private onBusinessTypeChange() {
        this.getDictList();
        // apiGetDictList({ businessType: this.form.businessType }).then((res) => {
        //     console.log(res);
        //     let {data,message,code}=res.data;
        //     this.tableData = data;
        //     // this.getDictList();
        // });
    }

    private onAdd() {
        this.operType = "ADD";
        this.form = {
            name: "",
            businessType: this.businessType,
        };
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }

    private onEdit(scope: any) {
        this.operType = "EDIT";
        this.form = Object.assign(this.form, scope.row);
        this.showDialog = true;
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteDict({ id: scope.row.id }).then(() => {
                    this.getDictList();
                });
            })
            .catch(() => {});
    }

    private onCancel() {
        this.showDialog = false;
    }

    private onConfirm() {
        (this.$refs.form as ElForm).validate((valid) => {
            if (valid) {
                if (this.operType === "EDIT") {
                    apiUpdateDict(this.form).then((res) => {
                        this.getDictList();
                        this.showDialog = false;
                    });
                } else {
                    apiAddDict(this.form).then((res) => {
                        this.getDictList();
                        this.showDialog = false;
                    });
                }
            }
        });
    }
}
</script>
