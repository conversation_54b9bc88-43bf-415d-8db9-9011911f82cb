<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井信息管理</div>
                <el-button
                    type="primary"
                    @click="onGetNewData"
                    style="float:right;margin-right: 20px"
                >
                    同步ERP数据
                </el-button>
            </div>
            <el-autocomplete
                v-model="wellNumberKey"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入内容"
                @select="handleSelect"
                clearable
                @clear="getWellList"
            ></el-autocomplete>
            <div class="simple-line"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                >
                    <template slot-scope="scope">
                        <router-link style="color: #3370ff;" :to="`/daily/index?wellNumber=${scope.row.wellNumber}`" v-if="p_JobListView">{{ scope.row.wellNumber }}</router-link>
                        <span v-else>{{ scope.row.wellNumber }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    label="井别"
                    prop="wellCategoryStr"
                ></el-table-column> -->
                <el-table-column
                    label="井型"
                    prop="wellTypeStr"
                ></el-table-column>
                <el-table-column label="区块" prop="blocks"></el-table-column>
                <el-table-column label="省份" prop="province"></el-table-column>
                <el-table-column
                    label="描述"
                    prop="description"
                ></el-table-column>
                <el-table-column label="操作" width="180" v-if="p_WellEdit||p_WellDelete">
                    <template slot-scope="scope">
                        <el-button
                            icon="el-icon-edit"
                            size="medium"
                            style="padding:6px 6px"
                            type="success"
                            @click="onEdit(scope)"
                            v-if="p_WellEdit"
                        >
                            编辑
                        </el-button>
                        <el-button
                            icon="el-icon-delete"
                            size="medium"
                            style="padding:6px 6px"
                            type="danger"
                            @click="onDelete(scope)"
                            v-if="p_WellDelete"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog
            :title="dialogTitle"
            :close-on-click-modal="false"
            :visible.sync="showDialog"
            width="1200px"
        >
            <el-form ref="form" :model="form" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item
                            label="井号："
                            prop="wellNumber"
                        >
                            <el-input
                                v-model="form.wellNumber"
                                disabled
                                style="width:100%"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item label="井别：" prop="wellCategory">
                            <el-select
                                v-model="form.wellCategory"
                                style="width:100%"
                            >
                                <el-option
                                    v-for="(item, key) in wellCategoryList"
                                    :key="key"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                        <el-form-item label="井型：" prop="wellType">
                            <el-select
                                v-model="form.wellType"
                                style="width:100%"
                            >
                                <el-option
                                    v-for="(item, key) in wellTypeList"
                                    :key="key"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="区块：" prop="blocks">
                            <el-input
                                v-model="form.blocks"
                                style="width:100%"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="省份：" prop="province">
                            <el-input
                                v-model="form.province"
                                style="width:100%"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="描述：" prop="description">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 4 }"
                                v-model="form.description"
                                style="width:100%"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiWellListPage, apiWellAdd, apiWellDelete_v2, apiWellUpdate, apiWellListFuzzy } from "@/api/wellInfo";
import { Form as ElForm } from "element-ui/types/element-ui";
import { apiGetDictList } from "@/api/dict";
import { IBusinessType } from "@/utils/constant";
import { ConstantModule } from "@/store/modules/constant";
import { apiSyncGroupData } from "@/api/erp";
interface IForm {
    id?: number;
    wellNumber?: string;
    wellCategory?: number;
    wellType?: number;
    blocks?: string;
    province?: string;
    description?: string;
}

@Component({})
export default class extends Vue {
    private wellNumberKey = "";
    private operType = "ADD";
    private showDialog = false;
    private tableData: any[] = [];
    private wellCategory?: number = -1;
    private wellCategoryList: IBusinessType[] = [];
    private wellTypeList: IBusinessType[] = [];
    private wellType?: number = -1;
    private form: IForm = {
        id: -1,
        wellNumber: "",
        wellCategory: -1,
        wellType: -1,
        blocks: "",
        province: "",
        description: "",
    };
    private total = 1;
    private currentPage = 1;

    get dialogTitle(): string {
        return this.operType === "ADD" ? "新增" : "编辑";
    }
    get p_WellEdit(){
        return this.$checkBtnPermission('sys:well:edit');
    }
    get p_WellDelete(){
        return this.$checkBtnPermission('sys:well:delete');
    }
    get p_JobListView(){
        return this.$checkBtnPermission('sys:job:list:view');
    }
    async mounted() {
        await this.getWellList();
        apiGetDictList({ businessType: 2 }).then((res) => {
            this.wellTypeList = res.data.data;
            // this.wellType = this.wellTypeList[0].id;
        });
        apiGetDictList({ businessType: 3 }).then((res) => {
            this.wellCategoryList = res.data.data;
            // this.wellCategory = this.wellCategoryList[0].id;
        });
    }
    handleSelect(){
        this.getWellList()
    }
    private querySearchAsync(qs, cb){
        const form = new FormData();
        form.append("wellNumberKey", qs || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item.wellNumber})))
        })
    }
    async getWellList() {
        apiWellListPage({wellNumber:this.wellNumberKey||undefined},{ current: this.currentPage, size: 10 }).then(res=>{
            this.tableData = res.data?.data?.records || [];
            this.total = res.data.data.total;
        })
        // this.tableData = await ConstantModule.getWellList(true);
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getWellList();
    }
    private onAddWell() {
        this.operType = "ADD";
        this.form = {
            id: undefined,
            wellNumber: "",
            wellCategory: undefined,
            wellType: undefined,
            blocks: "",
            province: "",
            description: "",
        };
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }

    private onEdit(scope: any) {
        this.operType = "EDIT";
        this.form = Object.assign(this.form, scope.row);
        this.showDialog = true;
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiWellDelete_v2({
                    wellInfo:{
                        wellId:scope.row.wellId
                    },
                    isBlock:true //这个为true则会加入黑名单, 黑名单的井下次不会再被同步进数据库了
                }).then(() => {
                    this.getWellList();
                });
            })
            .catch(() => {});
    }

    private onCancel() {
        this.showDialog = false;
    }

    private onConfirm() {
        (this.$refs.form as ElForm).validate((valid) => {
            if (valid) {
                if (this.operType === "EDIT") {
                    apiWellUpdate(this.form).then((res) => {
                        this.getWellList();
                        this.showDialog = false;
                    });
                } else {
                    apiWellAdd(this.form).then((res) => {
                        this.getWellList();
                        this.showDialog = false;
                    });
                }
            }
        });
    }
    onGetNewData() {
        let form = new FormData() as any;
        form.append("taskGroup", "ERP_WELL_INFO_SYNC_TASK");
        apiSyncGroupData(form)
            .then((res) => {
                // let {data,message,code}=res;
                if (res.data.code == 200) {
                    this.$message.success("同步成功");
                    this.getWellList();
                }
            })
            .catch((err) => {});
    }
}
</script>
