<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>仪器趟次信息</span>
            </div>
            <el-form :model="searchForm" label-width="auto" inline>
                <el-row :gutter="20">
                    <!-- <el-col :span="6"> -->
                        <el-form-item label="井号">
                            <el-select
                                v-model="searchForm.wellNumber"
                                @change="handleSelect"
                                filterable
                                remote
                                placeholder=""
                                :remote-method="remoteWellNumberMethod"
                                style="width:100%"
                            >
                                <el-option
                                v-for="item in fuzzyWellNumberList"
                                :key="item.value"
                                :label="item.value"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    <!-- </el-col> -->
                    <!-- <el-col :span="6"> -->
                        <el-form-item label="趟次">
                            <el-select v-model="searchForm.run">
                                <el-option
                                    v-for="item in runList"
                                    :key="item.run"
                                    :label="item.run"
                                    :value="item.run">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    <!-- </el-col> -->
                    <span v-if="maxBht" style="margin-left:10px;line-height:36px">本趟次最高温度：{{maxBht}}℃</span>
                </el-row>
            </el-form>
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 10px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column label="仪器名称" prop="invName" width="140">
                </el-table-column>
                <el-table-column label="序列号" prop="serialNumber" width="140">
                </el-table-column>
                <el-table-column label="起始时间" prop="start" width="100">
                </el-table-column>
                <el-table-column label="总循环时间" prop="total" width="100">
                </el-table-column>
                <el-table-column label="返修原因" prop="returnReason">
                </el-table-column>
                <el-table-column label="返修备注" prop="note">
                </el-table-column>
                <el-table-column label="维修工单号" prop="mwdId" width="160">
                    <template slot-scope="{row}">
                        <router-link tag="a" style="color: blue" :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" v-if="p_MwdInfoView">{{ row.mwdNumber }}</router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="检查发现" prop="findings">
                </el-table-column>
                <el-table-column label="维修措施" prop="repairAction">
                </el-table-column>
            </el-table>
            <div class="summary">
                <div class="title">
                    <span style="margin-right:20px">趟次总结</span>
                    <template v-if="p_MwdUpdateSummary">
                        <el-button v-if="!isEditSummary" type="text" @click="onEditSummary">编辑</el-button>
                        <template v-else>
                            <el-button type="text" @click="onSaveSummary">保存</el-button>
                            <el-button type="text" @click="onCancelSummary">取消</el-button>
                        </template>
                    </template>
                </div>
                <div class="content" v-if="!isEditSummary">
                    {{mwdSummary || "暂无"}}
                </div>
                <el-input v-else v-model="mwdSummary" type="textarea"></el-input>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { apiGetMwdRunDeviceInfo, apiUpdateMwdRunDeviceSummary } from "@/api/mwd";
@Component({})
export default class extends Vue {
    private originData:any[] = [];
    private searchForm:any = {
        wellNumber:"",
        run:''
    }
    private fuzzyWellNumberList:any[] = []
    private isEditSummary = false;
    get runList() {
        return this.originData.map(item=>({run: item.run}))
    }
    get maxBht(){
        return this.originData.find(item=>item.run==this.searchForm.run)?.maxBht || ""
    }
    get tableData(){
        return this.originData.find(item=>item.run==this.searchForm.run)?.toolList || []
    }
    get mwdSummary(){
        return this.originData.find(item=>item.run==this.searchForm.run)?.mwdSummary || ""
    }
    set mwdSummary(value){
        this.originData.find(item=>item.run==this.searchForm.run).mwdSummary = value
    }
    get jobId(){
        return this.originData.find(item=>item.run==this.searchForm.run)?.jobId || ""
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    get p_MwdUpdateSummary(){
        return this.$checkBtnPermission('sys:mwd:summary:update');
    }
    mounted() {
        const { wellNumber, run } = this.$route.query
        this.searchForm.wellNumber = wellNumber;
        if(!this.searchForm.wellNumber){
            return
        }
        this.searchForm.run = run ? +run : "";
        this.initData();
    }
    
    handleSelect(){
        this.initData()
    }
    async initData() {
        const form = new FormData();
        form.append("wellNumber", this.searchForm.wellNumber);
        apiGetMwdRunDeviceInfo(form).then(res=>{
            this.originData = res.data.data || [];
            if(!this.searchForm.run){
                this.searchForm.run = this.originData[0]?.run || ""
            }
        })
    }
    
    private onViewMwdInfo(row:any){
        this.$router.push(`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`)

    }
    
    private remoteWellNumberMethod(query){
        const form = new FormData();
        form.append("wellNumberKey", query || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            this.fuzzyWellNumberList = data.map(item=>({value:item.wellNumber}))
        })
    }
    private onEditSummary(){
        this.isEditSummary = true
    }
    private onCancelSummary(){
        this.initData();
        this.isEditSummary = false
    }
    private onSaveSummary(){
        apiUpdateMwdRunDeviceSummary({
            wellNumber: this.searchForm.wellNumber,
            run: this.searchForm.run,
            mwdSummary: this.mwdSummary,
            jobId: this.jobId,
        }).then(()=>{
            this.initData();
            this.isEditSummary = false
        })
    }
}
</script>

<style lang="scss" scoped>
.summary {
    margin-top: 10px;
    margin-left: 8px;
    .title{
        font-weight: 400;
        font-size: 18px;
        color: rgb(67, 86, 152);
        font-weight: bold;
    }
    .content{
        margin-top: 4px;
        font-size: 16px;
    }
}
</style>