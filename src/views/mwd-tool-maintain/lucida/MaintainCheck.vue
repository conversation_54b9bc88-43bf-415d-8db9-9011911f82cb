<template>
    <div>
        <!-- <div>
            <el-button
                :type="currentField === item.field ? 'primary' : 'default'"
                v-for="item in tabList"
                :key="item.field"
                @click="onClickTab(item)"
            >
                {{ item.title }}
            </el-button>
            <span style="float: right;">
                <span v-if="!isEdit">
                    <el-button type="primary" @click="isEdit = true">编辑</el-button>
                </span>
                <span v-else>
                    <el-button @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="onSave">保存</el-button>
                </span>
            </span>
        </div>
        <quill-editor
            class="lucida-quill-editor"
            style="width: 100%; margin-top: 10px;"
            ref="quillEditorRef"
            v-model="faultDiagnosis[currentField]"
            :options="options"
            v-if="isEdit"
        />
        <ImageViewer style="height: calc(100vh - 260px);overflow: auto;" v-html="faultDiagnosis[currentField]" v-else></ImageViewer> -->
        <span style="float: right">
            <span v-if="!isEdit">
                <el-button type="primary" @click="isEdit = true">编辑</el-button>
            </span>
            <span v-else>
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSave">保存</el-button>
            </span>
        </span>
        <div class="tool-card-title" style="margin-left: 0px;padding-bottom: 10px;">维修检查</div> 
        <el-form :model="faultDiagnosis" label-position="top" class="lucida-form">
            <el-row :gutter="20">
                <el-col :span="12" v-for="item in tabList" :key="item.field">
                    <el-form-item :label="item.title">
                        <quill-editor
                            class="lucida-quill-editor"
                            style="width: 100%;"
                            :ref="item.field"
                            v-model="faultDiagnosis[item.field]"
                            :options="getSetting(item.field)"
                            @focus="onEditorFocus(item.field)"
                            v-if="isEdit"
                        />
                        <ImageViewer class="lucida-quill-div" v-html="faultDiagnosis[item.field]" v-else></ImageViewer>
                    </el-form-item>
                </el-col>
            </el-row>
            
        </el-form>
        <form
            action
            method="post"
            enctype="multipart/form-data"
            id="uploadFormMulti"
        >
            <input
                style="display: none"
                id="ghostUploader"
                type="file"
                name="file"
                multiple
                accept="image/jpg,image/jpeg,image/png,image/gif"
                @change="uploadImg()"
            />
        </form>
        <div class="tool-card-title" style="margin-left: 0px;padding-bottom: 10px;">文件管理</div>
        <FileManage />
    </div>
</template>
<script>
import { apiUploadFile } from '@/api/file';
import ImageViewer from "@/components/ImageViewer/index.vue";
import FileManage from './FileManage.vue';
export default {
    name: "MaintainCheck",
    components: {
        ImageViewer, FileManage
    },
    props: {
        originForm: {
            type: Object,
            default: () => ({mwdRepairCheckData:{checkData:{faultDiagnosis:{}}}}),
        },
    },
    data(){
        return {
            isEdit: false,
            currentField: 'preliminaryTestResult',
            tabList: [
                {
                    name: '',
                    field: 'preliminaryTestResult',
                    title: '入场初步检查'
                },
                {
                    name: '',
                    field: 'testResult',
                    title: '检测与测试结果'
                },
                {
                    name: '',
                    field: 'dataAnalysis',
                    title: '下载数据分析'
                },
                {
                    name: '',
                    field: 'repairAdvice',
                    title: '维修措施'
                },
                {
                    name: '',
                    field: 'rootReasonAndImproveMethod',
                    title: '根本原因与改善方法'
                },
            ],
            options: {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: [["image"]],
                        handlers: {
                            image: () => {
                                let quill = this.$refs.quillEditorRef[0].quill;
                                const ghostUploaderDom = document.getElementById("ghostUploader");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click(); 
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    }
                },
            }
                
        }
    },
    computed: {
        faultDiagnosis(){
            return this.originForm?.mwdRepairCheckData?.checkData?.faultDiagnosis || {};
        },
    },
    methods: {
        getSetting(editorRef) {
            return {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: ["image"],
                        handlers: {
                            image: () => {
                                this.currentEditor = editorRef;
                                let quill = this.$refs[editorRef][0].quill;
                                const ghostUploaderDom = document.getElementById("ghostUploader");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click(); 
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    }
                },
            };
        },
        onCancel(){
            this.isEdit = false;
            this.$emit('getData');
        },
        onSave(){
            this.$emit('update');
            this.isEdit = false;

        },
        uploadImg() {
            let quill = this.$refs[this.currentEditor][0].quill;
            let files = document.getElementById("ghostUploader").files;
            Promise.all(Array.from(files).map(file=>{
                const form = new FormData();
                form.append("file", file);
                return apiUploadFile(form).then(res=>{
                    return res.data.data;
                })
            })).then((urls) => {
                urls.forEach((url) => {
                    quill.insertEmbed(this.insertPosition, "image", url);
                    this.insertPosition += 1;
                });
                quill.setSelection(this.insertPosition);
            });
        },
        onClickTab(item) {
            this.currentField = item.field;
        },
        
        handlePateImage(event){
            const quill = this.$refs[this.currentEditor][0].quill;
            var items = (event.clipboardData || event.originalEvent.clipboardData)
                .items;
            for (let index in items) {
                var item = items[index];
                if (item.kind === "file") {
                    event.preventDefault();
                    var file = item.getAsFile();
                    const form = new FormData();
                    form.append("file", file);
                    apiUploadFile(form).then((res) => {
                        quill.insertEmbed(this.insertPosition, "image", res.data.data);
                        quill.setSelection(this.insertPosition + 1);
                    });
                }
            }
        },
        onEditorFocus(editorRef) {
            this.currentEditor = editorRef;
            const quill = this.$refs[editorRef][0].quill;
            quill.root.addEventListener("paste", this.handlePateImage);
        }
    }
}
</script>
<style lang="scss">
.lucida-quill-editor{
    .ql-toolbar{
        padding: 0;
    }
    .ql-container{
        height: 360px;
    }
}
.lucida-form{
    .el-form-item__label{
        padding: 0;
    }
}
</style>
<style lang="scss">
.lucida-quill-div{
    height: 400px;
    overflow: auto;
    background-color: #fff;
    line-height: 1.3;
    p{
        margin: 0;
    }
    img{
        max-width: calc(100%) !important;
    }
}
</style>