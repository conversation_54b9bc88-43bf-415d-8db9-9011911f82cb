<template>
    <div>
        <el-button type="primary" :loading="isFileUploading"  @click="onClickUpload">上传</el-button>
        <div class="file-list" style="margin-top: 10px;">
            <div class="file-item" v-for="item in fileList" :key="item.massId">
                <span :title="item.massFileName" class="file-title">
                    {{ item.massFileName }}
                </span>
                <i @click="onPreviewFile(item.massId)" class="el-icon-view oper-icon"></i>
                <a :href="item.massFilePath">
                    <i class="el-icon-download oper-icon"></i>
                </a>
                <i @click="onDeleteMwdFile(item.massId)" class="el-icon-delete oper-icon"></i>
            </div>
        </div>
        <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadMwdFile"
            >
            <div ref="ghostFileUploadRef" style="width:0;height:0"></div>
        </el-upload>
    </div>
</template>
<script>
import { apiUploadMwdMassFile, apiGetMwdMassFileList, apiDeleteMwdMassFile, apiPreviewFile } from "@/api/mwd";
export default {
    data() {
        return {
            isFileUploading: false,
            fileList: [],
            mwdId: "",
        }
    },
    mounted(){
        this.mwdId = this.$route.query.mwdId || "";
        if(this.mwdId){
            this.getMwdFileList()
        }
    },
    methods: {
        onDeleteMwdFile(massId){
            this.$confirm("确认删除该文件?", "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(()=>{
                const form = new FormData();
                form.append('type', 'MASSID');
                form.append('idList', [massId].toString())
                apiDeleteMwdMassFile(form).then(()=>{
                    this.getMwdFileList()
                    this.$message.success('操作成功！')
                })
            })
        },
        onUploadMwdFile(file){
            this.isFileUploading  = true
            const form = new FormData();
            form.append('mwdId', String(this.mwdId));
            form.append('file', file);
            apiUploadMwdMassFile(form).then(()=>{
                this.getMwdFileList();
                this.$message.success('操作成功！')
            }).finally(()=>{
                this.isFileUploading = false
            })
        },
        getMwdFileList(){
            apiGetMwdMassFileList({mwdId: this.mwdId},{current: 1, size: 9999}).then(res=>{
                this.fileList = res.data?.data?.records || [];
            })
        },
        onClickUpload(){
            this.$refs.ghostFileUploadRef.click();
        },
        onPreviewFile(massId){
        apiPreviewFile({massId}).then(res=>{
            const previewPath = res.data.data?.previewPath;
            if(previewPath){
                window.open(previewPath);
            }
        })
    }
    },
}

</script>
<style lang="scss" scoped>
.file-list{
    
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 380px;
        margin-bottom: 10px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>