<template>
    <div style="height: 100%;position: relative;">
        <LucidaTreeGraph :template="treeGraphTemplate" :actualData="treeGraphActualData" ref="lucidaTreeGraphRef" />
        <div style="position: absolute;top: 0;right:0px">
             <template v-if="status === 'READ'">
                <el-button @click="assemble" type="primary">组装</el-button>
                <el-button @click="disassemble" type="primary">拆卸</el-button>
             </template>
            <template v-else>
                <el-button @click="onCancelOperate">取消</el-button>
                <el-button @click="onConfirmOperate" type="primary">{{ `确认${ status=='ASSEMBLE' ? '组装' : '拆卸' }` }}</el-button>
            </template>
        </div>
        <CreateEaWorkOrderDialog ref="createEaWorkOrderDialogRef" />
    </div>
</template>
<script>
import { deepCopyTree, findNodesWithParents } from "@/utils/tree";
import { ASSEMBLE_NODE, DISASSEMBLE_NODE } from "@/components/LucidaTreeGraph/utils/nodes"
import LucidaTreeGraph from "@/components/LucidaTreeGraph";
import { apiAssembleLucida } from "@/api/lucida";
import CreateEaWorkOrderDialog from "./CreateEaWorkOrderDialog.vue";
export default {
    components: {
        LucidaTreeGraph,
        CreateEaWorkOrderDialog
    },
    props: {
        outTree: {
            type: Object,
            default: null
        },
        treeGraphTemplate: {
            type: Object,
            default: null
        },
        detailForm: {
            type: Object,
            default: () => ({}),
        },
        originForm: {
            type: Object,
            default: () => ({}),
        },
    },
    data(){
        return {
            status: 'READ',
            treeGraphActualData: null,
        }
    },
    computed: {
        p_CreateEaWorkOrder(){
            return this.$checkBtnPermission(`sys:ea:create:all`)
        }
    },
    async mounted() {
        this.treeGraphActualData = deepCopyTree(this.outTree);
        this.$nextTick(()=>{
            this.$refs.lucidaTreeGraphRef.initData('READ');
        })
    },
    methods: {
        read(){
            this.status = 'READ';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('READ');
            })
        },
        assemble(){
            this.status = 'ASSEMBLE';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('ASSEMBLE');
            })
        },
        disassemble(){
            this.status = 'DISASSEMBLE';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('DISASSEMBLE');
            })
        },
        onCancelOperate(){
            this.read();
        },
        purifyNodeData(node){
            const fieldsToRemove = [
                'children',
                'id',
                'type',
                'collapsed',
                'isGhostHead',
                'isAssembleHead',
                'isDisassembleHead',
                'nodeType',
                'DISASSEMBLE_NODE_COLOR',
                'ASSEMBLE_NODE_COLOR',
                'style',
                'x',
                'y',
            ];
            fieldsToRemove.forEach(field => {
                delete node[field];
            });
            return node;
        },
        onConfirmOperate(){
            // 拆卸的时候直接删除parentComponentId，组装的时候要关联相应parentComponentId，也要处理sort字段，这个字段数据最好从模板数据中得到
            // 业务上第二层的数据都没有parentComponentId，要单独处理，而且要过滤掉GHOST_NODE
            // TODO: 抽象出组件逻辑，尽量不要在这里操作组件内部数据
            const inComponent = [];
            const outComponent = [];
            const inComponentFirstLevel = [];
            const outComponentFirstLevel = [];
                            
            const graphData = this.$refs.lucidaTreeGraphRef.graph.save();
            if(this.status === 'ASSEMBLE'){
                let list = findNodesWithParents(graphData, data => data.nodeType === ASSEMBLE_NODE);
                const ancestorDeviceId = graphData.deviceId;
                list.forEach(({node, parent})=>{
                    if(parent.componentId){
                        // 有parentComponentId, 说明不是第二层的节点
                        outComponent.push({
                            ...this.purifyNodeData({...node}),
                            componentId: node.componentId,
                            parentComponentId: parent.componentId,
                            sort: node.sort,
                            ancestorDeviceId,
                        })
                    }else{
                        // 没有parentComponentId, 说明是第二层的节点
                        outComponentFirstLevel.push({
                            ...this.purifyNodeData({...node}),
                            componentId: node.componentId,
                            parentComponentId: null,
                            sort: node.sort,
                            ancestorDeviceId,
                        })
                    }
                })
            }
            if(this.status === 'DISASSEMBLE'){
                let list = findNodesWithParents(graphData, data => data.nodeType === DISASSEMBLE_NODE);
                list.forEach(({node, parent})=>{
                    if(parent.componentId){
                        // 有parentComponentId, 说明不是第二层的节点
                        inComponent.push({
                            ...this.purifyNodeData({...node}),
                            componentId: node.componentId,
                            parentComponentId: node.isDisassembleHead ? null : parent.componentId,
                            ancestorDeviceId: null
                        })
                    }else{
                        // 没有parentComponentId, 说明是第二层的节点
                        inComponentFirstLevel.push({
                            ...this.purifyNodeData({...node}),
                            componentId: node.componentId,
                            ancestorDeviceId: null
                        })
                    }
                })
            }
            const form = {
                mwdId: this.originForm.mwdId,
                serialNumber: this.detailForm.serialNumber,
                inComponent,
                outComponent,
                inComponentFirstLevel,
                outComponentFirstLevel
            };
            apiAssembleLucida(form).then(async() => {
                this.$message.success(`${this.status==='DISASSEMBLE'?'拆卸':'组装'}成功`);
                if(this.p_CreateEaWorkOrder && this.status==='DISASSEMBLE'){
                    const disassembleHeadList = findNodesWithParents(graphData, data => data.nodeType === DISASSEMBLE_NODE && data.isDisassembleHead)
                    this.$refs.createEaWorkOrderDialogRef.showDialog(disassembleHeadList.map(item => item.node), this.originForm);
                }
                this.$emit('getData', ()=>{
                    this.$nextTick(()=>{
                        this.treeGraphActualData = deepCopyTree(this.outTree);
                        this.read();
                    })
                });
            })
        },
    }
}
</script>
  