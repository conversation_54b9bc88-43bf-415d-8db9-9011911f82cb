<template>
    <div style="height: 100%;position: relative;">
        <div id="container" style="height:100%"></div>
        
        <div style="position: absolute;top: 0;right:40px">
             <template v-if="status === 'READ'">
                <el-button @click="assemble" type="primary">组装</el-button>
                <el-button @click="disassemble" type="primary">拆卸</el-button>
             </template>
            <template v-else>
                <el-button @click="onCancelOperate">取消</el-button>
                <el-button @click="onConfirmOperate" type="primary">{{ `确认${ status=='ASSEMBLE' ? '组装' : '拆卸' }` }}</el-button>
            </template>
        </div>
    </div>
</template>
<script>
import G6 from "@antv/g6";
import { 
    registerCustomNodes, NODE_TYPE, ANTV_TREE_COLLAPSED_FLAG, ASSEMBLE_NODE, DISASSEMBLE_NODE, DEFAULT_NODE,
    GHOST_NODE, expandNode, collapseNode, expandNodeAll, deepCopyTree, findFirstNode, mergeCompleteTemplateWithPartData, 
    findNodesWithParents, getNextGreen, getNextRed
} from "./tree/nodes";
const icon = require("./tree/icon.svg")
import { apiGetLucidaTemplate, apiGetLucidaTree } from "@/api/lucida";
import { apiGetMwdWorkOrderInfo } from "@/api/tool-mantain";
const templateData = require("./tree/mock/complete.json");
const actualTreeData = require("./tree/mock/complete_need_10257767.json")
const partData = require("./tree/mock/10257767.json")
const incompletePartData = require("./tree/mock/10257767_need_10602970.json")
const partPartData = require("./tree/mock/10602970.json")
const realSuData = require("./tree/mock/real_su.json")
registerCustomNodes('tree-default-node');
// NOTE: 所有关于树的数据处理，原则上不应该允许原地操作，也不应该允许使用引用，只能使用深拷贝
export default {
    props: {
        outTree: {
            type: Object,
            default: null
        },
        templateType: {
            type: String,
            default: 'MWD'
        }
    },
    data(){
        return {
            graph: null,
            matrix: null, // 如果有值，则保持视图位置
            status: 'READ',
            // 完整模板树的预处理数据 - NOTE: 这棵树初始化后不允许任何形式的修改 TODO: 递归冻结
            treeTemplate: null,
            actualTreeData: deepCopyTree(actualTreeData),
            partData: deepCopyTree(partData),
            incompletePartData: deepCopyTree(incompletePartData),
            partPartData: deepCopyTree(partPartData),
            count: 0,
        }
    },
    async mounted() {
        apiGetLucidaTemplate({templateType: this.templateType}).then(res=>{
            const template = res.data.data;
            this.treeTemplate = this.handleTreeData(template, { operation: "DEFAULT", nodeType: GHOST_NODE, generateId: true });
            this.actualTreeData = deepCopyTree(this.outTree);
            this.initData();
        })
        // apiGetMwdWorkOrderInfo({mwdId: 5951})
        // this.treeTemplate = this.handleTreeData(templateData, { operation: "DEFAULT", nodeType: GHOST_NODE, generateId: true });
        // this.initData();
    },
    methods: {
        initData(){
            switch(this.status){
                case 'ASSEMBLE':
                    this.assemble();
                    break;
                case 'DISASSEMBLE':
                    this.disassemble();
                    break;
                case 'READ':
                    this.read();
                    break;
            }
        },
        read(){
            this.status = 'READ';
            // 处理目标数据，标记nodeType
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 业务要求下的预处理逻辑 - 无论是读、拆、装，都要显示可能没有的节点
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, false);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        assemble(){
            this.status = 'ASSEMBLE';
            // 处理目标数据，标记nodeType，并设置head
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 只有组装时会有isGhostHead标记
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, true);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        disassemble(){
            this.status = 'DISASSEMBLE';
            // 处理目标数据，标记nodeType
            this.actualTreeData = this.handleTreeData(this.actualTreeData, { operation: "DEFAULT", nodeType: DEFAULT_NODE });
            // 业务要求下的预处理逻辑 - 无论是读、拆、装，都要显示可能没有的节点
            this.originGraphData = mergeCompleteTemplateWithPartData(this.treeTemplate, this.actualTreeData, false);
            this.graphData = deepCopyTree(this.originGraphData);
            this.$nextTick(()=>{
                this.drawChart();
            })
        },
        // 统一的数据处理函数
        handleTreeData(data, options = {}) {
            const {
                operation = 'DEFAULT', // 'DEFAULT', 'ASSEMBLE', 'DISASSEMBLE', 'CANCEL_DISASSEMBLE', 'CANCEL_ASSEMBLE'
                nodeType,
                collapsed = false,
                generateId = false,
                color = null,
                parentNode = null, // 父节点类型，用于某些操作
            } = options;

            const handleFunc = (item, parent = null) => {
                // 根据不同操作类型进行处理
                switch (operation) {
                    case 'DEFAULT':
                        generateId && (item.id = 'nodeid' + this.count++);
                        item.nodeType = nodeType;
                        break;
                    case 'ASSEMBLE':
                        /* 
                            组装
                            预处理目标数据，标记nodeType，并设置head
                            合并该节点和对应完整的基础模板数据，并设置残缺部分的HEAD标记
                        */
                        item.nodeType = ASSEMBLE_NODE;
                        item.ASSEMBLE_NODE_COLOR = color;
                        break;
                    case 'DISASSEMBLE':
                        /* 
                            拆卸
                            忽略GHOST_NODE和isDisassembleHead为true的节点
                            该节点及其子节点中所有nodeType为DEFAULT_NODE的节点置为DISASSEMBLE_NODE
                            添加DISASSEMBLE_NODE_COLOR字段，值为getNextRed()
                            将该节点的isDisassembleHead置为true
                        */
                        if (item.isDisassembleHead) {
                            return item;
                        }
                        switch (item.nodeType) {
                            case DEFAULT_NODE:
                                item.nodeType = DISASSEMBLE_NODE;
                                item.DISASSEMBLE_NODE_COLOR = color;
                                break;
                            case DISASSEMBLE_NODE:
                                item.DISASSEMBLE_NODE_COLOR = color;
                                break;
                            case GHOST_NODE:
                                return item;
                        }
                        break;
                    case 'CANCEL_DISASSEMBLE':
                        /* 
                            取消拆卸
                            如果是GHOST_NODE或isDisassembleHead为true的节点，直接返回
                            如果是DISASSEMBLE_NODE，查看父节点是否是DISASSEMBLE_NODE
                                是，颜色保持和父节点一致
                                否，将节点类型置为DEFAULT_NODE，并删除DISASSEMBLE_NODE_COLOR
                        */
                        switch (item.nodeType) {
                            case DISASSEMBLE_NODE:
                                if (item.isDisassembleHead) {
                                    return item;
                                }
                                if (parentNode.nodeType === DISASSEMBLE_NODE) {
                                    item.DISASSEMBLE_NODE_COLOR = parentNode.DISASSEMBLE_NODE_COLOR;
                                } else {
                                    item.nodeType = DEFAULT_NODE;
                                    delete item.DISASSEMBLE_NODE_COLOR;
                                }
                                break;
                            case GHOST_NODE:
                                return item;
                        }
                        break;
                }
                // 这个逻辑会保持组装拆卸中GHOST_NODE的折叠状态
                item[ANTV_TREE_COLLAPSED_FLAG] = collapsed;
                if (item.children) {
                    item.children.forEach((child) => {
                        handleFunc(child, item);
                    });
                }
                return item;
            };
            return handleFunc(data);
        },
        initContainer() {
            this.container = document.getElementById("container");
            this.width = this.container.scrollWidth;
            this.height = this.container.scrollHeight || 500;
        },

        initGraph(data) {
            if (!data) {
                return;
            }
            const tooltip = new G6.Tooltip({
                offsetX: 20,
                offsetY: 46,
                // 允许出现 tooltip 的 item 类型
                itemTypes: ["node"],
                // 自定义 tooltip 内容
                getContent: (e) => {
                    const model = e.item.getModel();
                    const { maxBht, reviseTotalHours, riskType } = model;
                    return `
                        <div class="tooltip-item">
                            <div class="tooltip-label">最高温度</div>
                            <div class="tooltip-content">${maxBht}</div>
                        </div>
                        <div class="tooltip-item">
                            <div class="tooltip-label">总时间</div>
                            <div class="tooltip-content">${reviseTotalHours}</div>
                        </div>
                        <div class="tooltip-item">
                            <div class="tooltip-label">风险类型</div>
                            <div class="tooltip-content">${riskType}</div>
                        </div>
                    `;
                },
                shouldBegin: (e) => {
                    return e.target.get("name") === "serialNumber";
                },
            });
            const contextmenu = new G6.Menu({
                getContent: (e) => {
                    return e._contextMenuContent;
                },
                handleMenuClick: (target, item) => {
                    const name = target.getAttribute("name");
                    const model = item.getModel();
                    const [{node: templateCurrentNode, parent: templateParentNode}] = findNodesWithParents(this.treeTemplate, data => data.id === model.id);
                    switch(name) {
                        case "EXPAND":
                            expandNode(item, this.graph);
                            break;
                        case "COLLAPSE":
                            collapseNode(item, this.graph);
                            break;
                        case "ALL_EXPAND":
                            expandNodeAll(item, this.graph);
                            break;
                        case "DISASSEMBLE":
                            /* 
                                拆卸
                                忽略GHOST_NODE和isDisassembleHead为true的节点
                                该节点及其子节点中所有nodeType为DEFAULT_NODE的节点置为DISASSEMBLE_NODE
                                添加DISASSEMBLE_NODE_COLOR字段，值为getNextRed()
                                将该节点的isDisassembleHead置为true
                            */
                            const disassembleData = this.handleTreeData(model, { operation: 'DISASSEMBLE', color: getNextRed() });
                            disassembleData.isDisassembleHead = true;
                            this.graph.updateChild(disassembleData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                        case "CANCEL_DISASSEMBLE":
                            /* 
                                取消拆卸
                                先删除目标节点isDisassembleHead标记
                                如果是GHOST_NODE或isDisassembleHead为true的节点，直接返回
                                如果是DISASSEMBLE_NODE，查看父节点是否是DISASSEMBLE_NODE
                                    是，颜色保持和父节点一致
                                    否，将节点类型置为DEFAULT_NODE，并删除DISASSEMBLE_NODE_COLOR
                            */
                            const graphParentNode = this.graph.findDataById(templateParentNode.id);
                            delete model.isDisassembleHead;
                            const cancelDisassembleData = this.handleTreeData(model, { operation: 'CANCEL_DISASSEMBLE', parentNode: graphParentNode });
                            this.graph.updateChild(cancelDisassembleData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                        case "CANCEL_ASSEMBLE":
                            // 取消组装 - 这个逻辑反倒异常简单，原因是组装在真实业务中不能出现断点
                            // 将该节点及其子节点全部置为模板节点的GHOST
                            // 将该节点的isAssembleHead置为true
                            const ghostData = deepCopyTree(templateCurrentNode);
                            ghostData.isGhostHead = true;
                            this.graph.updateChild(ghostData, templateParentNode.id);
                            this.rerenderGraph();
                            break;
                    }
                },
                offsetX: 0,
                offsetY: 36, // 完全不知道这个36是怎么算出来的
                // 在哪些类型的元素上响应
                itemTypes: ['node'],
                // 在这里判断要不要显示contextmenu
                shouldBegin: (e) => {
                    // 将显示的内容计算逻辑放在这里, 存到e._contextMenuContent中, 否则getContent可能要重复这个逻辑
                    // 核心原因是getContent不能返回null或undefined, 而且就算返回空串也会显示一个空的contextmenu
                    const model = e.item.getModel();
                    // 是否有子节点
                    const hasChildren = !!(model.children && model.children.length > 0);
                    // 是否折叠状态
                    const isCollapsed = !!model[ANTV_TREE_COLLAPSED_FLAG];
                    // 递归判断是否是全部展开的状态
                    const checkAllExpanded = (m) => {
                        if (!m.children || m.children.length === 0) {
                            return true;
                        }
                        return m.children.every((child) => {
                            return !child[ANTV_TREE_COLLAPSED_FLAG] && checkAllExpanded(child);
                        });
                    };

                    const allExpandDiv = `<div class="menu-item" name="ALL_EXPAND">全部展开</div>`;
                    const expandDiv = `<div class="menu-item" name="EXPAND">展开</div>`;
                    const collapseDiv = `<div class="menu-item" name="COLLAPSE">折叠</div>`;
                    const disassembleDiv = `<div class="menu-item" name="DISASSEMBLE">拆卸</div>`;
                    const cancelDisassembleDiv = `<div class="menu-item" name="CANCEL_DISASSEMBLE">取消拆卸</div>`;
                    const cancelAssembleDiv = `<div class="menu-item" name="CANCEL_ASSEMBLE">取消组装</div>`;
                    let content = "";
                    if (hasChildren) {
                        if (isCollapsed) {
                            content = expandDiv + allExpandDiv;
                        } else {
                            // TODO: 全部展开孙节点会有问题, 先不展示
                            // if (checkAllExpanded(model)) {
                            //     content = collapseDiv;
                            // } else {
                            //     content = collapseDiv + allExpandDiv;
                            // }
                            content = collapseDiv;
                        }
                    }
                    if(this.status === 'ASSEMBLE'){
                        if(model.isAssembleHead){
                            content += cancelAssembleDiv;
                        }
                    }
                    if(this.status === 'DISASSEMBLE'){
                        if(model.nodeType === DISASSEMBLE_NODE){
                            if(model.isDisassembleHead){
                                content += cancelDisassembleDiv;
                            }else{
                                content += disassembleDiv;
                            }
                        }
                        if(model.nodeType === DEFAULT_NODE){
                            content += disassembleDiv;
                        }
                    }
                    e._contextMenuContent = content;
                    return content;
                    
                },
            });
            this.graph = new G6.TreeGraph({
                container: "container",
                ...this.defaultConfig,
                padding: [20, 50],
                defaultLevel: 3,
                defaultZoom: 0.7,
                animate: false,
                plugins: [
                    tooltip,
                    contextmenu,
                    // TODO: 场景，位置，大小，样式
                    // new G6.Minimap({
                    //     size: [150, 100],
                    // }),
                ],
            });
            this.graph.data(data);
            this.graph.render();
            
            // 给+-号添加点击事件
            const handleCollapse = (e) => {
                e.preventDefault();
                const item = e.item;
                const model = item.getModel();
                console.log(model[ANTV_TREE_COLLAPSED_FLAG]);
                model[ANTV_TREE_COLLAPSED_FLAG] ? expandNode(item, this.graph) : collapseNode(item, this.graph);
            };
            this.graph.on("collapse-text:click", handleCollapse);
            this.graph.on("collapse-rect:click", handleCollapse);
            this.graph.on("serialNumber:click", this.expandToNode);
            this.graph.on("assemble:click", (e)=>{
                const item = e.item;
                const model = item.getModel();
                /* 
                    组装
                    预处理目标数据，标记nodeType，并设置head
                    合并该节点和对应完整的基础模板数据，并设置残缺部分的HEAD标记
                */
                const [{node: templateCurrentNode, parent: templateParentNode}] = findNodesWithParents(this.treeTemplate, data => data.id === model.id);
                const partData = this.handleTreeData(model.invCode === '10257767' ? this.incompletePartData : this.partPartData, { operation: 'ASSEMBLE', color: getNextGreen() });
                partData.isAssembleHead = true;
                // 这里取巧了，直接合并了图数据，正常逻辑应该是合并模板数据后覆盖图数据
                const mergeNode = mergeCompleteTemplateWithPartData(model, partData, true);
                this.graph.updateChild(mergeNode, templateParentNode.id);
                this.rerenderGraph();
            });
            this.graph.on("viewportchange", () => {
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            });
            // 监听鼠标滚动，改为拖拽画布
            this.graph.on("wheel", (e) => {
                e.preventDefault();
                // 获取滚动的距离
                const deltaX = e.deltaX;
                const deltaY = e.deltaY;
                // 模拟拖拽画布的行为, TODO: 不能超出边界，但边界条件不应该在这里计算
                this.graph.translate(-deltaX, -deltaY);
            });
            this.graph.on('afterrender', () => {
                // 保持视图位置
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            })
            
            // 以保证第一个节点高度为40为标准, 设置整个画布的初始zoom
            const firstNode = this.graph.getNodes()[0];
            if (firstNode) {
                const bbox = firstNode.getBBox();
                const height = bbox.maxY - bbox.minY;
                this.graph.zoom(70 / height);
            }
            // 因为没有配置fitView, 这里手动平移一下, 否则默认的左上角是应该是第一个节点的中心
            this.graph.translate(140, 70);
        },
        drawChart() {
            if (!this.container) {
                this.initContainer();
            }
            this.defaultConfig = {
                width: this.width,
                height: this.height,
                modes: {
                    default: [
                        // TODO: 重定义折叠标志, 但可能会有bug, 暂时使用默认的字段collapsed
                        // {
                        //     type: "collapse-expand",
                        //     onChange: function onChange(item, collapsed) {
                        //         const data = item.get("model");
                        //         data[ANTV_TREE_COLLAPSED_FLAG] = collapsed;
                        //         return true;
                        //     },
                        // },
                        "drag-canvas",
                        // "zoom-canvas",
                    ]
                },
                // fitView: true, // 因为主动初始化了zoom, 这个不能开启
                animate: true,
                defaultNode: {
                    type: "tree-default-node",
                },
                defaultEdge: {
                    type: 'cubic-horizontal',
                    style: {
                        stroke: '#CED4D9',
                    },
                },
                layout: {
                    type: "indented",
                    direction: "LR",
                    dropCap: false,
                    indent: 300,
                    getHeight: () => {
                        return 80;
                    },
                    // 重定义折叠标志
                    // getChildren: (model) => {
                    //     return model[ANTV_TREE_COLLAPSED_FLAG] ? [] : model.children;
                    // },
                },
            };
            // this.registerFun();
            if (this.graph) {
                this.graph.destroy();
            }
            this.initGraph(this.graphData);
            window.onresize = () => {
                if (!this.graph || this.graph.get("destroyed")){
                    return;
                }
                if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight){
                    return;
                }
                this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight);
            };
            
        },
        rerenderGraph(){
            // TODO: 这里是hack，这个逻辑本不应该有
            this.matrix = this.graph.getGroup().getMatrix();
            this.graph.changeData(this.graph.save());
        },
        onCancelOperate(){
            this.read();
        },
        onConfirmOperate(){
            // 拆卸的时候直接删除parentComponentId，组装的时候要关联相应parentComponentId，也要处理sort字段，这个字段数据最好从模板数据中得到
            // 业务上第二层的数据都没有parentComponentId，要单独处理，而且要过滤掉GHOST_NODE
            const graphData = this.graph.save();
            if(this.status === 'ASSEMBLE'){
                let list = findNodesWithParents(graphData, data => data.isAssembleHead);
                const componentAssembleList = [];
                list.forEach(({node})=>{
                    if(node.parentComponentId){
                        const sort = findFirstNode(this.treeTemplate, data => data.id === node.id).sort;
                        // 有parentComponentId, 说明不是第二层的节点
                        componentAssembleList.push({
                            componentId: node.componentId,
                            parentComponentId: parent.componentId,
                            sort
                        })
                    }
                })
                let assembly = null;
                if(graphData.children.find(item=>item.isAssembleHead)){
                    // 第二层有isAssembleHead, 说明总成进行了组装
                    graphData.children = graphData.children.filter(item=>item.nodeType!==GHOST_NODE);
                    assembly = graphData;
                }
                console.log({componentAssembleList, assembly});
            }
            if(this.status === 'DISASSEMBLE'){
                let list = findNodesWithParents(graphData, data => data.isDisassembleHead);
                const componentDisassembleList = [];
                list.forEach(({node})=>{
                    if(node.parentComponentId){
                        // 有parentComponentId, 说明不是第二层的节点
                        componentDisassembleList.push({
                            componentId: node.componentId,
                            parentComponentId: null,
                        })
                    }
                })
                let assembly = null;
                if(graphData.children.find(item=>item.isDisassembleHead)){
                    // 第二层有isDisassembleHead, 说明总成进行了拆卸
                    graphData.children = graphData.children.filter(item=>!item.isDisassembleHead&&item.nodeType!==GHOST_NODE);
                    assembly = graphData;
                }
                console.log({componentDisassembleList, assembly});
            }
        },
        expandToNode(){
            const serialNumber = "16305432";
            const targetNodeId = findFirstNode(this.graph.save(), data => data.serialNumber === serialNumber).id;
            const model = this.graph.findById(targetNodeId)?.getModel();
            if (!model) return;

            let node = model;
            while (node && node.id) {
                const parentNode = this.graph.findById(node.parentId);
                if (parentNode) {
                    console.log(parentNode)
                    this.graph.updateItem(parentNode, { collapsed: false });
                }
                node = parentNode ? parentNode.getModel() : null;
            }
            this.rerenderGraph();
            
            // 等待动画完成后聚焦
            this.$nextTick(()=>{
                this.graph.focusItem(targetNodeId, true, {
                    easing: 'easeCubic',
                    duration: 500,
                });
            })
        },
        // 聚焦节点
        handleNodeClick(e) {
            const item = e.item;
            this.graph.focusItem(item, true, {
                easing: 'easeCubic',
                duration: 500,
            });
        }
    }
}
</script>
  