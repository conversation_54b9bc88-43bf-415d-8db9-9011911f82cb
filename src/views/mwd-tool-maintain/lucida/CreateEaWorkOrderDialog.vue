<template>
    <el-dialog title="创建EA工单" width="80%" :visible.sync="isDialogVisble">
        <el-table
            :data="originForm.detailList"
            style="margin-bottom: 20px"
            :header-cell-style="commmonTableHeaderCellStyle"
            @selection-change="handleSelectionChange">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column label="工单号" width="170">
                <template slot-scope="scope">
                    <el-input
                        style="width: 100%"
                        v-model="scope.row.eaNumber"
                    >
                    </el-input>
                </template> 
            </el-table-column>
            <el-table-column label="名称" prop="invName">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="170">
            </el-table-column>
            <el-table-column label="维修等级" prop="repairLevel">
                <template slot-scope="{row}">
                    <el-select v-model="row.repairLevel" style="width: 100%;" placeholder="">
                        <el-option v-for="item in 5" :key="item" :label="item" :value="item"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
                <template slot-scope="{row}">
                    <el-input style="width: 100%" v-model="row.returnReason">
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note">
                <template slot-scope="{row}">
                    <el-input style="width: 100%" v-model="row.note">
                    </el-input>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisble = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onBatchCreate">
                <span>{{ selectedRepairDetail.length>1?'批量':'' }}创建EA工单</span> 
            </el-button>
        </span>
    </el-dialog>
</template>
<script>
import FuzzyWellInput from "@/components/FuzzyWellInput/index.vue";
import { apiAddEaWorkOrder } from "@/api/ea"
export default{
    name: 'CreateEaWorkOrderDialog',
    components: {
        FuzzyWellInput,
    },
    data(){
        return {
            isDialogVisble: false,
            originForm: {
                parentMwdId: null,
                parentEaId: null,
                parentMwdNumber: null,
                parentEaNumber: null,
                wellNumber: null,
                jobNumber: null,
                kitNumber: null,
                detailList: [],
            },
            selectedRepairDetail: [],
            originFormRules:{},
        }
    },
    methods: {
        showDialog(detailList, ancestorInfo) {
            this.ancestorInfo = ancestorInfo;
            this.originForm.parentMwdId = ancestorInfo.mwdId;
            this.originForm.parentMwdNumber = ancestorInfo.mwdNumber;
            this.originForm.wellNumber = ancestorInfo.wellNumber;
            this.originForm.jobNumber = ancestorInfo.jobNumber;
            this.originForm.detailList = detailList;
            this.isDialogVisble = true;
        },
        handleSelectionChange(val){
            this.selectedRepairDetail = val
        },
        onBatchCreate() {
            if(!this.selectedRepairDetail?.length){
                this.$message.error('请选择要维修的部件！')
                return
            }
            
            if(this.selectedRepairDetail.some(item=>!item.eaNumber)){
                this.$message.error('请填写完整工单号和仪器类型！')
                return;
            }
            this.onCreateOrder()

        },
        onCreateOrder(){
            const form = this.selectedRepairDetail.map(item=>{
                const ret = {};
                const workOrderEaDetailList = [{}];
                ret.jobNumber = this.originForm.jobNumber;
                ret.wellNumber = this.originForm.wellNumber;
                ret.parentMwdId = this.originForm.parentMwdId;
                ret.parentMwdNumber = this.originForm.parentMwdNumber;
                ret.eaNumber = item.eaNumber;
                const detailForm = this.ancestorInfo.workOrderMwdDetailList[0];
                // 部件信息
                workOrderEaDetailList[0].invCode = item.invCode;
                workOrderEaDetailList[0].invName = item.invName;
                workOrderEaDetailList[0].serialNumber = item.serialNumber;
                workOrderEaDetailList[0].parentDeviceType = detailForm.deviceType;
                workOrderEaDetailList[0].parentSerialNumber = detailForm.serialNumber;
                workOrderEaDetailList[0].parentInvName = detailForm.invName;
                workOrderEaDetailList[0].componentTotalCirculateHrs = item.totalCirculateHrs;
                workOrderEaDetailList[0].componentMaxBht = item.maxBht;
                workOrderEaDetailList[0].componentReviseMaxBht = item.reviseMaxBht;
                workOrderEaDetailList[0].componentTotalInWellHrs = item.totalInWellHrs;
                workOrderEaDetailList[0].componentReviseTotalHrs = item.reviseTotalHours;
                workOrderEaDetailList[0].versionNumber = item.versionNumber;
                workOrderEaDetailList[0].riskType = item.riskType;
                workOrderEaDetailList[0].riskValue = item.riskValue;
                // 作业信息
                workOrderEaDetailList[0].wellNumber = this.ancestorInfo.wellNumber;
                workOrderEaDetailList[0].jobNumber = this.ancestorInfo.jobNumber;
                workOrderEaDetailList[0].kitNumber = this.ancestorInfo.kitNumber;
                workOrderEaDetailList[0].contactNumber = detailForm.contactNumber;
                workOrderEaDetailList[0].contactUser = detailForm.contactUser;
                workOrderEaDetailList[0].circulateHrs = detailForm.circulateHrs;
                workOrderEaDetailList[0].inWellHour = detailForm.inWellHour;
                workOrderEaDetailList[0].maxBht = detailForm.maxBht;
                workOrderEaDetailList[0].run = detailForm.run;
                // 自由信息
                workOrderEaDetailList[0].returnReason = item.returnReason;
                workOrderEaDetailList[0].notes = item.note;
                workOrderEaDetailList[0].repairLevel = item.repairLevel;
                ret.workOrderEaDetailList = workOrderEaDetailList;
                return ret
            })
            Promise.all(form.map(apiAddEaWorkOrder)).then(()=>{
                this.$message.success('创建EA工单成功！');
                this.isDialogVisble = false;
            })
        }
    }
}
</script>