<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="编辑井作业分析详情"
        width="80%"
        top="5vh"
        :before-close="handleClose"
    >
        <div v-loading="loading">
            <el-form ref="form" :model="form" :rules="rules" label-width="140px" size="small">
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="基本信息" name="basic">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="井号" prop="wellNumber">
                                    <el-input v-model="form.wellNumber"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="趟次" prop="run">
                                    <el-input v-model.number="form.run" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="井类别" prop="serviceWellCategory">
                                    <el-select v-model="form.serviceWellCategory" placeholder="请选择井类别">
                                        <el-option label="近钻井" value="近钻井"></el-option>
                                        <el-option label="非近钻井" value="非近钻井"></el-option>
                                        <el-option label="旋转导向" value="旋转导向"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="区块" prop="block">
                                    <el-input v-model="form.block"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="温度值" prop="temperatureValue">
                                    <el-input
                                        v-model.number="form.temperatureValue"
                                        type="number"
                                        :class="{
                                            'normal-temperature-input': form.temperatureValue <= 125,
                                            'high-temperature-input': form.temperatureValue > 125 && form.temperatureValue <= 135,
                                            'very-high-temperature-input': form.temperatureValue > 135
                                        }"
                                    >
                                        <template slot="append">°C</template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="振动级别" prop="vibrationLevel">
                                    <el-select v-model="form.vibrationLevel" placeholder="请选择振动级别">
                                        <el-option label="Low" value="Low"></el-option>
                                        <el-option label="Medium" value="Medium"></el-option>
                                        <el-option label="High" value="High"></el-option>
                                        <el-option label="Very High" value="Very High"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="振动超标" prop="vibrationOos">
                                    <el-switch v-model="form.vibrationOos"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="超标工况描述" prop="exceededConditionDescription">
                                    <el-input v-model="form.exceededConditionDescription"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="钻头尺寸" prop="bitSize">
                                    <el-input v-model="form.bitSize"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="泥浆类型" prop="mudType">
                                    <el-select v-model="form.mudType" placeholder="请选择泥浆类型">
                                        <el-option label="白油基" value="白油基"></el-option>
                                        <el-option label="油基" value="油基"></el-option>
                                        <el-option label="水基" value="水基"></el-option>                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="密度" prop="mudDensity">
                                    <el-input v-model.number="form.mudDensity" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="排量" prop="flowRate">
                                    <el-input v-model.number="form.flowRate" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="施工开始井深(MD)" prop="constructionStartDepthMd">
                                    <el-input v-model.number="form.constructionStartDepthMd" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="施工结束井深(MD)" prop="constructionEndDepthMd">
                                    <el-input v-model.number="form.constructionEndDepthMd" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="进尺(MD)" prop="footageMd">
                                    <el-input v-model.number="form.footageMd" type="number"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="Orifice配置" prop="orificeConfig">
                                    <el-input v-model="form.orificeConfig"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="Poppet配置" prop="poppetConfig">
                                    <el-input v-model="form.poppetConfig"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="信号强度" prop="signalStrength">
                                    <el-input v-model="form.signalStrength"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="入井时间" prop="dateIn">
                                    <el-date-picker v-model="form.dateIn" type="datetime" placeholder="选择日期时间"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="出井时间" prop="dateOut">
                                    <el-date-picker v-model="form.dateOut" type="datetime" placeholder="选择日期时间"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="是否故障" prop="isFailure">
                                    <el-select v-model="form.isFailure" placeholder="请选择">
                                        <el-option label="是" value="是"></el-option>
                                        <el-option label="否" value="否"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="起钻原因" prop="tripOutReasonType">
                                    <el-input v-model="form.tripOutReasonType"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="综合失效类型" prop="consolidatedFailureType">
                                    <el-select v-model="form.consolidatedFailureType" placeholder="请选择">
                                        <el-option label="空" value=""></el-option>
                                        <el-option label="正常运行" value="NO_FAILURE"></el-option>
                                        <el-option label="MWD服务失效" value="MWD_FAILURE"></el-option>
                                        <el-option label="近钻工具失效" value="AT_BIT_FAILURE"></el-option>
                                        <el-option label="旋导服务失效" value="RSS_FAILURE"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="失效时间" prop="failureTime">
                                    <el-date-picker v-model="form.failureTime" type="datetime" placeholder="选择失效时间"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="井下仪器情况描述" prop="downholeInstrumentStatusDesc">
                                    <el-select v-model="form.downholeInstrumentStatusDesc" placeholder="请选择">
                                        <el-option label="正常" value="正常"></el-option>
                                        <el-option label="异常" value="异常"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="入井小时" prop="tripInHours">
                                    <el-input v-model.number="form.tripInHours" type="number" placeholder="请输入入井小时">
                                        <template slot="append">小时</template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="循环小时" prop="circulatingHours">
                                    <el-input v-model.number="form.circulatingHours" type="number" placeholder="请输入循环小时">
                                        <template slot="append">小时</template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="多少时间失效(小时)" prop="hoursToFailure">
                                    <el-input v-model.number="form.hoursToFailure" type="number" placeholder="请输入失效时间">
                                        <template slot="append">小时</template>
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="维护级别" prop="maintenanceLevel">
                                    <el-input v-model="form.maintenanceLevel" placeholder="请输入维护级别"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="使用后维护完成日期" prop="postUseMaintenanceCompletionDate">
                                    <el-date-picker v-model="form.postUseMaintenanceCompletionDate" type="date" placeholder="选择日期"></el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item label="更换主要部件描述" prop="replacedMajorComponentsDesc">
                                    <el-input v-model="form.replacedMajorComponentsDesc" placeholder="请输入更换主要部件描述"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="具体失效总成分类" prop="specificFailedAssemblyCategory">
                                    <el-input v-model="form.specificFailedAssemblyCategory" placeholder="请输入具体失效总成分类"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="失效总成的部件分类" prop="failedComponentInAssemblyCategory">
                                    <el-input v-model="form.failedComponentInAssemblyCategory" placeholder="请输入失效总成的部件分类"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="失效主要原因标签" prop="primaryFailureReasonTag">
                                    <el-input v-model="form.primaryFailureReasonTag" placeholder="请输入失效主要原因标签"></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- 根本原因分析将在单独的标签页中显示 -->
                        </el-row>
                    </el-tab-pane>

                    <el-tab-pane label="工具信息" name="tools">
                        <div class="tools-header">
                            <el-button type="primary" size="small" @click="addTool">添加工具</el-button>
                        </div>
                        <el-table :data="form.instruments" border style="width: 100%">
                            <el-table-column prop="deviceType" label="仪器类型" min-width="120">
                                <template slot-scope="scope">
                                    <FuzzySelect
                                        placeholder="请选择仪器类型"
                                        clearable
                                        type="DEVICE_TYPE"
                                        :init-list="deviceTypeList"
                                        v-model="scope.row.deviceType"
                                        @change="item => onDeviceTypeChange(item, scope.$index)"
                                        :key="`device-${scope.$index}-${scope.row.toolType || 'empty'}`"
                                    ></FuzzySelect>
                                </template>
                            </el-table-column>
                            <el-table-column prop="invName" label="仪器角色" min-width="120">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.invName" placeholder="请输入仪器角色"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="serialNumber" label="仪器序列号" min-width="120">
                                <template slot-scope="scope">
                                    <el-autocomplete
                                        v-model="scope.row.serialNumber"
                                        :fetch-suggestions="(queryString, cb) => querySearchSerialNumber(queryString, cb, scope.$index)"
                                        style="width:100%"
                                        @select="item => handleSelectSerialNumber(item, scope.$index)"
                                        placeholder="请输入仪器序列号"
                                        value-key="serialNumber"
                                    ></el-autocomplete>
                                </template>
                            </el-table-column>
                            <el-table-column prop="riskType" label="仪器风险类型" min-width="120">
                                <template slot-scope="scope">
                                    <el-tag :type="getRiskTypeTagType(scope.row.riskType)" size="medium" v-if="scope.row.riskType">
                                        {{ scope.row.riskType }}
                                    </el-tag>
                                    <span v-else class="no-risk-type">未设置</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="120">
                                <template slot-scope="scope">
                                    <el-button type="text" @click="removeTool(scope.$index, scope.row)" class="delete-btn">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-tab-pane>

                    <el-tab-pane label="现场端主要情况" name="field">
                        <el-form-item prop="fieldIncidentDescription" class="rich-text-form-item">
                            <div class="rich-text-wrapper">
                                <quill-editor
                                    v-model="form.fieldIncidentDescription"
                                    :options="getEditorOptions('fieldEditor')"
                                    ref="fieldEditor"
                                    class="rich-text-editor"
                                ></quill-editor>
                            </div>
                        </el-form-item>
                    </el-tab-pane>

                    <el-tab-pane label="维护/研发端检查发现" name="workshop">
                        <el-form-item prop="workshopShopFinding" class="rich-text-form-item">
                            <div class="rich-text-wrapper">
                                <quill-editor
                                    v-model="form.workshopShopFinding"
                                    :options="getEditorOptions('workshopEditor')"
                                    ref="workshopEditor"
                                    class="rich-text-editor"
                                ></quill-editor>
                            </div>
                        </el-form-item>
                    </el-tab-pane>

                    <el-tab-pane label="改进计划/措施" name="improvement">
                        <el-form-item prop="improvementPlanMeasures" class="rich-text-form-item">
                            <div class="rich-text-wrapper">
                                <quill-editor
                                    v-model="form.improvementPlanMeasures"
                                    :options="getEditorOptions('improvementEditor')"
                                    ref="improvementEditor"
                                    class="rich-text-editor"
                                ></quill-editor>
                            </div>
                        </el-form-item>
                    </el-tab-pane>

                    <el-tab-pane label="根本原因分析" name="failure">
                        <el-form-item prop="failureReasonDescription" class="rich-text-form-item">
                            <div class="rich-text-wrapper">
                                <quill-editor
                                    v-model="form.failureReasonDescription"
                                    :options="getEditorOptions('failureEditor')"
                                    ref="failureEditor"
                                    class="rich-text-editor"
                                ></quill-editor>
                            </div>
                        </el-form-item>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">保存</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { apiGetWellAnalysisDetailById, apiUpdateWellAnalysisDetail, apiUpdateWellAnalysisRichTextField } from '@/api/wellAnalysis';
import { apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import FuzzySelect from '@/components/FuzzySelect/index.vue';
import getDict from '@/utils/getDict';
import imageCompression from 'browser-image-compression';

// 注意：需要安装DOMPurify库: npm install dompurify
// import DOMPurify from 'dompurify';

export default {
    name: 'EditDialog',
    components: {
        FuzzySelect
    },
    data() {
        return {
            isDialogVisible: false,
            loading: false,
            submitting: false,
            activeTab: 'basic',
            deviceTypeList: [],
            form: {
                id: null,
                wellNumber: '',
                run: null,
                serviceWellCategory: '',
                block: '',
                temperatureValue: null,
                vibrationLevel: '',
                vibrationOos: false,
                exceededConditionDescription: '',
                bitSize: '',
                mudType: '',
                mudDensity: null,
                flowRate: null,
                constructionStartDepthMd: null,
                constructionEndDepthMd: null,
                footageMd: null,
                orificeConfig: '',
                poppetConfig: '',
                signalStrength: '',
                dateIn: null,
                failureTime: null,
                dateOut: null,
                downholeInstrumentStatusDesc: '',
                tripOutReasonType: '',
                isFailure: '否',
                consolidatedFailureType: 'NO_FAILURE',
                tripInHours: null,
                circulatingHours: null,
                hoursToFailure: null,
                maintenanceLevel: '',
                postUseMaintenanceCompletionDate: null,
                replacedMajorComponentsDesc: '',
                specificFailedAssemblyCategory: '',
                failedComponentInAssemblyCategory: '',
                primaryFailureReasonTag: '',
                fieldIncidentDescription: '',
                failureReasonDescription: '',
                workshopShopFinding: '',
                improvementPlanMeasures: '',
                instruments: [],
                instrumentIdsToDelete: []
            },
            originalInstruments: [], // 用于跟踪原始工具列表，以便识别删除的工具
            rules: {
                wellNumber: [{ required: true, message: '请输入井号', trigger: 'blur' }],
                run: [{ required: true, message: '请输入趟次', trigger: 'blur' }],
                serviceWellCategory: [{ required: true, message: '请选择井类别', trigger: 'change' }]
            }
        };
    },
    mounted() {
        // 获取设备类型列表
        getDict([17]).then(res => {
            // 获取原始设备类型列表
            const originalDeviceTypeList = res[0] || [];

            // 创建一个映射，将工具类型编码映射到中文名称
            const toolTypeMap = this.getToolTypeMap();

            // 处理设备类型列表，确保每个设备类型都有正确的名称
            this.deviceTypeList = originalDeviceTypeList.map(item => {
                // 如果在工具类型映射中找到对应的中文名称，则使用它
                if (toolTypeMap[item.id]) {
                    return {
                        ...item,
                        name: toolTypeMap[item.id] // 使用中文名称
                    };
                }
                return item;
            });

            console.log('处理后的设备类型列表:', this.deviceTypeList);
        });
    },
    methods: {
        // 打开对话框
        open(id) {
            this.resetForm();
            this.isDialogVisible = true;
            this.loading = true;

            // 获取详情数据
            apiGetWellAnalysisDetailById(id)
                .then(response => {
                    console.log('API响应数据:', response.data);
                    const data = response.data.data || {};

                    // 检查instruments数组
                    if (data.instruments && data.instruments.length > 0) {
                        console.log('原始工具信息数据:', data.instruments);
                    } else {
                        console.warn('API返回的工具信息为空或不存在');
                    }

                    // 复制数据到表单
                    Object.keys(this.form).forEach(key => {
                        if (key !== 'instrumentIdsToDelete' && data[key] !== undefined) {
                            this.form[key] = data[key];
                        }
                    });

                    // 保存原始工具列表的副本
                    this.originalInstruments = JSON.parse(JSON.stringify(data.instruments || []));

                    // 确保instruments是数组
                    this.form.instruments = data.instruments || [];

                    // 处理工具信息，确保deviceType字段正确设置
                    if (this.form.instruments && this.form.instruments.length > 0) {
                        console.log('原始工具信息:', this.form.instruments);

                        // 获取工具类型映射
                        const toolTypeMap = this.getToolTypeMap();

                        // 处理每个工具信息
                        this.form.instruments.forEach(instrument => {
                            // 如果有toolType但没有deviceType，将toolType赋值给deviceType
                            if (instrument.toolType && !instrument.deviceType) {
                                instrument.deviceType = instrument.toolType;
                                console.log(`设置工具 ${instrument.serialNumber} 的deviceType为 ${instrument.toolType}`);
                            }
                        });

                        // 创建一个自定义的设备类型列表，包含工具类型编码和对应的中文名称
                        const customDeviceTypeList = Object.keys(toolTypeMap).map(code => ({
                            id: code,
                            name: toolTypeMap[code]
                        }));

                        // 更新设备类型列表
                        this.deviceTypeList = customDeviceTypeList;

                        console.log('自定义设备类型列表:', this.deviceTypeList);
                        console.log('处理后的工具信息:', this.form.instruments);

                        // 强制更新，确保UI正确显示
                        this.$forceUpdate();
                    } else {
                        console.warn('没有工具信息需要处理');
                    }
                })
                .catch(error => {
                    console.error('获取井作业分析详情失败:', error);
                    this.$message.error('获取详情数据失败，请稍后重试');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 重置表单
        resetForm() {
            this.form = {
                id: null,
                wellNumber: '',
                run: null,
                serviceWellCategory: '',
                block: '',
                temperatureValue: null,
                vibrationLevel: '',
                vibrationOos: false,
                exceededConditionDescription: '',
                bitSize: '',
                mudType: '',
                mudDensity: null,
                flowRate: null,
                constructionStartDepthMd: null,
                constructionEndDepthMd: null,
                footageMd: null,
                orificeConfig: '',
                poppetConfig: '',
                signalStrength: '',
                dateIn: null,
                failureTime: null,
                dateOut: null,
                downholeInstrumentStatusDesc: '',
                tripOutReasonType: '',
                isFailure: '否',
                consolidatedFailureType: 'NO_FAILURE',
                tripInHours: null,
                circulatingHours: null,
                hoursToFailure: null,
                maintenanceLevel: '',
                postUseMaintenanceCompletionDate: null,
                replacedMajorComponentsDesc: '',
                specificFailedAssemblyCategory: '',
                failedComponentInAssemblyCategory: '',
                primaryFailureReasonTag: '',
                fieldIncidentDescription: '',
                failureReasonDescription: '',
                workshopShopFinding: '',
                improvementPlanMeasures: '',
                instruments: [],
                instrumentIdsToDelete: []
            };
            this.originalInstruments = [];
            this.activeTab = 'basic';

            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
        },

        // 关闭对话框
        handleClose() {
            this.isDialogVisible = false;
            this.resetForm();
        },

        // 添加工具
        addTool() {
            this.form.instruments.push({
                deviceType: null,
                toolType: '',
                invName: '',
                serialNumber: '',
                riskType: ''
            });

            // 滚动到新添加的工具行
            this.$nextTick(() => {
                const container = document.querySelector('.el-table__body-wrapper');
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },

        // 删除工具
        removeTool(index, row) {
            // 如果工具有ID，将其添加到待删除列表
            if (row.instrumentRecordId) {
                this.form.instrumentIdsToDelete.push(row.instrumentRecordId);
            }
            // 从表格中移除
            this.form.instruments.splice(index, 1);
        },

        // 设备类型变更
        onDeviceTypeChange(item, index) {
            if (!item || !item.id) {
                // 如果清空了设备类型，也清空工具类型
                this.form.instruments[index].toolType = '';
                this.form.instruments[index].invName = '';
                return;
            }

            const deviceType = this.deviceTypeList.find(type => type.id == item.id);
            if (deviceType) {
                // 如果没有设置仪器角色，则使用设备类型名称
                if (!this.form.instruments[index].invName) {
                    this.form.instruments[index].invName = deviceType.name;
                }

                // 设置工具类型为设备类型ID
                this.form.instruments[index].toolType = deviceType.id;

                console.log(`设备类型已更新: ${deviceType.name}, 工具类型: ${deviceType.id}`);

                // 提示用户已选择的设备类型
                this.$message.success(`已选择设备类型: ${deviceType.name}`);
            }

            // 注意：我们不再自动清空序列号，允许用户保留已选择的序列号
            // 但如果用户希望重新选择，可以手动清除

            // 如果序列号与设备类型不匹配，可以提示用户
            const serialNumber = this.form.instruments[index].serialNumber;
            if (serialNumber && deviceType) {
                this.$message.info('您可以重新选择序列号以匹配当前设备类型');
            }
        },

        // 序列号查询建议
        querySearchSerialNumber(queryString, cb, index) {
            // 构建查询参数
            const params = {
                serialNumber: queryString || ''
            };

            // 如果有设备类型，可以添加到查询参数中以获得更精确的结果
            // 但不再强制要求必须先选择设备类型
            const deviceType = this.form.instruments[index].deviceType;
            if (deviceType) {
                params.deviceTypeList = [deviceType];
            }

            // 调用API获取序列号列表
            apiGetWarehouseDeviceBatchInfo(params)
                .then(res => {
                    const data = res.data.data || [];
                    // 返回序列号列表
                    cb(data);
                })
                .catch(error => {
                    console.error('获取序列号列表失败:', error);
                    cb([]);
                });
        },

        // 选择序列号
        handleSelectSerialNumber(item, index) {
            if (!item) return;

            // 更新设备信息
            this.form.instruments[index].deviceType = item.deviceType;
            this.form.instruments[index].invName = item.invName || '';
            this.form.instruments[index].riskType = item.riskType || '';

            // 设置工具类型，使用设备类型作为工具类型编码
            if (item.deviceType) {
                this.form.instruments[index].toolType = item.deviceType;
            }

            // 如果有其他信息，也一并更新
            if (item.owner) {
                this.form.instruments[index].owner = item.owner;
            }

            // 提示用户
            this.$message.success(`已选择序列号: ${item.serialNumber}`);

            // 如果有需要，可以在这里添加额外的逻辑，例如验证序列号有效性等
        },

        // 提交表单
        // 根据风险类型获取标签类型
        getRiskTypeTagType(riskType) {
            // 当风险类型为"未知"或为空值时，使用黄色标签（warning类型）
            if (!riskType) {
                return 'warning';
            }

            switch(riskType) {
                case 'RISK':
                    return 'danger';
                case 'PRODUCE':
                    return 'success';
                case 'TEST':
                    return 'primary';
                case 'SCRAP':
                    return 'info';
                default:
                    return 'warning'; // 未知风险类型也使用黄色标签
            }
        },

        // 获取工具类型映射
        getToolTypeMap() {
            return {
                '16001': '近钻头',
                '16002': '方位探管',
                '16003': '方位Gamma',
                '16004': 'MWD电池',
                '16005': '底部总成',
                '16006': '扶正器',
                '16007': '定向探管',
                '16008': 'Gamma',
                '16009': '绝缘短节',
                '16010': 'KIT箱',
                '16011': '脉冲器',
                '16012': '接收器',
                '16013': '打捞头',
                '16014': 'UBHO',
                '16015': '其他',
                '16016': '循环套',
                '16017': '整流器',
                '16018': '长无磁钻杆',
                '16019': '长无磁钻挺',
                '16020': '无磁短节',
                '16021': '多点仪器',
                '16022': '滤网短节',
                '16023': '钻具尾扶',
                '16024': '扣型转接头',
                '16025': 'TAPP发射机总成',
                '16026': 'TAPP电池总成',
                '16027': '旋导LCP',
                '16028': '旋导MWD',
                '16029': '旋导SU',
                '16030': 'TAPP绝缘总成',
                '16031': '司显',
                '16032': '井深盒子',
                '16033': 'Gamma Key',
                '16034': '电脑',
                '16035': '旋导箱',
                '16036': '随钻测压'
            };
        },

        // 根据工具类型编码获取中文名称
        getToolTypeName(toolType) {
            const toolTypeMap = this.getToolTypeMap();

            // 确保toolType是字符串
            const typeStr = String(toolType);

            // 返回映射的中文名称，如果没有找到则返回原始编码
            return toolTypeMap[typeStr] || typeStr;
        },

        // 获取富文本编辑器配置
        getEditorOptions(editorRef) {
            return {
                placeholder: "请输入内容...",
                modules: {
                    toolbar: {
                        container: [
                            ['bold', 'italic', 'underline', 'strike'],
                            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                            ['image']
                        ],
                        handlers: {
                            image: () => {
                                // 创建文件选择器
                                const input = document.createElement('input');
                                input.setAttribute('type', 'file');
                                input.setAttribute('accept', 'image/*');
                                input.click();

                                // 监听文件选择事件
                                input.onchange = async () => {
                                    const file = input.files[0];
                                    if (file) {
                                        // 显示加载提示
                                        const loading = this.$loading({
                                            lock: true,
                                            text: '图片处理中...',
                                            spinner: 'el-icon-loading',
                                            background: 'rgba(0, 0, 0, 0.7)'
                                        });

                                        try {
                                            // 图片压缩配置
                                            const options = {
                                                maxSizeMB: 0.5, // 最大尺寸为0.5MB
                                                maxWidthOrHeight: 1024, // 最大宽度或高度为1024像素
                                                useWebWorker: true
                                            };

                                            // 判断是否需要压缩
                                            const compressedFile = file.size > 512 * 1024
                                                ? await imageCompression(file, options)
                                                : file;

                                            console.log('原始图片大小:', file.size / 1024 / 1024, 'MB');
                                            console.log('压缩后图片大小:', compressedFile.size / 1024 / 1024, 'MB');

                                            const reader = new FileReader();
                                            reader.onload = (e) => {
                                                const quill = this.$refs[editorRef].quill;
                                                // 获取光标位置
                                                const range = quill.getSelection();
                                                // 插入图片
                                                quill.insertEmbed(range.index, 'image', e.target.result);
                                                // 将光标移动到图片后面
                                                quill.setSelection(range.index + 1);
                                                // 关闭加载提示
                                                loading.close();
                                            };
                                            reader.readAsDataURL(compressedFile);
                                        } catch (error) {
                                            console.error('图片压缩失败:', error);
                                            loading.close();
                                            this.$message.error('图片处理失败，请重试');
                                        }
                                    }
                                };
                            }
                        }
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: 'black',
                            border: 'none',
                            color: 'white'
                        },
                        modules: ['Resize', 'DisplaySize']
                    }
                }
            };
        },

        // 检查并处理富文本内容中的大型base64图片
        processRichTextContent() {
            // 处理根本原因分析
            if (this.form.failureReasonDescription) {
                this.form.failureReasonDescription = this.optimizeHtmlContent(this.form.failureReasonDescription);
            }

            // 处理井下仪器情况描述
            if (this.form.downholeInstrumentStatusDesc) {
                this.form.downholeInstrumentStatusDesc = this.optimizeHtmlContent(this.form.downholeInstrumentStatusDesc);
            }

            // 处理现场端主要情况
            if (this.form.fieldIncidentDescription) {
                this.form.fieldIncidentDescription = this.optimizeHtmlContent(this.form.fieldIncidentDescription);
            }

            // 处理维护/研发端检查发现
            if (this.form.workshopShopFinding) {
                this.form.workshopShopFinding = this.optimizeHtmlContent(this.form.workshopShopFinding);
            }

            // 处理改进计划/措施
            if (this.form.improvementPlanMeasures) {
                this.form.improvementPlanMeasures = this.optimizeHtmlContent(this.form.improvementPlanMeasures);
            }
        },

        // 优化HTML内容，处理大型base64图片
        optimizeHtmlContent(htmlContent) {
            try {
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, 'text/html');
                const images = doc.querySelectorAll('img');

                // 检查是否有base64图片需要处理
                let hasLargeBase64Images = false;

                images.forEach(img => {
                    const src = img.getAttribute('src');
                    // 检查是否是base64图片且大小超过限制
                    if (src && src.startsWith('data:image') && src.length > 100000) { // 约100KB
                        hasLargeBase64Images = true;
                        // 在这里可以添加图片压缩处理
                    }
                });

                // 如果有大型base64图片，提示用户
                if (hasLargeBase64Images) {
                    console.warn('检测到大型base64图片，可能会导致提交缓慢');
                }

                return htmlContent;
            } catch (error) {
                console.error('处理HTML内容失败:', error);
                return htmlContent;
            }
        },

        // 分批提交数据
        async submitInBatches() {
            try {
                // 显示加载提示
                const loading = this.$loading({
                    lock: true,
                    text: '正在提交数据，请耐心等待...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 1. 首先提交基本信息（不包含大型富文本字段）
                const basicFormData = { ...this.form };

                // 临时移除大型富文本字段
                const failureContent = basicFormData.failureReasonDescription;
                const downholeContent = basicFormData.downholeInstrumentStatusDesc;
                const fieldContent = basicFormData.fieldIncidentDescription;
                const workshopContent = basicFormData.workshopShopFinding;
                const improvementContent = basicFormData.improvementPlanMeasures;

                basicFormData.failureReasonDescription = '';
                basicFormData.downholeInstrumentStatusDesc = '';
                basicFormData.fieldIncidentDescription = '';
                basicFormData.workshopShopFinding = '';
                basicFormData.improvementPlanMeasures = '';

                // 提交基本信息
                await apiUpdateWellAnalysisDetail(basicFormData);
                loading.text = '基本信息已提交，正在处理富文本内容...';

                // 2. 然后分别提交富文本字段，使用专门的富文本更新接口
                if (failureContent) {
                    loading.text = '正在提交根本原因分析...';
                    await apiUpdateWellAnalysisRichTextField({
                        id: this.form.id,
                        fieldName: 'failureReasonDescription',
                        fieldValue: failureContent
                    });
                }

                if (downholeContent) {
                    loading.text = '正在提交井下仪器情况描述...';
                    await apiUpdateWellAnalysisRichTextField({
                        id: this.form.id,
                        fieldName: 'downholeInstrumentStatusDesc',
                        fieldValue: downholeContent
                    });
                }

                if (fieldContent) {
                    loading.text = '正在提交现场端主要情况...';
                    await apiUpdateWellAnalysisRichTextField({
                        id: this.form.id,
                        fieldName: 'fieldIncidentDescription',
                        fieldValue: fieldContent
                    });
                }

                if (workshopContent) {
                    loading.text = '正在提交维护/研发端检查发现...';
                    await apiUpdateWellAnalysisRichTextField({
                        id: this.form.id,
                        fieldName: 'workshopShopFinding',
                        fieldValue: workshopContent
                    });
                }

                if (improvementContent) {
                    loading.text = '正在提交改进计划/措施...';
                    await apiUpdateWellAnalysisRichTextField({
                        id: this.form.id,
                        fieldName: 'improvementPlanMeasures',
                        fieldValue: improvementContent
                    });
                }

                loading.close();
                return true;
            } catch (error) {
                console.error('分批提交失败:', error);
                this.$message.error('提交过程中发生错误，请稍后重试');
                throw error;
            }
        },

        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (!valid) {
                    this.$message.warning('请填写必填字段');
                    return;
                }

                this.submitting = true;

                // 处理富文本内容
                this.processRichTextContent();

                // 检查是否需要分批提交
                const needBatchSubmit =
                    (this.form.failureReasonDescription && this.form.failureReasonDescription.length > 50000) ||
                    (this.form.downholeInstrumentStatusDesc && this.form.downholeInstrumentStatusDesc.length > 50000) ||
                    (this.form.fieldIncidentDescription && this.form.fieldIncidentDescription.length > 50000) ||
                    (this.form.workshopShopFinding && this.form.workshopShopFinding.length > 50000) ||
                    (this.form.improvementPlanMeasures && this.form.improvementPlanMeasures.length > 50000);

                // 根据需要选择提交方式
                const submitPromise = needBatchSubmit
                    ? this.submitInBatches()
                    : apiUpdateWellAnalysisDetail(this.form);

                submitPromise
                    .then(() => {
                        this.$message.success('保存成功');
                        this.handleClose();
                        this.$emit('refresh'); // 通知父组件刷新列表
                    })
                    .catch(error => {
                        console.error('更新井作业分析详情失败:', error);
                        this.$message.error('保存失败，请稍后重试');
                    })
                    .finally(() => {
                        this.submitting = false;
                    });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.tools-header {
    margin-bottom: 15px;
}

.delete-btn {
    color: #F56C6C;
}

.delete-btn:hover {
    color: #f78989;
}

.no-risk-type, .no-tool-type {
    color: #909399;
    font-style: italic;
}

::v-deep .el-tag {
    display: inline-block;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    font-size: 12px;
    border-radius: 4px;
}

/* 温度值输入样式 */
.normal-temperature-input ::v-deep .el-input__inner {
    border-color: #409EFF;
    background-color: rgba(64, 158, 255, 0.1);
    color: #409EFF;
}

.high-temperature-input ::v-deep .el-input__inner {
    border-color: #F56C6C;
    background-color: rgba(245, 108, 108, 0.1);
    color: #F56C6C;
}

.very-high-temperature-input ::v-deep .el-input__inner {
    border-color: #F56C6C;
    background-color: rgba(245, 108, 108, 0.2);
    color: #F56C6C;
    font-weight: bold;
}

.temperature-warning {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
}

.temperature-warning i {
    margin-right: 5px;
}

/* 富文本表单项样式 */
.rich-text-form-item {
    margin-bottom: 0;
    width: 100%;
}

.rich-text-wrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

/* 富文本编辑器样式 */
.rich-text-editor {
    height: auto;
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
}

::v-deep .ql-container {
    font-size: 14px;
    border-radius: 0 0 4px 4px;
    height: auto;
}

::v-deep .ql-toolbar {
    border-radius: 4px 4px 0 0;
    background-color: #f5f7fa;
    padding: 8px;
}

::v-deep .ql-editor {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-top: none;
    padding: 12px;
    line-height: 1.5;
    box-sizing: border-box;
}

::v-deep .ql-editor p {
    margin: 8px 0;
    word-break: break-word;
}

::v-deep .ql-editor img {
    max-width: 100%;
    height: auto;
    max-height: 300px;
    margin: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: block;
}

/* 确保富文本编辑器中的表格正确显示 */
::v-deep .ql-editor table {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
}

::v-deep .ql-editor table td,
::v-deep .ql-editor table th {
    border: 1px solid #ddd;
    padding: 8px;
}

/* 确保富文本编辑器中的列表正确显示 */
::v-deep .ql-editor ul,
::v-deep .ql-editor ol {
    padding-left: 20px;
    margin: 8px 0;
}
</style>
