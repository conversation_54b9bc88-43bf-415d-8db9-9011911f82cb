<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="井作业分析详情"
        width="80%"
        top="5vh"
        :before-close="handleClose"
    >
        <div v-loading="loading">
            <div class="detail-container">
                <div class="section-title">基本信息</div>
                <el-descriptions :column="4" border>
                    <el-descriptions-item label="井号">{{ detail.wellNumber }}</el-descriptions-item>
                    <el-descriptions-item label="趟次">{{ detail.run }}</el-descriptions-item>
                    <el-descriptions-item label="井类别">
                        <el-tag
                            v-if="detail.serviceWellCategory"
                            :type="getWellCategoryTagType(detail.serviceWellCategory)"
                            size="medium"
                        >
                            {{ detail.serviceWellCategory }}
                        </el-tag>
                        <span v-else>无数据</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="区块">{{ detail.block }}</el-descriptions-item>
                    <el-descriptions-item label="温度值">
                        <span
                            v-if="detail.temperatureValue"
                            :class="{
                                'normal-temperature': detail.temperatureValue <= 125,
                                'high-temperature': detail.temperatureValue > 125 && detail.temperatureValue <= 135,
                                'very-high-temperature': detail.temperatureValue > 135
                            }"
                        >
                            {{ detail.temperatureValue }}°C
                        </span>
                        <span v-else>无数据</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="振动级别">
                        <el-tag
                            v-if="detail.vibrationLevel"
                            :type="getVibrationLevelTagType(detail.vibrationLevel)"
                            size="medium"
                        >
                            {{ detail.vibrationLevel }}
                        </el-tag>
                        <span v-else>无数据</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="振动超标">{{ detail.vibrationOos ? '是' : '否' }}</el-descriptions-item>
                    <el-descriptions-item label="超标工况描述">{{ detail.exceededConditionDescription }}</el-descriptions-item>
                    <el-descriptions-item label="钻头尺寸">{{ detail.bitSize }}</el-descriptions-item>
                    <el-descriptions-item label="泥浆类型">{{ detail.mudType }}</el-descriptions-item>
                    <el-descriptions-item label="密度">{{ detail.mudDensity }}</el-descriptions-item>
                    <el-descriptions-item label="排量">{{ detail.flowRate }}</el-descriptions-item>
                    <el-descriptions-item label="施工开始井深(MD)">{{ detail.constructionStartDepthMd }}</el-descriptions-item>
                    <el-descriptions-item label="施工结束井深(MD)">{{ detail.constructionEndDepthMd }}</el-descriptions-item>
                    <el-descriptions-item label="进尺(MD)">{{ detail.footageMd }}</el-descriptions-item>
                    <el-descriptions-item label="Orifice配置">{{ detail.orificeConfig }}</el-descriptions-item>
                    <el-descriptions-item label="Poppet配置">{{ detail.poppetConfig }}</el-descriptions-item>
                    <el-descriptions-item label="信号强度">{{ detail.signalStrength }}</el-descriptions-item>
                    <el-descriptions-item label="入井时间">{{ formatDate(detail.dateIn) }}</el-descriptions-item>
                    <el-descriptions-item label="出井时间">{{ formatDate(detail.dateOut) }}</el-descriptions-item>
                    <el-descriptions-item label="起钻原因">{{ detail.tripOutReasonType }}</el-descriptions-item>
                    <el-descriptions-item label="是否故障">
                        <el-tag :type="detail.isFailure === '是' ? 'danger' : 'success'">
                            {{ detail.isFailure }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="综合失效类型">
                        <template v-if="detail.consolidatedFailureType">
                            <el-tooltip v-if="detail.failureReasonDescription" :content="detail.failureReasonDescription" placement="top">
                                <el-tag :type="getFailureTypeTagType(detail.consolidatedFailureType)" size="medium">
                                    {{ getFailureTypeDisplay(detail.consolidatedFailureType) }}
                                </el-tag>
                            </el-tooltip>
                            <el-tag v-else :type="getFailureTypeTagType(detail.consolidatedFailureType)" size="medium">
                                {{ getFailureTypeDisplay(detail.consolidatedFailureType) }}
                            </el-tag>
                        </template>
                        <span v-else>-</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="失效时间">
                        <span v-if="detail.failureTime" style="color: #F56C6C;">{{ formatDate(detail.failureTime) }}</span>
                        <span v-else>-</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="入井小时">{{ detail.tripInHours }}</el-descriptions-item>
                    <el-descriptions-item label="循环小时">{{ detail.circulatingHours }}</el-descriptions-item>
                    <el-descriptions-item label="多少时间失效(小时)">{{ detail.hoursToFailure }}</el-descriptions-item>
                    <el-descriptions-item label="维护级别">{{ detail.maintenanceLevel }}</el-descriptions-item>
                    <el-descriptions-item label="使用后维护完成日期">{{ formatDate(detail.postUseMaintenanceCompletionDate) }}</el-descriptions-item>
                    <el-descriptions-item label="更换主要部件描述">{{ detail.replacedMajorComponentsDesc }}</el-descriptions-item>
                    <el-descriptions-item label="具体失效总成分类">{{ detail.specificFailedAssemblyCategory }}</el-descriptions-item>
                    <el-descriptions-item label="失效总成的部件分类">{{ detail.failedComponentInAssemblyCategory }}</el-descriptions-item>
                    <el-descriptions-item label="失效主要原因标签">{{ detail.primaryFailureReasonTag }}</el-descriptions-item>
                    <el-descriptions-item label="井下仪器情况">{{ detail.downholeInstrumentStatusDesc }}</el-descriptions-item>
                    <el-descriptions-item label="数据录入人">{{ detail.dataEntryPerson }}</el-descriptions-item>
                    <el-descriptions-item :span="25" label="更新时间">{{ formatDate(detail.updateTime) }}</el-descriptions-item>
                    <el-descriptions-item :span="15" label="现场端主要情况">
                        <div class="rich-text-container">
                            <image-viewer>
                                <div v-html="sanitizeHtml(detail.fieldIncidentDescription || '无')" class="rich-text-content"></div>
                            </image-viewer>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item :span="15" label="维护/研发端检查发现">
                        <div class="rich-text-container">
                            <image-viewer>
                                <div v-html="sanitizeHtml(detail.workshopShopFinding || '无')" class="rich-text-content"></div>
                            </image-viewer>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item :span="15" label="根本原因分析">
                        <div class="rich-text-container">
                            <image-viewer>
                                <div v-html="sanitizeHtml(detail.failureReasonDescription || '无')" class="rich-text-content"></div>
                            </image-viewer>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item :span="15" label="改进计划/措施">
                        <div class="rich-text-container">
                            <image-viewer>
                                <div v-html="sanitizeHtml(detail.improvementPlanMeasures || '无')" class="rich-text-content"></div>
                            </image-viewer>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>

                <div class="section-title">工具信息</div>
                <div class="tool-info-container">
                    <div
                        v-for="(instrument, index) in detail.instruments"
                        :key="index"
                        :class="['tool-info-item', instrument.riskType ? `risk-${instrument.riskType}` : '']"
                    >
                        <div class="tool-role">{{ instrument.invName }}</div>
                        <el-tooltip :content="'风险类型: ' + (instrument.riskType || '未知')" placement="top">
                            <el-tag
                                :type="getRiskTypeTagType(instrument.riskType)"
                                size="small"
                                @click.native.stop="handleSerialNumberClick(instrument.serialNumber)"
                                style="cursor: pointer;"
                                >
                                {{ instrument.serialNumber }}
                            </el-tag>
                        </el-tooltip>
                    </div>
                    <div v-if="!detail.instruments || detail.instruments.length === 0" class="no-tools">
                        暂无工具信息
                    </div>
                </div>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { apiGetWellAnalysisDetailById } from '@/api/wellAnalysis';
import moment from 'moment';
import DOMPurify from 'dompurify';
import ImageViewer from '@/components/ImageViewer/index.vue';

export default {
    name: 'DetailDialog',
    components: {
        ImageViewer
    },
    data() {
        return {
            isDialogVisible: false,
            loading: false,
            detail: {}
        };
    },
    methods: {
        // 打开对话框
        open(id) {
            this.isDialogVisible = true;
            this.loading = true;
            this.detail = {};

            // 获取详情数据
            apiGetWellAnalysisDetailById(id)
                .then(response => {
                    this.detail = response.data.data || {};
                })
                .catch(error => {
                    console.error('获取井作业分析详情失败:', error);
                    this.$message.error('获取详情数据失败，请稍后重试');
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        // 关闭对话框
        handleClose() {
            this.isDialogVisible = false;
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '';
            return moment(date).format('YYYY-MM-DD HH:mm');
        },

        // 根据风险类型获取标签类型
        getRiskTypeTagType(riskType) {
            // 当风险类型为"未知"或为空值时，使用黄色标签（warning类型）
            if (!riskType) {
                return 'warning';
            }

            switch(riskType) {
                case 'RISK':
                    return 'danger';
                case 'PRODUCE':
                    return 'success';
                case 'TEST':
                    return 'primary';
                case 'SCRAP':
                    return 'info';
                default:
                    return 'warning'; // 未知风险类型也使用黄色标签
            }
        },

        // 根据振动级别获取标签类型
        getVibrationLevelTagType(level) {
            if (!level) return '';

            // 转换为小写以便比较
            const lowerLevel = level.toLowerCase();

            if (lowerLevel.includes('high') || lowerLevel.includes('高')) {
                return 'danger';
            } else if (lowerLevel.includes('medium') || lowerLevel.includes('中')) {
                return 'warning';
            } else if (lowerLevel.includes('low') || lowerLevel.includes('低')) {
                return 'success';
            } else if (lowerLevel.includes('very') || lowerLevel.includes('极')) {
                return 'danger'; // very high 使用深红色
            } else {
                return 'info';
            }
        },

        // 根据井类别获取标签类型
        getWellCategoryTagType(category) {
            if (!category) return '';
            switch (category) {
                case '近钻井':
                    return 'primary'; // 蓝色
                case '非近钻井':
                    return 'info';    // 灰色
                case '旋转导向':
                    return 'success'; // 绿色
                default:
                    return '';
            }
        },

        // 根据故障类型获取标签类型
        getFailureTypeTagType(failureType) {
            switch(failureType) {
                case 'NO_FAILURE':
                    return 'success';
                case 'RSS_FAILURE':
                    return 'warning';
                case 'AT_BIT_FAILURE':
                    return 'danger';
                case 'MWD_FAILURE':
                    return 'danger';
                default:
                    return 'info';
            }
        },

        // 获取故障类型显示文本
        getFailureTypeDisplay(failureType) {
            if (!failureType) return '';

            const failureTypeMap = {
                'NO_FAILURE': '正常运行',
                'RSS_FAILURE': '旋导服务失效',
                'AT_BIT_FAILURE': '近钻工具失效',
                'MWD_FAILURE': 'MWD服务失效',
                'OTHER_FAILURE': '其他类型失效'
            };

            // 如果在映射表中找到对应的中文描述，则返回中文描述
            if (failureTypeMap[failureType]) {
                return failureTypeMap[failureType];
            }

            // 如果不在映射表中，尝试格式化显示
            // 将下划线替换为空格，每个单词首字母大写
            return failureType
                .split('_')
                .map(word => word.charAt(0) + word.slice(1).toLowerCase())
                .join(' ');
        },

        // 根据工具类型编码获取中文名称
        getToolTypeName(toolType) {
            const toolTypeMap = {
                '16001': '近钻头',
                '16002': '方位探管',
                '16003': '方位Gamma',
                '16004': 'MWD电池',
                '16005': '底部总成',
                '16006': '扶正器',
                '16007': '定向探管',
                '16008': 'Gamma',
                '16009': '绝缘短节',
                '16010': 'KIT箱',
                '16011': '脉冲器',
                '16012': '接收器',
                '16013': '打捞头',
                '16014': 'UBHO',
                '16015': '其他',
                '16016': '循环套',
                '16017': '整流器',
                '16018': '长无磁钻杆',
                '16019': '长无磁钻挺',
                '16020': '无磁短节',
                '16021': '多点仪器',
                '16022': '滤网短节',
                '16023': '钻具尾扶',
                '16024': '扣型转接头',
                '16025': 'TAPP发射机总成',
                '16026': 'TAPP电池总成',
                '16027': '旋导LCP',
                '16028': '旋导MWD',
                '16029': '旋导SU',
                '16030': 'TAPP绝缘总成',
                '16031': '司显',
                '16032': '井深盒子',
                '16033': 'Gamma Key',
                '16034': '电脑',
                '16035': '旋导箱',
                '16036': '随钻测压'
            };

            // 确保toolType是字符串
            const typeStr = String(toolType);

            // 返回映射的中文名称，如果没有找到则返回原始编码
            return toolTypeMap[typeStr] || typeStr;
        },

        // HTML内容净化，防止XSS攻击
        sanitizeHtml(html) {
            if (!html) return '';

            try {
                // 使用DOMPurify库进行HTML内容净化
                // 配置DOMPurify允许保留图片但限制大小
                const sanitizedHtml = DOMPurify.sanitize(html, {
                    ADD_TAGS: ['img'],
                    ADD_ATTR: ['src', 'alt', 'width', 'height', 'style', 'class', 'loading', 'data-original'],
                    FORBID_TAGS: ['script', 'iframe', 'object', 'embed'],
                    FORBID_ATTR: ['onerror', 'onload', 'onclick']
                });

                // 处理HTML中的图片，限制最大宽度
                const parser = new DOMParser();
                const doc = parser.parseFromString(sanitizedHtml, 'text/html');
                const images = doc.querySelectorAll('img');

                images.forEach(img => {
                    // 保存原始图片URL
                    const originalSrc = img.getAttribute('src');
                    if (originalSrc) {
                        img.setAttribute('data-original', originalSrc);
                    }

                    // 设置样式
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = '300px';
                    img.style.cursor = 'pointer';
                    img.style.transition = 'transform 0.3s';
                    img.style.borderRadius = '4px';
                    img.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                    img.style.margin = '8px 0';

                    // 添加懒加载属性
                    img.setAttribute('loading', 'lazy');

                    // 添加类名，方便CSS样式控制
                    img.classList.add('rich-content-image');
                });

                // 将处理后的HTML转换回字符串
                return doc.body.innerHTML;
            } catch (error) {
                console.error('HTML净化失败:', error);
                // 如果处理失败，返回原始HTML（不推荐，但作为降级处理）
                return html;
            }
        },

        // 处理序列号点击
        handleSerialNumberClick(serialNumber) {
            if (!serialNumber) return;
            this.$router.push({
                path: '/mwd-tool-maintain/device',
                query: { sn: serialNumber }
            });
        },
    }
};
</script>

<style lang="scss" scoped>
.detail-container {
    padding: 0 10px;
}

.el-descriptions {
    margin-bottom: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #409EFF;
    margin: 20px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
}

.tool-info-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 3px 0;
    padding: 6px;
    background-color: #f0f7ff;
    border-radius: 4px;
    max-height: 180px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.tool-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-width: 100px;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
}

.tool-info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f9f9f9;
}

/* 根据风险类型添加不同的边框颜色 */
.tool-info-item.risk-RISK {
    border-left: 3px solid #F56C6C;
}

.tool-info-item.risk-PRODUCE {
    border-left: 3px solid #67C23A;
}

.tool-info-item.risk-TEST {
    border-left: 3px solid #409EFF;
}

.tool-info-item.risk-SCRAP {
    border-left: 3px solid #909399;
}

.tool-role {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
    font-weight: 500;
}

.tool-type {
    font-size: 11px;
    color: #909399;
    margin-bottom: 4px;
    background-color: #f0f2f5;
    padding: 2px 6px;
    border-radius: 2px;
}

.no-tools {
    color: #909399;
    font-style: italic;
    padding: 8px;
    width: 100%;
    text-align: center;
}

::v-deep .el-tag {
    display: inline-block;
    padding: 0 10px;
    height: 28px;
    line-height: 26px;
    font-size: 13px;
    border-radius: 4px;
}

/* 温度值样式 */
.normal-temperature {
    background-color: rgba(64, 158, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    color: #409EFF;
    font-weight: bold;
}

.high-temperature {
    background-color: rgba(245, 108, 108, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    color: #F56C6C;
    font-weight: bold;
}

.very-high-temperature {
    background-color: #F56C6C;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
}

/* 富文本容器和内容样式 */
.rich-text-container {
    width: 100%;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

.rich-text-content {
    overflow-y: auto;
    padding: 12px;
    background-color: #fff;
    border-radius: 4px;
    line-height: 1.5;
    border: 1px solid #ebeef5;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
    min-height: 100px;
}

/* 在el-descriptions中的富文本内容样式 */
::v-deep .el-descriptions-item__content {
    padding: 12px !important;
}

::v-deep .el-descriptions-item__content .rich-text-container {
    width: 100%;
    margin: 0;
    padding: 0;
}

::v-deep .el-descriptions-item__content .rich-text-content {
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}

/* 富文本内容中的图片样式 */
.rich-text-content img {
    max-width: 100%;
    height: auto;
    max-height: 300px;
    margin: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: block;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.rich-text-content img:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 图片查看器样式优化 */
::v-deep .el-image-viewer__wrapper {
    z-index: 3000 !important;
}

::v-deep .el-image-viewer__close {
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 24px;
}

::v-deep .el-image-viewer__prev,
::v-deep .el-image-viewer__next {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    font-size: 24px;
    color: #fff;
}

/* 富文本内容中的段落样式 */
.rich-text-content p {
    margin: 8px 0;
    word-break: break-word;
}

/* 确保富文本内容中的表格正确显示 */
.rich-text-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
}

.rich-text-content table td,
.rich-text-content table th {
    border: 1px solid #ddd;
    padding: 8px;
}

/* 确保富文本内容中的列表正确显示 */
.rich-text-content ul,
.rich-text-content ol {
    padding-left: 20px;
    margin: 8px 0;
}
</style>
