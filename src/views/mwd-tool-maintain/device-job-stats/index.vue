<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">井作业运行及失效分析详情</div>
            <div class="app-card-content">
                <!-- 搜索区域 -->
                <div class="filter-container">
                    <el-form :inline="true" :model="searchForm" class="form-inline">
                        <el-form-item label="井号">
                            <el-input v-model="searchForm.wellNumber" placeholder="请输入井号" clearable @change="handleSearch" @clear="handleSearch"></el-input>
                        </el-form-item>
                        <el-form-item label="趟次" v-if="viewMode === 'run'">
                            <el-input v-model="searchForm.runNumber" placeholder="请输入趟次" clearable type="number" @change="handleSearch" @clear="handleSearch"></el-input>
                        </el-form-item>
                        <el-form-item label="井趟类别" v-if="viewMode === 'run'">
                            <el-select v-model="searchForm.serviceWellCategory" placeholder="请选择井趟类别" clearable @change="handleSearch">
                                <el-option label="近钻井" value="近钻井"></el-option>
                                <el-option label="非近钻井" value="非近钻井"></el-option>
                                <el-option label="旋转导向" value="旋转导向"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="是否故障">
                            <el-select v-model="searchForm.isFailure" placeholder="请选择" clearable @change="handleSearch">
                                <el-option label="是" value="是"></el-option>
                                <el-option label="否" value="否"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-radio-group v-model="viewMode" @change="handleViewModeChange" class="view-switch">
                                <el-radio-button label="run">趟次视图</el-radio-button>
                                <el-radio-button label="well">井汇总视图</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item class="action-buttons">
                            <el-button size="small" icon="el-icon-refresh" circle @click="refreshTable" title="刷新"></el-button>
                            <el-button size="small" icon="el-icon-download" type="success" circle @click="handleExport" title="导出"></el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 表格区域 -->
                <div class="table-container">
                    <div class="table-info">
                        <span class="table-count">共 {{ currentDisplayTotal }} 条记录</span>
                        <span v-if="viewMode === 'well'" class="table-summary">
                            （{{ totalWells }} 口井，{{ totalRuns }} 个趟次）
                        </span>
                    </div>
                    <el-table
                        v-loading="tableLoading"
                        :data="currentTableData"
                        :header-cell-style="commonTableHeaderCellStyle"
                        height="calc(100vh - 275px)"
                        @sort-change="handleSortChange"
                        ref="wellAnalysisTable"
                        :row-key="getRowKey"
                        :key="tableKey"
                        stripe
                        highlight-current-row
                        @row-click="handleRowClick"
                        @expand-change="handleExpandChange"
                        :row-class-name="tableRowClassName"
                        :default-sort="{ prop: 'createTime', order: 'descending' }"
                    >
                        <!-- 展开行 -->
                        <el-table-column type="expand">
                            <template slot-scope="scope">
                                <!-- 趟次视图的展开内容 -->
                                <div v-if="viewMode === 'run'">
                                    <!-- 加载中状态 -->
                                    <div v-if="rowLoadingStatus[scope.row.id]" class="loading-container">
                                        <el-skeleton :rows="6" animated />
                                        <div class="loading-text">正在加载详细内容，请稍候...</div>
                                    </div>

                                    <!-- 已加载内容 -->
                                    <el-form v-else label-width="110px" class="expanded-form">
                                        <el-row :gutter="10">
                                            <el-col :span="20">
                                                <div class="section-title">基本信息</div>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="泥浆类型" class="compact-form-item">
                                                    <span>{{ scope.row.mudType }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="泥浆密度" class="compact-form-item">
                                                    <span>{{ scope.row.mudDensity }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="排量" class="compact-form-item">
                                                    <span>{{ scope.row.flowRate }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="Orifice配置" class="compact-form-item">
                                                    <span>{{ scope.row.orificeConfig }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="Poppet配置" class="compact-form-item">
                                                    <span>{{ scope.row.poppetConfig }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="信号强度" class="compact-form-item">
                                                    <span>{{ scope.row.signalStrength }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="振动级别" class="compact-form-item">
                                                    <el-tag
                                                        v-if="scope.row.vibrationLevel"
                                                        :type="getVibrationLevelTagType(scope.row.vibrationLevel)"
                                                        size="small"
                                                    >
                                                        {{ scope.row.vibrationLevel }}
                                                    </el-tag>
                                                    <span v-else>无数据</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="振动超标" class="compact-form-item">
                                                    <span>{{ scope.row.vibrationOos ? '是' : '否' }}</span>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="6">
                                                <el-form-item label="井下仪器情况" class="compact-form-item">
                                                    <span>{{ scope.row.downholeInstrumentStatusDesc }}</span>
                                                </el-form-item>
                                            </el-col>

                                            <!-- 工具信息 -->
                                            <el-col :span="24">
                                                <el-form-item label="工具信息" class="compact-form-item">
                                                    <div class="tool-info-container">
                                                        <div
                                                            v-for="(instrument, index) in scope.row.instruments"
                                                            :key="index"
                                                            :class="['tool-info-item', instrument.riskType ? `risk-${instrument.riskType}` : '']"
                                                        >
                                                            <div class="tool-role">{{ instrument.invName }}</div>
                                                            <el-tooltip :content="'风险类型: ' + (instrument.riskType || '未知')" placement="top">
                                                                <el-tag
                                                                    :type="getRiskTypeTagType(instrument.riskType)"
                                                                    size="small"
                                                                    @click.native.stop="handleSerialNumberClick(instrument.serialNumber)"
                                                                    style="cursor: pointer;"
                                                                >
                                                                    {{ instrument.serialNumber }}
                                                                </el-tag>
                                                            </el-tooltip>
                                                        </div>
                                                        <div v-if="!scope.row.instruments || scope.row.instruments.length === 0" class="no-tools">
                                                            暂无工具信息
                                                        </div>
                                                    </div>
                                                </el-form-item>
                                            </el-col>

                                            <el-col :span="24">
                                                <div class="section-title">故障信息</div>
                                            </el-col>
                                            <el-col :span="24">
                                                <el-form-item label="现场端主要情况" class="compact-form-item">
                                                    <rich-text-preview
                                                        :content="scope.row.fieldIncidentDescription"
                                                        title="现场端主要情况"
                                                        :preview-max-length="250"
                                                        empty-text="无"
                                                        expand-button-text="查看完整内容"
                                                        :show-expand-button="true"
                                                        size="large"
                                                    ></rich-text-preview>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="24">
                                                <el-form-item label="维护/研发端" class="compact-form-item">
                                                    <rich-text-preview
                                                        :content="scope.row.workshopShopFinding"
                                                        title="维护/研发端检查发现"
                                                        :preview-max-length="200"
                                                        empty-text="无"
                                                        expand-button-text="查看完整内容"
                                                        :show-expand-button="true"
                                                        size="large"
                                                    ></rich-text-preview>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="24">
                                                <el-form-item label="根本原因分析" class="compact-form-item">
                                                    <rich-text-preview
                                                        :content="scope.row.failureReasonDescription"
                                                        title="根本原因分析"
                                                        :preview-max-length="200"
                                                        empty-text="无"
                                                        expand-button-text="查看完整内容"
                                                        :show-expand-button="true"
                                                        size="large"
                                                    ></rich-text-preview>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="24">
                                                <el-form-item label="改进计划/措施" class="compact-form-item">
                                                    <rich-text-preview
                                                        :content="scope.row.improvementPlanMeasures"
                                                        title="改进计划/措施"
                                                        :preview-max-length="200"
                                                        empty-text="无"
                                                        expand-button-text="查看完整内容"
                                                        :show-expand-button="true"
                                                        size="large"
                                                    ></rich-text-preview>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>

                                <!-- 井汇总视图的展开内容：显示该井的所有趟次 -->
                                <div v-else-if="viewMode === 'well'" class="well-runs-container">
                                    <!-- 加载中状态 -->
                                    <div v-if="rowLoadingStatus[getRowKey(scope.row)]" class="loading-container">
                                        <el-skeleton :rows="3" animated />
                                        <div class="loading-text">正在加载该井的趟次详情，请稍候...</div>
                                    </div>

                                    <!-- 已加载内容 -->
                                    <div v-else>
                                        <div class="section-title">{{ scope.row.wellNumber }} 的所有趟次详情</div>

                                        <!-- 趟次列表 -->
                                        <div class="runs-list">
                                            <div
                                                v-for="(run, index) in getWellRunsData(scope.row.wellNumber)"
                                                :key="run.id || index"
                                                :data-run-id="run.id"
                                                :class="['run-item', { 'run-item-unmodified': isRunUnmodified(run) }]"
                                            >
                                                <!-- 趟次基本信息 -->
                                                <div class="run-header" @click.stop="toggleRunDetail(run)">
                                                    <div class="run-basic-info">
                                                        <div class="run-number">
                                                            <el-tag type="primary" size="small">第{{ run.run }}趟</el-tag>
                                                        </div>
                                                        <div class="job-number">
                                                            <span class="label">作业号：</span>
                                                            <span class="value">{{ run.jobNumber }}</span>
                                                        </div>
                                                        <div class="failure-status">
                                                            <el-tag :type="run.isFailure === '是' ? 'danger' : 'success'" size="mini">
                                                                {{ run.isFailure === '是' ? '故障' : '正常' }}
                                                            </el-tag>
                                                        </div>
                                                        <div class="work-time">
                                                            <span class="label">入井小时：</span>
                                                            <span class="value">{{ (parseFloat(run.tripInHours) || 0).toFixed(1) }}h</span>
                                                        </div>
                                                        <div class="tool-count">
                                                            <span class="label">工具：</span>
                                                            <span class="value">{{ run.instruments ? run.instruments.length : 0 }}个</span>
                                                        </div>
                                                    </div>
                                                    <div class="run-header-right">
                                                        <div class="run-actions">
                                                            <el-button type="text" size="mini" @click.stop="handleDetail(run)">详情</el-button>
                                                            <el-button type="text" size="mini" @click.stop="handleEdit(run)">编辑</el-button>
                                                        </div>
                                                        <div class="expand-icon" @click.stop="toggleRunDetail(run)">
                                                            <i :class="isRunExpanded(run.id) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                                                            <span class="expand-text">{{ isRunExpanded(run.id) ? '收起' : '展开' }}</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 趟次详细信息（可展开） -->
                                                <div v-if="isRunExpanded(run.id)" class="run-details">
                                                    <el-row :gutter="20">
                                                        <!-- 技术参数 -->
                                                        <el-col :span="12">
                                                            <div class="detail-section">
                                                                <h4 class="detail-title">技术参数</h4>
                                                                <div class="detail-content">
                                                                    <div class="param-item">
                                                                        <span class="param-label">温度：</span>
                                                                        <span class="param-value">{{ run.temperatureValue || '-' }}°C</span>
                                                                    </div>
                                                                    <div class="param-item">
                                                                        <span class="param-label">井深：</span>
                                                                        <span class="param-value">{{ run.constructionStartDepthMd || '-' }} - {{ run.constructionEndDepthMd || '-' }}m</span>
                                                                    </div>
                                                                    <div class="param-item">
                                                                        <span class="param-label">进尺：</span>
                                                                        <span class="param-value">{{ run.footageMd || '-' }}m</span>
                                                                    </div>
                                                                                                                        <div class="param-item">
                                                        <span class="param-label">时间：</span>
                                                        <span class="param-value">{{ formatDate(run.dateIn, 'MM-DD HH:mm') }} - {{ formatDate(run.dateOut, 'MM-DD HH:mm') }}</span>
                                                    </div>
                                                    <div class="param-item" v-if="run.failureTime">
                                                        <span class="param-label">失效时间：</span>
                                                        <span class="param-value" style="color: #F56C6C;">{{ formatDate(run.failureTime, 'MM-DD HH:mm') }}</span>
                                                    </div>
                                                                </div>
                                                            </div>
                                                        </el-col>

                                                        <!-- 工具信息 -->
                                                        <el-col :span="12">
                                                            <div class="detail-section">
                                                                <h4 class="detail-title">工具信息</h4>
                                                                <div class="detail-content">
                                                                    <div v-if="run.instruments && run.instruments.length > 0" class="tool-info-container">
                                                                        <div
                                                                            v-for="tool in run.instruments"
                                                                            :key="tool.id"
                                                                            :class="['tool-info-item', tool.riskType ? `risk-${tool.riskType}` : '']"
                                                                        >
                                                                            <div class="tool-role">{{ tool.invName || tool.instrumentRole || '-' }}</div>
                                                                            <el-tooltip :content="'风险类型: ' + (tool.riskType || '未知')" placement="top">
                                                                                <el-tag
                                                                                    :type="getRiskTypeTagType(tool.riskType)"
                                                                                    size="small"
                                                                                    @click.native.stop="handleSerialNumberClick(tool.serialNumber)"
                                                                                    style="cursor: pointer;"
                                                                                >
                                                                                    {{ tool.serialNumber }}
                                                                                </el-tag>
                                                                            </el-tooltip>
                                                                        </div>
                                                                    </div>
                                                                    <div v-else class="no-tools">暂无工具信息</div>
                                                                </div>
                                                            </div>
                                                        </el-col>
                                                    </el-row>

                                                    <!-- 富文本内容 -->
                                                    <div v-if="hasRichContent(run)" class="rich-content-section">
                                                        <h4 class="detail-title">详细信息</h4>
                                                        <el-row :gutter="20">
                                                            <el-col :span="12" v-if="run.fieldIncidentDescription">
                                                                <div class="rich-content-item">
                                                                    <div class="rich-content-label">现场端主要情况：</div>
                                                                    <rich-text-preview
                                                                        :content="run.fieldIncidentDescription"
                                                                        title="现场端主要情况"
                                                                        :preview-max-length="150"
                                                                        empty-text="无"
                                                                        expand-button-text="查看完整内容"
                                                                        :show-expand-button="true"
                                                                        dialog-width="60%"
                                                                        size="large"
                                                                    ></rich-text-preview>
                                                                </div>
                                                            </el-col>
                                                            <el-col :span="12" v-if="run.workshopShopFinding">
                                                                <div class="rich-content-item">
                                                                    <div class="rich-content-label">维护/研发端检查发现：</div>
                                                                    <rich-text-preview
                                                                        :content="run.workshopShopFinding"
                                                                        title="维护/研发端检查发现"
                                                                        :preview-max-length="150"
                                                                        empty-text="无"
                                                                        expand-button-text="查看完整内容"
                                                                        :show-expand-button="true"
                                                                        dialog-width="60%"
                                                                        size="large"
                                                                    ></rich-text-preview>
                                                                </div>
                                                            </el-col>
                                                            <el-col :span="12" v-if="run.failureReasonDescription">
                                                                <div class="rich-content-item">
                                                                    <div class="rich-content-label">根本原因分析：</div>
                                                                    <rich-text-preview
                                                                        :content="run.failureReasonDescription"
                                                                        title="根本原因分析"
                                                                        :preview-max-length="150"
                                                                        empty-text="无"
                                                                        expand-button-text="查看完整内容"
                                                                        :show-expand-button="true"
                                                                        dialog-width="60%"
                                                                        size="large"
                                                                    ></rich-text-preview>
                                                                </div>
                                                            </el-col>
                                                            <el-col :span="12" v-if="run.improvementPlanMeasures">
                                                                <div class="rich-content-item">
                                                                    <div class="rich-content-label">改进计划/措施：</div>
                                                                    <rich-text-preview
                                                                        :content="run.improvementPlanMeasures"
                                                                        title="改进计划/措施"
                                                                        :preview-max-length="150"
                                                                        empty-text="无"
                                                                        expand-button-text="查看完整内容"
                                                                        :show-expand-button="true"
                                                                        dialog-width="60%"
                                                                        size="large"
                                                                    ></rich-text-preview>
                                                                </div>
                                                            </el-col>
                                                        </el-row>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <!-- 主要字段 -->
                        <el-table-column prop="wellNumber" label="井号" min-width="160" sortable>
                            <template slot-scope="scope">
                                <el-tooltip
                                    :content="getJobStatusTooltip(scope.row.jobStatus)"
                                    placement="top"
                                >
                                    <span
                                        :class="getJobStatusClass(scope.row.jobStatus)"
                                        class="well-number-text"
                                    >
                                        {{ scope.row.wellNumber }}
                                    </span>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <!-- 趟次视图专有列 -->
                        <el-table-column v-if="viewMode === 'run'" prop="jobNumber" label="作业号" min-width="90" sortable>
                            <template slot-scope="scope">
                                <span
                                    @click.stop="handleJobNumberClick(scope.row.jobInfoIdFk)"
                                    style="cursor: pointer; color: #409EFF;"
                                >
                                    {{ scope.row.jobNumber }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" prop="run" label="趟次" min-width="75" sortable></el-table-column>

                        <!-- 趟次视图：井趟类别列 -->
                        <el-table-column v-if="viewMode === 'run'" prop="serviceWellCategory" label="井趟类别" min-width="100" sortable>
                            <template slot-scope="scope">
                                <el-tag
                                    :type="getWellCategoryTagType(scope.row.serviceWellCategory)"
                                    size="small"
                                >
                                    {{ scope.row.serviceWellCategory }}
                                </el-tag>
                            </template>
                        </el-table-column>

                        <!-- 井汇总视图专有列 -->
                        <el-table-column v-if="viewMode === 'well'" prop="jobNumber" label="作业号" min-width="90" sortable>
                            <template slot-scope="scope">
                                <span
                                    @click.stop="handleJobNumberClick(scope.row.jobInfoIdFk)"
                                    style="cursor: pointer; color: #409EFF;"
                                >
                                    {{ scope.row.jobNumber }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'well'" prop="totalRuns" label="总趟次" min-width="100" sortable align="center">
                            <template slot-scope="scope">
                                <el-tag type="primary">{{ scope.row.totalRuns }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'well'" label="失效情况" min-width="120" sortable>
                            <template slot-scope="scope">
                                <el-tooltip :content="`失效趟次: ${scope.row.failureRuns}/${scope.row.totalRuns}`" placement="top">
                                    <el-tag :type="parseFloat(scope.row.failureRate) > 20 ? 'danger' : 'success'">
                                        {{ scope.row.failureRate }}
                                    </el-tag>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'well'" prop="totalTripInHours" label="总入井小时" min-width="120" sortable align="center">
                            <template slot-scope="scope">
                                <el-tag type="info">{{ scope.row.totalTripInHours || '0' }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'well'" prop="totalCirculatingHours" label="总循环小时" min-width="120" sortable align="center">
                            <template slot-scope="scope">
                                <el-tag type="success">{{ scope.row.totalCirculatingHours || '0' }}</el-tag>
                            </template>
                        </el-table-column>
                        
                                <!-- 温度列 - 根据视图模式显示不同内容 -->
        <el-table-column label="温度" min-width="130" sortable :prop="viewMode === 'well' ? 'temperatureRange' : 'temperatureValue'">
            <template slot-scope="scope">
                <span v-if="viewMode === 'run'">
                    <span
                        v-if="scope.row.temperatureValue"
                        :class="{
                            'normal-temperature': scope.row.temperatureValue <= 125,
                            'high-temperature': scope.row.temperatureValue > 125 && scope.row.temperatureValue <= 150,
                            'very-high-temperature': scope.row.temperatureValue > 150
                        }"
                    >
                        {{ scope.row.temperatureValue }}
                    </span>
                    <span v-else>-</span>
                </span>
                <span v-else>{{ scope.row.temperatureRange }}</span>
            </template>
        </el-table-column>

        <!-- 振动级别列 - 仅在趟次视图显示 -->
        <el-table-column v-if="viewMode === 'run'" label="振动级别" min-width="120" sortable prop="vibrationLevel">
            <template slot-scope="scope">
                <el-tag
                    v-if="scope.row.vibrationLevel"
                    :type="getVibrationLevelTagType(scope.row.vibrationLevel)"
                    size="small"
                >
                    {{ scope.row.vibrationLevel }}
                </el-tag>
                <span v-else>-</span>
            </template>
        </el-table-column>

        <!-- 区块列 -->
                        <el-table-column prop="block" label="区块" min-width="120" sortable></el-table-column>

                        <!-- 井汇总视图专有列 -->
                        <el-table-column v-if="viewMode === 'well'" prop="constructionPeriod" label="施工周期" min-width="220" sortable>
                            <template slot-scope="scope">
                                <span>{{ scope.row.constructionPeriod }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'well'" prop="mainFailureTypes" label="主要故障类型" min-width="120">
                            <template slot-scope="scope">
                                <template v-if="scope.row.mainFailureTypes && scope.row.mainFailureTypes !== '-'">
                                    <span v-for="(type, index) in scope.row.mainFailureTypes.split(', ')" :key="index">
                                        <el-tag
                                            :type="getFailureTypeTagType(type)"
                                            size="medium"
                                            style="margin-right: 4px; margin-bottom: 2px; cursor: pointer;"
                                            @click.native.stop="handleFailureTypeClick(scope.row.jobInfoIdFk, type)"
                                        >
                                            {{ getFailureTypeDisplay(type) }}
                                        </el-tag>
                                    </span>
                                </template>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>

                        <!-- 趟次视图专有列 -->
                        <el-table-column v-if="viewMode === 'run'" label="施工井深(MD)" min-width="140" sortable prop="constructionEndDepthMd">
                            <template slot-scope="scope">
                                <el-tooltip :content="`开始: ${scope.row.constructionStartDepthMd || '无'}, 结束: ${scope.row.constructionEndDepthMd || '无'}`" placement="top">
                                    <span>{{ scope.row.constructionStartDepthMd}} → {{ scope.row.constructionEndDepthMd}}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" prop="footageMd" label="进尺(MD)" min-width="110" sortable></el-table-column>
                        <el-table-column v-if="viewMode === 'run'" prop="isFailure" label="是否故障" min-width="100" align="center" sortable>
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.isFailure === '是' ? 'danger' : 'success'">
                                    {{ scope.row.isFailure }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" prop="consolidatedFailureType" label="故障类型" min-width="120" sortable>
                            <template slot-scope="scope">
                                <template v-if="scope.row.consolidatedFailureType">
                                    <el-tag
                                        :type="getFailureTypeTagType(scope.row.consolidatedFailureType)"
                                        size="medium"
                                        @click.native.stop="handleFailureTypeClick(scope.row.jobInfoIdFk, scope.row.consolidatedFailureType)"
                                        style="cursor: pointer;"
                                    >
                                        {{ getFailureTypeDisplay(scope.row.consolidatedFailureType) }}
                                    </el-tag>
                                </template>
                                <span v-else></span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="失效时间" min-width="150" sortable prop="failureTime">
                            <template slot-scope="scope">
                                <span v-if="scope.row.failureTime" style="color: #F56C6C;">{{ formatDate(scope.row.failureTime, 'YYYY-MM-DD HH:mm') }}</span>
                                <span v-else style="color: #909399;">-</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="入井/出井时间" min-width="200" sortable prop="dateIn">
                            <template slot-scope="scope">
                                <el-tooltip :content="`入井: ${formatDate(scope.row.dateIn)}, 出井: ${formatDate(scope.row.dateOut)}`" placement="top">
                                    <span>{{ formatDate(scope.row.dateIn, 'YYYY-MM-DD') }} / {{ formatDate(scope.row.dateOut, 'YYYY-MM-DD') }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="入井小时" min-width="100" sortable prop="tripInHours" align="center">
                            <template slot-scope="scope">
                                <el-tag type="info">{{ scope.row.tripInHours || '0' }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="循环小时" min-width="100" sortable prop="circulatingHours" align="center">
                            <template slot-scope="scope">
                                <el-tag type="success">{{ scope.row.circulatingHours || '0' }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="维护级别" min-width="120" prop="maintenanceLevel" align="center">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.maintenanceLevel" type="warning">{{ scope.row.maintenanceLevel }}</el-tag>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="维护完成日期" min-width="140" prop="postUseMaintenanceCompletionDate" align="center">
                            <template slot-scope="scope">
                                <span v-if="scope.row.postUseMaintenanceCompletionDate">
                                    {{ formatDate(scope.row.postUseMaintenanceCompletionDate, 'YYYY-MM-DD HH:mm') }}
                                </span>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="更换主要部件" min-width="180" prop="replacedMajorComponentsDesc">
                            <template slot-scope="scope">
                                <el-tag v-if="scope.row.replacedMajorComponentsDesc" type="warning">{{ scope.row.replacedMajorComponentsDesc }}</el-tag>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="viewMode === 'run'" label="起钻原因" min-width="200" prop="tripOutReasonType">
                            <template slot-scope="scope">
                                <rich-text-preview
                                    :content="scope.row.tripOutReasonType"
                                    title="起钻原因"
                                    :preview-max-length="60"
                                    empty-text="-"
                                    :show-expand-button="false"
                                ></rich-text-preview>
                            </template>
                        </el-table-column>

                        <!-- 趟次视图专有的富文本预览列 -->
                        <el-table-column v-if="viewMode === 'run'" label="现场端主要情况" min-width="180">
                            <template slot-scope="scope">
                                <rich-text-preview
                                    :content="scope.row.fieldIncidentDescription"
                                    title="现场端主要情况"
                                    :preview-max-length="120"
                                    empty-text="-"
                                    :show-expand-button="false"
                                ></rich-text-preview>
                            </template>
                        </el-table-column>

                        <el-table-column v-if="viewMode === 'run'" label="维护/研发端检查发现" min-width="180">
                            <template slot-scope="scope">
                                <rich-text-preview
                                    :content="scope.row.workshopShopFinding"
                                    title="维护/研发端检查发现"
                                    :preview-max-length="120"
                                    empty-text="-"
                                    :show-expand-button="false"
                                ></rich-text-preview>
                            </template>
                        </el-table-column>

                        <el-table-column v-if="viewMode === 'run'" label="根本原因分析" min-width="180">
                            <template slot-scope="scope">
                                <rich-text-preview
                                    :content="scope.row.failureReasonDescription"
                                    title="根本原因分析"
                                    :preview-max-length="100"
                                    empty-text="-"
                                    :show-expand-button="false"
                                ></rich-text-preview>
                            </template>
                        </el-table-column>

                        <el-table-column v-if="viewMode === 'run'" label="改进计划/措施" min-width="180">
                            <template slot-scope="scope">
                                <rich-text-preview
                                    :content="scope.row.improvementPlanMeasures"
                                    title="改进计划/措施"
                                    :preview-max-length="100"
                                    empty-text="-"
                                    :show-expand-button="false"
                                ></rich-text-preview>
                            </template>
                        </el-table-column>

                        <el-table-column v-if="viewMode === 'run'" label="操作" fixed="right" width="120">
                            <template slot-scope="scope">
                                <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
                                <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination
                        :hide-on-single-page="true"
                        style="margin-top: 20px"
                        background
                        layout="prev, pager, next"
                        :total="total"
                        :page-size="pageSize"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                    ></el-pagination>
                </div>
            </div>
        </div>

        <!-- 详情对话框 -->
        <detail-dialog ref="detailDialog" />

        <!-- 编辑对话框 -->
        <edit-dialog ref="editDialog" @refresh="getWellAnalysisDetailList" />


    </div>
</template>

<script>
import { apiGetWellAnalysisBasicInfoList, apiGetWellAnalysisRichContent, apiSynchronizeWellAnalysisData, apiExportWellAnalysisExcel, apiGetWellRunsSummary, apiGetWellSummaryList } from '@/api/wellAnalysis';
import DetailDialog from './components/detailDialog.vue';
import EditDialog from './components/editDialog.vue';
import moment from 'moment';
import ImageViewer from '@/components/ImageViewer/index.vue';
import RichTextPreview from '@/components/RichTextPreview/index.vue';

// 常量定义
const CONSTANTS = {
    // 搜索防抖延迟
    SEARCH_DEBOUNCE_DELAY: 300,
    // 分页大小
    DEFAULT_PAGE_SIZE: 50,
    // 温度阈值
    TEMPERATURE_THRESHOLDS: {
        HIGH: 125,
        VERY_HIGH: 150
    },
    // 视图模式
    VIEW_MODES: {
        RUN: 'run',
        WELL: 'well'
    },
    // 风险类型
    RISK_TYPES: {
        RISK: 'RISK',
        PRODUCE: 'PRODUCE',
        TEST: 'TEST',
        SCRAP: 'SCRAP'
    },
    // 故障状态
    FAILURE_STATUS: {
        YES: '是',
        NO: '否'
    }
};

export default {
    name: "DeviceJobStats",
    components: {
        DetailDialog,
        EditDialog,
        ImageViewer,
        RichTextPreview
    },
    data() {
        return {
            // 搜索表单
            searchForm: {
                wellNumber: '',
                runNumber: '',
                serviceWellCategory: '',
                isFailure: ''
            },
            // 视图模式：'run' 趟次视图，'well' 井汇总视图
            viewMode: 'run',
            // 表格数据
            tableData: [], // 趟次原始数据
            aggregatedData: [], // 井汇总数据
            // 数据缓存
            cachedRunData: [], // 缓存的趟次数据
            cachedWellData: [], // 缓存的井汇总数据
            cachedRunTotal: 0, // 缓存的趟次总数
            cachedWellTotal: 0, // 缓存的井汇总总数
            // 表格key，用于强制重新渲染
            tableKey: 0,
            tableLoading: false,
            // 分页
            currentPage: 1,
            pageSize: CONSTANTS.DEFAULT_PAGE_SIZE,
            total: 0,
            // 排序
            orderBy: 'create_time',
            orderType: 'desc',
            // 本地排序标志，用于特殊列（如工具数量）
            localSortEnabled: false,
            localSortProp: '',
            localSortOrder: '',
            // 展开行的行ID
            expandedRowKeys: [],
            // 富文本内容缓存，key为行ID，value为富文本内容
            richContentCache: {},
            // 行加载状态，key为行ID，value为布尔值
            rowLoadingStatus: {},
            // 井趟次数据缓存，key为井号，value为趟次数据列表
            wellRunsCache: {},
            // 趟次展开状态管理，key为趟次ID，value为布尔值
            runExpandedStates: {},
            // 预览内容最大长度
            previewMaxLength: 100,
            // 搜索防抖定时器
            searchTimer: null
        };
    },
    computed: {
        // 当前显示的数据
        currentTableData() {
            return this.viewMode === 'run' ? this.tableData : this.aggregatedData;
        },

        // 当前显示的总数
        currentDisplayTotal() {
            return this.currentTableData.length;
        },

        // 总井数（根据视图模式计算）
        totalWells() {
            if (this.viewMode === 'well') {
                // 井汇总视图：直接返回井汇总数据的长度
                return this.aggregatedData.length;
            } else {
                // 趟次视图：从趟次数据中计算唯一井数
                if (!this.tableData.length) return 0;
                const wellSet = new Set(this.tableData.map(item => item.wellNumber));
                return wellSet.size;
            }
        },

        // 总趟次数（根据视图模式计算）
        totalRuns() {
            if (this.viewMode === 'well') {
                // 井汇总视图：汇总所有井的趟次数
                return this.aggregatedData.reduce((total, well) => total + (well.totalRuns || 0), 0);
            } else {
                // 趟次视图：直接返回趟次数据的长度
                return this.tableData.length;
            }
        },

        // 统计信息（合并计算，减少重复遍历）
        statisticsInfo() {
            if (!this.tableData.length) {
                return { totalWells: 0, totalRuns: 0, failureRuns: 0, failureRate: '0%' };
            }

            const wellSet = new Set();
            let failureCount = 0;

            this.tableData.forEach(item => {
                wellSet.add(item.wellNumber);
                if (item.isFailure === '是') {
                    failureCount++;
                }
            });

            const totalRuns = this.tableData.length;
            const failureRate = totalRuns > 0 ? ((failureCount / totalRuns) * 100).toFixed(1) + '%' : '0%';

            return {
                totalWells: wellSet.size,
                totalRuns,
                failureRuns: failureCount,
                failureRate
            };
        }
    },

    watch: {
        // 监听视图模式变化，重新获取数据
        viewMode: {
            handler(newVal, oldVal) {
                // 只有在值真正改变时才重新获取数据，避免初始化时的重复调用
                if (oldVal !== undefined && newVal !== oldVal) {
                    this.currentPage = 1; // 重置页码
                    this.getWellAnalysisDetailList(); // 重新获取数据
                }
            }
        }
    },

    created() {
        this.getWellAnalysisDetailList();
    },

    beforeDestroy() {
        // 清理定时器
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
            this.searchTimer = null;
        }

        // 清理缓存数据
        this.richContentCache = {};
        this.wellRunsCache = {};
        this.rowLoadingStatus = {};
        this.runExpandedStates = {};
    },
    methods: {
        // 获取井作业分析详情列表（仅基本信息，不包含富文本内容）
        getWellAnalysisDetailList() {
            // 根据视图模式调用不同的数据获取方法
            if (this.viewMode === 'well') {
                this.getWellSummaryList();
            } else {
                this.getRunDetailList();
            }
        },

        // 获取趟次详情列表
        getRunDetailList(useCache = false) {
            // 如果使用缓存且缓存中有数据，直接使用缓存
            if (useCache && this.cachedRunData.length > 0) {
                this.tableData = this.cachedRunData;
                this.total = this.cachedRunTotal;
                this.tableLoading = false;
                return;
            }

            this.tableLoading = true;
            const params = {
                current: this.currentPage,
                size: this.pageSize,
                wellNumber: this.searchForm.wellNumber || undefined,
                runNumber: this.searchForm.runNumber || undefined,
                serviceWellCategory: this.searchForm.serviceWellCategory || undefined,
                isFailure: this.searchForm.isFailure || undefined
            };

            // 只有在非本地排序模式下，才添加排序参数
            if (!this.localSortEnabled) {
                params.orderBy = this.orderBy;
                params.orderType = this.orderType;
            }

            console.log('趟次视图请求参数:', params);

            apiGetWellAnalysisBasicInfoList(params)
                .then(response => {
                    console.log('趟次视图API响应:', response);
                    const data = response.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total || 0;

                    // 缓存数据
                    this.cachedRunData = [...this.tableData];
                    this.cachedRunTotal = this.total;

                    // 如果启用了本地排序，则在获取数据后执行本地排序
                    if (this.localSortEnabled && this.localSortProp && this.localSortOrder) {
                        this.performLocalSort(this.localSortProp, this.localSortOrder);
                    }

                    // 确保每条记录都有createTime和updateTime字段
                    this.tableData.forEach(record => {
                        if (!record.createTime) record.createTime = '';
                        if (!record.updateTime) record.updateTime = '';
                    });

                    // 不默认展开任何行
                    this.$nextTick(() => {
                        // 清空已展开的行
                        this.expandedRowKeys = [];
                        // 确保所有行都是收起状态
                        if (this.tableData.length > 0 && this.$refs.wellAnalysisTable) {
                            this.tableData.forEach(row => {
                                this.$refs.wellAnalysisTable.toggleRowExpansion(row, false);
                            });
                        }
                    });
                })
                .catch(error => {
                    console.error('获取趟次详情列表失败:', error);
                    this.$message.error('获取数据失败，请稍后重试');
                })
                .finally(() => {
                    this.tableLoading = false;
                });
        },

        // 获取井汇总列表
        getWellSummaryList(useCache = false) {
            // 如果使用缓存且缓存中有数据，直接使用缓存
            if (useCache && this.cachedWellData.length > 0) {
                this.aggregatedData = this.cachedWellData;
                this.total = this.cachedWellTotal;
                this.tableData = []; // 清空趟次数据
                this.tableLoading = false;
                return;
            }

            this.tableLoading = true;

            // 清理之前的状态，避免重复键问题
            this.expandedRowKeys = [];
            this.rowLoadingStatus = {};
            this.wellRunsCache = {};

            const params = {
                current: this.currentPage,
                size: this.pageSize, // 使用动态分页大小
                wellNumber: this.searchForm.wellNumber || undefined,
                isFailure: this.searchForm.isFailure || undefined
            };

            // 只有在非本地排序模式下，才添加排序参数
            if (!this.localSortEnabled) {
                params.orderBy = this.orderBy;
                params.orderType = this.orderType;
            }

            console.log('井汇总视图请求参数:', params);

            apiGetWellSummaryList(params)
                .then(response => {
                    console.log('井汇总视图API响应:', response);
                    const data = response.data.data || {};
                    let records = data.records || [];

                    // 直接使用后端返回的数据，不进行去重处理
                    // 因为现在按井号+作业号分组，同一井号可能有多个不同作业
                    this.aggregatedData = records;
                    this.total = data.total || 0;

                    // 缓存数据
                    this.cachedWellData = [...this.aggregatedData];
                    this.cachedWellTotal = this.total;

                    // 清空趟次数据，避免混淆
                    this.tableData = [];

                    console.log('井汇总数据（去重后）:', this.aggregatedData);

                    // 确保表格重新渲染
                    this.$nextTick(() => {
                        if (this.$refs.wellAnalysisTable) {
                            this.$refs.wellAnalysisTable.clearSelection();
                            this.$refs.wellAnalysisTable.doLayout();
                        }
                    });
                })
                .catch(error => {
                    console.error('获取井汇总列表失败:', error);
                    this.$message.error('获取井汇总数据失败，请稍后重试');
                })
                .finally(() => {
                    this.tableLoading = false;
                });
        },

        // 查询（添加防抖优化）
        handleSearch() {
            // 清除之前的定时器
            if (this.searchTimer) {
                clearTimeout(this.searchTimer);
            }

            // 设置新的定时器
            this.searchTimer = setTimeout(() => {
                // 清空缓存，确保搜索时重新获取数据
                this.clearCache();
                this.currentPage = 1;
                this.getWellAnalysisDetailList();
            }, CONSTANTS.SEARCH_DEBOUNCE_DELAY);
        },

        // 清空缓存
        clearCache() {
            this.cachedRunData = [];
            this.cachedWellData = [];
            this.cachedRunTotal = 0;
            this.cachedWellTotal = 0;
        },

        // 重置查询 - 已不再使用，保留方法以防其他地方调用
        resetSearch() {
            this.searchForm = {
                wellNumber: '',
                runNumber: '',
                serviceWellCategory: '',
                isFailure: ''
            };
            this.currentPage = 1;
            this.getWellAnalysisDetailList();
        },

        // 分页变化
        handleCurrentChange(page) {
            this.currentPage = page;
            this.getWellAnalysisDetailList();
        },

        // 每页条数变化
        handleSizeChange(size) {
            this.pageSize = size;
            this.currentPage = 1;
            this.getWellAnalysisDetailList();
        },

        // 排序变化 - 后端API排序实现
        handleSortChange({ prop, order }) {
            // 如果没有指定排序属性或排序方向，使用默认排序
            if (!prop || !order) {
                this.orderBy = 'create_time';
                this.orderType = 'desc';
            } else {
                // 检查是否是需要本地排序的特殊列
                if (prop === 'instruments') {
                    // 工具数量列需要本地排序
                    this.localSortEnabled = true;
                    this.localSortProp = prop;
                    this.localSortOrder = order;

                    // 执行本地排序
                    this.performLocalSort(prop, order);
                    return; // 不需要调用后端API
                }

                // 将前端的排序字段映射到后端的数据库字段
                this.orderBy = this.getSortFieldMapping(prop);
                this.orderType = order === 'ascending' ? 'asc' : 'desc';
            }

            // 重新获取数据
            this.getWellAnalysisDetailList();
        },

        // 执行本地排序（仅用于特殊列，如工具数量）
        performLocalSort(prop, order) {
            const isAsc = order === 'ascending';

            // 根据不同的特殊列进行排序
            if (prop === 'instruments') {
                // 工具数量排序
                this.tableData.sort((a, b) => {
                    const aCount = a.instruments ? a.instruments.length : 0;
                    const bCount = b.instruments ? b.instruments.length : 0;
                    return isAsc ? aCount - bCount : bCount - aCount;
                });
            }
        },

        // 获取表格行的唯一键
        getRowKey(row) {
            if (this.viewMode === 'well') {
                // 井汇总视图：使用井号+作业号作为唯一键
                return `well_${row.wellNumber}_${row.jobNumber}`;
            } else {
                // 趟次视图：使用ID作为唯一键
                return row.id || `run_${row.wellNumber}_${row.run}`;
            }
        },

        // 获取排序字段映射
        getSortFieldMapping(prop) {
            // 将前端的属性名映射到后端的数据库字段名
            const fieldMapping = {
                'wellNumber': 'well_number',
                'jobNumber': 'job_number',
                'jobStatus': 'job_status',
                'run': 'run',
                'serviceWellCategory': 'service_well_category',
                'temperatureValue': 'temperature_value',
                'block': 'block',
                'constructionEndDepthMd': 'construction_end_depth_md',
                'footageMd': 'footage_md',
                'isFailure': 'is_failure',
                'dateIn': 'date_in',
                'tripInHours': 'trip_in_hours',
                'circulatingHours': 'circulating_hours',
                'maintenanceLevel': 'maintenance_level',
                'postUseMaintenanceCompletionDate': 'post_use_maintenance_completion_date',
                'replacedMajorComponentsDesc': 'replaced_major_components_desc',
                'tripOutReasonType': 'trip_out_reason_type',
                'totalTripInHours': 'totalTripInHours',
                'totalCirculatingHours': 'totalCirculatingHours',
                'totalWorkHours': 'totalWorkHours',
                'createTime': 'create_time',
                'updateTime': 'update_time'
            };

            return fieldMapping[prop] || prop;
        },

        // 查看详情
        handleDetail(row) {
            this.$refs.detailDialog.open(row.id);
        },

        // 编辑
        handleEdit(row) {
            this.$refs.editDialog.open(row.id);
        },


        // 格式化日期
        formatDate(date, format = 'YYYY-MM-DD HH:mm') {
            if (!date) return '';
            return moment(date).format(format);
        },

        // 表头样式 - 系统通用样式
        commonTableHeaderCellStyle() {
            return {
                backgroundColor: '#f5f7fa',
                color: '#606266',
                fontWeight: 'bold'
            };
        },

        // 处理表格展开/收起事件
        handleExpandChange(row, expandedRows) {
            // 获取行的唯一键
            const rowKey = this.getRowKey(row);

            // 判断当前行是展开还是收起
            const isExpanded = expandedRows.some(r => this.getRowKey(r) === rowKey);

            if (isExpanded) {
                // 更新展开行ID列表
                if (!this.expandedRowKeys.includes(rowKey)) {
                    this.expandedRowKeys.push(rowKey);
                }

                // 根据视图模式加载不同的内容
                if (this.viewMode === 'run') {
                    // 趟次视图：加载富文本内容
                    this.loadRowRichContent(row);
                } else if (this.viewMode === 'well') {
                    // 井汇总视图：加载该井的所有趟次数据
                    this.loadWellRunsData(row);
                }
            } else {
                // 如果是收起操作，从展开行ID列表中移除
                const index = this.expandedRowKeys.indexOf(rowKey);
                if (index !== -1) {
                    this.expandedRowKeys.splice(index, 1);
                }
            }
        },

        // 行点击处理
        handleRowClick(row, column) {
            // 如果点击的是操作列，不执行展开/收起操作
            if (column.label === '操作') {
                return;
            }

            // 获取行的唯一键
            const rowKey = this.getRowKey(row);

            // 获取当前行的展开状态
            const isExpanded = this.expandedRowKeys.includes(rowKey);

            if (!isExpanded) {
                // 如果行未展开，则展开行并加载内容
                this.expandRow(row);
            } else {
                // 如果行已展开，则收起行
                this.collapseRow(row);
            }
        },

        // 加载行的富文本内容（仅用于趟次视图）
        loadRowRichContent(row) {
            const rowId = row.id;

            // 检查是否为井汇总视图的ID格式，如果是则不加载富文本内容
            if (typeof rowId === 'string' && rowId.startsWith('well_')) {
                console.warn('井汇总视图不支持富文本内容加载，ID:', rowId);
                return;
            }

            // 更新展开行ID列表（如果不存在）
            if (!this.expandedRowKeys.includes(rowId)) {
                this.expandedRowKeys.push(rowId);
            }

            // 如果缓存中已有该行的富文本内容，则直接使用缓存
            if (this.richContentCache[rowId]) {
                // 使用缓存的富文本内容更新行数据
                this.updateRowWithRichContent(row, this.richContentCache[rowId]);
                return;
            }

            // 设置行加载状态
            this.$set(this.rowLoadingStatus, rowId, true);

            // 加载富文本内容
            apiGetWellAnalysisRichContent(rowId)
                .then(response => {
                    const richContent = response.data.data;
                    if (richContent) {
                        // 缓存富文本内容
                        this.richContentCache[rowId] = richContent;

                        // 更新行数据
                        this.updateRowWithRichContent(row, richContent);
                    }
                })
                .catch(error => {
                    console.error('获取富文本内容失败:', error);
                    this.$message.error('获取详细内容失败，请稍后重试');

                    // 如果加载失败，收起行
                    this.collapseRow(row);
                })
                .finally(() => {
                    // 清除行加载状态
                    this.$set(this.rowLoadingStatus, rowId, false);
                });
        },

        // 展开行并加载内容
        expandRow(row) {
            const rowKey = this.getRowKey(row);

            // 更新展开行ID列表
            if (!this.expandedRowKeys.includes(rowKey)) {
                this.expandedRowKeys.push(rowKey);
            }

            // 展开行（这会触发 handleExpandChange 事件，所以不需要在这里重复调用加载方法）
            this.$refs.wellAnalysisTable.toggleRowExpansion(row, true);
        },

        // 收起行
        collapseRow(row) {
            const rowKey = this.getRowKey(row);

            // 从展开行ID列表中移除
            const index = this.expandedRowKeys.indexOf(rowKey);
            if (index !== -1) {
                this.expandedRowKeys.splice(index, 1);
            }

            // 收起行
            this.$refs.wellAnalysisTable.toggleRowExpansion(row, false);
        },

        // 使用富文本内容更新行数据
        updateRowWithRichContent(row, richContent) {
            // 使用 Vue.set 确保响应式更新
            const fieldsToUpdate = [
                'mudType', 'mudDensity', 'flowRate', 'orificeConfig',
                'poppetConfig', 'signalStrength', 'failureReasonDescription',
                'improvementPlanMeasures', 'fieldIncidentDescription',
                'downholeInstrumentStatusDesc', 'workshopShopFinding',
                'maintenanceLevel', 'postUseMaintenanceCompletionDate', 'replacedMajorComponentsDesc',
                'tripOutReasonType'
            ];

            fieldsToUpdate.forEach(field => {
                if (richContent[field] !== undefined) {
                    this.$set(row, field, richContent[field]);
                }
            });
        },

        // 导出数据
        handleExport() {
            this.$confirm('确认导出当前筛选条件下的所有数据？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                // 显示加载状态
                const loadingInstance = this.$loading({
                    lock: true,
                    text: '正在导出数据，请稍候...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 构建查询参数
                const params = {};

                // 添加当前筛选条件
                if (this.searchForm.wellNumber) {
                    params.wellNumber = this.searchForm.wellNumber;
                }
                if (this.searchForm.runNumber) {
                    params.runNumber = this.searchForm.runNumber;
                }
                if (this.searchForm.serviceWellCategory) {
                    params.serviceWellCategory = this.searchForm.serviceWellCategory;
                }
                if (this.searchForm.isFailure) {
                    params.isFailure = this.searchForm.isFailure;
                }

                // 调用API导出Excel
                apiExportWellAnalysisExcel(params)
                    .then(response => {
                        // 创建Blob对象
                        const blob = new Blob([response.data], {
                            type: 'application/vnd.ms-excel;charset=utf-8'
                        });

                        // 生成文件名
                        const fileName = `井作业分析详情_${new Date().toISOString().split('T')[0]}.xlsx`;

                        // 创建下载链接
                        const link = document.createElement('a');
                        link.style.display = 'none';
                        link.href = window.URL.createObjectURL(blob);
                        link.download = fileName;

                        // 触发下载
                        document.body.appendChild(link);
                        link.click();

                        // 清理
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(link.href);

                        // 关闭加载状态
                        loadingInstance.close();

                        this.$message({
                            type: 'success',
                            message: '导出成功'
                        });
                    })
                    .catch(error => {
                        // 关闭加载状态
                        loadingInstance.close();

                        console.error('导出失败:', error);
                        this.$message.error('导出失败: ' + (error.message || '未知错误'));
                    });
            }).catch(() => {
                // 取消导出
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
        },

        // 刷新表格
        refreshTable() {
            this.tableLoading = true;

            // 先调用后端同步接口
            const loadingMessage = this.$message({
                type: 'info',
                message: '正在同步数据，请稍候...',
                duration: 0,
                showClose: true
            });

            apiSynchronizeWellAnalysisData()
                .then(() => {
                    // 同步成功后，清空缓存并获取最新数据
                    this.$message({
                        type: 'success',
                        message: '数据同步成功，正在刷新表格...',
                        duration: 1500
                    });

                    // 清空缓存，确保获取最新数据
                    this.clearCache();
                    // 获取最新数据列表
                    this.getWellAnalysisDetailList();
                })
                .catch(error => {
                    console.error('数据同步失败:', error);
                    this.$message({
                        type: 'error',
                        message: '数据同步失败，请稍后重试',
                        duration: 3000
                    });

                    // 即使同步失败，也清空缓存并刷新表格显示当前数据
                    this.clearCache();
                    this.getWellAnalysisDetailList();
                })
                .finally(() => {
                // 无论成功还是失败，都确保关闭加载消息
                loadingMessage.close();
            });
        },

        // 根据风险类型获取标签类型
        getRiskTypeTagType(riskType) {
            // 当风险类型为"未知"或为空值时，使用黄色标签（warning类型）
            if (!riskType) {
                return 'warning';
            }

            switch(riskType) {
                case 'RISK':
                    return 'danger';
                case 'PRODUCE':
                    return 'success';
                case 'TEST':
                    return 'primary';
                case 'SCRAP':
                    return 'info';
                default:
                    return 'warning'; // 未知风险类型也使用黄色标签
            }
        },

        // 根据故障类型获取标签类型
        getFailureTypeTagType(failureType) {
            // 处理英文故障类型
            switch(failureType) {
                case 'NO_FAILURE':
                    return 'success';
                case 'RSS_FAILURE':
                    return 'warning';
                case 'AT_BIT_FAILURE':
                    return 'danger';
                case 'MWD_FAILURE':
                    return 'danger';
                default:
                    break;
            }

            // 处理中文故障类型
            switch(failureType) {
                case '正常运行':
                    return 'success';
                case '旋导服务失效':
                case '旋转导向失效':
                    return 'warning';
                case '近钻工具失效':
                case '近钻失效':
                    return 'danger';
                case 'MWD系统失效':
                case 'MWD服务失效':
                    return 'danger';
                case '其他类型失效':
                    return 'info';
                default:
                    return 'info';
            }
        },

        // 获取故障类型显示文本
        getFailureTypeDisplay(failureType) {
            if (!failureType) return '';

            const failureTypeMap = {
                'NO_FAILURE': '正常运行',
                'RSS_FAILURE': '旋导服务失效',
                'AT_BIT_FAILURE': '近钻工具失效',
                'MWD_FAILURE': 'MWD系统失效',
                'OTHER_FAILURE': '其他类型失效'
            };

            // 如果在映射表中找到对应的中文描述，则返回中文描述
            if (failureTypeMap[failureType]) {
                return failureTypeMap[failureType];
            }

            // 如果不在映射表中，尝试格式化显示
            // 将下划线替换为空格，每个单词首字母大写
            return failureType
                .split('_')
                .map(word => word.charAt(0) + word.slice(1).toLowerCase())
                .join(' ');
        },

        // 根据井类别获取标签类型
        getWellCategoryTagType(category) {
            if (!category) return '';
            switch (category) {
                case '近钻井':
                    return 'primary'; // 蓝色
                case '非近钻井':
                    return 'info';    // 灰色
                case '旋转导向':
                    return 'success'; // 绿色
                default:
                    return '';
            }
        },

        // 根据振动级别获取标签类型
        getVibrationLevelTagType(level) {
            if (!level) return '';

            // 转换为小写以便比较
            const lowerLevel = level.toLowerCase();

            if (lowerLevel.includes('high') || lowerLevel.includes('高')) {
                return 'danger';
            } else if (lowerLevel.includes('medium') || lowerLevel.includes('中')) {
                return 'warning';
            } else if (lowerLevel.includes('low') || lowerLevel.includes('低')) {
                return 'success';
            } else if (lowerLevel.includes('very') || lowerLevel.includes('极')) {
                return 'danger'; // very high 使用深红色
            } else {
                return 'info';
            }
        },



        // 处理序列号点击
        handleSerialNumberClick(serialNumber) {
            if (!serialNumber) return;
            this.$router.push({
                path: '/mwd-tool-maintain/device',
                query: { sn: serialNumber }
            });
        },

        // 处理作业号点击
        handleJobNumberClick(jobInfoIdFk) {
            if (!jobInfoIdFk) return;
            // 在新标签页打开链接
            const routeUrl = this.$router.resolve({
                path: '/daily/detail',
                query: { jobid: jobInfoIdFk }
            });
            window.open(routeUrl.href, '_blank');
        },

        // 处理故障类型点击
        handleFailureTypeClick(jobInfoIdFk, failureType) {
            if (!jobInfoIdFk) return;

            // 判断是否为RSS故障类型
            const isRssFailure = failureType && (
                failureType.includes('RSS') ||
                failureType.includes('RSS_FAILURE') ||
                failureType === 'RSS_FAILURE'
            );

            // 根据故障类型选择不同的路径
            const path = isRssFailure ? '/failure/rss/index' : '/failure/index';

            const routeUrl = this.$router.resolve({
                path: path,
                query: { jobid: jobInfoIdFk }
            });
            // 在新标签页打开故障分析链接
            window.open(routeUrl.href, '_blank');
        },

        // 获取字段显示名称
        getFieldDisplayName(fieldName) {
            const fieldNameMap = {
                'failureReasonDescription': '根本原因分析',
                'improvementPlanMeasures': '改进计划/措施',
                'fieldIncidentDescription': '现场端主要情况',
                'workshopShopFinding': '维护/研发端检查发现',
                'downholeInstrumentStatusDesc': '井下仪器情况',
                'maintenanceLevel': '维护级别',
                'postUseMaintenanceCompletionDate': '使用后维护完成日期',
                'replacedMajorComponentsDesc': '更换主要部件描述',
                'tripOutReasonType': '起钻原因'
            };

            return fieldNameMap[fieldName] || fieldName;
        },

        // 判断是否为新记录
        isNewRecord(row) {
            // 井汇总视图：使用后端返回的 hasUnmodifiedRecords 字段
            if (this.viewMode === 'well') {
                return row.hasUnmodifiedRecords === true;
            }

            // 趟次视图：保持原有逻辑，比较 createTime 和 updateTime
            if (!row.createTime || !row.updateTime) return false;

            try {
                // 使用moment处理ISO 8601格式的日期字符串
                // 将日期转换为UTC时间戳进行比较，避免时区问题
                const createTime = moment(row.createTime).valueOf();
                const updateTime = moment(row.updateTime).valueOf();

                // 如果创建时间和更新时间相同，则为新记录
                return createTime === updateTime;
            } catch (error) {
                console.error('日期比较错误:', error);
                return false;
            }
        },

        // 表格行的类名
        tableRowClassName({ row }) {
            // 如果是新记录，添加new-record类名
            const isNew = this.isNewRecord(row);
            const className = isNew ? 'new-record' : '';
            return className;
        },

        // 获取作业状态对应的CSS类
        getJobStatusClass(jobStatus) {
            if (jobStatus === 0) {
                return 'job-status-in-progress'; // 未完趟
            } else if (jobStatus === 1) {
                return 'job-status-completed'; // 已完趟
            } else if (jobStatus === 2) {
                return 'job-status-finished'; // 完趟
            }
            return ''; // 未知状态不添加特殊样式
        },

        // 获取作业状态的tooltip内容
        getJobStatusTooltip(jobStatus) {
            if (jobStatus === 0) {
                return '未完趟';
            } else if (jobStatus === 1) {
                return '完井';
            } else if (jobStatus === 2) {
                return '完趟';
            }
            return '未知状态';
        },

        // 视图模式切换处理
        handleViewModeChange(newMode) {
            // 显示切换加载状态
            this.tableLoading = true;

            // 使用 nextTick 确保UI更新
            this.$nextTick(() => {
                this.viewMode = newMode;

                // 清空展开行和相关缓存
                this.expandedRowKeys = [];
                this.wellRunsCache = {};
                this.rowLoadingStatus = {};
                this.runExpandedStates = {};

                // 强制表格重新渲染，解决column错位问题
                this.tableKey += 1;

                // 重置页码
                this.currentPage = 1;

                // 根据视图模式使用缓存数据或重新获取数据
                if (newMode === 'run') {
                    // 切换到趟次视图，尝试使用缓存
                    this.getRunDetailList(true);
                } else if (newMode === 'well') {
                    // 切换到井汇总视图，尝试使用缓存
                    this.getWellSummaryList(true);
                }

                // 确保表格重新渲染
                this.$nextTick(() => {
                    if (this.$refs.wellAnalysisTable) {
                        this.$refs.wellAnalysisTable.clearSelection();
                        this.$refs.wellAnalysisTable.doLayout();
                    }
                });
            });
        },



        // 加载井的所有趟次数据
        loadWellRunsData(row) {
            const wellNumber = row.wellNumber;
            const rowKey = this.getRowKey(row);

            // 如果缓存中已有该井的趟次数据，则直接使用缓存
            if (this.wellRunsCache[wellNumber]) {
                // 确保缓存的数据有正确的 _expanded 属性
                this.initializeRunsExpandState(this.wellRunsCache[wellNumber]);
                console.log('使用缓存的井趟次数据:', wellNumber, this.wellRunsCache[wellNumber].length);
                return;
            }

            // 设置行加载状态
            this.$set(this.rowLoadingStatus, rowKey, true);

            console.log('开始加载井趟次数据:', wellNumber);

            // 调用API获取该井的所有趟次数据
            apiGetWellRunsSummary(wellNumber)
                .then(response => {
                    const runsData = response.data.data;
                    if (runsData && Array.isArray(runsData)) {
                        // 初始化展开状态
                        this.initializeRunsExpandState(runsData);
                        // 缓存趟次数据
                        this.wellRunsCache[wellNumber] = runsData;
                        console.log('成功加载井趟次数据:', wellNumber, runsData.length);
                    } else {
                        this.wellRunsCache[wellNumber] = [];
                        console.log('井趟次数据为空:', wellNumber);
                    }
                })
                .catch(error => {
                    console.error('获取井趟次数据失败:', error);
                    this.$message.error('获取井趟次详情失败，请稍后重试');

                    // 如果加载失败，收起行
                    this.collapseRow(row);
                })
                .finally(() => {
                    // 清除行加载状态
                    this.$set(this.rowLoadingStatus, rowKey, false);
                });
        },

        // 初始化趟次数据的展开状态
        initializeRunsExpandState(runs) {
            if (!runs || !Array.isArray(runs)) return;

            runs.forEach(run => {
                if (!run.hasOwnProperty('_expanded')) {
                    this.$set(run, '_expanded', false);
                }
            });
        },

        // 获取井的趟次数据
        getWellRunsData(wellNumber) {
            const runs = this.wellRunsCache[wellNumber] || [];
            console.log('getWellRunsData 调用，井号:', wellNumber, '趟次数量:', runs.length);
            return runs;
        },

        // 检查趟次是否展开
        isRunExpanded(runId) {
            return !!this.runExpandedStates[runId];
        },

        // 切换趟次详情展开状态
        toggleRunDetail(run) {
            console.log('toggleRunDetail 被调用，run:', run);
            const runId = run.id;
            const currentState = this.isRunExpanded(runId);
            const newState = !currentState;

            console.log('当前展开状态:', currentState, '新状态:', newState);

            // 使用Vue.set更新响应式状态
            this.$set(this.runExpandedStates, runId, newState);

            console.log('状态更新完成，runExpandedStates:', this.runExpandedStates);

            // 使用nextTick确保DOM更新
            this.$nextTick(() => {
                console.log('DOM更新完成，当前状态:', this.isRunExpanded(runId));
                // 检查DOM元素是否存在
                const detailElement = document.querySelector(`[data-run-id="${runId}"] .run-details`);
                const debugElement = document.querySelector(`[data-run-id="${runId}"] .debug-info`);
                console.log('展开详情DOM元素:', detailElement);
                console.log('调试信息DOM元素:', debugElement);
            });
        },

        // 检查是否有富文本内容
        hasRichContent(run) {
            const hasContent = run.fieldIncidentDescription ||
                              run.workshopShopFinding ||
                              run.failureReasonDescription ||
                              run.improvementPlanMeasures;
            console.log('hasRichContent 检查:', run.id, hasContent);
            return hasContent;
        },

        // 判断井汇总中的趟次是否未修改过
        isRunUnmodified(run) {
            if (!run.createTime || !run.updateTime) return false;

            try {
                // 使用moment处理ISO 8601格式的日期字符串
                // 将日期转换为UTC时间戳进行比较，避免时区问题
                const createTime = moment(run.createTime).valueOf();
                const updateTime = moment(run.updateTime).valueOf();

                // 如果创建时间和更新时间相同，则为未修改记录
                return createTime === updateTime;
            } catch (error) {
                console.error('趟次日期比较错误:', error);
                return false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.app-card-content {
    padding: 1px;
}

.filter-container {
    padding: 10px;
    border-radius: 4px;
}

.form-inline {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    margin-bottom: 0;
}

.form-inline .el-form-item {
    margin-right: 10px;
    margin-bottom: 0;
    flex-shrink: 0;
}

.action-buttons {
    margin-left: auto;
    margin-right: 0 !important;
}

.table-container {
    margin-top: 1px;
}

.table-info {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 10px;
}

.table-count {
    font-size: 13px;
    color: #909399;
    padding: 0 10px;
}

.delete-btn {
    color: #F56C6C;
}

.delete-btn:hover {
    color: #f78989;
}

.expanded-form {
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

/* 优化表单项间距 */
.expanded-form .el-form-item {
    margin-bottom: 6px;
}

/* 紧凑型表单项 */
.expanded-form .compact-form-item {
    margin-bottom: 5px;
}

.expanded-form .compact-form-item ::v-deep .el-form-item__label {
    line-height: 1.3;
    padding-bottom: 4px;
}

.expanded-form .compact-form-item ::v-deep .el-form-item__content {
    line-height: 1.3;
}

.section-title {
    font-size: 14px;
    font-weight: bold;
    color: #409EFF;
    margin: 8px 0 6px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
}

.tool-info-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 3px 0;
    padding: 6px;
    background-color: #f0f7ff;
    border-radius: 4px;
    max-height: 180px;
    overflow-y: auto;
}

.tool-info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-width: 100px;
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
}

.tool-info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f9f9f9;
}

/* 根据风险类型添加不同的边框颜色 */
.tool-info-item.risk-RISK {
    border-left: 3px solid #F56C6C;
}

.tool-info-item.risk-PRODUCE {
    border-left: 3px solid #67C23A;
}

.tool-info-item.risk-TEST {
    border-left: 3px solid #409EFF;
}

.tool-info-item.risk-SCRAP {
    border-left: 3px solid #909399;
}

.tool-role {
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
    font-weight: 500;
    text-align: center;
}

/* 加载状态容器 */
.loading-container {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.loading-text {
    text-align: center;
    color: #909399;
    margin-top: 15px;
    font-size: 14px;
}



.no-tools {
    color: #909399;
    font-style: italic;
    padding: 8px;
    width: 100%;
    text-align: center;
}

/* 优化标签样式 */
.tool-info-item .el-tag {
    font-size: 12px;
    height: 24px;
    line-height: 22px;
    padding: 0 8px;
}

/* 表格行样式 */
::v-deep .el-table__row {
    transition: background-color 0.3s;
    height: 32px; /* 减小行高 */
}

::v-deep .el-table__row:hover {
    background-color: #f0f9ff !important;
}

::v-deep .el-table__row.current-row {
    background-color: #e6f7ff !important;
}

/* 表格单元格样式 */
::v-deep .el-table .cell {
    padding: 2px 8px;
    line-height: 1.1;
}

/* 优化表头高度 */
::v-deep .el-table__header-row {
    height: 36px;
}

/* 表格样式优化 */
::v-deep .el-table {
    border-radius: 4px;
    overflow: hidden;
}

/* 优化表格标签样式 */
::v-deep .el-table .el-tag {
    height: 22px;
    line-height: 20px;
    padding: 0 6px;
}

/* 优化表格按钮样式 */
::v-deep .el-table .el-button--text {
    padding: 2px 0;
}

/* 修复表格动画效果 */
::v-deep .el-table__body-wrapper {
    transition: all 0.3s;
}

/* 确保表格行高一致 */
::v-deep .el-table__row td {
    height: 32px !important;
}

/* 温度值样式 */
.normal-temperature {
    background-color: rgba(64, 158, 255, 0.1);
    padding: 4px 8px;
    color: #409EFF;
    font-weight: bold;
}

.high-temperature {
    background-color: rgba(245, 108, 108, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    color: #F56C6C;
    font-weight: bold;
}

.very-high-temperature {
    background-color: #F56C6C;
    padding: 4px 8px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
}

/* 富文本内容样式 */
/* 数据状态指示器样式 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    line-height: 1;
    white-space: nowrap;
    transition: all 0.2s;
}

.status-indicator i {
    margin-right: 2px;
    font-size: 12px;
}

.status-indicator.new {
    background-color: rgba(103, 194, 58, 0.1);
    color: #67C23A;
    border: 1px solid rgba(103, 194, 58, 0.2);
}

.status-indicator.edited {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
    border: 1px solid rgba(144, 147, 153, 0.2);
}

/* 新数据行样式 - 只保留左侧边框 */
::v-deep .el-table .new-record td:first-child {
    position: relative;
}

::v-deep .el-table .new-record td:first-child::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #67C23A;
    z-index: 1;
}

/* 图片查看器样式优化 */
::v-deep .el-image-viewer__wrapper {
    z-index: 3000 !important;
}

::v-deep .el-image-viewer__close {
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 24px;
}

::v-deep .el-image-viewer__prev,
::v-deep .el-image-viewer__next {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    font-size: 24px;
    color: #fff;
}

.rich-text-content p {
    margin: 8px 0;
    word-break: break-word;
}

/* 预览内容样式 */

/* 井号作业状态颜色样式 */
.well-number-text {
    font-weight: 500;
    transition: all 0.2s ease;
}

.job-status-in-progress {
    color: #E6A23C !important; /* 橙色 - 未完趟 */
}

.job-status-completed {
    color: #67C23A !important; /* 绿色 - 已完趟 */
}

.job-status-finished {
    color: #409EFF !important; /* 蓝色 - 完趟 */
}

.well-number-text:hover {
    opacity: 0.8;
}

.view-switch {
    margin-right: 20px;
}

.table-summary {
    color: #909399;
    font-size: 12px;
    margin-left: 10px;
}

/* 井汇总视图展开行样式 */
.well-runs-container {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.well-runs-container .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
}

.runs-list {
    margin-top: 12px;
}

.run-item {
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: visible;
    transition: all 0.3s ease;
}

.run-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 未修改的趟次样式 - 浅绿色边框 */
.run-item-unmodified .run-header {
    border: 2px solid rgba(103, 194, 58, 0.3) !important;
    background: rgba(103, 194, 58, 0.02);
}

.run-item-unmodified .run-header:hover {
    border-color: rgba(103, 194, 58, 0.5) !important;
    background: rgba(103, 194, 58, 0.05);
}



.run-basic-info > div {
    display: flex;
    align-items: center;
    gap: 4px;
}

.run-basic-info .label {
    color: #666;
    font-size: 13px;
}

.run-basic-info .value {
    color: #333;
    font-weight: 500;
    font-size: 13px;
}



.expand-text {
    font-size: 12px;
    font-weight: 500;
}

.run-details {
    padding: 20px;
    background-color: #fafafa;
    border-top: 1px solid #eee;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-title {
    margin: 0 0 12px 0;
    color: #409EFF;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #e6f7ff;
    padding-bottom: 6px;
}

.detail-content {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.param-item {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
}

.param-item:last-child {
    margin-bottom: 0;
}

.param-label {
    color: #666;
    font-size: 13px;
    min-width: 60px;
    flex-shrink: 0;
}

.param-value {
    color: #333;
    font-size: 13px;
    font-weight: 500;
}



/* 保留原有的网格样式，用于其他地方 */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
}

.tool-item {
    background: #f0f7ff;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #d6e7ff;
    text-align: center;
}

.tool-sn {
    font-size: 12px;
    font-weight: 600;
    color: #409EFF;
    margin-bottom: 2px;
}

.no-tools {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 12px;
}

.rich-content-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e8e8e8;
}

.rich-content-item {
    margin-bottom: 16px;
}

.rich-content-label {
    color: #666;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.rich-content-label::before {
    content: '';
    width: 3px;
    height: 14px;
    border-radius: 2px;
}

/* 富文本预览组件的样式优化 */
.rich-content-item .rich-text-preview {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
}

.rich-content-item .rich-text-preview:hover {
    border-color: #409EFF;
    background: #f0f7ff;
}

/* 保留原有样式作为备用 */
.rich-content-value {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    max-height: 120px;
    overflow-y: auto;
}

.rich-content-value:empty::before {
    content: '暂无内容';
    color: #999;
    font-style: italic;
}

/* 趟次头部布局 */
.run-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e8e8e8;
    margin: 0 0 8px 0;
    position: relative;
    overflow: visible;
}

.run-header:hover {
    background: #e6f7ff;
    border-color: #409EFF;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.run-basic-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    flex-wrap: wrap;
}

.run-header-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

/* 趟次操作按钮样式 */
.run-actions {
    display: flex;
    gap: 6px;
    align-items: center;
}

.run-actions .el-button--text {
    padding: 4px 8px;
    font-size: 12px;
    color: #409EFF;
    border: 1px solid #d6e7ff;
    border-radius: 3px;
    background: #f0f7ff;
    transition: all 0.2s;
    min-width: 40px;
    text-align: center;
}

.run-actions .el-button--text:hover {
    background: #409EFF;
    color: white;
    border-color: #409EFF;
}

/* 展开图标样式 */
.expand-icon {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 3px;
    transition: all 0.2s;
}

.expand-icon:hover {
    background: #f0f0f0;
    color: #409EFF;
}

</style>
