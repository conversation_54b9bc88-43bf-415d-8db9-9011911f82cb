<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">仪器历史追溯</div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
@Component({})
export default class extends Vue {}
</script>
<style lang="scss" scoped></style>
