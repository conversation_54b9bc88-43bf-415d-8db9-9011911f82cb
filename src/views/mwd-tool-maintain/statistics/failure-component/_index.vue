<template>
    <div style="position:relative">
        <el-row>
            <el-form :model="form">
                <el-form-item label="年份" label-width="40px">
                    <el-date-picker
                        v-model="form.year"
                        type="year"
                        value-format="yyyy"
                        placeholder="选择年"
                        @change="onSearch"
                    >
                    </el-date-picker>
                    <MonthsPicker
                        ref="selectMonths"
                        style="margin-left:10px"
                        @change="onMonthsChange"
                        placeholder="月份筛选"
                    />
                </el-form-item>
            </el-form>
        </el-row>
        <div style="display:flex" v-if="hasData">
            <div style="flex: 500px 0 0">
                <el-table
                    v-for="item in tableDataList"
                    :key="item.deviceTypeName"
                    :data="item.tableData"
                    style="width: 100%;margin-bottom: 10px"
                    class="mini-padding-table black-header"
                    border
                >
                    <el-table-column
                        :key="item"
                        prop="failedTypeName"
                        :label="item.deviceTypeName"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="failedCount"
                        label="失效次数"
                        align="center"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="failedPercentage"
                        label="占比"
                        align="center"
                        width="100"
                    >
                    </el-table-column>
                </el-table>
            </div>
            <div style="width:400px;height:500px;">
                <div id="chart" style="width: 600px; height: 500px;margin-left:20px"></div>
            </div>
        </div>
        <div v-else>
            无数据
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { pieOption } from "../option";
import * as echarts from "echarts";
import { apiGetMwdFailureComponentStats } from "@/api/mwd";
import MonthsPicker from "@/components/MonthsPicker/index.vue"
@Component({ components: { MonthsPicker } })
export default class extends Vue {
    private option: any = pieOption;
    private chart: any = null;
    private hoverType: any = "";
    private tableData: any[] = [];
    private tableKey: any[] = [];
    private form: any = { year: "" };
    private hasData = false;
    private tableDataList: any[] = [];
    private onSearch() {
        if (!this.form.year) return;
        apiGetMwdFailureComponentStats(this.form).then((res) => {
            const data = res.data.data || {};
            this.tableDataList = Object.keys(data).map(deviceTypeName=>{
                return {
                    deviceTypeName,
                    tableData: data[deviceTypeName]
                }
            }).filter(item=>item.tableData?.length);
            this.hasData = this.tableDataList.length>0;

            if(!this.hasData) {
                this.chart&&this.chart.dispose();
                return;
            }
            this.$nextTick(() => {
                this.chart&&this.chart.dispose();
                this.chart = echarts.init(
                    document.getElementById(`chart`) as HTMLDivElement
                );
                const flatData = this.tableDataList.reduce((prev,cur) =>{ return [...prev, ...cur.tableData]}, []);
                this.option.series[0].data = flatData.map(item=>({value:item.failedCount, name:item.failedTypeName, per: item.failedPercentage}))
                this.option.tooltip.formatter = (d)=>{
                    if(d.data){
                        return `失效部件 <br/>${d.data.name} : ${d.data.value} (${d.data.per}%)`
                    }
                    return ``
                };
                this.chart.setOption(this.option);
            });
        });
    }
    private async mounted() {
        this.form.year = String(new Date().getFullYear());
        this.onSearch();
    }
    private renderChart() {
        this.chart = echarts.init(
            document.getElementById(`chart`) as HTMLDivElement
        );
        this.chart.setOption(this.option);
    }
    private tableRowClassName({ row }) {
        if (row.type === this.hoverType) {
            return "hover-row";
        }
        return "";
    }
    private onMonthsChange(months){
        this.form.monthList = months;
        this.onSearch();
    }
    
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.mini-padding-table.el-table--medium .el-table__cell {
    padding: 2px 0 !important;
}
.mini-padding-table.black-header.el-table thead{
    color: black !important;
}
.el-table .hover-row {
    // background: purple;
}
</style>
