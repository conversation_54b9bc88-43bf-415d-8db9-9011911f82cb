<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">维修统计</div>
            <el-tabs v-model="activeName" tab-position="top" style="">
                <el-tab-pane name="工作量统计" label="工作量统计">
                    <keep-alive>
                        <Workload v-if="activeName == '工作量统计'" />
                    </keep-alive>
                </el-tab-pane>
                <el-tab-pane name="工时统计" label="工时统计">
                    <keep-alive>
                        <LabourHour v-if="activeName == '工时统计'" />
                    </keep-alive>
                </el-tab-pane>
                <el-tab-pane name="失效统计" label="失效统计">
                    <keep-alive>
                        <Failure v-if="activeName == '失效统计'" />
                    </keep-alive>
                </el-tab-pane>
                <el-tab-pane name="失效部件统计" label="失效部件统计">
                    <keep-alive>
                        <FailureComponent v-if="activeName == '失效部件统计'" />
                    </keep-alive>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import Failure from "./failure/_index.vue";
import FailureComponent from "./failure-component/_index.vue";
import LabourHour from "./labourHour/_index.vue";
import Workload from "./workload/_index.vue";
@Component({ components: { Failure, FailureComponent, LabourHour, Workload } })
export default class extends Vue {
    private activeName = "工作量统计";
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.mini-padding-table.el-table--medium .el-table__cell {
    padding: 2px 0 !important;
}
.el-table .hover-row {
    // background: purple;
}
</style>
