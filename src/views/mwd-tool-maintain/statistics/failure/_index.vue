<template>
    <div>
        <el-row>
            <el-form :model="form" inline>
                <el-form-item label="年份" label-width="40px">
                    <el-date-picker
                        v-model="form.year"
                        type="year"
                        value-format="yyyy"
                        placeholder="选择年"
                        @change="onSearch"
                        :clearable="false"
                        style="width:120px"
                    >
                    </el-date-picker>
                    <MonthsPicker
                        ref="selectMonths"
                        style="margin-left:10px"
                        @change="onMonthsChange"
                        placeholder="月份筛选"
                    />
                </el-form-item>

                <el-form-item label="Target Line(%)" label-width="110px">
                    <el-input
                        v-model="targetLine"
                        @change="onSearch"
                    ></el-input>
                </el-form-item>
                <el-form-item v-if="!isYear" label="仪器类型" label-width="80px">
                    <el-select
                        style="width: 100%"
                        v-model="form.deviceType"
                        @change="onSearch"
                    >
                        <el-option
                            v-for="item in deviceTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-button v-if="!isYear" type="text" style="margin-left:10px" @click="backToYear">返回</el-button>
            </el-form>
        </el-row>
        <div style="display: flex" v-if="isYear">
            <div style="flex: 700px 0 0">
                <el-table
                    class="mini-padding-table"
                    border
                    :data="tableData"
                    style="width: 100%"
                    v-if="tableData.length"
                >
                    <el-table-column align="center" prop="type" label="type">
                        <template slot-scope="scope">
                            <span style="border-bottom:1px solid grey; cursor: pointer" @click="onClickTool(scope.row.deviceId)">{{ scope.row.type }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" prop="pm" label="PM">
                    </el-table-column>
                    <el-table-column align="center" prop="df" label="DF">
                    </el-table-column>
                    <el-table-column align="center" prop="sf" label="SF">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="rate"
                        label="Ratio of DF"
                        width="100"
                    >
                        <template slot-scope="{ row }">
                            {{ formatPercent(row.rate) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="targetLine"
                        label="Target Line"
                        width="110"
                    >
                        <template slot-scope="{}">
                            {{ targetLine }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div id="failure-chart" style="flex: 1 0 0; height: 500px"></div>
        </div>
        <div style="display: flex" v-else>
            <div style="flex: 700px 0 0">
                <el-table
                    class="mini-padding-table"
                    border
                    :data="tableData"
                    style="width: 100%"
                    v-if="tableData.length"
                >
                    <el-table-column align="center" prop="time" label="time">
                    </el-table-column>
                    <el-table-column align="center" prop="pm" label="PM">
                    </el-table-column>
                    <el-table-column align="center" prop="df" label="DF">
                    </el-table-column>
                    <el-table-column align="center" prop="sf" label="SF">
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="rate"
                        label="Ratio of DF"
                        width="100"
                    >
                        <template slot-scope="{ row }">
                            {{ formatPercent(row.rate) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        prop="targetLine"
                        label="Target Line"
                        width="110"
                    >
                        <template slot-scope="{}">
                            {{ targetLine }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div id="failure-chart" style="flex: 1 0 0; height: 500px"></div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { option2 } from "../option";
import * as echarts from "echarts";
import { apiGetMwdWorkOrderStatistics } from "@/api/tool-mantain";
import getDict from "@/utils/getDict";
import { formatPercent } from "../option";
import { apiGetYearFailureStats } from "@/api/mwd";
import MonthsPicker from "@/components/MonthsPicker/index.vue"
@Component({ components: { MonthsPicker } })
export default class extends Vue {
    private option: any = option2;
    private chart: any = null;
    private tableData: any = [];
    private form: any = { deviceType: "", deviceTypeList: [], year: "" };
    private deviceTypeList: any[] = [];
    private targetLine = "";
    private isYear = true;
    private backToYear(){
        this.isYear = true;
        this.onSearch();
    }
    private renderTool() {
        this.form.deviceTypeList = [this.form.deviceType];
        this.tableData = [];
        this.chart&&this.chart.dispose();
        apiGetMwdWorkOrderStatistics(this.form)
            .then((res) => {
                const data = res.data.data || [];
                this.tableData = data[0].statisticsVos.filter(vo=>{
                    if(this.form.monthList&&this.form.monthList.length){
                        return this.form.monthList.includes(new Date(vo.time).getMonth()+1)
                    }else{
                        return true
                    }
                });
                this.$nextTick(() => {
                    this.renderChart();
                });
            })
            .catch(() => {
                this.tableData = [];
                this.chart.dispose();
            });
    }
    private renderYear() {
        this.form.deviceTypeList = [this.form.deviceType];
        this.tableData = [];
        this.chart&&this.chart.dispose();
        apiGetYearFailureStats(this.form)
            .then((res) => {
                const data = res.data.data || [];
                this.tableData = data.filter(item=>item.type!='avg_Y2D');
                this.$nextTick(() => {
                    this.renderChart();
                });
            })
            .catch(() => {
                this.tableData = [];
                this.chart.dispose();
            });
    }
    private onSearch() {
        if (!this.form.deviceType || !this.form.year) {
            return;
        }
        if(this.isYear){
            this.renderYear();
        }else{
            this.renderTool();
        }
    }
    private onClickTool(tool) {
        this.form.deviceType = tool;
        this.isYear = false;
        this.onSearch();
    }
    private async mounted() {
        [this.deviceTypeList] = await getDict([17]);
        this.form.deviceType = this.deviceTypeList[0].id;
        this.form.year = String(new Date().getFullYear());
        this.onSearch();
    }
    private formatPercent = formatPercent;
    private onMonthsChange(months){
        this.form.monthList = months;
        this.onSearch();
    }
    private renderChart() {
        this.chart = echarts.init(
            document.getElementById(`failure-chart`) as HTMLDivElement
        );
        this.option.legend.data = [
            "PM",
            "DF",
            "SF",
            "Ratio of DF",
            "Target Line",
        ];
        this.option.xAxis[0].data = this.tableData.map((item) => this.isYear ? item.type : item.time);
        this.option.xAxis[0].axisLabel = this.isYear ? { interval: 0, rotate: 30 } : {};
        this.option.series[0].data = this.tableData.map((item) => item.pm);
        this.option.series[1].data = this.tableData.map((item) => item.df);
        this.option.series[2].data = this.tableData.map((item) => item.sf);
        this.option.series[3].data = this.tableData.map((item) => item.rate || 0);
        this.option.series[4].data = this.tableData.map(
            () => Number(this.targetLine) / 100
        );
        this.chart.setOption(this.option);
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.mini-padding-table.el-table--medium .el-table__cell {
    padding: 2px 0 !important;
}
.el-table .hover-row {
    // background: purple;
}
</style>
