<template>
    <div>
        <el-row>
            <el-form :model="form">
                <el-col :span="24">
                    <el-form-item label="年份" label-width="40px">
                        <el-date-picker
                            v-model="form.year"
                            type="year"
                            value-format="yyyy"
                            placeholder="选择年"
                            @change="onYearChange"
                            :clearable="false"
                        >
                        </el-date-picker>
                        <el-select v-model="searchFor" @change="onConditionsChange" style="width:100px;margin-left:10px">
                            <el-option value="finish" label="完成量"></el-option>
                            <el-option v-if="statsType==='month'" value="receive" label="接收量"></el-option>
                        </el-select>
                        <el-select v-model="statsType" @change="onConditionsChange" style="width:100px;margin-left:10px">
                            <el-option value="month" label="按月"></el-option>
                            <el-option v-if="searchFor==='finish'" value="week" label="按周"></el-option>
                        </el-select>
                        <MonthsPicker
                            ref="selectMonths"
                            style="margin-left:10px"
                            @change="onMonthsChange"
                            placeholder="月份筛选"
                            v-if="statsType==='month'&&!isDay"
                        />
                        <div style="display:inline-block;margin-left:20px;user-select: none;" v-if="statsType==='month'&&isDay">
                            <i @click="month>1 && month--" class="el-icon-arrow-left" style="cursor:pointer"></i>
                            <span>第</span>
                            <el-select v-model="month" style="width:80px;margin:0 6px"><el-option v-for="item in 12" :key="item" :value="item"></el-option></el-select>
                            <span>月</span>
                            <i @click="month<12 && month++" class="el-icon-arrow-right" style="cursor:pointer"></i>
                            <el-button type="text" style="margin-left: 10px" @click="onBackToMonth">返回</el-button>
                        </div>
                        <div style="display:inline-block;margin-left:20px;user-select: none;" v-if="statsType==='week'">
                            <i @click="week>1 && week--" class="el-icon-arrow-left" style="cursor:pointer"></i>
                            <span>第</span>
                            <el-select v-model="week" style="width:80px;margin:0 6px"><el-option v-for="item in totalWeek" :key="item" :value="item"></el-option></el-select>
                            <span>周</span>
                            <i @click="week<totalWeek && week++" class="el-icon-arrow-right" style="cursor:pointer"></i>
                            <span style="margin-left: 10px">
                                <span>{{weekStart}}</span> 至 <span>{{weekEnd}}</span>
                            </span>
                        </div>
                    </el-form-item>
                </el-col>
            </el-form>
        </el-row>
        <div style="display: flex; width:100%" v-if="statsType==='month'" v-loading="loading">
            <div style="flex: 1;position:relative;" :style="`min-height: ${monthTableData.length*26}px`">
                <el-table
                    :data="monthTableData"
                    class="mini-padding-table"
                    style="position: absolute; width: 100%;"
                    border
                    :row-class-name="tableRowClassName"
                    v-if="monthTableData.length > 1"
                >
                    <el-table-column
                        v-for="item in monthTableKey"
                        :key="item+'workload'"
                        :prop="item"
                        :label="item"
                        :fixed="getMonthTableFloat(item)"
                        align="center"
                        :width="getMonthTableCellWidth(item)"
                    >
                        <template #header>
                            <span @click="onClickMonthHeader(item)" v-if="isClickable(item)" style="text-decoration:underline;cursor:pointer;">{{item}}</span>
                            <span v-else>{{item}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div id="chart" style="flex: 0 0 850px; height: 500px"></div>
        </div>
        <div v-else>
            <el-table 
                :data="weekTableData" 
                border 
                style="width:800px;"
                class="mini-padding-table"
                :cell-style="getCellStyle"
            >
                <el-table-column align="center" prop="toolName" width="150px" label="type"></el-table-column>
                <el-table-column align="center" prop="completedWeek" label="周完成量"></el-table-column>
                <el-table-column align="center" prop="repairingWeek" label="周在修量"></el-table-column>
                <el-table-column align="center" prop="pending" label="年滞留量">
                    <template slot="header">
                        <el-tooltip class="item" effect="dark" content="当年接收日期起大于30天滞留量" placement="top-start">
                            <span>年滞留量<i class="el-icon-info"></i></span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="repairWeekTotal" label="周返修量"></el-table-column>
                <el-table-column align="center" prop="repairingTotal" label="在修量">
                </el-table-column>
                <el-table-column align="center" prop="pendingTotal" label="历史滞留总量" width="140px"></el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import Vue from "vue";
import MonthsPicker from "@/components/MonthsPicker/index.vue"
import { option1, longBarOption } from "../option";
import * as echarts from "echarts";
import { apiGetMwdWorkloadMonth, apiGetMwdWorkloadWeek } from "@/api/tool-mantain";
import Decimal from 'decimal.js';
import dayjs from "dayjs";
import weekOfYear from "dayjs/plugin/weekOfYear";
import isoWeeksInYear  from "dayjs/plugin/isoWeeksInYear";
import isLeapYear  from "dayjs/plugin/isLeapYear";
dayjs.extend(weekOfYear);
dayjs.extend(isoWeeksInYear);
dayjs.extend(isLeapYear)
const now = new Date();
const monthAbbrList = ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"];
function getNaturalWeeksInYear(year) {
  const start = Number(new Date(year, 0, 1)); // Jan 1
  const end = Number(new Date(year, 11, 31)); // Dec 31

  // 计算这两个日期之间相差的天数（包含首尾）
  const diffDays = Math.floor((end - start) / (1000 * 60 * 60 * 24)) + 1;

  // 每7天为一周，多出的天数也算一整周
  const weeks = Math.ceil(diffDays / 7);

  return weeks;
}
@Component({ components: {
    MonthsPicker
} })
export default class extends Vue {
    private option: any = option1;
    private longBarOption: any = longBarOption;
    private chart: any = null;
    private hoverType: any = "";
    private monthTableData: any[] = [];
    private monthTableKey: any[] = [];
    private weekTableData: any[] = [];
    private form: any = { year:  dayjs(now).year()+'' };
    private statsType = "month";
    private isDay = false;
    private month = dayjs(now).month()+1+'';
    private months: number[] = []
    private week = dayjs(now).week();
    private totalWeek = getNaturalWeeksInYear(now.getFullYear());
    private weekStart = "";
    private weekEnd = "";
    private weekMax = 0;
    private loading = false;
    private searchFor = "finish";
    @Watch('week')
    private onWeekChange() {
        this.$nextTick(()=>{
            this.onWeekWorkLoad();
        })
    }
    @Watch('month')
    private onMonthChange() {
        this.$nextTick(()=>{
            this.onConditionsChange();
        })
    }
    get isFullYear() {
        return !this.months || this.months.length === 0 || this.months.length === 12;
    }
    private onMonthsChange(months) {
        this.months = months;
        this.onConditionsChange();
    }
    private isClickable(item) {
        return !this.isDay&&item!='type'&&item!='YearTotal'&&item!='MonthTotal'
    }
    private getMonthTableCellWidth(item){
        if(item==='type'){
            return "100px";
        }
        if(item==='YearTotal' || item==='MonthTotal'){
            return "100px";
        }
        return this.isDay ? "100px" : ''
    }
    private getMonthTableFloat(item){
        if(item==='type'){
            return "left";
        }
        if(item==='YearTotal' || item==='MonthTotal'){
            return "right";
        }
        return false
    }
    private dateToMonth(date) {
        return monthAbbrList[new Date(date).getMonth()];
    }
    private onYearChange(){
        this.onConditionsChange();
    }
    private async onConditionsChange() {
        // NOTE: 清空，不然会有概率阻塞
        this.monthTableData = [];
        this.monthChart&&this.monthChart.dispose();
        const year = this.form.year
        this.totalWeek = getNaturalWeeksInYear(year);
        if(this.statsType == "month"){
            if(this.isDay){
                const firstDayOfMonth = dayjs(`${year}-${this.month}-01`).format('YYYY-MM-DD');
                const lastDayOfMonth = dayjs(firstDayOfMonth).endOf('month').format('YYYY-MM-DD');
                const form = {
                    startTime: firstDayOfMonth,
                    endTime: lastDayOfMonth,
                    statisticType: 'byDay',
                    searchFor: this.searchFor
                }
                this.loading = true;
                try {
                    const data = await this.onMonthWorkLoad(form);
                    if(data.length<2){
                        this.$message.error("暂无数据");
                        return;
                    }
                    this.drawMonthDayTable(data);
                    this.renderChartMonthDay();
                } finally {
                    this.loading = false;
                }
            }else{
                const form = { 
                    year: this.form.year,
                    statisticType: "byMonth",
                    searchFor: this.searchFor
                }
                this.loading = true;                    
                try {
                    const data = await this.onMonthWorkLoad(form);
                    if(data.length<2){
                        this.$message.error("暂无数据");
                        return;
                    }
                    this.drawMonthTable(data);
                    this.renderChartMonth();
                } finally {
                    this.loading = false;
                }
            }
        }else{
            this.$nextTick(()=>{
                this.onWeekWorkLoad();
            })
        }
    }
    private onBackToMonth(){
        this.isDay = false;
        this.onConditionsChange();
    }
    private onClickMonthHeader(item) {
        const month = String(monthAbbrList.indexOf(item) + 1);
        this.isDay = true;
        // watch了month, 但第一次切换如果没有变化需要手动执行
        if(this.month === month){
            this.onConditionsChange();
        }else{
            this.month = month;
        }
    }
    private drawMonthDayTable(data){
        this.monthTableData = data.map((item) => {
            let tmp = {};
            let monthTotal = new Decimal(0);
            Object.keys(item).forEach((k) => {
                if (k === "type") {
                    tmp[k] = item[k];
                } else {
                    tmp[k] = new Decimal(item[k]).toNumber();
                    monthTotal =  new Decimal(item[k]).plus(monthTotal);
                }
            });
            tmp[`MonthTotal`] = monthTotal.toNumber()
            return tmp;
        });
        const keys = Object.keys(this.monthTableData[0]);
        const typeIndex = keys.findIndex(item=>item==='type');
        keys.unshift(keys.splice(typeIndex , 1)[0]);
        this.monthTableKey = keys;
    }
    private drawMonthTable(data){
        this.monthTableData = data.map((item) => {
            let tmp = {};
            let yearTotal = new Decimal(0);
            Object.keys(item).forEach((k) => {
                if (k === "type") {
                    tmp[k] = item[k];
                } else {
                    const monthIndex = new Date(k).getMonth();
                    const monthNumber = monthIndex + 1;
                    if(this.isFullYear || this.months.includes(monthNumber)){
                        const monthString = monthAbbrList[monthIndex]
                        tmp[monthString] = new Decimal(item[k]).toNumber();
                        yearTotal =  new Decimal(item[k]).plus(yearTotal);
                    }
                }
            });
            if(this.isFullYear){
                tmp[`YearTotal`] = yearTotal.toNumber()
            }
            return tmp;
        });
        const keys = Object.keys(this.monthTableData[0]);
        const typeIndex = keys.findIndex(item=>item==='type');
        keys.unshift(keys.splice(typeIndex , 1)[0]);
        this.monthTableKey = keys;
    }
    private onWeekWorkLoad(){
        apiGetMwdWorkloadWeek({
            year: this.form.year,
            weekNumber: this.week
        }).then((res: any) => {
            this.weekTableData = res.data.data.list || [];
            this.weekStart = res.data.data.weekSpan?.[0] || "";
            this.weekEnd = res.data.data.weekSpan?.[1] || "";
            this.weekTableData.forEach((item,index)=>{
                if(index>=16){
                    return;
                }
                for(let key in item){
                    if(key!=='toolName'&&key!=='deviceType'){
                        if(item[key]>this.weekMax){
                            this.weekMax = item[key];
                        }
                    }
                }
            })
        });
    }
    private onMonthWorkLoad(form) {
        return new Promise<any[]>((resolve, reject) => {
            apiGetMwdWorkloadMonth(form).then((res) => {
                resolve(res.data.data || []);
            }).catch(error => reject(error))
        })
    }
    private async mounted() {
        this.onConditionsChange();
    }
    private renderChartMonthDay() {
        let series: any = [];
        this.longBarOption.legend.data = this.monthTableData.map((item) => item.type);
        const date: any = [...this.monthTableKey];
        date.shift();
        date.pop();
        const tableDataLen = this.monthTableData.length;
        for (let i = 0; i < tableDataLen - 1; i++) {
            let item = {...this.monthTableData[i]};
            let name = item.type;
            delete item.type;
            series.push({
                name,
                type: "bar",
                stack: "Ad",
                emphasis: {
                    focus: "series",
                },
                barWidth: "30",
                label: {
                    show: true,
                },
                data: Object.values(item),
            });
        }
        let totalItem = {...this.monthTableData[tableDataLen - 1]};
        delete totalItem.type;
        series.push({
            name: "total",
            type: "line",
            emphasis: {
                focus: "series",
            },
            smooth: true,
            label: { show: true },
            data: Object.values(totalItem),
        });
        this.monthChart = echarts.init(
            document.getElementById(`chart`) as HTMLDivElement
        );
        this.longBarOption.xAxis.data = date;
        this.longBarOption.series = series;
        this.monthChart&&this.monthChart.clear();
        this.monthChart.setOption(this.longBarOption);
        this.monthChart.on("mouseover", (params) => {
            this.hoverType = params.seriesName;
        });
        this.monthChart.on("mouseout", () => {
            this.hoverType = "";
        });
    }
    private renderChartMonth() {
        let series: any = [];
        this.option.legend.data = this.monthTableData.map((item) => item.type);
        const date: any = [...this.monthTableKey];
        date.shift();
        this.isFullYear && date.pop();
        const tableDataLen = this.monthTableData.length;
        for (let i = 0; i < tableDataLen - 1; i++) {
            let item = [...Object.values(this.monthTableData[i])];
            let name = item.shift();
            this.isFullYear && item.pop();
            series.push({
                name,
                type: "bar",
                stack: "Ad",
                emphasis: {
                    focus: "series",
                },
                barWidth: "30",
                label: {
                    show: true,
                },
                data: item,
            });
        }
        const total = [...Object.values(this.monthTableData[tableDataLen - 1])];
        const totalName = total.shift();
        this.isFullYear && total.pop();
        series.push({
            name: totalName,
            type: "line",
            emphasis: {
                focus: "series",
            },
            smooth: true,
            label: { show: true },
            data: total,
        });
        this.monthChart = echarts.init(
            document.getElementById(`chart`) as HTMLDivElement
        );
        this.option.xAxis.data = date;
        this.option.series = series;
        this.monthChart&&this.monthChart.clear();
        this.monthChart.setOption(this.option);
        this.monthChart.on("mouseover", (params) => {
            this.hoverType = params.seriesName;
        });
        this.monthChart.on("mouseout", () => {
            this.hoverType = "";
        });
        // chart.dispatchAction({ type: "highlight", seriesName: "AZ-DM" });
    }
    private tableRowClassName({ row }) {
        if (row.type === this.hoverType) {
            return "hover-row";
        }
        return "";
    }
    private getCellStyle({ row, column, rowIndex, columnIndex }) {
        const value = +row[column.property];
        if(!isNaN(value)&&rowIndex<this.weekTableData.length-1){
            return {
                backgroundColor: this.getRGBByNumber(value),
            };
        }
    }
    
    private getRGBByNumber(num){
        const color1 = [99, 190, 123]; // rgb(99,190,123)
        const color2 = [255, 235, 132]; // rgb(255,235,132)
        const color3 = [248, 105, 107]; // rgb(248,105,107)
        let result:any[] = [];
        const factor = this.weekMax===0 ? 0: num / this.weekMax;
        if(factor===0){
            result = color1;
        } else if (factor===1){
            result = color3;
        } else {
            for (let i = 0; i < 3; i++) {
                result.push(Math.round(color2[i] + (num-1)/(this.weekMax) * (color3[i] - color2[i])));
            }
        }
        return `rgb(${result.join(',')})`;
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.mini-padding-table.el-table--medium .el-table__cell {
    padding: 2px 0 !important;
}
.el-table .hover-row {
    // background: purple;
}
</style>
<style>
  .el-table__fixed {
    height: 100% !important;
  }
 .el-table__fixed-right {
    height: 100% !important; 
  }
</style>