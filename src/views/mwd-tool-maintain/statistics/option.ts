export const colorList = {
  PM: 'rgb(79,129,189)',
  DF: 'rgb(192,80,77)',
  SF: 'rgb(155,187,89)',
  RatioDF: 'rgb(128,100,162)',
  TargetLine: 'rgb(75,172,198)'
}
export const formatPercent = (num) => {
  return num ? parseInt(String(num * 100)) + "%" : 0;
}
export const option2 = {
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     type: 'shadow'
  //   }
  // },
  legend: {
    data: []
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    }
  ],
  yAxis: [
    { type: 'value' },
    {
      type: 'value',
      position: 'right',
      splitLine: { show: false },
      axisLabel: {
        formatter(value: any) {
          return `${value * 100}%`
        }
      }
    }
  ],
  series: [
    {
      name: 'PM',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,
      },
      barMaxWidth: 200,
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: 'DF',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,
      },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: 'SF',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,
      },
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: 'Ratio of DF',
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,

        formatter: (num) => {
          return formatPercent(num.value);
        },
      },
      yAxisIndex: 1,
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: 'Target Line',
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      yAxisIndex: 1,
      data: [150, 232, 201, 154, 190, 330, 410]
    },
  ],
  animation: false
};
export const option1 = {
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     // Use axis to trigger tooltip
  //     type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
  //   }
  // },
  legend: {
    // data: [],
    // type: 'scroll',
    // orient: 'vertical',
    // right: 0,
    // top: 60,
    // bottom: 20,
    top: 0,
  },
  grid: {
    top: 100,
    left: '3%',
    right: '3%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    data: []
  },
  series: [
  ]
}

export const laborBarOption = {
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     // Use axis to trigger tooltip
  //     type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
  //   }
  // },
  legend: {
    top: 0,
  },
  grid: {
    top: 100,
    left: '3%',
    right: '3%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    data: []
  },
  series: [
  ]
}
export const pieOption = {
  color: [
    '#c23531',
    '#2f4554',
    '#61a0a8',
    '#d48265',
    '#91c7ae',
    '#749f83',
    '#ca8622',
    '#bda29a',
    '#6e7074',
    '#546570',
    '#c4ccd3',
    '#dd6b66',
    '#759aa0',
    '#e69d87',
    '#8dc1a9',
    '#ea7e53',
    '#eedd78',
    '#73a373',
    '#73b9bc',
    '#7289ab',
    '#91ca8c',
    '#f49f42'
  ],
  title: {
    text: '失效部件统计',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '失效部件 <br/>{b} : {c} ({d}%)'
  },
  legend: {
    show: false,
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      type: 'pie',
      radius: '50%',
      stillShowZeroSum: false,
      data: [
        { value: 1048, name: 'Search Engine' },
        { value: 735, name: 'Direct' },
        { value: 580, name: 'Email' },
        { value: 484, name: 'Union Ads' },
        { value: 300, name: 'Video Ads' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

const fiftyThree = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53]
export const longBarOption = {
  tooltip: {
    trigger: 'axis',
    show: false,
    axisPointer: {
      type: 'shadow',
      label: {
        show: true
      }
    }
  },
  legend: {
    data: [],
    type: 'scroll',
    orient: 'vertical',
    right: 0,
    top: 60,
    bottom: 20,
  },
  // toolbox: {
  //   show: true,
  //   feature: {
  //     mark: { show: true },
  //     dataView: { show: true, readOnly: false },
  //     magicType: { show: true, type: ['line', 'bar'] },
  //     restore: { show: true },
  //     saveAsImage: { show: true }
  //   }
  // },
  calculable: true,
  grid: {
    top: '12%',
    left: '1%',
    right: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
  },
  yAxis: {
    type: 'value',
  },
  dataZoom: [
    {
      show: true,
      start: 0,
      end: 30
    },
  ],
  series: []
}