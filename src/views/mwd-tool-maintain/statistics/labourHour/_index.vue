<template>
    <div>
        <el-row>
            <el-form :model="form" inline label-width="40px">
                
                <el-form-item label="员工" style="margin-left:10px">
                    <el-autocomplete
                        popper-class="my-autocomplete"
                        v-model="form.name"
                        :fetch-suggestions="querySearch"
                        placeholder="请输入员工姓名并选择"
                        @select="handleSelect"
                        @clear="handleClear"
                        clearable
                    >
                        <template slot-scope="{ item }">
                            <div class="name">{{ item.name }}</div>
                        </template>
                    </el-autocomplete>
                </el-form-item>
                <el-form-item label="" v-if="statsType==='year'">
                    <el-date-picker
                        v-model="form.year"
                        style="width:260px"
                        type="year"
                        value-format="yyyy"
                        placeholder="选择年"
                        @change="onSearch"
                        :clearable="false"
                    >
                    </el-date-picker>
                    <MonthsPicker
                        ref="selectMonths"
                        style="margin-left:10px"
                        @change="onMonthsChange"
                        placeholder="月份筛选"
                    />
                </el-form-item>
                <el-form-item label="" v-if="statsType==='month'">
                    <el-date-picker
                        v-model="form.month"
                        style="width:260px"
                        type="month"
                        value-format="yyyy-MM"
                        format="yyyy-MM"
                        placeholder="选择月"
                        @change="onSearch"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="" label-width="80px"  v-if="statsType==='daterange'">
                    <el-date-picker
                        style="width:260px"
                        v-model="form.daterange"
                        @change="onSearch"
                        class="fixed-separator-date-picker"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                        format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <!-- <el-select v-model="statsType" @change="onSearch" style="width:110px">
                    <el-option v-for="item in statsTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                </el-select> -->
            </el-form>
        </el-row>
        <div style="display: flex">
            <div style="flex: 1;position:relative;" :style="`min-height: ${tableData.length*26}px`">
                <el-table
                    :data="tableData"
                    class="mini-padding-table"
                    style="position:absolute;width: 100%"
                    border
                    :row-class-name="tableRowClassName"
                    v-if="tableData.length > 1"
                    table-layout="auto"
                >
                    <el-table-column
                        v-for="item in tableKey"
                        :key="item"
                        :prop="item"
                        :label="item"
                        align="center"
                        :width="getMonthTableCellWidth(item)"
                        :fixed="getMonthTableFloat(item)"
                    >
                    </el-table-column>
                </el-table>
            </div>

            <div
                id="labour-hour-chart"
                style="flex: 0 0 850px; height: 500px"
            ></div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { laborBarOption } from "../option";
import * as echarts from "echarts";
import { apiGetMwdLabourHour } from "@/api/tool-mantain";
import { apiGetMemberList } from "@/api/users";
import Decimal from 'decimal.js';
import dayjs from "dayjs";
import MonthsPicker from "@/components/MonthsPicker/index.vue"
function getMonthDays(yyyy_MM: string | Date){
    return dayjs(yyyy_MM).daysInMonth();
}
const monthAbbrList = ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"];
@Component({ components: { MonthsPicker } })
export default class extends Vue {
    private option: any = laborBarOption;
    private chart: any = null;
    private hoverType: any = "";
    private tableData: any[] = [];
    private tableKey: any[] = [];
    private form: any = { year: "", month: "", name: "", memberId: "", daterange:null };
    private months: number[] = [];
    private statsTypeList = [
        { value: "year", label: "按年" },
        { value: "month", label: "按月" },
        // { value: "daterange", label: "时间范围" }
    ]
    private statsType = "year";
    get isFullYear() {
        return !this.months || this.months.length === 0 || this.months.length === 12;
    }
    private getMonthTableFloat(item){
        if(item==='type'){
            return "left";
        }
        if(item==='YearTotal'){
            return "right";
        }
        return false
    }
    private getMonthTableCellWidth(item){
        if(item==='type'){
            return "100px";
        }
        if(item==='YearTotal'){
            return "100px";
        }
        return  ''
    }
    private onSearch() {
        this.tableData = [];
        this.chart&&this.chart.dispose();
        const form:any = {
            memberId: this.form.memberId || undefined,
        };
        switch (this.statsType) {
            case "year":
                form.year = this.form.year;
                break;
            case "month":
                [form.startTime, form.endTime] = this.getDateRangeByYM(this.form.month)
                break;
            case "daterange":
                [form.startTime, form.endTime] =  this.form.daterange;
                break;
        }
        apiGetMwdLabourHour(form).then((res) => {
            const data = res.data.data || [];
            this.tableData = data.map((item) => {
                let tmp:any = {};
                let yearTotal = new Decimal(0);
                Object.keys(item).forEach((k) => {
                    if (k === "type") {
                        tmp[k] = item[k];
                    } else {
                        const monthIndex = new Date(k).getMonth();
                        const monthNumber = monthIndex + 1;
                        if(this.isFullYear || this.months.includes(monthNumber)){
                            const monthString = monthAbbrList[monthIndex]
                            tmp[monthString] = new Decimal(item[k]).toNumber();
                            yearTotal =  new Decimal(item[k]).plus(yearTotal);
                        }
                    }
                });
                if(this.isFullYear){
                    tmp[`YearTotal`] = yearTotal.toNumber();
                }
                return tmp;
            });
            if (this.tableData.length < 2) {
                this.$message.error("暂无数据");
                return;
            }
            const keys = Object.keys(this.tableData[0]);
            const typeIndex = keys.findIndex(item=>item==='type');
            keys.unshift(keys.splice(typeIndex , 1)[0]);
            this.tableKey = keys;
            this.$nextTick(() => {
                this.renderChart();
            });
        });
    }
    private getDateRangeByYM(ym: string | Date = new Date()){
        const lastDayOfMonth = String(getMonthDays(ym));
        const yyyy_MM = dayjs(ym).format('YYYY-MM');
        return [yyyy_MM + '-' +'01', yyyy_MM + '-' + lastDayOfMonth];
    }
    private async mounted() {
        const now = new Date();
        this.form.year = String(now.getFullYear());
        this.form.month = dayjs(now).format('YYYY-MM');
        this.form.daterange = this.getDateRangeByYM(now);
        this.onSearch();
    }
    private renderChart() {
        let series: any = [];
        this.option.legend.data = this.tableData.map((item) => item.type);
        const date: any = [...this.tableKey];
        date.shift();
        this.isFullYear && date.pop();
        const tableDataLen = this.tableData.length;
        for (let i = 0; i < tableDataLen - 1; i++) {
            let item = [...Object.values(this.tableData[i])];
            let name = item.shift();
            this.isFullYear && item.pop();
            series.push({
                name,
                type: "bar",
                stack: "Ad",
                emphasis: {
                    focus: "series",
                },
                barWidth: "30",
                label: {
                    show: true,
                },
                data: item,
            });
        }
        const total = [...Object.values(this.tableData[tableDataLen - 1])];
        const totalName = total.shift();
        this.isFullYear && total.pop();
        series.push({
            name: totalName,
            type: "line",
            emphasis: {
                focus: "series",
            },
            smooth: true,
            label: { show: true },
            data: total,
        });
        this.chart = echarts.init(
            document.getElementById(`labour-hour-chart`) as HTMLDivElement
        );
        this.option.xAxis.data = date;
        this.option.series = series;
        this.chart&&this.chart.clear();
        this.chart.setOption(this.option);
        this.chart.on("mouseover", (params) => {
            this.hoverType = params.seriesName;
        });
        this.chart.on("mouseout", () => {
            this.hoverType = "";
        });
        // chart.dispatchAction({ type: "highlight", seriesName: "AZ-DM" });
    }
    private tableRowClassName({ row }) {
        if (row.type === this.hoverType) {
            return "hover-row";
        }
        return "";
    }
    private querySearch(qs, cb) {
        apiGetMemberList({name:qs}).then(res=>{
            cb(res.data.data?.memberInfoList || [])
        })
    }
    private handleClear() {
        this.form.name = "";
        this.form.memberId = ""
        this.onSearch();
    }
    private handleSelect(item) {
        this.form.name = item.name;
        this.form.memberId = item.memberId;
        this.onSearch();
    }
    private onMonthsChange(months){
        this.months = months;
        this.onSearch();
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.mini-padding-table.el-table--medium .el-table__cell {
    padding: 2px 0 !important;
}
.el-table .hover-row {
    // background: purple;
}
.hide-year{
    .el-date-picker__header.el-date-picker__header--bordered{
        display: none;
    }
}
</style>
<style scoped>

::v-deep .el-table .cell {
    white-space: pre-line;
}

</style>
<style>
.el-table th.gutter {
  display: table-cell !important;
}

.el-table colgroup.gutter {
  display: table-cell !important;
}
</style>