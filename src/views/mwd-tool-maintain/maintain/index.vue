<template>
    <div>
        <div class="tool-maintain-container" v-loading="loading">
            <div class="tool-card" style="position: relative">
                <!-- #region 创建工单 -->
                <template v-if="!mwdId">
                    <Create />
                </template>
                <!-- #endregion -->
                <template v-else>
                    <div class="update-info" style="position: absolute; right:0; color: grey; font-size: 14px;line-height:20px">
                        <div v-if="detailForm.lastModifiedByStr">
                            最近编辑人: {{ detailForm.lastModifiedByStr }}
                        </div>
                        <div v-if="detailForm.lastModifiedDate">
                            更新于: {{ detailForm.lastModifiedDate}}
                        </div>
                    </div>
                    <div class="operators" :style="`top:calc(50% - ${operatorList.length*15+7}px)`" v-if="showOperators">
                        <div
                            class="operator"
                            v-for="item in operatorList"
                            :key="item.type"
                            v-html="item.title"
                            @click.stop="onClickOperator(item.type)"
                        ></div>
                    </div>

                    <div
                        class="operator-cover"
                        @click.stop="showOperators = true"
                        v-else
                    >
                        操作
                    </div>
                    <div class="tool-card-title">
                        工单信息
                    </div>
                    <el-form :model="originForm" label-width="60px">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="工单号">
                                    <el-input
                                        disabled
                                        v-model="originForm.mwdNumber"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <BaseInfo
                        :originForm="originForm"
                        :failureTypeList="failureTypeList"
                        :failureReasonTypeList="failureReasonTypeList"
                        :failureComponentTypeList="failureComponentTypeList"
                        :deviceTypeList="deviceTypeList"
                        :isEdit="isEdit"
                        :riskInfo="riskInfo"
                        :ownerTypeList="ownerTypeList"
                    />

                    <InComponent
                        ref="inComponents"
                        :inComponentList="inComponentList"
                        :outComponentList="outComponentList"
                        :originForm="originForm"
                        :isEdit="isEdit"
                        :riskInfo="riskInfo"
                    />
                    <Check :originForm="originForm" :isEdit="isEdit" />
                    <Assemble
                        ref="assemble"
                        :inComponentList="inComponentList"
                        :outComponentList="outComponentList"
                        :originForm="originForm"
                        :isEdit="isEdit"
                    />
                    <div class="tool-card-title">更换清单</div>
                    <Exchange :originForm="originForm" :isEdit="isEdit" />
                    <Test :originForm="originForm" :isEdit="isEdit" />
                    <LaborHours :mwdId="mwdId" :originForm="originForm" :isEdit="isEdit" />
                    <MwdFile :mwdId="mwdId" :isEdit="isEdit" />
                    <div class="tool-card-title">完成工单</div>
                    <el-button type="primary" @click="onFinishWorkOrder" v-if="!isFinished">完成工单</el-button>
                    <span v-else>工单已完成</span>
                </template>
            </div>
        </div>
        <Component ref="certTemplate" :is="certMap[deviceType]"></Component>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import {
    apiCheckSerialNumber,
    apiFinishMwdWorkOrder, apiGetMwdMaintainReport, apiGetMwdWorkOrderInfo,
    apiMwdAssemble, apiUpdateMwdWorkOrder, apiUpdateMwdWorkOrderShareStatus,
} from "@/api/tool-mantain";
import { apiCreateHandover } from "@/api/handover";
import { mergeRun, SHARE_STATUS, splitRun } from "@/utils/constant.mwd";
import getDict, { getNameById } from "@/utils/getDict";
import { apiGetCertInfo, apiGetMwdRiskInfo } from "@/api/mwd";

import Create from "./components/create.vue";
import BaseInfo from "./components/base.vue";
import InComponent from "./components/inComponent.vue";
import Check from "./components/check.vue";
import Assemble from "./components/assemble.vue";
import Exchange from "./components/exchange.vue";
import Test from "./components/test.vue";
import LaborHours from "./components/labor.vue";
import MwdFile from "./components/file.vue";

import AtBit from "./components/certificate/atBit.vue";
import AzDm from "./components/certificate/azdm.vue";
import AzGamma from "./components/certificate/azgamma.vue";
import Battery from "./components/certificate/battery.vue";
import Dm from "./components/certificate/dm.vue";
import Gamma from "./components/certificate/gamma.vue";
import Pulser from "./components/certificate/pulser.vue";
import Receiver from "./components/certificate/receiver.vue";
import { component } from "vue/types/umd";
@Component({
    name: "MwdOrder",
    components: {
        Create, BaseInfo, InComponent, Check, Assemble, Exchange, LaborHours,
        MwdFile, Test, AtBit, AzDm, AzGamma, Battery, Dm, Gamma, Pulser, Receiver,
    },
})
export default class extends Vue {
    private deviceType = "";
    private certMap = {
        16004: "Battery",
        16012: "Receiver",
        16001: "AtBit",
        16011: "Pulser",
        16008: "Gamma",
        16003: "AzGamma",
        16007: "AzDm",
        16002: "Dm"
    }
    private showOperators = false;
    private finish = 0;
    private mwdId: number | null = 3639;
    private originForm: any = {
        workOrderMwdDetailList: [],
        mwdDeviceAssembleRecord: {
            inComponentList: [],
            outComponentList: [],
        },
        mwdRepairCheckData: {
            checkData: {
                replaceItemList: [],
            },
        },
    };
    private detailForm: any = {};
    private inComponentList: any[] = [];
    private outComponentList: any[] = [];
    private failureTypeList: any[] = [];
    private failureReasonTypeList: any[] = [];
    private failureComponentTypeList: any[] = [];
    private deviceTypeList: any[] = [];
    private ownerTypeList: any[] = [];
    private loading = false;
    private isEdit = false;
    private riskInfo:any = {}
    get isNewOrder() {
        return !this.mwdId;
    }
    get isFinished() {
        return this.finish == 1;
    }
    get p_MwdUpdate() {
        return this.$checkBtnPermission(`sys:mwd:update`);
    }
    get p_CreateHandover(){
        return this.$checkBtnPermission(`sys:mwd:handover`)
    }
    get handoverStatus(){
        return this.originForm.handoverStatus
    }
    get hasShared(){
        return this.originForm.workOrderMwdDetailList[0]?.shareStatus === SHARE_STATUS.SHARED
    }
    get operatorList() {
        return [
            {
                show: !this.isEdit&&!this.isFinished,
                type: "EDIT",
                title: "编辑工单",
            },
            {
                show: !this.isEdit&&this.isFinished&&this.$checkBtnPermission(`sys:mwd:finish_update`),
                type: "FINISH_EDIT",
                title: "编辑工单",
            },
            {
                show: this.isEdit,
                type: "SAVE",
                title: "保存编辑",
            },
            {
                show: this.isEdit,
                type: "CANCEL",
                title: "取消编辑",
            },
            // {
            //     show: !this.isFinished,
            //     type: "COMPLETE",
            //     title: "完成工单",
            // },
            { show: true, type: "EXPORT", title: "导出报告" },
            {
                show: this.$checkBtnPermission(`sys:mwd:deviceinfo`) && !!this.originForm.workOrderMwdDetailList[0]?.run && !!this.originForm.wellNumber,
                type: "DEVICE_INFO",
                title: "趟次信息",
            },
            {
                show: true,
                type: "COPY",
                title: "复制创建",
            },
            {
                show: this.$checkBtnPermission(`sys:mwd:cert:view`) && !!this.certMap[this.originForm.workOrderMwdDetailList[0]?.deviceType],
                type: "CERT",
                title: "合&nbsp;&nbsp;格&nbsp;&nbsp;证",
            },
            {
                show: this.p_CreateHandover,
                type: "HANDOVER",
                title: this.handoverStatus ? (this.handoverStatus === 'HANDOVER' ? '交&nbsp;&nbsp;接&nbsp;&nbsp;中' : '已&nbsp;&nbsp;交&nbsp;&nbsp;接' ) : '交&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;接'
            },
            {
                show: !this.isEdit && this.$checkBtnPermission(`sys:mwd:share`),
                type: "SHARE",
                title: this.hasShared ? "取消分享" : "分&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;享",
            },
            {
                show: true,
                type: "BACK",
                title: "返&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;回",
            },
        ].filter((item) => item.show);
    }
    private onClickOperator(type){
        switch(type){
            case "EDIT":
                this.isEdit = true;
                break;
            case "FINISH_EDIT":
                this.isEdit = true;
                break;
            case "SAVE":
                this.saveOrder();
                break;
            case "CANCEL":
                this.onCancel();
                break;
            case "COMPLETE":
                this.onFinishWorkOrder();
                break;
            case "DEVICE_INFO":
                this.onToDeviceInfo();
                break;
            case "EXPORT":
                this.onClickExportWord();
                break;
            case "COPY":
                this.onCopy();
                break;
            case "CERT":
                this.onCert();
                break;
            case "HANDOVER":
                this.onHandover();
                break;
            case "SHARE":
                this.onHandleShare();
                break;
            case "BACK":
                this.showOperators = false;
                break;
            default:
                return;
        }
    }
    private onHandleShare() {
        const tips = this.hasShared ? "确认取消分享该工单信息?" : "确认分享该工单信息?";
        this.$confirm(tips, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            apiUpdateMwdWorkOrderShareStatus({
                mwdId: this.mwdId,
                shareStatus: this.hasShared ? SHARE_STATUS.UNSHARED : SHARE_STATUS.SHARED,
            }).then(()=>{
                this.$message.success("操作成功!");
                this.getWorkOrderMwdInfo();
            })
        });
    }
    private onToDeviceInfo(){
        const wellNumber = this.originForm.wellNumber;
        const run = this.originForm.workOrderMwdDetailList[0]?.run;
        this.$router.push(`/mwd-tool-maintain/deviceinfo?wellNumber=${wellNumber}&run=${run}`)
    }
    private onClickExportWord() {
        // const lastModifiedByStr = this.detailForm.lastModifiedByStr;
        // this.$prompt('', '请输入编制人', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     inputValue: lastModifiedByStr,
        //     inputValidator: (value)=>value.trim()?true:'请输入编制人'
        // }).then(({ value }: any) => {
        //     this.onExportWord(value.trim());
        // }).catch(() => {     
        // });
        this.onExportWord();
    }
    private onExportWord() {
        this.loading = true;
        apiGetMwdMaintainReport({ mwdId: this.mwdId })
            .then((res) => {
                this.loadingExportExcelAll = false;
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const deviceTypeStr = getNameById(this.deviceTypeList, this.detailForm.deviceType)
                const fileName = `${deviceTypeStr}_${this.detailForm.serialNumber}_${this.originForm.mwdNumber}_维修报告.docx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            })
            .finally(() => {
                this.loading = false;
            });
    }
    private onFinishWorkOrder() {
        this.$confirm("确认完成工单?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            apiFinishMwdWorkOrder({
                mwdId: this.mwdId,
                finish: 1,
            }).then(async ()=>{
                await this.getWorkOrderMwdInfo();
                this.snValid && apiMwdAssemble(this.originForm.mwdDeviceAssembleRecord);
            });
        });
        
    }
    private onHandover() {
        if(!this.isFinished){
            this.$message.error("未完成的工单不能交接！");
            return;
        }
        if(this.handoverStatus){
            return
        }
        this.$confirm("确认交接?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            const form = new FormData();
            form.append("idList", [this.mwdId].toString());
            form.append("type", "MWD");
            apiCreateHandover(form).then(()=>{
                this.$message.success("交接成功！");
                this.getWorkOrderMwdInfo();
            })
        });
    }
    private async saveOrder(){
        const assembleRefs:any = this.$refs.assemble;
        // 校验序列号
        const validateResult = await assembleRefs.validateAssembleSN();
        if(validateResult?.length>0) {
            this.$message.error('拆卸组装中有已组装在其它仪器中的部件！')
            return;
        }
        // 工时
        this.originForm.repairMemberList = this.originForm.repairMemberList.filter(item=>item.memberId&&item.name);
        // 测试件
        this.originForm.componentTestList = this.originForm.componentTestList.filter(item=>item.invName);
        // 获取flag: 是否维修或组装
        const hasAssemble = assembleRefs.isChange;
        const inComponentsRefs:any = this.$refs.inComponents
        const hasRepair = inComponentsRefs.isChange;
        // repair
        let repairList = inComponentsRefs.tableData;
        if(hasRepair){
            this.originForm.repairList = repairList;
        }
        // assemble
        if(hasAssemble && this.snValid){
            await apiMwdAssemble(this.originForm.mwdDeviceAssembleRecord);
        }
        // 重置flag
        assembleRefs.isChange = false;
        inComponentsRefs.isChange = false;
        // 处理入井趟次列表为字符串
        this.originForm.workOrderMwdDetailList[0].run = mergeRun(this.originForm.workOrderMwdDetailList[0].runList);
        // update
        apiUpdateMwdWorkOrder(this.originForm).then(async () => {
            await this.getWorkOrderMwdInfo();
            this.getMwdRiskInfo();
            this.isEdit = false;
            this.$message.success("更新成功！");
        });
    }
    private async onCancel(){
        (this.$refs.assemble as any).isChange = false;
        (this.$refs.inComponents as any).isChange = false;
        await this.getWorkOrderMwdInfo();
        this.isEdit = false;
    }
    private async mounted() {
        this.mwdId = Number(this.$route.query.mwdId);
        this.loading = true;
        try {
            [
                this.failureTypeList,
                this.failureComponentTypeList,
                this.failureReasonTypeList,
                this.deviceTypeList,
                this.ownerTypeList
            ] = await getDict([13, 14, 15, 17, 22]);
            if (this.mwdId) {
                await this.getWorkOrderMwdInfo();
                this.getMwdRiskInfo();
                this.validateSN();
            }
        } finally {
            this.loading = false;
        }
    }
    getMwdRiskInfo(){
        const detailForm = this.originForm.workOrderMwdDetailList[0] || {};
        if(detailForm.serialNumber) {
            apiGetMwdRiskInfo({serialNumber: detailForm.serialNumber}).then(res=>{
                this.riskInfo = res.data.data || {};
            })
        }
        
    }
    private async validateSN(){
        const serialNumber = this.originForm.workOrderMwdDetailList[0].serialNumber;
        if(!serialNumber){
            this.snValid = false;
            return;
        }
        const res = await apiCheckSerialNumber({ serialNumber });
        const code = res.data.data;
        this.snValid = code == 1 || code == 0;
    }
    private getComponentItem(invName){
        return {
            invName,
            serialNumber: null,
            serviceVersionNumber: null,
            updatedVersionNumber: null,
            serviceRiskType: null,
            updatedRiskType: null,
            quantity: null,
            totalHours: null,
            reviseTotalHours: null,
            maxBht: null,
            repairType: null,
            repairAction: null,
            failureReason: null,
            curve: null,
            angle: null,
            rubberType: null,
            isBroke: null,
            manufacturer: null,
            tempType: null,
            note: null
        }
    }
    private getWorkOrderMwdInfo() {
        return apiGetMwdWorkOrderInfo({ mwdId: this.mwdId }).then((res) => {
            const data = res.data.data || {};
            data.workOrderMwdDetailList[0].runList = splitRun(data.workOrderMwdDetailList[0].run);
            // 预处理componentList
            if(!data.mwdDeviceAssembleRecord){
                data.mwdDeviceAssembleRecord = {
                    inComponentList: [],
                    outComponentList: [],
                };
            }
            data.mwdDeviceAssembleRecord.inComponentList = (data.mwdDeviceAssembleRecord?.inComponentList || []).map(item=>{
                if(item.serialNumber){
                    return item;
                }else{
                    return this.getComponentItem(item.invName)
                }
            })
            data.mwdDeviceAssembleRecord.outComponentList = (data.mwdDeviceAssembleRecord.outComponentList || []).map(item=>{
                if(item.serialNumber){
                    return item;
                }else{
                    return this.getComponentItem(item.invName)
                }
            })
            this.originForm = data;
            this.finish = data.finish;
            this.initOriginForm();
            this.detailForm = data.workOrderMwdDetailList?.[0] || {};
            this.inComponentList =
                this.originForm.mwdDeviceAssembleRecord.inComponentList;
            this.outComponentList =
                this.originForm.mwdDeviceAssembleRecord.outComponentList;
        });
    }
    private initOriginForm() {
        if (!this.originForm.workOrderMwdDetailList?.length) {
            this.originForm.workOrderMwdDetailList = [];
        }
        if (!this.originForm.mwdDeviceAssembleRecord) {
            this.originForm.mwdDeviceAssembleRecord = {
                inComponentList: [],
                outComponentList: [],
            };
        }
        if (!this.originForm.mwdDeviceAssembleRecord.inComponentList) {
            this.originForm.mwdDeviceAssembleRecord.inComponentList = [];
        }
        if (!this.originForm.mwdDeviceAssembleRecord.outComponentList) {
            this.originForm.mwdDeviceAssembleRecord.outComponentList = [];
        }
        if (!this.originForm.mwdRepairCheckData) {
            this.originForm.mwdRepairCheckData = {
                checkData: {
                    replaceItemList: [],
                },
            };
        }
        if (!this.originForm.mwdRepairCheckData.checkData) {
            this.originForm.mwdRepairCheckData.checkData = {
                replaceItemList: [],
            };
        }
        if (!this.originForm.mwdRepairCheckData.checkData.replaceItemList) {
            this.originForm.mwdRepairCheckData.checkData.replaceItemList = [];
        }
    }
    private onCopy(){
        this.$router.replace(`/redirect/mwd-tool-maintain/maintain?copyid=${this.mwdId}`)
    }
    private onCert() {
        const form = new FormData();
        form.append("mwdId", (this.mwdId as number).toString());
        apiGetCertInfo(form).then(res=>{
            const data = res.data.data;
            this.deviceType = data?.certInfo?.deviceType;
            if(this.certMap[this.deviceType]){
                this.$nextTick(()=>{
                    (this.$refs.certTemplate as any).showDialog(data)
                })
                
            }else{
                this.$message.error("无相关合格证模板！")
            }
        })
    }
}
</script>
<style lang="scss" scoped>
.operators {
    position: fixed;
    right: 30px;
    top: calc(50% - 97px);
    display: flex;
    flex-direction: column;
    justify-content: center;

    padding: 6px 0;
    margin: 2px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .operator {
        line-height: 30px;
        padding: 0 10px;
        margin: 0;
        font-size: 14px;
        text-align: center;
        color: #606266;
        cursor: pointer;
        outline: none;
        &:hover {
            background-color: #ecf5ff;
            color: #66b1ff;
        }
    }
}
.operator-cover {
    position: fixed;
    right: 30px;
    top: calc(50% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    color: white;
    padding-left: 6px;
    font-size: 14px;
    line-height: 2;
    width: 26px;
    height: 80px;
    background: rgba(67, 86, 152, 0.8);
    cursor: pointer;
    user-select: none;
    .operator {
        font-size: 14px;
        line-height: 1.4;
        border: 1px solid grey;
        padding: 4px;
    }
}
.step-container {
    display: flex;
    justify-content: space-between;

    .step {
        width: 140px;
        height: 60px;
        line-height: 58px;
        text-align: center;
        font-size: 18px;
        border: 1px solid #ccc;
        font-weight: 500;
        border-radius: 16px;
        cursor: pointer;
        user-select: none;
        &.active {
            background: pink;
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
    
    .highlighted {
        padding: 2px;
        border: 1px solid rgb(67, 86, 152);
    }
}
</style>
<style lang="scss" scoped>
.tool-maintain-container {
    padding: 20px 40px;
    padding-right: 100px;
    margin: 12px;
    background: white;
    overflow: hidden;
    .tool-maintain-title {
        font-size: 16px;
        font-weight: 600;
    }
    .tool-card-subtitle {
        padding-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        color: rgb(67, 86, 152);
        margin-left: -12px;
        &::before {
            display: inline-block;
            margin-right: 6px;
            content: "-";
        }
    }
    .is-process {
        color: rgb(67, 86, 152) !important;
        font-weight: normal !important;
        .el-step__icon {
            border-color: rgb(67, 86, 152) !important;
        }
    }
    .active-step {
        .is-process {
            color: rgb(67, 86, 152) !important;
            font-weight: bold !important;
            .el-step__icon {
                border-color: rgb(67, 86, 152) !important;
            }
        }
    }
}
.el-table__expanded-cell {
    background: rgb(235, 238, 245);
}
.el-table__expanded-cell:hover {
    background: rgb(235, 238, 245) !important;
}
.active-step .el-step__title {
    color: rgb(67, 86, 152) !important;
    font-weight: bold;
}
</style>
