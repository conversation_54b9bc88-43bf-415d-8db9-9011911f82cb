<template>
    <div style="margin-bottom: 20px">
        <div class="tool-card-title"style="margin-top:20px">入厂部件清单</div>
        <el-table :data="tableData" border :span-method="objectSpanMethod">
            <el-table-column prop="invName" label="核心部件"> </el-table-column>
            <el-table-column label="进出" align="center" width="60px">
                <template slot-scope="scope">
                    <span>{{ !(scope.$index%2) ? '入厂' : '出厂' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="serialNumber" label="序列号" align="center">
                <template slot-scope="scope">
                    <span style="margin-right:10px">{{ scope.row.serialNumber }}</span>
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onViewHistory(scope.row.serialNumber,scope.row.invName)"
                        v-if="scope.row.serialNumber"
                        >历史追溯</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column :label="isBattery?`厂家/温度类型/备注`: `厂家/备注`" align="center" width="160px">
                <template slot-scope="scope">
                    {{ getMergeStr(scope.row) }}
                </template>
            </el-table-column>
            <el-table-column prop="serviceRiskType" label="风险类型" align="center" width="120px">
                <template slot-scope="scope">
                    <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.serviceRiskType"></CustomTag>
                </template>
            </el-table-column>
            <el-table-column prop="riskValue" label="风险值" align="center" width="120px">
            </el-table-column>
            <!-- 如果序列号为空, 该行不能编辑, 且值赋空(已预处理)-->
            <!-- <el-table-column prop="updatedRiskType" label="更新风险" align="center" width="80px">
                <template slot-scope="scope">
                    <el-select
                        clearable
                        v-model="scope.row.updatedRiskType"
                        style="width: calc(100% - 10px)"
                        @clear="scope.row.updatedRiskType=null"
                        placeholder=""
                        v-if="scope.row.serialNumber&&isEdit&&(!(scope.$index%2)||(scope.row.rowType==='OUT'))"
                        @change="isChange = true"
                    >
                        <el-option
                            v-for="item in riskTypeList"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></el-option>
                    </el-select>
                    <CustomTag v-else tagType="RISK_TYPE" :tagValue="scope.row.updatedRiskType"></CustomTag>
                </template>
            </el-table-column> -->
            <el-table-column prop="serviceVersionNumber" label="当前固件" align="center" width="100px">
                <template slot-scope="scope">
                    <span>{{ scope.row.serviceVersionNumber }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="updatedVersionNumber" label="更新固件" align="center" width="100px">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.updatedVersionNumber"
                        v-if="scope.row.serialNumber&&isEdit&&(!(scope.$index%2)||(scope.row.rowType==='OUT'))"
                        @change="isChange = true"
                    ></el-input>
                    <span v-else>{{ scope.row.updatedVersionNumber }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="maxBht" label="最高温度" align="center" width="120px">
                <template slot-scope="scope">
                    <!-- <el-input
                        v-model="scope.row.maxBht"
                        v-if="scope.row.serialNumber&&isEdit&&!(scope.$index%2)"
                        @change="isChange = true"
                    ></el-input>
                    <span v-else>{{ scope.row.maxBht }}</span> -->
                    <span>{{ scope.row.maxBht }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="reviseMaxBht" label="修正最高温度" align="center" width="120px">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.reviseMaxBht"
                        v-if="scope.row.serialNumber&&isEdit&&!(scope.$index%2)"
                        @change="isChange = true"
                    ></el-input>
                    <span v-else>{{ scope.row.reviseMaxBht }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="totalInWellHrs" label="总时间" align="center" width="120px">
                <template slot-scope="scope">
                    <span>{{ scope.row.totalInWellHrs }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="reviseTotalHours" label="修正总入井时间" align="center" width="120px">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.reviseTotalHours"
                        v-if="scope.row.serialNumber&&isEdit&&!(scope.$index%2)"
                        @change="isChange = true"
                    ></el-input>
                    <span v-else>{{ scope.row.reviseTotalHours }}</span>
                </template>
            </el-table-column>
            
            <!-- <el-table-column prop="repairType" label="维修类型" align="center" width="100px">
                <template slot-scope="scope">
                    <template v-if="!(scope.$index%2)">
                        <el-select
                            v-model="scope.row.repairType"
                            v-if="scope.row.serialNumber&&isEdit"
                            @change="onRepairTypeChange(scope)"
                        >
                            <el-option
                                v-for="item in repairTypeList"
                                :value="item.value"
                                :label="item.label"
                                :key="item.value"
                            ></el-option>
                        </el-select>
                        <CustomTag v-else tagType="MAINTAIN_TYPE" :tagValue="scope.row.repairType"></CustomTag>
                    </template>
                </template>
            </el-table-column>
            <el-table-column prop="failureReason" label="故障原因" align="center" width="100px">
                <template slot-scope="scope">
                    <template v-if="!(scope.$index%2)">
                        <el-input
                            v-model="scope.row.failureReason"
                            v-if="scope.row.serialNumber&&isEdit"
                            @change="isChange = true"
                        ></el-input>
                        <span v-else>{{ scope.row.failureReason }}</span>
                    </template>
                </template>
            </el-table-column>
            <el-table-column prop="repairAction" label="方案&执行措施" >
                <template slot-scope="scope">
                    <template v-if="!(scope.$index%2)">
                        <el-input
                            v-model="scope.row.repairAction"
                            v-if="scope.row.serialNumber&&isEdit"
                            @change="isChange = true"
                        ></el-input>
                        <span v-else>{{ scope.row.repairAction }}</span>
                    </template>
                </template>
            </el-table-column> -->
        </el-table>
        <HistoryTrack ref="historyTrack" />
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import HistoryTrack from "./HistoryTrack.vue";
import { riskTypeList } from "@/utils/constant";
import { repairTypeList } from "@/utils/constant.mwd";
@Component({ name: "InComponents", components: { HistoryTrack } })
export default class extends Vue {
    @Prop({ default: () => {} }) originForm!: any;
    private repairTypeList = repairTypeList;
    private isChange = false;
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => {} }) riskInfo!: any;
    private riskTypeList = riskTypeList
    // 只有在 deviceType 为16004, 核心部件的invName是Core的时候才会有温度类型这个字段
    get deviceType() {
        return this.originForm?.workOrderMwdDetailList?.[0]?.deviceType || "";
    }
    get isBattery() {
        return this.deviceType === 16004
    }
    get inComponentList() {
        return this.originForm?.mwdDeviceAssembleRecord?.inComponentList || [];
    }
    get outComponentList() {
        return this.originForm?.mwdDeviceAssembleRecord?.outComponentList || [];
    }
    // 这个列表每行的数据来源都是基于assembleRecord中的in/out, 组装拆卸时必须要响应式的更新in/out列表！
    // 这里不会产生替换行数据的情况, 所以不会影响组装拆卸到这里的单向数据流向
    get tableData() {
        // 出入场序列号一样时, 只用inComponent
        // 用行的奇偶判断出入厂, 用IN/OUT判断用的哪个component
        const ret :any = [];
        this.inComponentList.forEach((item, index)=>{
            if(!item.serialNumber && !this.outComponentList[index].serialNumber){
                return
            }
            const inItem = item;
            inItem.rowType = "IN";
            // HACK: 这里使用了inItem.serviceRiskType, 会导致inItem修改时, 触发tableData的getter, 会覆盖原始的数据
            // 这段逻辑能保证originServiceRiskType要么是最原始的serviceRiskType, 要么是自定义的INITIAL
            inItem.originServiceRiskType = inItem.originServiceRiskType ? inItem.originServiceRiskType : inItem.serviceRiskType || "INITIAL";
            let outItem;
            if(this.outComponentList[index].serialNumber === inItem.serialNumber){
                outItem = inItem
            }else{
                outItem = this.outComponentList[index]
                outItem.rowType = "OUT";
            }
            ret.push(inItem, outItem);
        })
        return ret;
    }
    private onRepairTypeChange(scope: any) {
        this.isChange = true;
        if(scope.row.repairType==="DISCARD"){
            scope.row.serviceRiskType = "SCRAP"
        }else{
            // 如果originServiceRiskType是INITIAL, 将serviceRiskType赋值为默认值null
            scope.row.serviceRiskType = scope.row.originServiceRiskType === "INITIAL" ? null : scope.row.originServiceRiskType
        }
    }
    private getMergeStr(row){
        const {manufacturer, tempType, note} = row;
        if(this.isBattery){
            if(manufacturer || tempType || note){
                return `${manufacturer ?? ""} / ${tempType ?? ""} / ${note ?? ""}`
            }
            return ""
        }else{
            if(manufacturer || note){
                return `${manufacturer ?? ""} / ${note ?? ""}`
            }
            return ""
        }
    }
    private objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
            if (rowIndex % 2 === 0) {
                return {
                    rowspan: 2,
                    colspan: 1
                };
            } else {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }
        }
    }
    private onViewHistory(serialNumber: string, invName: string) {
        (this.$refs.historyTrack as any).showDialog({ serialNumber, invName });
    }
}
</script>
<style lang="scss" scoped>
.hover-icon {
    display: none;
    position: absolute;
    right: 5px;
}
.hover-icon.add {
    bottom: 5px;
}
.hover-icon.delete {
    top: 5px;
}

.el-table__row:hover .hover-icon {
    display: block;
}
.el-table__row:hover .tail-input {
    width: calc(100% - 20px) !important;
}
</style>
<style lang="scss" scoped>
.hover-btn {
    display: none;
    padding: 0;
    // margin-left: 20px;
}
.el-table__row:hover .hover-btn {
    display: inline-block;
}
:deep() .el-table .cell {
    padding-left:  4px;
    padding-right: 4px;
}
</style>
