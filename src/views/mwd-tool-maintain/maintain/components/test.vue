<template>
    <div style="margin-top: 20px">
        <div class="tool-card-title">试用件</div>
        <div  style="margin-bottom:10px" v-if="isEdit">
            <el-button @click="onAddTest()" type="primary" style="margin-right:10px">添加试用件</el-button>
        </div>
        
        <el-table :data="tableData.filter(item=>item.serviceStatus!==TEST_SERVICE_STATUS.END_SERVICE)" border>
            <el-table-column prop="invName" label="试用件名称" align="center">
            </el-table-column>
            <el-table-column prop="serialNumber" label="序列号" align="center">
            </el-table-column>
            <el-table-column prop="quantity" label="数量" align="center">
            </el-table-column>
            <el-table-column prop="quantity" label="验收标准" align="center">
                <template slot-scope="{row}">
                    <span style="white-space: pre-line">
                        {{ getTestWarnStrategyStr(row) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="是否达到验收标准" align="center">
                <template slot-scope="{row}">
                    <CustomTag tagType="TRUE_FALSE" :tagValue="row.warn" />
                </template>
            </el-table-column>
            <el-table-column prop="description" label="试用过程描述" align="center">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.description" style="width:100%" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.description}}</span>
                    
                    
                </template>
            </el-table-column>
            <el-table-column width="100" label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onShowDetail(scope.row)">详情</el-button>
                    <el-button type="text" @click="onDelete(scope)" v-if="isEdit">拆卸</el-button>
                </template>
            </el-table-column>
        </el-table>
        <TestDetailDialog ref="testDetailDialog" />
        <el-dialog title="选择试用件(非服役状态)" :visible.sync="isSelectTestDialogVisible" width="80%">
            <el-table :header-cell-style="commmonTableHeaderCellStyle" @row-click="onClickRow" max-height="500px" ref="testListTable" :data="testList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column label="试用物资名称" fixed="left" width="150" prop="invName">
                </el-table-column>
                <el-table-column label="序列号" width="150" prop="serialNumber">
                </el-table-column>
                <el-table-column label="自带序列号" width="150" prop="merSerialNumber">
                </el-table-column>
                <el-table-column label="引进品牌" width="120" prop="importBrand">
                </el-table-column>
                <el-table-column label="引入目的" width="120" prop="testPurpose">
                </el-table-column>
                <el-table-column label="需要现场测试验证的点" width="auto" prop="testContent">
                </el-table-column>
                <el-table-column label="试用计划安排" width="200" prop="testPlan">
                </el-table-column>
                <el-table-column label="数量" width="60" align="center" prop="quantity">
                </el-table-column>
                <el-table-column label="服役状态" width="100" prop="quantity">
                    <template slot-scope="scope">
                        <CustomTag tagType="TEST_SERVICE_STATUS" :tagValue="scope.row.serviceStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="验收标准" width="200" prop="quantity" align="center">
                    <template slot-scope="{row}">
                        <span style="white-space: pre-line">
                            {{ getTestWarnStrategyStr(row)}}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="是否到达验收标准" width="140" prop="quantity" align="center">
                    <template slot-scope="{row}">
                        <CustomTag tagType="TRUE_FALSE" :tagValue="row.warn" />
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <el-button @click="isSelectTestDialogVisible=false">
                    取消
                </el-button>
                <el-button @click="onConfirmAdd" type="primary">确认添加</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { apiGetTestList } from "@/api/mwd";
import { TEST_SERVICE_STATUS, getTestWarnStrategyStr } from "@/utils/constant.mwd";
import { ElTable } from "element-ui/types/table";
import { Component, Prop, Vue } from "vue-property-decorator";
import TestDetailDialog from "./TestDetailDialog.vue";
@Component({ name: "Test", components: {TestDetailDialog}})
export default class extends Vue {
    private isSelectTestDialogVisible = false;
    @Prop({default:()=>false}) isEdit !:Boolean
    @Prop({default:()=>{}}) originForm !:any;
    get tableData(){
        return this.originForm?.componentTestList || []
    }
    
    private getTestWarnStrategyStr = getTestWarnStrategyStr;
    private TEST_SERVICE_STATUS = TEST_SERVICE_STATUS;
    private testList:any[] = [];
    private selectedTestList:any[] = [];
    private onDelete(scope:any) {
        let index = scope.$index;
        const serviceStatus = scope.row.serviceStatus;
        if(serviceStatus === TEST_SERVICE_STATUS.SERVICING || serviceStatus === TEST_SERVICE_STATUS.START_SERVICE){
            scope.row.serviceStatus = TEST_SERVICE_STATUS.END_SERVICE;
        }else{
            this.tableData.splice(index, 1);
        }
    }
    private async onAddTest() {
        await apiGetTestList({}, {current:1,size:99999}).then(res=>{
            const data = res.data.data?.records || [];
            this.testList = data.filter(item=>{
                return (!item.serviceStatus || item.serviceStatus === TEST_SERVICE_STATUS.END_SERVICE) 
                    && !this.tableData.find(td=>td.componentTestId===item.componentTestId);
            });
        })
        this.selectedTestList = []
        this.isSelectTestDialogVisible = true;
    }
    
    private onShowDetail(row) {
        (this.$refs.testDetailDialog as any).showDialog(row);
    }
    
    private handleSelectionChange(val){
        this.selectedTestList = val;
    }
    private onClickRow(row){
        (this.$refs.testListTable as ElTable).toggleRowSelection(row)
    }
    private onConfirmAdd(){
        this.tableData.push(...this.selectedTestList.map(item=>({...item, description: ""})));
        this.isSelectTestDialogVisible = false;
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
<style lang="scss" scoped>
.hover-btn {
    display: none;
    padding: 0;
    // margin-left: 20px;
}
.el-table__row:hover .hover-btn {
    display: inline-block;
}
</style>