<template>
    <div style="margin-bottom:20px">
        <div class="tool-card-title">拆卸组装</div>
        <el-button @click="onPaste" v-if="isEdit" type="primary" style="margin-bottom:10px">出入厂一致</el-button>
        <el-table :data="tableData" border>
            <el-table-column prop="invName" label="名称"></el-table-column>
            <el-table-column prop="serialNumber" label="入厂序列号">
                <template slot-scope="scope">
                    <span style="margin-right:10px">{{ scope.row.serialNumber }}</span>
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onClickCreateRepair('IN', scope.$index)"
                        v-if="scope.row.serialNumber&&!isEdit&&!scope.row.rmaRepairId"
                        >创建RMA返修单</el-button
                    >
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onGoRmaRepair('IN', scope.$index)"
                        v-if="scope.row.serialNumber&&!isEdit&&scope.row.rmaRepairId"
                        >去RMA返修单</el-button
                    >
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onClickCreateRmaWorkOrder('IN', scope.$index)"
                        v-if="scope.row.serialNumber&&!isEdit&&!scope.row.rmaId"
                        >创建EA工单</el-button
                    >
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onGoRmaWorkOrder('IN', scope.$index)"
                        v-if="scope.row.serialNumber&&!isEdit&&scope.row.rmaId"
                        >去EA工单</el-button
                    >
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onViewHistory(scope.row.serialNumber,scope.row.invName)"
                        v-if="scope.row.serialNumber"
                        >历史追溯</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column prop="" label="出厂序列号">
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.replacedSerialNumber"
                        filterable
                        clearable
                        remote
                        reserve-keyword
                        placeholder="请输入关键词"
                        @focus="onFocus(scope.row.invName)"
                        @change="onChange(scope)"
                        :remote-method="onSearchSerialNumber"
                        v-if="isEdit"
                    >
                        <el-option
                            v-for="item in filterSNList"
                            :key="item.componentId"
                            :label="item.serialNumber"
                            :value="item.serialNumber"
                        >
                        </el-option>
                    </el-select>
                    <span v-else>
                        <span style="margin-right:10px">{{ scope.row.replacedSerialNumber }}</span>
                        <el-button
                            class="hover-btn"
                            type="text"
                            @click="onViewHistory(scope.row.replacedSerialNumber,scope.row.invName)"
                            v-if="scope.row.replacedSerialNumber"
                            >历史追溯</el-button
                        >
                        <el-button
                            class="hover-btn"
                            type="text"
                            v-if="p_PartAdd"
                            @click="onAddComponent(scope.row.invName)"
                            >新增部件</el-button
                        >
                    </span>
                    <div v-if="isEdit&&!scope.row.valid" class="sn-error-msg">
                        该部件已组装在其它仪器中
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <HistoryTrack ref="historyTrack" />
    </div>
</template>
<script lang="ts">
import { apiValidateMwdAssemble, apiGetComponentItem } from "@/api/mwd";
import { apiGetCustomToolList } from "@/api/tools";
import { Component, Prop, Vue } from "vue-property-decorator";
import HistoryTrack from "./HistoryTrack.vue";
import { RmaRepairDataByCoreFormKey, RmaWorkOrderDataByCoreFormKey } from "@/utils/constant.rma";
import { apiCreateRmaWorkOrderByRepair, apiGetRmaRepairDataByCore } from "@/api/rma";
@Component({ name: "Assemble", components: { HistoryTrack } })
export default class extends Vue {
    @Prop({ default: () => [] }) inComponentList!: any[];
    @Prop({ default: () => [] }) outComponentList!: any[];
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => ({ mwdDeviceAssembleRecord: {} }) }) originForm!: any;
    private filterSNList: any[] = [];
    private currentInvName = "";
    private isChange = false;
    get tableData() {
        return this.inComponentList.map((inItem, index) => {
            let tmp = {...inItem}
            let outItem = this.outComponentList[index];
            let replacedSerialNumber = outItem?.serialNumber || "";
            tmp.replacedSerialNumber = replacedSerialNumber;
            tmp.valid = tmp.valid === false ? false : true;
            return tmp;
        });
    }
    private onFocus(invName: string) {
        this.currentInvName = invName;
    }
    private onPaste() {
        this.isChange = true;
        this.tableData.forEach((_,index) => {
            // 替换行数据时, 要保证响应式替换
            this.$set(this.outComponentList, index, {...this.inComponentList[index]})
        })
    }
    get p_PartAdd(){
        return this.$checkBtnPermission(`sys:partservice:add`)
    }
    getComponentRow(row){
        return {
            invName: row.invName,
            serialNumber: null,
            serviceVersionNumber: null,
            updatedVersionNumber: null,
            serviceRiskType: null,
            updatedRiskType: null,
            quantity: null,
            totalHours: null,
            reviseTotalHours: null,
            maxBht: null,
            repairType: null,
            repairAction: null,
            failureReason: null,
            curve: null,
            angle: null,
            rubberType: null,
            isBroke: null,
            manufacturer: null,
            tempType: null,
            note: null
        }
    }
    private async onChange(scope) {
        const { row, $index: index } = scope;
        this.isChange = true;
        // NOTE: 替换行数据时, 要保证响应式替换
        if(row.replacedSerialNumber){
            // 先赋值后校验
            await apiGetComponentItem({serialNumber: row.replacedSerialNumber, invName: row.invName}).then(res=>{
                const data = res.data?.data || [];
                let rowItem;
                const dataItem = data[0];
                if(dataItem){
                    rowItem = { ...dataItem };
                    rowItem.serviceVersionNumber = dataItem.versionNumber;
                    rowItem.serviceRiskType = dataItem.riskType;
                }else{
                    rowItem = this.getComponentRow(row);
                    rowItem.serialNumber = row.replacedSerialNumber
                }
                this.$set(this.outComponentList, index, rowItem)
            })
            await this.validateAssembleSN();
           
        }else{
            this.$set(this.outComponentList, index, this.getComponentRow(row))
        }
    }
    private onSearchSerialNumber(query){
        apiGetCustomToolList(
            { invName: this.currentInvName, serialNumber: query, },
            { current: 1, size: 20 }
        ).then((res) => {
            this.filterSNList = (res.data.data?.records || []).filter(item=>item.invName==this.currentInvName);
        });
    }
    private onViewHistory(serialNumber: string, invName: string) {
        (this.$refs.historyTrack as any).showDialog({ serialNumber, invName });
    }
    private onAddComponent(invName){
        this.$router.push(`/mwd-tool-maintain/partservice?event=add&invName=${invName}`)
    }
    
    private async validateAssembleSN(){
        const mwdDeviceAssembleRecord = this.originForm.mwdDeviceAssembleRecord
        this.tableData.forEach((td, index) => {
            mwdDeviceAssembleRecord.outComponentList[index].serialNumber = td?.replacedSerialNumber||"";
        });
        const validateInfo = await apiValidateMwdAssemble(mwdDeviceAssembleRecord);
        const validateData = validateInfo?.data?.data || [];
        validateData.forEach((target)=>{
            const idx = this.tableData.findIndex(item=>item.invName===target.invName&&item.replacedSerialNumber===target.serialNumber);
            if(idx>=0){
                this.tableData[idx].valid = false;
            }
        })
        return validateData;
    }
    private onClickCreateRepair(type, index){
        const componentListKey = type==='OUT'?'outComponentList':'inComponentList';
        const item = this.originForm.mwdDeviceAssembleRecord[componentListKey][index];
        const repairCode = item.rmaNumber;
        if(repairCode){
            this.$confirm(`确认要创建单号为${repairCode}的RMA返修单？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(()=>{
                this.onCreateRepair(repairCode, item)
            })
        }else{
            this.$prompt('', '请输入RMA返修单号', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValidator: (value)=>value&&value.trim()?true:'请输入返修单号'
            }).then(({ value }: any) => {
                this.onCreateRepair(value.trim(), item)
            }).catch(() => {     
            });
        }
    }
    
    private onGoRmaWorkOrder(type, index){
        const componentListKey = type==='OUT'?'outComponentList':'inComponentList';
        const rmaId = this.originForm.mwdDeviceAssembleRecord[componentListKey][index].rmaId;
        this.$router.push(`/rma/maintain?rmaId=${rmaId}`)
    }
    private onCreateRepair(repairCode, item){
        apiGetRmaRepairDataByCore({mwdId: this.originForm.mwdId,repairCode, assembleComponent: item}).then(res=>{
            const id = res.data.data.repairId;
            this.$router.push(`/mwd-tool-maintain/rma-repair/detail?id=${id}`)
        })
    }
    private onGoRmaRepair(type, index){
        const componentListKey = type==='OUT'?'outComponentList':'inComponentList';
        const rmaRepairId = this.originForm.mwdDeviceAssembleRecord[componentListKey][index].rmaRepairId;
        this.$router.push(`/mwd-tool-maintain/rma-repair/detail?id=${rmaRepairId}`)
    }
    private onClickCreateRmaWorkOrder(type, index){
        const componentListKey = type==='OUT'?'outComponentList':'inComponentList';
        const item = this.originForm.mwdDeviceAssembleRecord[componentListKey][index]
        const rmaNumber = item.rmaRepairCode;
        if(rmaNumber){
            this.$confirm(`确认要创建工单号为${rmaNumber}的EA工单？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(()=>{
                this.onCreateRmaWorkOrder(rmaNumber, item)
            })
        }else{
            this.$prompt('', '请输入EA工单号', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValidator: (value)=>value&&value.trim()?true:'请输入EA工单号'
            }).then(({ value }: any) => {
                this.onCreateRmaWorkOrder(value.trim(), item)
            }).catch(() => {     
            });
        }
    }
    private onCreateRmaWorkOrder(rmaNumber, item){
        const detailForm = this.originForm.workOrderMwdDetailList[0];
        const form = {
            wellNumber: this.originForm.wellNumber,
            jobNumber: this.originForm.jobNumber,
            rmaNumber,
            workOrderRmaDetail: {
                invName: item.invName,
                serialNumber: item.serialNumber,
                contactUser: detailForm.contactUser,
                contactNumber: detailForm.contactNumber,
                inWellHour: detailForm.inWellHour,
                circulateHrs: detailForm.circulateHrs,
                run: detailForm.run,
                maxBht: detailForm.maxBht,
                returnReason: detailForm.returnReason,
                notes: detailForm.notes,
                receiveDate: detailForm.receiveDate,
                findings: detailForm.contactUser,
                versionNumber: item.updatedVersionNumber || item.serviceVersionNumber || null,
                
            }
        }
        sessionStorage.setItem(RmaWorkOrderDataByCoreFormKey, JSON.stringify(form));
        this.$router.push('/rma/maintain?core=true')
    }
}
</script>
<style lang="scss" scoped>
.hover-btn {
    display: none;
    padding: 0;
}
.el-table__row:hover .hover-btn {
    display: inline-block;
}
.sn-error-msg{
    color: red;
    font-size: 12px;
}
</style>
