<template>
    <el-dialog width="960px" custom-class="c-template-dialog" :visible.sync="isDialogVisible" :title="title">
        <el-table border :show-header="false" :data="headerTableData" :span-method="headerMergeMethod">
            <el-table-column prop="col0" align="center">
                <template>
                    <img :src="logo" height="100px" width="160px" alt="">
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-input class="center-input" v-if="isEdit" size="mini" v-model="certForm.fileNo"></el-input>
                        <span v-else>{{scope.row.col1}}</span>
                    </template>
                    <span v-else>{{scope.row.col1}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
            </el-table-column>
            <el-table-column prop="col4" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-input size="mini" class="center-input" v-model="certForm.qualityNo" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <template v-else-if="scope.$index===1">
                        <el-input size="mini" class="center-input" v-model="certForm.no" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <template v-else-if="scope.$index===2">
                        <el-input size="mini" class="center-input" v-model="certForm.versionNo" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <span v-else>
                        {{scope.row.col4}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="baseTableData" :span-method="baseMergeMethod">
            <el-table-column prop="col0" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-select style="width:100%" v-if="isEdit" v-model="certForm.riskType" size="mini" class="center-input">
                            <el-option v-for="item in riskTypeList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <div style="min-height: 20px" v-else>{{scope.row.col0}}</div>
                    </template>
                    <div v-else>{{scope.row.col0}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===4">
                        <el-autocomplete
                            class="center-input"
                            size="mini"
                            v-model="certForm.repairLevel"
                            v-if="isEdit"
                            :fetch-suggestions="createQueryString(maintainLevelList)"
                            style="width:100%"
                        ></el-autocomplete>
                        <span v-else>{{scope.row.col1}}</span>
                    </template>
                    <span v-else>{{scope.row.col1}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
            </el-table-column>
            <el-table-column prop="col4" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===2">
                        <el-input size="mini" class="center-input" v-model="certForm.createDate" v-if="isEdit">
                        </el-input>
                        <div style="min-height: 20px" v-else>{{scope.row.col4}}</div>
                    </template>
                    <div style="min-height: 20px" v-else>{{scope.row.col4}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="maintainTableData" :span-method="maintainMergeMethod">
            <el-table-column prop="col0" width="153" align="center">
            </el-table-column>
            <el-table-column prop="col1" width="153" align="center">
            </el-table-column>
            <el-table-column prop="col2" width="153" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===1">
                        <el-select style="width:100%" v-if="isEdit" v-model="certForm.surfaceCheck" size="mini" class="center-input">
                            <el-option v-for="item in qualifyResultList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===2">
                        <el-select style="width:100%" v-if="isEdit" v-model="certForm.needleCheck" size="mini" class="center-input">
                            <el-option v-for="item in qualifyResultList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===3">
                        <el-select style="width:100%" v-if="isEdit" v-model="certForm.functionCheck" size="mini" class="center-input">
                            <el-option v-for="item in qualifyResultList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===4">
                        <el-select style="width:100%" v-if="isEdit" v-model="certForm.ringoutCheck" size="mini" class="center-input">
                            <el-option v-for="item in qualifyResultList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===5">
                        <el-input size="mini" class="center-input" v-model="certForm.gammaRatio" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <span v-else>{{scope.row.col3}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col4" align="center">
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
            <el-table-column prop="col6" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="footerTableData" :span-method="footerMergeMethod">
            <el-table-column prop="col0" align="left">
                <template slot-scope="scope">
                    <template v-if="scope.$index===1">
                        <el-input type="textarea" v-model="certForm.checkResult" v-if="isEdit">
                        </el-input>
                        <div style="min-height: 20px" v-else>{{scope.row.col0}}</div>
                    </template>
                    <div style="min-height: 20px" v-else>{{scope.row.col0}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="certForm.repairUser" v-if="isEdit">
                        </el-input>
                        <div style="min-height: 20px" v-else>{{scope.row.col1}}</div>
                    </template>
                    <div style="min-height: 20px" v-else>{{scope.row.col1}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="certForm.supervisorUser" v-if="isEdit">
                        </el-input>
                        <div style="min-height: 20px" v-else>{{scope.row.col3}}</div>
                    </template>
                    <div style="min-height: 20px" v-else>{{scope.row.col3}}</div>
                </template>
            </el-table-column>
            <el-table-column prop="col4" align="center">
            </el-table-column>
            <el-table-column prop="col5" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="certForm.qualityCheckUser" v-if="isEdit">
                        </el-input>
                        <div style="min-height: 20px" v-else>{{scope.row.col5}}</div>
                    </template>
                    <div style="min-height: 20px" v-else>{{scope.row.col5}}</div>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <template v-if="isEdit">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSave">保存</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="onEdit" v-if="p_CertUpdate">编辑</el-button>
                <el-button type="primary" @click="onExport" v-if="p_CertExport">导出</el-button>
            </template>
        </template>
    </el-dialog>
</template>
<script>
import logo from "@/assets/images/tartan.jpg";
import { maintainLevelList, qualifyResultList, riskTypeList } from "./constant"
import { apiExportCert, apiGetCertInfo, apiUpdateCertInfo } from '@/api/mwd';
export default {
    name: "Dm",
    data() {
        return {
            isEdit: false,
            certForm: {
                replaceItemList:[]
            },
            riskTypeList,
            maintainLevelList,
            qualifyResultList,
            logo,
            isDialogVisible: false,
            title: "合格证",
        }
    },
    computed:{
        p_CertUpdate(){
            return this.$checkBtnPermission("sys:mwd:cert:edit")
        },
        p_CertExport(){
            return this.$checkBtnPermission("sys:mwd:cert:export")
        },
        headerTableData(){
            return [
                {
                    col0: "img",
                    col1: this.certForm.fileNo,
                    col3: "质量体系编号",
                    col4: this.certForm.qualityNo
                },
                {
                    col3: "NO:",
                    col4: this.certForm.no
                },
                {
                    col1: '四川达坦能源科技有限公司',
                    col3: "版本/修改",
                    col4: this.certForm.versionNo
                },
                {
                    col3: "页次",
                    col4: "1 of 1"
                }
            ]
        },
        baseTableData(){
            return [
                {
                    col0: this.certForm.riskType,
                },
                {
                    col0: "方位探管维护报告（合格证）"
                },
                {
                    col0: "仪器编号（S/N）",
                    col1: this.certForm.serialNumber,
                    col3: "出厂日期",
                    col4: this.certForm.createDate
                },
                {
                    col0: "维修工单",
                    col1: this.certForm.mwdNumber
                },
                {
                    col0: "维修等级",
                    col1: this.certForm.repairLevel
                }
            ]
        },
        maintainTableData() {
            return [
                {
                    col0:'已执行维修',
                    col1: "项目",
                    col3: "结果"
                },
                {   
                    col1: "外观检查",
                    col3: this.certForm.surfaceCheck
                },
                {   
                    col1: "针孔测试",
                    col3: this.certForm.needleCheck
                },
                {   
                    col1: "功能测试",
                    col3: this.certForm.functionCheck
                },
                {   
                    col1: "Ringout检查",
                    col3: this.certForm.ringoutCheck
                },
                {   
                    col1: "伽马系数",
                    col3: this.certForm.gammaRatio
                },
                // {
                //     col1: "更换部件明细",
                //     col3: "名称",
                //     col4: "数量",
                //     col5: "单位",
                //     col6: "备注"
                // },
                // ...this.certForm.replaceItemList.map(item=>({
                //     col3: item.invName,
                //     col4: item.quantity,
                //     col5: item.unitName,
                //     col6: item.note
                // }))
            ]
        },
        footerTableData(){
            return [
                {
                    col0: "检测结果"
                },
                {
                    col0: this.certForm.checkResult
                },
                {
                    col0: "签字Signature"
                },
                {
                    col0:"维护人员:",
                    col1:this.certForm.repairUser,
                    col2:"主管人员:",
                    col3:this.certForm.supervisorUser,
                    col4:"质检人员:",
                    col5:this.certForm.qualityCheckUser,
                }
            ]
        }
    },
    methods: {
        showDialog(form){
            this.certForm = form.certInfo;
            this.certForm.replaceItemList = this.certForm.replaceItemList || [];
            this.isDialogVisible = true;
        },
        async getCertInfo(){
            const form = new FormData();
            form.append("mwdId", this.certForm.mwdId);
            await apiGetCertInfo(form).then(res=>{
                this.certForm = res.data.data.certInfo;
                this.certForm.replaceItemList = this.certForm.replaceItemList || [];
            })
        },
        async onCancel(){
            await this.getCertInfo();
            this.isEdit = false;
        },
        onSave(){
            apiUpdateCertInfo({
                certInfo: this.certForm
            }).then(res=>{
                this.isEdit = false;
            });
        },
        onEdit(){
            this.isEdit = true;
        },
        onExport() {
            const form = new FormData();
            form.append("mwdId", this.certForm.mwdId);
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            apiExportCert(form).then(res => {
                const blob = new Blob([res.data], {
                    type: 'application/word;charset=UTF-8',
                });
                const fileName = `${this.certForm.serialNumber}合格证.xlsx`;
                const link = document.createElement('a');
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                //释放内存
                link.remove(); // 下载完成移除元素
                window.URL.revokeObjectURL(link.href);
            }).finally(() => {
                loading.close();
            });
        },
        headerMergeMethod({ rowIndex, columnIndex }) {
            if(rowIndex===0&&columnIndex===0){
                return [4,1]
            }
            if(rowIndex===0&&columnIndex===1){
                return [2,2]
            }
            if(rowIndex===2&&columnIndex===1){
                return [2,2]
            }
            if(columnIndex===4){
                return [1,2]
            }
            if(columnIndex===3){
                return [1,1]
            }
            return [0,0]
        },
        baseMergeMethod({ rowIndex, columnIndex }) {
            if(rowIndex===0 || rowIndex === 1){
                return [1,6]
            }
            if(rowIndex===2){
                if(columnIndex === 0){
                    return [1,1]
                }
                if(columnIndex===1){
                    return [1,2]
                }
                if(columnIndex===3){
                    return [1,1]
                }
                if(columnIndex===4){
                    return [1,2]
                }
            }
            if(rowIndex===3 || rowIndex === 4){
                if(columnIndex === 0){
                    return [1,1]
                }
                if(columnIndex===1){
                    return [1,5]
                }
            }
            return [0,0]
        },
        maintainMergeMethod({ rowIndex, columnIndex }) {
            const exchangeRowNumber = 0,
                itemRowNumber = 6;
            const totalRowNumber = exchangeRowNumber + itemRowNumber;

            if(columnIndex===0&&rowIndex===0) {
                return [totalRowNumber,1]
            }
            if(columnIndex===1){
                if(rowIndex<itemRowNumber){
                    return [1,2]
                }
                if(rowIndex===itemRowNumber){
                    return [exchangeRowNumber, 2]
                }
            
            }
            if(columnIndex===3){
                if(rowIndex<itemRowNumber){
                    return [1,4]
                }else{
                    return [1,1]
                }
            }
            if(columnIndex>3 && rowIndex>=itemRowNumber){
                return [1,1]
            }
            return [0,0]
        },
        footerMergeMethod({ rowIndex, columnIndex }) {
            if(rowIndex===0 || rowIndex===1 || rowIndex === 2){
                return [1,6]
            }
            if(rowIndex===3){
                return [1,1]
            }
            return [0,0]
        },
        createQueryString(target){
            return (qs,cb) => {
                let results = qs ? target.filter(this.createFilter(qs)) : target;
                cb(results.map(r=>({value:r})));
            }
        },
        createFilter(qs) {
            return (item) => {
                return (item.toLowerCase().indexOf(qs.toLowerCase()) >= 0);
            };
        },
    }
}
</script>

<style lang="scss">
.c-template-dialog .el-table--medium .el-table__cell{
    padding: 0;
}
.c-template-dialog .el-table .cell{
    padding: 0 !important;
}
.c-template-dialog .el-dialog__body{
    height: 60vh !important;
    overflow: auto;
    padding: 10px 20px 20px !important;
}
</style>
<style scoped>
::v-deep .el-table tbody tr:hover > td{
    background-color: transparent !important;
}
::v-deep .center-input .el-input__inner{
    text-align: center;
}
</style>