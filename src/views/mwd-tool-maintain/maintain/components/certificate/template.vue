<template>
    <el-dialog custom-class="c-template-dialog" :visible.sync="isDialogVisible" :title="title">
        <el-table border :show-header="false" :data="headerTableData" :span-method="headerMergeMethod">
            <el-table-column prop="col0" align="center">
                <template>
                    <img :src="logo" height="100px" width="160px" alt="">
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-input class="center-input" v-if="isEdit" size="mini" v-model="id"></el-input>
                        <span v-else>{{scope.row.col1}}</span>
                    </template>
                    <span v-else>{{scope.row.col1}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
            </el-table-column>
            <el-table-column prop="col4" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-input size="mini" class="center-input" v-model="qualityNumber" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <template v-else-if="scope.$index===1">
                        <el-input size="mini" class="center-input" v-model="number" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <template v-else-if="scope.$index===2">
                        <el-input size="mini" class="center-input" v-model="version" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col4}}</span>
                    </template>
                    <span v-else>
                        {{scope.row.col4}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="baseTableData" :span-method="baseMergeMethod">
            <el-table-column prop="col0" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===0">
                        <el-autocomplete
                            class="center-input"
                            size="mini"
                            v-model="temperature"
                            v-if="isEdit"
                            :fetch-suggestions="createQueryString(temperatureSuggestions)"
                            style="width:100%"
                        ></el-autocomplete>
                        <span v-else>{{scope.row.col0}}</span>
                    </template>
                    <span v-else>{{scope.row.col0}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===4">
                        <el-autocomplete
                            class="center-input"
                            size="mini"
                            v-model="maintainLevel"
                            v-if="isEdit"
                            :fetch-suggestions="createQueryString(maintainLevelList)"
                            style="width:100%"
                        ></el-autocomplete>
                        <span v-else>{{scope.row.col1}}</span>
                    </template>
                    <span v-else>{{scope.row.col1}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
            </el-table-column>
            <el-table-column prop="col4" align="center">
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="maintainTableData" :span-method="maintainMergeMethod">
            <el-table-column prop="col0" align="center">
            </el-table-column>
            <el-table-column prop="col1" align="center">
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index>=1&&scope.$index<=4">
                        <el-select style="width:100%" v-if="isEdit" v-model="qualified" size="mini" class="center-input">
                            <el-option v-for="item in qualifyResultList" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===5">
                        <el-input size="mini" class="center-input" v-if="isEdit" v-model="noLoadVoltage">

                        </el-input>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <template v-else-if="scope.$index===6">
                        <el-input size="mini" class="center-input" v-if="isEdit" v-model="loadVoltage">

                        </el-input>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <span v-else>{{scope.row.col3}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col4" align="center">
            </el-table-column>
            <el-table-column prop="col5" align="center">
            </el-table-column>
        </el-table>
        <el-table style="margin-top:-1px" border :show-header="false" :data="footerTableData" :span-method="footerMergeMethod">
            <el-table-column prop="col0" align="left">
                <template slot-scope="scope">
                    <template v-if="scope.$index===1">
                        <el-input type="textarea" v-model="testResult" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col0}}</span>
                    </template>
                    <span v-else>{{scope.row.col0}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col1" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="maintainStaff" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col1}}</span>
                    </template>
                    <span v-else>{{scope.row.col1}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col2" align="center">
            </el-table-column>
            <el-table-column prop="col3" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="supervisorUser" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col3}}</span>
                    </template>
                    <span v-else>{{scope.row.col3}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="col4" align="center">
            </el-table-column>
            <el-table-column prop="col5" align="center">
                <template slot-scope="scope">
                    <template v-if="scope.$index===3">
                        <el-input v-model="qualityCheckUser" v-if="isEdit">
                        </el-input>
                        <span v-else>{{scope.row.col5}}</span>
                    </template>
                    <span v-else>{{scope.row.col5}}</span>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="isDialogVisible=false">取消</el-button>
            <el-button type="primary" @click="onConfirm">确定</el-button>
        </template>
    </el-dialog>
</template>
<script>
import logo from "@/assets/images/tartan.jpg";
import { temperatureSuggestions, maintainLevelList, qualifyResultList } from "./constant"
export default {
    name: "CertificateTemplate",
    data() {
        return {
            isEdit: false,
            temperatureSuggestions,
            maintainLevelList,
            qualifyResultList,
            logo,
            isDialogVisible: false,
            title: "证书模板",
            id: 'TSC/ZY/MWD:2020',
            qualityNumber: "TSC/ZY-MWD-BT-002-1.0",
            number: "BT1005-240510",
            version: '1',
            headerTableData: [
                {
                    col0: "img",
                    col1: 'TSC/ZY/MWD:2020',
                    col3: "质量体系编号",
                    col4: "TSC/ZY-MWD-BT-002-1.0"
                },
                {
                    col3: "NO:",
                    col4: "BT1005-240510"
                },
                {
                    col1: '四川达坦能源科技有限公司',
                    col3: "版本/修改",
                    col4: "1"
                },
                {
                    col3: "页次",
                    col4: "1 of 1"
                }
            ],
            temperature: "165℃ New",
            maintainLevel: '二级维护',
            baseTableData: [
                {
                    col0: "165℃ New",
                },
                {
                    col0: "锂电池维护报告（合格证）"
                },
                {
                    col0: "仪器编号（S/N）",
                    col1: "BT1005",
                    col3: "出厂日期",
                    col4: "2024/5/10"
                },
                {
                    col0: "维修工单",
                    col1: "5301-240304018"
                },
                {
                    col0: "维修等级",
                    col1: "二级维护"
                }
            ],
            qualified: '合格',
            noLoadVoltage: '29.05V',
            loadVoltage: '--V',
            maintainTableData: [
                {
                    col0:'已执行维修',
                    col1: "项目",
                    col3: "结果"
                },
                {   
                    col1: "外观检查",
                    col3: "合格"
                },
                {   
                    col1: "针孔测试",
                    col3: "合格"
                },
                {   
                    col1: "电压检查",
                    col3: "合格"
                },
                {   
                    col1: "Ringout检查",
                    col3: "合格"
                },
                {   
                    col1: "未加载电压",
                    col3: "29.05V"
                },
                {   
                    col1: "加载电压",
                    col3: "--V"
                },
                {
                    col1: "更换部件明细",
                    col3: "名称",
                    col4: "数量",
                    col5: "备注"
                },
                {
                    col3: "名称1",
                    col4: "数量1",
                    col5: "备注1"
                },
                {
                    col3: "名称2",
                    col4: "数量2",
                    col5: "备注2"
                },
                {
                    col3: "名称3",
                    col4: "数量3",
                    col5: "备注3"
                },
                {
                    col3: "名称4",
                    col4: "数量4",
                    col5: "备注4"
                },
                {
                    col3: "名称5",
                    col4: "数量5",
                    col5: "备注5"
                },
                {
                    col3: "名称6",
                    col4: "数量6",
                    col5: "备注6"
                }
            ],
            testResult: "按照达坦锂电池检查测试规范进行出厂检测并通过",
            maintainStaff: '廖鹏',
            supervisorUser: "丁俊",
            qualityCheckUser: "何永达",
            footerTableData:[
                {
                    col0: "检测结果"
                },
                {
                    col0:"按照达坦锂电池检查测试规范进行出厂检测并通过"
                },
                {
                    col0: "签字Signature"
                },
                {
                    col0:"维护人员:",
                    col1:"廖鹏",
                    col2:"主管人员:",
                    col3:"丁俊",
                    col4:"质检人员:",
                    col5:"何永达"
                }
            ]
        }
    },
    methods: {
        headerMergeMethod({ row, column, rowIndex, columnIndex }) {
            if(rowIndex===0&&columnIndex===0){
                return [4,1]
            }
            if(rowIndex===0&&columnIndex===1){
                return [2,2]
            }
            if(rowIndex===2&&columnIndex===1){
                return [2,2]
            }
            if(columnIndex===4){
                return [1,2]
            }
            if(columnIndex===3){
                return [1,1]
            }
            return [0,0]
        },
        baseMergeMethod({ row, column, rowIndex, columnIndex }) {
            if(rowIndex===0 || rowIndex === 1){
                return [1,6]
            }
            if(rowIndex===2){
                if(columnIndex === 0){
                    return [1,1]
                }
                if(columnIndex===1){
                    return [1,2]
                }
                if(columnIndex===3){
                    return [1,1]
                }
                if(columnIndex===4){
                    return [1,2]
                }
            }
            if(rowIndex===3 || rowIndex === 4){
                if(columnIndex === 0){
                    return [1,1]
                }
                if(columnIndex===1){
                    return [1,5]
                }
            }
            return [0,0]
        },
        maintainMergeMethod({ row, column, rowIndex, columnIndex }) {
            const exchangeRowNumber = this.certForm.replaceItemList.length + 1,
                itemRowNumber = 7;
            const totalRowNumber = exchangeRowNumber + itemRowNumber;

            if(columnIndex===0&&rowIndex===0) {
                return [totalRowNumber,1]
            }
            if(columnIndex===1){
                if(rowIndex<itemRowNumber){
                    return [1,2]
                }
                if(rowIndex===itemRowNumber){
                    return [exchangeRowNumber, 2]
                }
            
            }
            if(columnIndex===3){
                if(rowIndex<itemRowNumber){
                    return [1,3]
                }else{
                    return [1,1]
                }
            }
            if(columnIndex>3 && rowIndex>=itemRowNumber){
                return [1,1]
            }
            return [0,0]
        },
        footerMergeMethod({ row, column, rowIndex, columnIndex }) {
            if(rowIndex===0 || rowIndex===1 || rowIndex === 2){
                return [1,6]
            }
            if(rowIndex===3){
                return [1,1]
            }
            return [0,0]
        },
        onConfirm() {
            this.isDialogVisible = false;
        },
        createQueryString(target){
            return (qs,cb) => {
                let results = qs ? target.filter(this.createFilter(qs)) : target;
                cb(results.map(r=>({value:r})));
            }
        },
        querySearch(queryString, cb) {
            var temperatureSuggestions = this.temperatureSuggestions;
            var results = queryString ? temperatureSuggestions.filter(this.createFilter(queryString)) : temperatureSuggestions;
            cb(results.map(r=>({value:r})));
        },
        createFilter(queryString) {
            return (item) => {
                return (item.toLowerCase().indexOf(queryString.toLowerCase()) >= 0);
            };
        },
    }
}
</script>

<style lang="scss">
.c-template-dialog .el-table--medium .el-table__cell{
    padding: 0;
}
.c-template-dialog .el-table .cell{
    padding: 0 !important;
}
.c-template-dialog .el-dialog__body{
    height: 60vh !important;
    overflow: auto;
    padding: 10px 20px 20px !important;
}
</style>
<style scoped>
::v-deep .el-table tbody tr:hover > td{
    background-color: transparent !important;
}
::v-deep .center-input .el-input__inner{
    text-align: center;
}
</style>