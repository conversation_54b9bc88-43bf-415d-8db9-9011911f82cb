<template>
    <el-dialog :visible.sync="isHistoryTrackDialogVisible" :title="`历史追溯 - ${invName} / ${serialNumber}`" width="80%">
        <el-table v-if="activeName==='service'" :data="serviceTableData">
            <el-table-column prop="invName" label="品名"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>
            <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
            <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
            <el-table-column prop="mwdNumber" label="维修单号">
                <template slot-scope="{row}">
                    <router-link 
                        tag="a" 
                        style="color: blue" 
                        target="_blank" 
                        :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                        v-if="p_MwdInfoView"
                    >
                        {{ row.mwdNumber }}
                    </router-link>
                <span v-else>{{ row.mwdNumber }}</span>
            </template>
            </el-table-column>
            <el-table-column prop="wellNumber" label="井号"></el-table-column>
            <el-table-column prop="jobNumber" label="作业号"></el-table-column>
            <el-table-column prop="circulateHrs" label="循环时间" width="80"></el-table-column>
            <el-table-column prop="inWellHour" label="入井时间" width="80"></el-table-column>
            <el-table-column prop="maxBht" label="最高温度" width="80"></el-table-column>
            <el-table-column prop="run" label="入井趟次" width="80"></el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetTestServiceHistoryList } from "@/api/mwd";
import { Component, Vue } from "vue-property-decorator";
@Component({ name: "TestTrack" })
export default class extends Vue {
    private activeName = "service";
    private isHistoryTrackDialogVisible = false;
    private serviceTableData:any[] = [];
    private invName = "";
    private serialNumber = "";
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private async showDialog({ serialNumber, invName}:any){
        if(!serialNumber){
            return;
        }
        this.serialNumber = serialNumber;
        this.invName = invName;
        await this.getServiceHistoryList();
        this.isHistoryTrackDialogVisible = true;
        
    }
    private getServiceHistoryList(){
       return apiGetTestServiceHistoryList({ serialNumber:this.serialNumber }).then(res=>{
            const data = res.data.data || {};
            this.serviceTableData = data || [];
        })
    }
    
    
}
</script>