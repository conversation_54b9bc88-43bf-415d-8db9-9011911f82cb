<template>
    <el-dialog :visible.sync="isDialogVisible" custom-class="test-detail-dialog" :title="`试用件详情 - ${invName} / ${serialNumber}`" width="60%">
        <el-tabs v-model="activeName">
            <el-tab-pane label="试用件信息" name="info">
                <div class="info-item">
                    <div class="info-title">试用物资名称</div>
                    <div class="info-content">{{testDetail.invName}}</div>
                </div>
                <div class="info-item">
                    <div class="info-title">序列号</div>
                    <div class="info-content">{{testDetail.serialNumber}}</div>
                </div>
                <div class="info-item">
                    <div class="info-title">引入目的</div>
                    <div class="info-content">{{testDetail.testPurpose}}</div>
                </div>
                <div class="info-item">
                    <div class="info-title">需要现场测试验证的点</div>
                    <div class="info-content">{{testDetail.testContent}}</div>
                </div>
                <div class="info-item">
                    <div class="info-title">验收标准</div>
                    <div class="info-content">
                        {{standardStr}}
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-title">试用期计划安排</div>
                    <div class="info-content">{{testDetail.testPlan}}</div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="历史追溯" name="history">
                <el-table :data="serviceTableData">
                    <el-table-column prop="description" label="描述"></el-table-column>
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column prop="mwdNumber" label="维修单号">
                        <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                v-if="p_MwdInfoView"
                            >
                                {{ row.mwdNumber }}
                            </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                    </el-table-column>
                    <el-table-column prop="wellNumber" label="井号"></el-table-column>
                    <el-table-column prop="jobNumber" label="作业号"></el-table-column>
                    <el-table-column prop="circulateHrs" label="循环时间" width="80"></el-table-column>
                    <el-table-column prop="inWellHour" label="入井时间" width="80"></el-table-column>
                    <el-table-column prop="maxBht" label="最高温度" width="80"></el-table-column>
                    <el-table-column prop="run" label="入井趟次" width="80"></el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetTestServiceHistoryList } from "@/api/mwd";
import { getTestWarnStrategyStr } from "@/utils/constant.mwd";
import { Component, Vue } from "vue-property-decorator";
@Component({ name: "TestDetailDialog" })
export default class extends Vue {
    private activeName = "info";
    private isDialogVisible = false;
    private serviceTableData:any[] = [];
    private invName = "";
    private serialNumber = "";
    private testDetail:any = {};
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    get standardStr(){
        return getTestWarnStrategyStr(this.testDetail)
    }
    private async showDialog(row){
        const { invName, serialNumber } = row;
        this.testDetail = row;
        if(!serialNumber){
            return;
        }
        this.serialNumber = serialNumber;
        this.invName = invName;
        await this.getServiceHistoryList();
        this.isDialogVisible = true;
        
    }
    private getServiceHistoryList(){
       return apiGetTestServiceHistoryList({ serialNumber:this.serialNumber }).then(res=>{
            const data = res.data.data || {};
            this.serviceTableData = data || [];
        })
    }
    
    
}
</script>
<style lang="scss" scoped>
    ::v-deep .test-detail-dialog .el-dialog__body{
        padding: 10px 20px 30px;
    }
    .info-item {
        margin-bottom: 10px;
        .info-title{
            font-size: 16px;
            color: rgb(67, 86, 152);
            font-weight: bold;
        }
        
        .info-title::before{
            content: " ";
            display: inline-block;
            width: 0;
            height: 0;
            border: 6px solid;
            border-color: transparent transparent transparent rgb(67, 86, 152);
        }
        .info-content{
            font-size: 15px;
            margin: 10px 0 10px 12px;
            white-space: pre-line;
        }
    }
</style>