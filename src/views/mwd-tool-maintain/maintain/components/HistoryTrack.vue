<template>
    <el-dialog :visible.sync="isHistoryTrackDialogVisible" :title="`服役详情 - ${invName} / ${serialNumber}`" width="80%">
        <el-tabs v-model="activeName" style="margin-top:-20px">
            <el-tab-pane label="历史服役" name="service" style="min-height:200px">
                <el-table v-if="activeName==='service'" :data="serviceTableData">
                    <el-table-column
                        prop="outMwdNumber"
                        label="出厂操作单号"
                        min-width="110"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/mwd-tool-maintain/maintain?mwdId=${row.commonId}`" 
                                    v-if="p_MwdInfoView"
                                >
                                    {{ row.outMwdNumber }}
                                </router-link>
                            <span v-else>{{ row.outMwdNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="versionNumber" label="固件版本号"></el-table-column>
                    <el-table-column prop="startDate" label="服役日期" width="100"></el-table-column>
                    <!-- <el-table-column prop="endDate" label="服役结束时间"></el-table-column> -->
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column prop="wellNumber" label="井号"></el-table-column>
                    <el-table-column prop="jobNumber" label="作业号"></el-table-column>
                    <el-table-column prop="circulateHrs" label="循环时间" width="80"></el-table-column>
                    <el-table-column prop="inWellHour" label="入井时间" width="80"></el-table-column>
                    <el-table-column prop="maxBht" label="最高温度" width="80"></el-table-column>
                    <el-table-column prop="run" label="入井趟次" width="80"></el-table-column>
                    <el-table-column
                        prop="mwdNumber"
                        label="入厂操作单号"
                        min-width="110"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/mwd-tool-maintain/maintain?mwdId=${row.endMwdId}`" 
                                    v-if="p_MwdInfoView"
                                >
                                    {{ row.mwdNumber }}
                                </router-link>
                            <span v-else>{{ row.mwdNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="serviceTotal"
                    :page-size="pageSize"
                    @current-change="onServiceCurrentChange"
                    :current-page="serviceCurrentPage"
                ></el-pagination>
            </el-tab-pane>
            <!-- <el-tab-pane label="历史维修" name="maintain" style="min-height:200px">
                <el-table v-if="activeName==='maintain'" :data="maintainTableData">
                    <el-table-column
                        prop="serviceVersionNumber"
                        label="服役固件版本号"
                        align="center"
                        width="120"
                    ></el-table-column>
                    <el-table-column
                        prop="updatedVersionNumber"
                        label="更新固件版本号"
                        align="center"
                        width="120"
                    ></el-table-column>
                    <el-table-column
                        prop="maxBht"
                        label="最高温度(℃)"
                        align="center"
                        width="110"
                    ></el-table-column>
                    <el-table-column
                        prop="reviseMaxBht"
                        label="修正最高温度(℃)"
                        align="center"
                        width="130"
                    ></el-table-column>
                    <el-table-column
                        prop="totalInWellHrs"
                        label="总入井时间(h)"
                        align="center"
                        width="120"
                    ></el-table-column>
                    <el-table-column
                        prop="reviseTotalHours"
                        label="修正总入井时间(h)"
                        align="center"
                        width="140"
                    ></el-table-column>
                    <el-table-column prop="parentInvName" align="center" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" align="center" label="母件序列号" width="120"></el-table-column>
                    <el-table-column
                        prop="mwdNumber"
                        label="维修单号"
                        min-width="110"
                        align="center"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                    v-if="p_MwdInfoView"
                                >
                                    {{ row.mwdNumber }}
                                </router-link>
                            <span v-else>{{ row.mwdNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="repairDate" label="维修日期" align="center"></el-table-column>
                    <el-table-column prop="repairType" label="维修类型" align="center">
                        <template slot-scope="scope">
                            <CustomTag tagType="MAINTAIN_TYPE" :tagValue="scope.row.repairType"></CustomTag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="failureReason" label="故障原因" align="center"></el-table-column>
                    <el-table-column prop="repairAction" label="方案&执行措施" align="center" width="120"></el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="maintainTotal"
                    :page-size="pageSize"
                    @current-change="onMaintainCurrentChange"
                    :current-page="maintainCurrentPage"
                ></el-pagination>
            </el-tab-pane> -->
            <el-tab-pane label="出厂列表" name="out" style="min-height:200px">
                <el-table v-if="activeName==='out'" :data="outTableData">
                    <el-table-column prop="invName" label="部件名称"></el-table-column>
                    <el-table-column prop="serialNumber" label="序列号"></el-table-column>
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column
                        prop="mwdNumber"
                        label="维修单号"
                        min-width="110"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/mwd-tool-maintain/maintain?mwdId=${row.commonId}`" 
                                    v-if="p_MwdInfoView"
                                >
                                    {{ row.mwdNumber }}
                                </router-link>
                            <span v-else>{{ row.mwdNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="outTotal"
                    :page-size="pageSize"
                    @current-change="onOutCurrentChange"
                    :current-page="outCurrentPage"
                ></el-pagination>
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetCoreOutList } from "@/api/mwd";
import { apiGetServiceHistoryList, apiGetMaintainHistoryList } from "@/api/tool-track";
import { Component, Vue } from "vue-property-decorator";
@Component({ name: "HistoryTrack" })
export default class extends Vue {
    private activeName = "service";
    private isHistoryTrackDialogVisible = false;
    private serviceTableData:any[] = [];
    private maintainTableData:any[] = [];
    private pageSize = 10;
    private serviceCurrentPage = 1;
    private serviceTotal = 0;
    private maintainCurrentPage = 1;
    private maintainTotal = 0;
    private outTableData:any[] = [];
    private outCurrentPage = 1;
    private outTotal = 0;
    private serialNumber: string = '';
    private invName: string = '';
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private async showDialog({ serialNumber, invName}:any){
        if(!serialNumber){
            return;
        }
        this.serialNumber = serialNumber;
        this.invName = invName;
        await this.getServiceHistoryList();
        // await this.getMaintainHistoryList();
        await this.getOutList();
        this.isHistoryTrackDialogVisible = true;
        
    }
    private getServiceHistoryList(){
       return apiGetServiceHistoryList({serialNumber:this.serialNumber, invName: this.invName}, {current:this.serviceCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.serviceTableData = data.records || [];
            this.serviceTotal = data.total;
        })
    }
    private onServiceCurrentChange(currentPage: number) {
        this.serviceCurrentPage = currentPage;
        this.getServiceHistoryList();
    }
    private getMaintainHistoryList(){
        return apiGetMaintainHistoryList({serialNumber:this.serialNumber, invName: this.invName}, {current:this.maintainCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.maintainTableData = data.records || [];
            this.maintainTotal = data.total;
        })
    }
    private onMaintainCurrentChange(currentPage: number) {
        this.maintainCurrentPage = currentPage;
        this.getMaintainHistoryList();
    }
    private getOutList(){
        return apiGetCoreOutList({serialNumber:this.serialNumber, invName: this.invName}, {current:this.outCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.outTableData = data.records || [];
            this.outTotal = data.total;
        })
    }
    private onOutCurrentChange(currentPage: number) {
        this.outCurrentPage = currentPage;
        this.getOutList();
    }
    
    
}
</script>