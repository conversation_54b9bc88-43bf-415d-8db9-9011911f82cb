<template>
    <div>
        <div class="tool-card-title">维修检查</div>
        <div class="step-container">
            <div
                class="step"
                :class="activeStep === item.component ? 'active' : ''"
                @click="onClickStep(item)"
                v-for="item in steps"
                :key="item.name"
            >
                <span v-html="item.name" style="white-space: pre-line"></span>
            </div>
        </div>
        <div class="component-container">
            <component
                :is="activeStep"
                :checkData="checkData"
                :isEdit="isEdit"
            ></component>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import RingOutTest from "./checks/ringouttest.vue";
import Checks from "./checks/checks.vue";
import Dimensions from "./checks/dimensions.vue";
import RollTest from "./checks/rolltest.vue";
import HeatTest from "./checks/heattest.vue";
import FunctionTest from "./checks/functiontest.vue";
import ConfigureCheck from "./checks/configurationcheck.vue";
import FaultDiagnosis from "./checks/faultdiagnosis.vue";
import DriverInspection from "./checks/driverinspection.vue";
import OilFillSectionInspection from "./checks/oilfillsectioninspection.vue";
import DriverRingOut from "./checks/driverringout.vue";
import MotorRingOut from "./checks/motorringout.vue";
import OutgoingChecks from "./checks/outgoingchecks.vue";
import IncomingTearDownChecks from "./checks/incomingteardownchecks.vue";
import AtBit from "./checks/atbit.vue";
@Component({
    components: {
        RingOutTest,
        Checks,
        Dimensions,
        RollTest,
        HeatTest,
        FunctionTest,
        ConfigureCheck,
        FaultDiagnosis,
        DriverInspection,
        OilFillSectionInspection,
        DriverRingOut,
        MotorRingOut,
        OutgoingChecks,
        IncomingTearDownChecks,
        AtBit,
    },
})
export default class extends Vue {
    private activeStep = "";
    get steps() {
        switch (String(this.deviceType)) {
            case "16007":
                return [
                    { name: "Ring Out Test", component: "RingOutTest" },
                    { name: "Checks", component: "Checks" },
                    { name: "Dimensions", component: "Dimensions" },
                    {
                        name: "Roll Test\n(Level 2 and Level 3)",
                        component: "RollTest",
                    },
                    {
                        name: "Heat Test\n(Level 3 Only)",
                        component: "HeatTest",
                    },
                    { name: "Function Test", component: "FunctionTest" },
                    { name: "故障诊断", component: "FaultDiagnosis" },
                ];
            case "16002":
                return [
                    { name: "Ring Out Test", component: "RingOutTest" },
                    { name: "Checks", component: "Checks" },
                    { name: "Dimensions", component: "Dimensions" },
                    {
                        name: "Roll Test\n(Level 2 and Level 3)",
                        component: "RollTest",
                    },
                    {
                        name: "Heat Test\n(Level 3 Only)",
                        component: "HeatTest",
                    },
                    { name: "Function Test", component: "FunctionTest" },
                    { name: "Configure Check", component: "ConfigureCheck" },
                    { name: "故障诊断", component: "FaultDiagnosis" },
                ];
            case "16001":
                return [
                    { name: "Checks", component: "AtBit" },
                    { name: "故障诊断", component: "FaultDiagnosis" },
                ];
            case "16005":
                return [
                    {
                        name: "入厂拆卸检查",
                        component: "IncomingTearDownChecks",
                    },
                    { name: "出厂检查", component: "OutgoingChecks" },
                    { name: "故障诊断", component: "FaultDiagnosis" },
                ];
            case "16011":
                return [
                    {
                        name: "Driver Inspection",
                        component: "DriverInspection",
                    },
                    {
                        name: "Oil Fill Section Inspection",
                        component: "OilFillSectionInspection",
                    },
                    { name: "Driver Ring Out", component: "DriverRingOut" },
                    { name: "Motor Ring Out", component: "MotorRingOut" },
                    {
                        name: "Heat Test\n(Level 3 Only)",
                        component: "HeatTest",
                    },
                    { name: "Function Test", component: "FunctionTest" },
                    { name: "故障诊断", component: "FaultDiagnosis" },
                ];
            default:
                return [{ name: "故障诊断", component: "FaultDiagnosis" }];
        }
    }
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => {} }) originForm!: any;
    get checkData(){
        return this.originForm?.mwdRepairCheckData?.checkData || {};
    }
    get deviceType(){
        return this.originForm?.workOrderMwdDetailList[0]?.deviceType
    }
    @Watch("deviceType")
    onDeviceTypeChange(nv: string) {
        switch (nv) {
            case "DM":
                this.activeStep = "RingOutTest";
                break;
            case "AZ-DM":
                this.activeStep = "RingOutTest";
                break;
            case "Pulser":
                this.activeStep = "DriverInspection";
                break;
            case "At Bit":
                this.activeStep = "AtBit";
                break;
            case "Bottom End":
                this.activeStep = "IncomingTearDownChecks";
                break;
            default:
                this.activeStep = "FaultDiagnosis";
                break;
        }
    }
    private onClickStep(item: any) {
        this.activeStep = item.component;
    }
}
</script>
<style lang="scss" scoped>
.step-container {
    display: flex;
    .step {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 50px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        border: 1px solid #ccc;
        user-select: none;
        cursor: pointer;
        &.active {
            background: rgba(67, 86, 152, 0.8);
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
