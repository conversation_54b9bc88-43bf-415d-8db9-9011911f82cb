<template>
    <div>
        <el-table :data="inTableData" border>
            <el-table-column prop="name" label="拆卸检查">
                <template slot-scope="scope">
                    <span>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.value" style="width:100%" class="tail-input"></el-input>
                    </template>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-table :data="outTableData" border>
            <el-table-column prop="name" label="出厂检查">
                <template slot-scope="scope">
                    <span>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.value" style="width:100%" class="tail-input"></el-input>
                    </template>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "BottomEnd" })
export default class extends Vue {
    @Prop({default:()=>false})isEdit !:Boolean
    @Prop({default:()=>{inCheckList:[];outCheckList:[]}}) checkData !:any
    get inTableData(){
        // if(!this.checkData.inCheckList){
        //     this.$set(this.checkData, "inCheckList", [])
        // }
        return this.checkData.inCheckList
    }
    get outTableData(){
        // if(!this.checkData.outCheckList){
        //     this.$set(this.checkData, "outCheckList", [])
        // }
        return this.checkData.outCheckList
    }
    private addRowItem = {name:'',value:'',in:'',out:''}
    private deleteCell(scope:any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope:any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem,
            });
        } else {
            this.tableData.unshift({
                ...this.addRowItem,
            });
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
