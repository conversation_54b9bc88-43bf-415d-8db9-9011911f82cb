<template>
    <div>
        <el-table :data="tableData" border>
            <el-table-column prop="name" label="拆卸检查">
                <template slot-scope="scope">
                    <span>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.value" style="width:100%" class="tail-input"></el-input>
                    </template>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "IncomingTearDownChecks" })
export default class extends Vue {
    @Prop({default:()=>false})isEdit !:Boolean
    @Prop({default:()=>{inCheckList:[]}}) checkData !:any
    get tableData(){
        // if(!this.checkData.inCheckList){
        //     this.$set(this.checkData, "inCheckList", [])
        // }
        return this.checkData.inCheckList
    }
}
</script>
<style lang="scss" scoped>
</style>
