<template>
    <div>
        <el-table :data="tableData" border>
            <el-table-column prop="name" label="出厂检查">
                <template slot-scope="scope">
                    <span>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.value" style="width:100%" class="tail-input"></el-input>
                    </template>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "OutgoingChecks" })
export default class extends Vue {
    @Prop({default:()=>false})isEdit !:Boolean
    @Prop({default:()=>{outCheckList:[]}}) checkData !:any
    get tableData(){
        // if(!this.checkData.outCheckList){
        //     this.$set(this.checkData, "outCheckList", [])
        // }
        console.log(this.checkData.outCheckList)
        return this.checkData.outCheckList
    }
}
</script>
<style lang="scss" scoped>
</style>
