<template>
    <div>
        <el-table :data="tableData" border :span-method="arraySpanMethod">
            <el-table-column prop="name" label="Checks">
                <template slot-scope="scope">
                    <span >{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="Nominal正常值">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.value" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="in" label="Incoming">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.in" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.in}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="out" label="Outgoing">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.out" style="width:100%" class="tail-input"></el-input>
                    </template>
                    <span v-else>{{scope.row.out}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Inject, Prop, Vue } from "vue-property-decorator";
@Component({ name: "AtBit" })
export default class extends Vue {
    @Prop({default:()=>{atBitChecksList:[]}}) checkData !:any
    get tableData(){
        // if(!this.checkData.atBitChecksList){
        //     this.$set(this.checkData, "atBitChecksList", [])
        // }
        return this.checkData.atBitChecksList
    }
    @Prop({default:()=>false})isEdit !:Boolean
    private arraySpanMethod({ row, column, rowIndex, columnIndex }:any) {
        if (rowIndex>=3 &&rowIndex<= 11 &&columnIndex>=1) {
            return [1, 3];          
        }
      }

}
</script>
<style lang="scss" scoped>
</style>
