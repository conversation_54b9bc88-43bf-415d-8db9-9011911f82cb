<template>
    <div>
        <el-button @click="addRow()" type="primary" style="margin-bottom:10px" v-if="isEdit">首行插入</el-button>
        <el-table :data="tableData" border>
            <el-table-column prop="name" label="Oilfill Section Inspection">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.name" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="in" label="In">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.in" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.in}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="out" label="Out">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-input v-model="scope.row.out" style="width:100%" class="tail-input"></el-input>
                            <i 
                                @click.stop="deleteCell(scope)"
                                class="el-icon-close hover-icon delete"
                            ></i>
                            <i
                                @click.stop="addRow(scope)"
                                class="el-icon-plus hover-icon add"
                            ></i>
                    </template>
                    <span v-else>{{scope.row.out}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "OilFillSectionInspection" })
export default class extends Vue {
    @Prop({default:()=>false})isEdit !:Boolean
    @Prop({default:()=>{oilfillSectionInspectionList:[]}}) checkData !:any
    get tableData(){
        // if(!this.checkData.oilfillSectionInspectionList){
        //     this.$set(this.checkData, "oilfillSectionInspectionList", [])
        // }
        return this.checkData.oilfillSectionInspectionList
    }
    private addRowItem = {name:'',value:'',in:'',out:''}
    private deleteCell(scope:any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope:any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem,
            });
        } else {
            this.tableData.unshift({
                ...this.addRowItem,
            });
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
