<template>
    <div style="margin-top: 20px">
        <div class="tool-card-title" style="position:relative;">
            <span id="file-manage">文件管理</span>
            <el-button 
                v-if="isEdit" 
                style="position:absolute; top:-5px;left:120px" 
                type="text" 
                :loading="isFileUploading" 
                @click="onClickUpload"
            >
                上传
            </el-button>
        </div> 
        <div class="file-list">
            <div class="file-item" v-for="item in fileList" :key="item.massId">
                <span :title="item.massFileName" class="file-title">
                    {{ item.massFileName }}
                </span>
                <i @click="onPreviewFile(item.massId)" class="el-icon-view oper-icon"></i>
                <a :href="item.massFilePath">
                    <i class="el-icon-download oper-icon"></i>
                </a>
                <i v-if="isEdit" @click="onDeleteMwdFile(item.massId)" class="el-icon-delete oper-icon"></i>
            </div>
        </div>
        <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadMwdFile"
            >
            <div ref="ghostFileUploader" style="width:0;height:0"></div>
        </el-upload>
    </div>
</template>
<script lang="ts">
import { apiUploadMwdMassFile, apiGetMwdMassFileList, apiDeleteMwdMassFile, apiPreviewFile } from "@/api/mwd";
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({})
export default class extends Vue {
    private isFileUploading = false
    private fileList:any[] = []
    private mwdId: any = "";
    @Prop({ default: () => false }) isEdit!: Boolean;
    private mounted(){
        this.mwdId = this.$route.query.mwdId || "";
        if(this.mwdId){
            this.getMwdFileList()
        }
        document.addEventListener('click', this.removeHighlight)
    }
    private removeHighlight(e){
        const fileManageDOM = document.getElementById('file-manage') as HTMLSpanElement;
        const classList = fileManageDOM.classList;
        if(classList.contains('highlighted')&&e.target.id!='dataAnalysisEditor'){
            fileManageDOM.classList.remove("highlighted");
        }
    }
    private onDeleteMwdFile(massId){
        this.$confirm("确认删除该文件?", "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(()=>{
            const form = new FormData();
            form.append('type', 'MASSID');
            form.append('idList', [massId].toString())
            apiDeleteMwdMassFile(form).then(()=>{
                this.getMwdFileList()
                this.$message.success('操作成功！')
            })
        })
    }
    private onUploadMwdFile(file){
        this.isFileUploading  = true
        const form = new FormData();
        form.append('mwdId', String(this.mwdId));
        form.append('file', file);
        apiUploadMwdMassFile(form).then(()=>{
            this.getMwdFileList();
            this.$message.success('操作成功！')
        }).finally(()=>{
            this.isFileUploading = false
        })
    }
    private getMwdFileList(){
        apiGetMwdMassFileList({mwdId: this.mwdId},{current: 1, size: 9999}).then(res=>{
            this.fileList = res.data?.data?.records || [];
        })
    }
    private onClickUpload(){
        (this.$refs.ghostFileUploader as any).click();
    }
    private onPreviewFile(massId){
        apiPreviewFile({massId}).then(res=>{
            const previewPath = res.data.data?.previewPath;
            if(previewPath){
                window.open(previewPath);
            }
        })
    }
    private beforeDestroy(){
        document.removeEventListener('click', this.removeHighlight)
    }
}

</script>
<style lang="scss" scoped>
.file-list{
    
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 380px;
        margin-bottom: 10px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>