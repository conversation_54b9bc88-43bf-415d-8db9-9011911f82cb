<template>
    <el-dialog :visible.sync="isDialogVisible" title="创建工单" width="1200px">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="工单号" prop="mwdNumber" align="center">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.mwdNumber"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="仪器类型" prop="deviceTypeStr" align="center" width="120px"></el-table-column>
            <el-table-column label="序列号" prop="serialNumber" align="center" width="120px"></el-table-column>
            <el-table-column label="品名" prop="invName" align="center" width="120px"></el-table-column>
            <el-table-column label="所有者" prop="owner" align="center" width="100px"></el-table-column>
            <el-table-column label="返回原因" prop="returnReason" align="center">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.returnReason"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="notes" align="center">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.notes"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onConfirm">
                创建工单
            </el-button>
        </span>
    </el-dialog>
</template>
<script>
import { apiCreateMwdOrderByOutbound } from '@/api/tool-mantain';

export default {
    name: 'MaterialOrderDialog',
    data() {
        return {
            tableData: [],
            isDialogVisible: false,
        }
    },
    methods: {
        async showDialog(rows){
            this.tableData = rows.map(row=>({...row, returnReason: row.reason}));
            this.isDialogVisible = true;
        },
        onConfirm(){
            apiCreateMwdOrderByOutbound({batchList: this.tableData}).then(() => {
                this.$message.success('操作成功！');
                this.isDialogVisible = false;
                this.$emit('success');
            });
        },
    }
}
</script>