<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="material-dialog"
        :close-on-click-modal="false"
        title="领取仪器"
        width="1500px"
        top="6vh"
    >
        <div style="display: flex;width: 100%;">
            <div style="border: 2px solid #ccc; padding: 8px;">
                <div style="text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 10px;">待选区</div>
                <el-form inline ref="searchForm" :model="searchForm">
                    <el-form-item label="仪器类型: " label-width="80px">
                        <el-select
                            clearable
                            style="width: 100%"
                            v-model="searchForm.deviceType"
                            @change="getMaterialList(true)"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in deviceTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="序列号: " label-width="80px">
                        <el-input v-model="searchForm.serialNumber" @change="getMaterialList(true)"></el-input>
                    </el-form-item>
                    <el-form-item label="库存时间大于(天): " label-width="140px">
                        <el-input v-model="searchForm.greaterThan" @change="getMaterialList(true)"></el-input>
                    </el-form-item>
                </el-form>
                <el-table :data="tableData" v-loading="tableLoading" :header-cell-style="commmonTableHeaderCellStyle">
                    <el-table-column label="品名" prop="invName" width="120px"></el-table-column>
                    <el-table-column label="序列号" prop="serialNumber" align="center" width="120px"></el-table-column>
                    <el-table-column label="状态" prop="status" align="center">
                        <template slot-scope="scope">
                            <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="风险类型" prop="risktype" align="center" width="100px">
                        <template slot-scope="scope">
                            <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="库存时间(天)" prop="loadInDays" align="center" width="150px"></el-table-column>
                    <el-table-column label="操作" align="center" width="60px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="onSelect(scope.row)" :disabled="disableSelection(scope.row)">选择</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top: 20px"
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="pageSize"
                    @current-change="onCurrentChange"
                    :current-page="currentPage"
                ></el-pagination>
            </div>
            <div style="flex: 1;margin-left: 20px;border: 2px solid #ccc; padding: 8px;">
                <div style="text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 10px;">已选择</div>
                <el-form :model="reasonForm">
                    <el-form-item label="领取原因: " label-width="80px">
                        <el-input v-model="reasonForm.reason" placeholder="在选择仪器前，请先填写领取原因"></el-input>
                    </el-form-item>
                </el-form>
                <el-table :data="selectedList" :header-cell-style="commmonTableHeaderCellStyle" height="600">
                    <el-table-column label="品名" prop="invName" width="120px"></el-table-column>
                    <el-table-column label="序列号" prop="serialNumber" align="center" width="120px"></el-table-column>
                    <el-table-column label="领取原因" prop="reason" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.reason"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="60px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="onDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onConfirm">
                确 定
            </el-button>
        </span>
    </el-dialog>
</template>
<script>
import { apiCreateOutboundOrder, apiGetFreeMaterialList } from '@/api/warehouse';
import getDict from '@/utils/getDict';

export default {
    name: 'MaterialDialog',
    props: {
        activePanelName: String,
        deviceType: Number
    },
    data() {
        return {
            searchForm: {
                deviceType: '',
                serialNumber: '',
                greaterThan: '',
                isInProcess: 0
            },
            reasonForm: {
                reason: ''
            },
            selectedList:[],
            deviceTypeList: [],
            tableData: [],
            tableLoading: false,
            total: 0,
            pageSize: 10,
            currentPage: 1,
            isDialogVisible: false,
        }
    },
    methods: {
        async showDialog(){
            [this.deviceTypeList] = await getDict([17]);
            this.isDialogVisible = true;
            this.selectedList = [];
            this.getMaterialList();
        },
        getMaterialList(bool=false) {
            if(bool){
                this.currentPage = 1;
            }
            this.tableLoading = true;
            return apiGetFreeMaterialList(this.searchForm, {current: this.currentPage, size: this.pageSize}).then(res=>{
                const data = res.data.data || {};
                this.total = data.total;
                this.tableData = data.records || [];
            }).finally(()=>{
                this.tableLoading = false;
            })
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getMaterialList();
        },
        onConfirm(){
            if(!this.selectedList.length){
                this.$message.error('请先选择仪器！');
                return;
            }
            apiCreateOutboundOrder({orderList: this.selectedList}).then(()=>{
                this.$message.success('操作成功！');
                this.isDialogVisible = false;
                this.$emit('success');
            })
        },
        onSelect(row){
            if(this.selectedList.find(item=>item.deviceId == row.deviceId)){
                this.$message.error('该仪器已选择！');
                return;
            }
            this.selectedList.push({...row, reason: this.reasonForm.reason || null});
        },
        onDelete(row){
            this.selectedList = this.selectedList.filter(item=>item.deviceId!==row.deviceId);
        },
        disableSelection(row){
            return this.selectedList.find(item=>item.deviceId == row.deviceId);
        }
    }
}
</script>
<style lang="scss">
.material-dialog{
    .el-dialog__body {
        padding: 10px 16px !important;
    }
}
</style>