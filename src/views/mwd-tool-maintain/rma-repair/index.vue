<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">核心部件返修单</div>
                <el-button @click="onCreate" type="primary" v-if="p_RmaRepairAdd">创建返修单</el-button>
            </div>
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
                style="margin-top: 10px;"
            >
                <el-table-column
                    label="RMA工单号"
                    prop="repairCode"
                    fixed="left"
                    width="200"
                ></el-table-column>
                <el-table-column
                    label="品名"
                    prop="invName"
                    align="center"
                    fixed="left"
                    width="140"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="返修人"
                    prop="sendUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="返修时间"
                    prop="sendDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="接收状态"
                    prop="received"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            v-if="scope.row.received == 1"
                            type="success"
                            effect="dark"
                        >
                            已接收
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="danger"
                            v-else
                        >
                            未接收
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收人"
                    prop="receiveUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receiveDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="MWD工单号"
                    prop="mwdNumber"
                    align="center"
                    width="140"
                >
                    <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                v-if="p_MwdInfoView"
                            >
                                {{ row.mwdNumber }}
                            </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="本次入井时间"
                    prop="currentInWellHour"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总入井时间"
                    prop="totalInWellHrs"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="本次最高温度"
                    prop="currentMaxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总最高温度"
                    prop="maxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="返修原因"
                    prop="returnReason"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="备注"
                    prop="notes"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="140"
                    align="center"
                    fixed="right"
                    v-if="p_RmaRepairInfo || p_RmaRepairDelete"
                >
                    <template slot-scope="scope">
                        <el-button type="text" @click="onDetail(scope.row)" v-if="p_RmaRepairInfo">
                            详情
                        </el-button>
                        <el-button type="text" @click="onFile(scope.row)" v-if="p_RmaRepairInfo">
                            附件
                        </el-button>
                        <el-button type="text" @click="onDelete(scope.row)" v-if="p_RmaRepairDelete">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <RepairDialog @getRepairList="getRepairList" ref="RepairDialog" />
        <RepairFileDialog ref="fileDialogRef" />
    </div>
</template>

<script>
import { apiGetRmaRepairList, apiDeleteRmaRepair, apiGetRmaRepairDataByCore } from "@/api/rma";
import RepairDialog from "./repairDialog.vue";
import RepairFileDialog from "./fileDialog.vue";
import { RmaRepairDataByCoreFormKey } from "@/utils/constant.rma";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    name: "RepairList",
    components: { RepairDialog, RepairFileDialog },
    data() {
        return {
            orderBy: "createTime",
            orderType: "desc",
            isLoading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {
            },
        };
    },
    computed: {
        p_RmaRepairAdd(){
            return this.$checkBtnPermission('sys:mwd:core:repair:add')
        },
        p_RmaRepairInfo(){
            return this.$checkBtnPermission('sys:mwd:core:repair:info')
        },
        p_RmaRepairDelete(){
            return this.$checkBtnPermission('sys:mwd:core:repair:delete')
        },
        p_MwdInfoView(){
            return this.$checkBtnPermission('sys:mwd:info');
        }
    },
    async mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.getRepairList();
        const isFromCore = this.$route.query.core === 'true';
        if(isFromCore){
            const form = sessionStorage.getItem(RmaRepairDataByCoreFormKey);
            if(form){
                apiGetRmaRepairDataByCore(JSON.parse(form)).then(res=>{
                    const data = res.data.data || {};
                    this.$refs.RepairDialog.showDialog('ADD', data);
                });
            }

        }
    },
    methods: {
        onCreate(){
            this.$router.push(`/mwd-tool-maintain/rma-repair/detail`);
        },
        getRepairList() {
            apiGetRmaRepairList(
                {
                    ...this.searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },
        onFile(row){
            const mode = row.received === 1 ? "READ" : "EDIT";
            this.$refs.fileDialogRef.showDialog(mode, row.repairId)
        },
        async onDetail(row) {
            this.$router.push(`/mwd-tool-maintain/rma-repair/detail?id=${row.repairId}`);
            // this.isLoading = true;
            // await this.$refs.RepairDialog.showDialog("DETAIL", row);
            // this.isLoading = false;
        },

        onDelete(row) {
            this.$confirm("确认删除吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiDeleteRmaRepair({repairId: row.repairId})
                    .then(() => {
                        this.$message.success("操作成功！");
                        this.currentPage = 1;
                        this.getRepairList();
                    })
                    .catch((err) => {});
            });
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getRepairList();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.getRepairList();
        },
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
