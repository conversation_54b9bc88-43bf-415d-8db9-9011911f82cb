<template>
    <el-dialog title="附件" :custom-class="mode==='EDIT' ? 'rma-file-edit-dialog': ''" :visible.sync="isDialogVisible" width="420px">
        <FileManage v-if="isDialogVisible" empty-text="暂无附件" :mode="mode" ref="fileManageRef" bussinessType="RMA_REPAIR" :bussinessId="repairId" />
        <template #footer v-if="mode==='EDIT'">
            <el-button @click="onClickUpload" type="text">上传附件</el-button>
        </template>
    </el-dialog>
</template>
<script>
import FileManage from "@/components/FileManage/index.vue"
export default {
    name: 'RmaRepairFileDialog',
    components: { FileManage },
    data() {
        return {
            isDialogVisible: false,
            repairId: '',
            mode: 'READ'
        }
    },
    methods: {
        showDialog(mode, repairId){
            this.mode = mode;
            this.repairId = repairId;
            this.isDialogVisible = true;
        },
        onClickUpload(){
            this.$refs.fileManageRef.onUpload();
        }
    }
}
</script>

<style lang="scss">
.rma-file-edit-dialog {
    .el-dialog__body{
        padding-top: 30px;
        padding-bottom: 0px;
    }
}
</style>