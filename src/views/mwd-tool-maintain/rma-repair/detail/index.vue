<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isPageLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">核心部件返修单详情</div>
                <div v-if="!isReceived">
                    <template v-if="!isEdit">
                        <el-button type="primary" @click="onEdit" key="9527">编辑</el-button>
                        <el-button type="primary" @click="onOpenEmailDialog" key="5173">邮件通知</el-button>
                    </template>
                    <template v-else>
                        <el-button type="primary" :loading="isBtnLoading" @click="onUpdate">{{`${isAdd ? '创建' : '保存'}`}}</el-button>
                        <el-button @click="onCancel">取消</el-button>
                    </template>
                </div>
            </div>
            <el-form ref="detailForm" :rules="formRules" :model="detailForm" label-width="80px" style="margin-top: 10px">
                <div class="rma-item-container" style="margin-bottom: 10px">
                    <div class="item-title">
                        基础信息
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="RMA返修单号: " prop="repairCode" label-width="120px">
                                <el-input :disabled="!isAdd" v-model="detailForm.repairCode"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="井号: ">
                                <FuzzySelect :disabled="!isEdit" placeholder="" type="WELL_NUMBER" v-model="detailForm.wellNumber"></FuzzySelect>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业号: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.jobNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="现场联系人: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.contactUser"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="部件品名: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.invName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="部件序列号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.serialNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="固件版本号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.versionNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="现场联系方式: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.contactNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="母件品名: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.parentInvName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="母件序列号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.parentSerialNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="检修人: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.repairUser"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="总入井时间: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.totalInWellHrs"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="本次入井时间: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.currentInWellHour"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="本次最高温度: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.currentMaxBht"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="总最高温度: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.maxBht"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="现场返修原因: " label-width="110px">
                                <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.returnReason"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="备注: ">
                                <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.notes"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        功能检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                常温功能检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.normalTemperatureFunctionCheck" :class="getCheckClass(detailForm.normalTemperatureFunctionCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                高温测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.highTemperatureTest" :class="getCheckClass(detailForm.highTemperatureTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                振动测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.vibrationTest" :class="getCheckClass(detailForm.vibrationTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                旋转测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.rotationTest" :class="getCheckClass(detailForm.rotationTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                短传性能测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.shortTransmissionPerformanceTest" :class="getCheckClass(detailForm.shortTransmissionPerformanceTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                LOG数据存储测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.logDataStorageTest" :class="getCheckClass(detailForm.logDataStorageTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                
                <div class="rma-item-container">
                    <div class="item-title">
                        机械检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                绝缘检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.insulationCheck" :class="getCheckClass(detailForm.insulationCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                本体检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.bodyCheck" :class="getCheckClass(detailForm.bodyCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                盖板检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.coverCheck" :class="getCheckClass(detailForm.coverCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        电子/线路检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                本体线路ringout测量
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.bodyLineRingOutMeasurement" :class="getCheckClass(detailForm.bodyLineRingOutMeasurement)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                线路固封情况检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.lineSealingConditionCheck" :class="getCheckClass(detailForm.lineSealingConditionCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                接头状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.connectorStatusCheck" :class="getCheckClass(detailForm.connectorStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                EC物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.ecPhysicalStatusCheck" :class="getCheckClass(detailForm.ecPhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                伽马探头物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.gammaProbePhysicalStatusCheck" :class="getCheckClass(detailForm.gammaProbePhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                电池物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.batteryPhysicalStatusCheck" :class="getCheckClass(detailForm.batteryPhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        车间诊断初步结论
                    </div>
                    <el-form-item label-width="0">
                        <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.workshopPreliminaryDiagnosisConclusion"></el-input>
                    </el-form-item>
                </div>
                <div class="rma-item-container" v-if="!isAdd">
                    <div class="item-title">
                        文件上传 <el-button type='text' @click="onUploadFile" v-if="isEdit">上传</el-button>
                    </div>
                    <FileManage empty-text="暂无附件" :mode="isEdit?'EDIT':'READ'" ref="fileManageRef" bussinessType="RMA_REPAIR" :bussinessId="repairId" />
                </div>
            </el-form>
            <EmailDialog email-business-type="RMA_REPAIR" :email-business-id="repairId" ref="emailDialogRef"></EmailDialog>
        </div>
    </div>
</template>
<script>
import { apiGetRmaRepairInfo, apiUpdateRmaRepair, apiAddRmaRepair, apiCreateRmaWorkOrderByRepair } from "@/api/rma";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import FileManage from "@/components/FileManage/index.vue";
import EmailDialog from "@/components/EmailDialog/index.vue";
export default {
    components: { FuzzySelect, FileManage, EmailDialog },
    data(){
        return {
            repairId: null,
            isBtnLoading: false,
            detailDialogVisible : false,
            detailForm: {},
            selectedRepairDetail: [],
            isEdit: false,
            checkList: [
                {value: -1, label: 'FAIL', class: 'FAIL'},
                {value: 0, label: 'N/A', class: 'NA'},
                {value: 1, label: 'PASS', class: 'PASS'},
            ],
            formRules: {
                repairCode: [{required: true, message: '请填写返修单号'}]
            },
            isPageLoading: false
        }
    },
    computed: {
        isAdd(){
            return !this.repairId
        },
        p_RmaRepairUpdate(){
            return this.$checkBtnPermission('sys:mwd:core:repair:update')
        },
        isReceived(){
            return this.detailForm.received === 1
        },
    },
    mounted(){
        this.repairId = this.$route.query.id;
        if(this.repairId){
            this.getRepairInfo();
        }else{
            this.isEdit = true;
        }
    },
    methods: {
        getRepairInfo(){
            return new Promise((resolve)=>{
                this.isPageLoading = true;
                apiGetRmaRepairInfo({repairId: this.repairId, needExcel: true}).then(res=>{
                    this.detailForm = res.data.data || {};
                    resolve(this.detailForm);
                }).finally(()=>{
                    this.isPageLoading = false;
                })
            })
        },
        onCancelEdit(){
            this.isEdit = false;
            this.getRepairInfo();
        },
        getCheckClass(value){
            return this.checkList.find(item=>item.value === value)?.label || ''
        },
        onConfirmRepairForm() {
            if(this.isAdd){
                this.onOpenEmailDialog()
            }else{
                this.isBtnLoading = true;
                apiUpdateRmaRepair(this.detailForm).then(res=>{
                    this.$message.success('操作成功!');
                    this.getRepairInfo();
                    this.$emit('getRepairList');
                    this.isEdit = false;
                }).finally(()=>{
                    this.isBtnLoading = false;
                });
            }
        },
        onCreateRmaWorkOrder(){
            this.isBtnLoading = true;
            apiCreateRmaWorkOrderByRepair({repairId: this.repairId}).then(res=>{
                const rmaId = res?.data?.data?.rmaId;
                if(rmaId){
                    this.$message.success('操作成功！');
                    this.$router.push(`/rma/maintain?rmaId=${rmaId}`)
                }
            })
        },
        onOpenEmailDialog(){
            this.$refs.emailDialogRef.showDialog([...this.detailForm.attachments] || [])
        },
        onEdit(){
            this.isEdit = true;
        },
        async onUpdate(){
            if(this.isAdd){
                const valid = await this.$refs.detailForm.validate();
                if(valid){
                    this.isBtnLoading = true;
                    apiAddRmaRepair(this.detailForm).then(res=>{
                        this.$message.success('操作成功!');
                        const repairId = res?.data?.data?.repairId;
                        this.$router.replace(`/redirect/mwd-tool-maintain/rma-repair/detail?id=${repairId}`).catch(err => {
                            console.warn(err);
                        });
                    }).finally(()=>{
                        this.isBtnLoading = false;
                    });
                }    
            }else{
                const valid = await this.$refs.detailForm.validate();
                if(valid){
                    this.isBtnLoading = true;
                    apiUpdateRmaRepair(this.detailForm).then(async ()=>{
                        this.isEdit = false;
                        await this.getRepairInfo();
                        this.$message.success('操作成功!');
                    }).finally(()=>{
                        this.isBtnLoading = false;
                    });
                }  
            }
        },
        onCancel(){
            if(this.isAdd){
                this.$router.back();
            }else{
                this.isEdit = false;
                this.getRepairInfo();
            }
        },
        onUploadFile(){
            this.$refs.fileManageRef.onUpload();
        }
    }
}
</script>

<style lang="scss" scoped>
.rma-repair-dialog .el-dialog__body{
    padding-top: 10px !important
}
.rma-item-container{
    margin-bottom: 26px;
    .item-title{
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 18px;
        &::before {
            display: inline-block;
            width:6px;
            height: 20px;
            vertical-align: sub;
            background: rgb(67, 86, 152);
            margin-right: 6px;
            content: "";
        }
    }
    .sub-item-title{
        font-size: 14px;
        // font-weight: bold;
        margin-left: 18px;
        margin-bottom: 8px;
        // &::before {
        //     display: inline-block;
        //     width: 10px;
        //     height: 10px;
        //     background: rgb(67, 86, 152);
        //     margin-right: 6px;
        //     margin-left: -2px;
        //     border-radius: 999999%;
        //     content: "";
        // }
    }
    .check-select {
        width: 100px;
        margin-left: 16px;
        &.PASS :deep() .el-input__inner{
            background: #67c23a;
            color: white;
        }
        &.NA :deep() .el-input__inner{

        }
        &.FAIL :deep() .el-input__inner{
            background: #ff4949;
            color: white;
        }

    }
}

</style>