<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>工单文件列表</span>
            </div>
            
            <el-button type="primary" v-if="p_FileDownload" @click="onBatchDownload" style="float: right">
                批量下载
            </el-button>
            <el-form :model="searchForm" label-width="80px" style="width: calc(100% - 200px)">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="文件名">
                            <el-input @change="initData(true)" v-model="searchForm.massFileName" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="工单号">
                            <el-input @change="initData(true)" v-model="searchForm.mwdNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="井号">
                            <el-input @change="initData(true)" v-model="searchForm.wellNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业号">
                            <el-input @change="initData(true)" v-model="searchForm.mwdNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="工单状态">
                            <el-select
                                style="width:calc(100%)"
                                v-model="searchForm.finish"
                                @change="initData(true)"
                                clearable
                            >
                                <el-option
                                    label="完成"
                                    :value="1"
                                ></el-option>
                                <el-option
                                    label="未完成"
                                    :value="0"
                                ></el-option>
                                <el-option
                                    label="滞留"
                                    :value="-1"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="文件后缀">
                            <el-select style="width:calc(100%)" v-model="searchForm.massFileSuffix" @change="initData(true)" clearable>
                                <el-option v-for="item in fileTypeList" :key="item" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 10px"
                :header-cell-style="commmonTableHeaderCellStyle"
                @selection-change="handleSelectionChange"
                @sort-change="handleSortChange"
            >
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column label="文件名" prop="massFileName">
                </el-table-column>
                <el-table-column label="工单号" prop="mwdNumber">
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                            v-if="p_MwdInfoView"
                        >
                            {{ row.mwdNumber }}
                        </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="工单状态" prop="finish" sortable="custom">
                    <template slot-scope="scope">
                        <CustomTag tagType="FINISH" :tagValue="scope.row.finish" />
                    </template>
                </el-table-column>
                <el-table-column label="井号" prop="wellNumber">
                </el-table-column>
                <el-table-column label="作业号" prop="jobNumber">
                </el-table-column>
                <el-table-column label="上传时间" prop="fileCreateTime" sortable="custom">
                    <template slot-scope="scope">
                        {{new Date(scope.row.fileCreateTime).Format('yyyy-MM-dd hh:mm:ss')}}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100" v-if="p_FileDownload">
                    <template slot-scope="scope">
                        <!-- <router-link
                            tag="el-button"
                            type="text"
                            style="margin-right: 10px"
                            :to="`${scope.row.massFilePath}`"
                            v-if="p_FileDownload"
                        >
                            下载
                        </router-link> -->
                        <a :href="`${scope.row.massFilePath}`">
                            <span style="color:blue;">
                                下载
                            </span>
                        </a>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetMaintainFileList, apiDownloadMaintainFile, apiGetMaintainFileTypeList } from "@/api/mwd";
import { Form as ElForm } from "element-ui/types/element-ui";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({})
export default class extends Vue {
    private tableData: any[] = [];
    private total = 1;
    private currentPage = 1;
    private searchForm:any = {
        massFileType: "MWD_WORK_ORDER_FILE",
        wellNumber:"",
        jobNumber:""
    }
    private fileTypeList:any[] = [];
    private selectedData:any[] = [];
    get p_FileDownload(){
        return this.$checkBtnPermission('sys:mwd:file:download');
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    async activated() {
        this.searchForm.wellNumber = this.$route.query.wellNumber;
        apiGetMaintainFileTypeList({}).then(res=>{
            this.fileTypeList = res.data.data;
        })
        await this.initData();
    }
    handleSelect(){
        this.initData()
    }
    async initData(bool=false) {
        if(bool){
            this.currentPage = 1;
        }
        apiGetMaintainFileList({
                ...this.searchForm,
                orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
            },
            { current: this.currentPage, size: 10 }).then(res=>{
            this.tableData = res.data?.data?.records || [];
            this.total = res.data.data.total;
        })
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    private onAddWell() {
        this.operType = "ADD";
        this.form = {
            id: undefined,
            wellNumber: "",
            wellCategory: undefined,
            wellType: undefined,
            blocks: "",
            province: "",
            description: "",
        };
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }

    private onEdit(scope: any) {
        this.operType = "EDIT";
        this.form = Object.assign(this.form, scope.row);
        this.showDialog = true;
    }
    private onView(scope:any){
        this.$router.push(`/daily/detail?jobid=${scope.row.jobId}`)

    }
    private onBatchDownload(){
        if(!this.selectedData.length){
            this.$message.error("请选择要下载的文件");
            return;
        }
        this.$confirm("确认批量下载当前选择的文件？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(()=>{
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            const form = new FormData();
            form.append('massIdList', this.selectedData.map(item=>item.massId).toString())
            apiDownloadMaintainFile(form).then(res => {
                const blob = new Blob([res.data], {
                    type: 'application/word;charset=UTF-8',
                });
                const fileName = `批量下载工单文件.zip`;
                const link = document.createElement('a');
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                //释放内存
                link.remove(); // 下载完成移除元素
                window.URL.revokeObjectURL(link.href);
            }).finally(() => {
                loading.close();
            });
        })
    }
    private handleSelectionChange(val) {
        this.selectedData = val;
    }
    private handleSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.initData(true);
    }
}
</script>
