<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="返修单"
        width="80%"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="作业号: ">
                        <span>{{ detailForm.jobNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="井号: ">
                        <span>{{ detailForm.wellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱: ">
                        <span>{{ detailForm.kitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="返修日期: ">
                        <span>{{ detailForm.returnDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="客户名称: ">
                        <span>{{ detailForm.customerName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人: ">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: ">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.repairDetailList"
            style="margin-bottom: 20px"
            :header-cell-style="commmonTableHeaderCellStyle"
            @selection-change="handleSelectionChange">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column label="工单号" width="170">
                <template slot-scope="scope">
                    <el-input
                        style="width: 100%"
                        v-model="scope.row.mwdNumber"
                    >
                    </el-input>
                </template> 
            </el-table-column>
            <el-table-column label="仪器类型" prop="deviceType" width="170">
                <template slot-scope="scope">
                    <FuzzySelect placeholder="" clearable type="DEVICE_TYPE" v-model="scope.row.deviceType"></FuzzySelect>
                </template>
            </el-table-column>
            <el-table-column label="仪器名称" prop="invName">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="170">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.serialNumber"
                        @change="onSerialNumberChange(row)"
                    >
                    </el-input>
                    <div v-if="!row.valid" style="color:red;font-size:10px">无效序列号</div>
                </template>
            </el-table-column>
            <el-table-column label="入井时间" prop="inWellHour">
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
            </el-table-column>
            <el-table-column label="循环温度" prop="circulateBht">
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht"> </el-table-column>

            <el-table-column label="趟次" prop="run"> </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
            </el-table-column>
            <el-table-column label="是否存在故障" prop="failureStatus">
                <template slot-scope="{row}">
                    <CustomTag v-if="row.failureStatus==1" tagType="NEGATIVE_TRUE_FALSE" :tagValue="true" />
                    <CustomTag v-if="row.failureStatus==0" tagType="NEGATIVE_TRUE_FALSE" :tagValue="false" />
                </template>
            </el-table-column>
            <el-table-column label="故障描述" prop="failureDescription">
            </el-table-column>
            <el-table-column label="是否有震动超标" prop="vssStatus">
            </el-table-column>
            <el-table-column label="备注" prop="note"> </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onBatchCreate">
                <span>{{ selectedRepairDetail.length>1?'批量':'' }}创建维修工单</span> 
            </el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiGetRepairInfo } from "@/api/repair";
import { ElForm } from "element-ui/types/form";
import { apiBatchAddMwdWorkOrder, apiCheckSerialNumber } from "@/api/tool-mantain";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import CustomTag from "@/components/CustomTag";
interface IForm {
    jobNumber?: string;
    wellNumber?: string;
    kitNumber?: string;
    returnDate?: string;
    customerName?: string;
    contactUser?: string;
    contactNumber?: string;
    repairDetailList: IRepairDetail[];
    toolType: string;
    repairCode: string
}

interface IRepairDetail {
    serialNumber?: number | string;
    inWellHour?: number;
    circulateBht?: number;
    maxBht?: number;
    circulateHrs?: number;
    run?: number;
    returnReason?: string;
    note?: string;
    valid?: boolean;
}
@Component({components:{FuzzySelect}})
export default class extends Vue {
    @Prop({ default: () => [] }) deviceTypeList!: any[];
    private detailDialogVisible = false;
    private isLoading = false;
    private detailForm: IForm = this.getInitForm();
    private selectedRepairDetail:any[] = [];
    private getInitForm(): IForm {
        return {
            jobNumber: "",
            wellNumber: "",
            contactUser: "",
            contactNumber: "",
            kitNumber: "",
            returnDate: "",
            customerName: "",
            repairDetailList: [],
            toolType: "MWD",
            repairCode:''
        };
    }
    private async onSerialNumberChange(row){
        const serialNumber = row.serialNumber;
        if(serialNumber){
           row.valid = await this.onValidateSN(row.serialNumber) 
        }else{
            row.valid = false;
        }
    }
    private showDialog(type: string, repairId: number) {
        return apiGetRepairInfo({ repairId: repairId }).then(async(res) => {
            this.detailForm = res.data.data;
            this.detailForm.repairDetailList = await Promise.all(this.detailForm.repairDetailList.map(async item=>{
                let valid = true
                if(item.serialNumber){
                    valid = await this.onValidateSN(item.serialNumber)
                }
                item.valid = valid
                return {...item, valid}
            }))
            this.detailDialogVisible = true;
        });
        
    }
    private handleSelectionChange(val){
        this.selectedRepairDetail = val
    }
    private async onValidateSN(serialNumber){
        const res = await apiCheckSerialNumber({ serialNumber });
        const code = res.data.data;
        const valid = code == 1 || code == 0;
        return valid
    }
    private onBatchCreate() {
        if(!this.selectedRepairDetail?.length){
            this.$message.error('请选择要维修的仪器！')
            return
        }
        if(this.selectedRepairDetail?.length==1){
            const item = this.selectedRepairDetail[0];
            this.$router.push(`/mwd-tool-maintain/maintain?repairCode=${this.detailForm.repairCode}&repairDetailId=${item.repairDetailId}&deviceType=${item.deviceType||''}&mwdNumber=${item.mwdNumber||''}&serialNumber=${item.serialNumber||''}`)
        }else{
            
            if(this.selectedRepairDetail.some(item=>!item.mwdNumber||!item.deviceType)){
                this.$message.error('请填写完整工单号和仪器类型！')
                return;
            }
            if(this.selectedRepairDetail.some(item=>!item.valid||!item.serialNumber)){
                this.$confirm("当前有无效序列号，还要继续创建吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                }).then(() => {
                    this.onCreateOrder()
                })
            }else{
                this.onCreateOrder()
            }
            
        }

    }
    private onCreateOrder(){
        const form = this.selectedRepairDetail.map(item=>{
            const ret:any = {};
            const workOrderMwdDetailList:any[] = [{}]
            ret.jobNumber = this.detailForm.jobNumber;
            ret.wellNumber = this.detailForm.wellNumber;
            ret.kitNumber = this.detailForm.kitNumber;
            ret.mwdNumber = item.mwdNumber;
            ret.repairDetailId = item.repairDetailId;
            workOrderMwdDetailList[0].invName = item.invName;
            workOrderMwdDetailList[0].deviceType = item.deviceType;
            workOrderMwdDetailList[0].serialNumber =
                item.serialNumber;
            workOrderMwdDetailList[0].inWellHour = item.inWellHour;
            workOrderMwdDetailList[0].circulateHrs =
                item.circulateHrs;
            workOrderMwdDetailList[0].maxBht = item.maxBht;
            workOrderMwdDetailList[0].circulateBht =
                item.circulateBht;
            workOrderMwdDetailList[0].run = item.run;
            workOrderMwdDetailList[0].returnReason =
                item.returnReason;
            workOrderMwdDetailList[0].contactNumber =
                this.detailForm.contactNumber;
            workOrderMwdDetailList[0].contactUser =
                this.detailForm.contactUser;
            workOrderMwdDetailList[0].mwdNumber = item.mwdNumber;
            workOrderMwdDetailList[0].vssStatus = item.vssStatus;
            workOrderMwdDetailList[0].failureDescription = item.failureDescription;
            workOrderMwdDetailList[0].failureStatus = item.failureStatus;
            workOrderMwdDetailList[0].notes = item.note;
            ret.workOrderMwdDetailList = workOrderMwdDetailList;
            return ret
        })
        apiBatchAddMwdWorkOrder(form).then(()=>{
            this.$message.success("创建成功")
        })
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
