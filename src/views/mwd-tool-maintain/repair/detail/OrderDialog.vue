<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="返修单"
        width="80%"
    >
    <el-form
            ref="originForm"
            :model="originForm"
            :rules="originFormRules"
            label-width="100px"
        >
            <el-row>
                <el-col :span="8">
                    <el-form-item label="返修单类型: " prop="toolType">
                        <el-select
                            v-model="originForm.toolType"
                            style="width: 80%"
                        >
                            <el-option label="MWD" value="MWD"></el-option>
                            <el-option
                                label="井下工具"
                                value="UNDER_WELL"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="井号: " prop="wellNumber">
                        <FuzzyWellInput v-model="originForm.wellNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号: " prop="jobNumber">
                        <el-input
                            style="width: 80%"
                            v-model="originForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <el-input
                            style="width: 80%"
                            v-model="originForm.kitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返修日期: " prop="returnDate">
                        <el-date-picker
                            style="width: 80%"
                            v-model="originForm.returnDate"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            type="date"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称: ">
                        <el-input
                            style="width: 80%"
                            v-model="originForm.customerName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人: ">
                        <el-input
                            style="width: 80%"
                            v-model="originForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="联系方式: ">
                        <el-input
                            style="width: 80%"
                            v-model="originForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="originForm.repairDetailList"
            style="margin-bottom: 20px"
            :header-cell-style="commmonTableHeaderCellStyle"
        >
            <el-table-column label="工单号" width="170">
                <template slot-scope="scope">
                    <el-input
                        style="width: 100%"
                        v-model="scope.row.mwdNumber"
                    >
                    </el-input>
                </template> 
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="170">
                <template slot-scope="scope">
                    <!-- <el-autocomplete 
                        v-model="scope.row.serialNumber" 
                        :fetch-suggestions="querySearchValidSN"
                        style="width:100%"
                        @select="onSelectSerialNumber($event, scope.row)"
                        @change="onSnChange($event, scope.row)"
                        value-key="serialNumber"
                    ></el-autocomplete> -->
                    <span>{{ scope.row.serialNumber }}</span>
                    <div v-if="scope.row.isValid==='invalid'&&scope.row.serialNumber" style="color:red;font-size:10px">无效序列号</div>
                </template>
            </el-table-column>
            <el-table-column label="仪器类型" prop="deviceType" width="170">
                <template slot-scope="scope">
                    <FuzzySelect :init-list="deviceTypeList" placeholder="" clearable type="DEVICE_TYPE" v-model="scope.row.deviceType"></FuzzySelect>
                </template>
            </el-table-column>
            <el-table-column label="仪器名称" prop="invName">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.invName"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="入井时间" prop="inWellHour">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.inWellHour"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.circulateHrs"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="循环温度" prop="circulateBht">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.circulateBht"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.maxBht"
                    >
                    </el-input>
                </template>
            </el-table-column>

            <el-table-column label="趟次" prop="run">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.run"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.returnReason"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="是否存在故障" prop="failureStatus">
                <template slot-scope="{row}">
                    <el-select
                        style="width: 100%"
                        v-model="row.failureStatus"
                    >
                        <el-option label="是" :value="1"></el-option>
                        <el-option label="否" :value="0"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="故障描述" prop="failureDescription">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.failureDescription"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="是否有震动超标" prop="vssStatus">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.vssStatus"
                    >
                    </el-input>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note">
                <template slot-scope="{row}">
                    <el-input
                        style="width: 100%"
                        v-model="row.note"
                    >
                    </el-input>
                </template>
            </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onBatchCreate">
                <span>创建维修工单</span> 
            </el-button>
        </span>
    </el-dialog>
</template>
<script>
import { apiBatchAddMwdWorkOrder } from "@/api/tool-mantain";
import { apiCheckSerialNumberList, apiGetWarehouseDeviceBatchInfo } from "@/api/warehouse";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import FuzzyWellInput from "@/components/FuzzyWellInput/index.vue";
import getDict from "@/utils/getDict";
export default {
    components: { FuzzySelect, FuzzyWellInput },
    data(){
        return {
            originForm: {},
            originFormRules: {
                wellNumber: [{ required: true, message: "*", trigger: "blur" }],
                jobNumber: [{ required: true, message: "*", trigger: "blur" }],
            },
            isDialogVisible: false,
            deviceTypeList: [],
        }
    },
    props: {
        detailForm: Object
    },
    methods: {
        
        querySearchValidSN(query, cb){
            apiGetWarehouseDeviceBatchInfo({serialNumber: query}).then(res=>{
                const data = res.data.data || [];
                cb(data)
            })
        },
        onSelectSerialNumber(item, row){
            this.onSnChange(item.serialNumber, row)
        },
        
        onSnChange(serialNumber, row){
            apiCheckSerialNumberList({serialNumberList: [serialNumber]}).then(res=>{
                const info = res.data?.data?.[0] || {};
                row.deviceId = info.deviceId;
                row.invName = info.invName;
                row.deviceType = info.deviceType;
                row.isValid = info.isValid;
            })
        },
        async showDialog(selectedRepairList) {
            [this.deviceTypeList] = await getDict([17]);
            const originForm = { ...this.detailForm };
            // 筛选出选中的仪器
            originForm.repairDetailList = this.detailForm.repairDetailList.filter(item=>!!selectedRepairList.find(r=>r.repairDetailId===item.repairDetailId));
            const deviceInfoList = await apiCheckSerialNumberList({serialNumberList: originForm.repairDetailList.map(item=>(item.serialNumberUpdated || item.serialNumber))}).then(res=>{
                return res.data.data || [];
            });
            // 添加仪器额外信息
            originForm.repairDetailList = originForm.repairDetailList.map((item, index)=>{
                return {
                    ...item,
                    serialNumber: deviceInfoList[index].serialNumber,
                    deviceType: deviceInfoList[index].deviceType || null,
                    isValid: deviceInfoList[index].isValid,
                    invName: deviceInfoList[index].invName
                }
            })
            this.originForm = originForm;
            this.isDialogVisible = true;
            
        },
        onBatchCreate() {
            if(!this.originForm.repairDetailList?.length){
                this.$message.error('请选择要维修的仪器！')
                return
            }
                
            if(this.originForm.repairDetailList.some(item=>!item.mwdNumber||!item.deviceType)){
                this.$message.error('请填写完整工单号和仪器类型！')
                return;
            }
            if(this.originForm.repairDetailList.some(item=>item.isValid==='invalid'||!item.serialNumber)){
                this.$confirm("当前有无效序列号，还要继续创建吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                }).then(() => {
                    this.onCreateOrder()
                })
            }else{
                this.onCreateOrder()
            }
        },
        onCreateOrder(){
            const form = this.originForm.repairDetailList.map(item=>{
                const ret = {};
                const workOrderMwdDetailList = [{}]
                ret.jobNumber = this.originForm.jobNumber;
                ret.wellNumber = this.originForm.wellNumber;
                ret.kitNumber = this.originForm.kitNumber;
                ret.mwdNumber = item.mwdNumber;
                ret.repairDetailId = item.repairDetailId;
                workOrderMwdDetailList[0].invName = item.invName;
                workOrderMwdDetailList[0].deviceType = item.deviceType;
                workOrderMwdDetailList[0].serialNumber =
                    item.serialNumber;
                workOrderMwdDetailList[0].inWellHour = item.inWellHour;
                workOrderMwdDetailList[0].circulateHrs =
                    item.circulateHrs;
                workOrderMwdDetailList[0].maxBht = item.maxBht;
                workOrderMwdDetailList[0].circulateBht =
                    item.circulateBht;
                workOrderMwdDetailList[0].run = item.run;
                workOrderMwdDetailList[0].returnReason =
                    item.returnReason;
                workOrderMwdDetailList[0].contactNumber =
                    this.originForm.contactNumber;
                workOrderMwdDetailList[0].contactUser =
                    this.originForm.contactUser;
                workOrderMwdDetailList[0].mwdNumber = item.mwdNumber;
                workOrderMwdDetailList[0].notes = item.note;
                ret.workOrderMwdDetailList = workOrderMwdDetailList;
                return ret
            })
            apiBatchAddMwdWorkOrder(form).then(()=>{
                this.$message.success("创建成功");
                this.$emit("update");
                this.isDialogVisible = false;
            })
        }
    }
    
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
