<template>
    <el-dialog :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="serialNumber" label="序列号" width="200px">
                <template slot-scope="scope">
                    <!-- <el-autocomplete 
                        v-model="scope.row.serialNumber" 
                        :fetch-suggestions="querySearchValidSN"
                        style="width:100%"
                        @select="onSelectSerialNumber($event, scope.row)"
                        @change="onSnChange($event, scope.row)"
                        value-key="serialNumber"
                    ></el-autocomplete> -->
                    <span>{{ scope.row.serialNumber }}</span>
                    <div v-if="scope.row.isValid==='invalid'&&scope.row.serialNumber" style="color:red;font-size:10px;">无效序列号</div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="invName" label="品名" width="120px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%" v-if="scope.row.isValid==='valid'">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.receivedSendDate"
                        v-if="scope.row.isValid==='valid'"
                        style="width: calc(100%)"
                        type="date"
                        placeholder=""
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在Kit箱" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDate" label="完工日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)&&scope.row.isValid==='valid'"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDate"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDays" label="完工时间" width="100px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)&&scope.row.isValid==='valid'"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDays"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiReceiveRepairItem } from '@/api/repair';
import { apiBatchUpdateWarehouseDeviceList, apiCheckSerialNumberList, apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import { CIRCULATE_TYPE } from '@/utils/constant.warehouse';
import getDict from '@/utils/getDict';
const REPAIR_STATUS = 19002;
const STOCK_LOCATION = "Chengdu Shop";
export default {
    name: "SyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: []
        }
    },
    props: {
        detailForm: Object
    },
    methods: {
        isBattery(item){
            return item.deviceType == 16004
        },
        querySearchValidSN(query, cb){
            apiGetWarehouseDeviceBatchInfo({serialNumber: query}).then(res=>{
                const data = res.data.data || [];
                cb(data)
            })
        },
        onSelectSerialNumber(item, row){
            this.onSnChange(item.serialNumber, row)
        },
        onSnChange(serialNumber, row){
            apiCheckSerialNumberList({serialNumberList: [serialNumber]}).then(res=>{
                const info = res.data?.data?.[0] || {};
                Object.keys(row).forEach(key=>{
                    row[key] = info[key];
                })
                if(info.isValid==='invalid'){
                    return;
                }
                row.status = REPAIR_STATUS;
                row.receivedSendDate = new Date().Format("yyyy-MM-dd");
                row.location = STOCK_LOCATION;
            })
        },
        async showDialog(selectedRepairList) {
            [this.deviceStatusList] = await getDict([20]);
            this.selectedRepairList = selectedRepairList;
            await apiCheckSerialNumberList({serialNumberList:selectedRepairList.map(item=>(item.serialNumberUpdated || item.serialNumber))}).then(res=>{
                this.tableData = (res.data.data || []);
                this.tableData.forEach((item,index)=>{
                    if(item.isValid==='invalid'){
                        item.invName = selectedRepairList[index].invName;
                        return;
                    }
                    item.status = REPAIR_STATUS;
                    item.receivedSendDate = new Date().Format("yyyy-MM-dd");
                    item.location = STOCK_LOCATION;
                    item.kitNumber = null;
                })
                this.isDialogVisible = true;
            })
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            const detailIdList = this.selectedRepairList.map(item=>item.repairDetailId);
            const form = new FormData();
            form.append("detailIdList", detailIdList);
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            
            Promise.all([apiReceiveRepairItem(form), apiBatchUpdateWarehouseDeviceList({
                deviceList: this.tableData,
                circulateType: CIRCULATE_TYPE.REPAIR,
                businessId: this.detailForm.repairId,
            })]).then(()=>{
                this.$emit("update");
                this.isDialogVisible = false;
            }).finally(()=>{
                loading.close();
            })
        },
    }
}
</script>
