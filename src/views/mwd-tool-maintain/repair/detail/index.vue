<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div>
                <div class="app-card-title">
                    <span>返修单详情</span>
                    <span style="float: right;">
                        <el-button type="primary" v-if="p_MwdInfoView" @click="onBatchRevise">批量纠正序列号</el-button>
                        <el-button type="primary" v-if="p_ReceiveRepair" @click="onBatchReceive">批量接收仪器</el-button>
                        <el-button type="primary" v-if="p_CreateOrder" @click="onBatchCreate">批量创建工单</el-button>
                    </span>
                </div>
            </div>
            <el-form ref="detailForm" label-position="left" class="less-margin-form" style="margin-top: 20px" :model="detailForm" label-width="90px">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="作业号: ">
                            <span>{{ detailForm.jobNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="井号: ">
                            <span>{{ detailForm.wellNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="kit箱: ">
                            <span>{{ detailForm.kitNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="返修日期: ">
                            <span>{{ detailForm.returnDate }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户名称: ">
                            <span>{{ detailForm.customerName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系人: ">
                            <span>{{ detailForm.contactUser }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系方式: ">
                            <span>{{ detailForm.contactNumber }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :data="detailForm.repairDetailList"
                style="margin-bottom: 20px"
                :header-cell-style="commmonTableHeaderCellStyle"
                @selection-change="handleSelectionChange"
                height="calc(100vh - 300px)"
            >
                <el-table-column type="selection" width="55" v-if="p_CreateOrder || p_ReceiveRepair" fixed="left"></el-table-column>
                <el-table-column label="仪器名称" prop="invName" align="center" fixed="left">
                </el-table-column>
                <el-table-column label="序列号" prop="serialNumber" width="170" align="center" fixed="left">
                    <template slot-scope="{row}">
                        <span>{{ row.serialNumber }}<span v-if="row.serialNumberUpdated"> / {{ row.serialNumberUpdated }}</span></span>
                        <div v-if="row.invalid&&(row.serialNumber||row.serialNumberUpdated)" style="color:red;font-size:10px;">无效序列号</div>
                    </template>
                </el-table-column>
                <el-table-column label="入井时间" prop="inWellHour" align="center" width="100">
                </el-table-column>
                <el-table-column label="循环时间" prop="circulateHrs" align="center" width="100">
                </el-table-column>
                <el-table-column label="循环温度" prop="circulateBht" align="center" width="100">
                </el-table-column>
                <el-table-column label="最高温度" prop="maxBht" align="center" width="100"> </el-table-column>

                <el-table-column label="趟次" prop="run" align="center" width="80"> </el-table-column>
                <el-table-column label="返回原因" prop="returnReason" align="center">
                </el-table-column>
                <el-table-column label="是否存在故障" prop="failureStatus" width="120">
                    <template slot-scope="{row}">
                        <CustomTag v-if="row.failureStatus==1" tagType="NEGATIVE_TRUE_FALSE" :tagValue="true" />
                        <CustomTag v-if="row.failureStatus==0" tagType="NEGATIVE_TRUE_FALSE" :tagValue="false" />
                    </template>
                </el-table-column>
                <el-table-column label="故障描述" prop="failureDescription">
                </el-table-column>
                <el-table-column label="是否有震动超标" prop="vssStatus" width="120">
                </el-table-column>
                <el-table-column label="备注" prop="note" align="center" width="120"> </el-table-column>
                <el-table-column label="状态" prop="receiveStatus" width="100" align="center">
                    <template slot-scope="scope">
                        <CustomTag tagType="RECEIVE_STATUS" v-if="scope.row.receiveStatus == 'RECEIVED'" tagValue="RECEIVED" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else tagValue="SUBMITTED" />
                    </template>
                </el-table-column>
                <el-table-column label="接收人" prop="receiveUser" align="center"> </el-table-column>
                <el-table-column label="接收时间" prop="receiveDate" align="center" width="100"> </el-table-column>
                <el-table-column label="工单号" width="170" prop="mwdNumber" align="center">
                    <template slot-scope="{row}">
                        <router-link tag="a" style="color: blue" :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" v-if="p_MwdInfoView">{{ row.mwdNumber }}</router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="140" v-if="p_ReceiveRepair||p_CreateOrder" fixed="right">
                    <template slot-scope="{row}">
                        <el-button type="text" v-if="p_ReceiveRepair" @click="onReceive(row)" :disabled="row.receiveStatus==='RECEIVED'">接收</el-button>
                        <el-button type="text" v-if="p_CreateOrder" @click="onCreateOrder(row)" :disabled="row.mwdId||row.receiveStatus!=='RECEIVED'">创建工单</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <SyncTable @update="getRepairInfo" :detailForm="detailForm" ref="syncTableRef" />
        <OrderDialog @update="getRepairInfo" :detailForm="detailForm" ref="orderDialogRef" />
        <ReviseDialog @update="getRepairInfo" :detailForm="detailForm" ref="reviseDialogRef" />
    </div>
</template>
<script>
import { apiGetRepairInfo } from "@/api/repair";
import { apiCheckSerialNumber } from "@/api/tool-mantain";
import { apiCheckSerialNumberList } from "@/api/warehouse";
import SyncTable from "./SyncTable.vue";
import OrderDialog from "./OrderDialog.vue";
import ReviseDialog from "./ReviseDialog.vue";
export default {
    components: { SyncTable, OrderDialog, ReviseDialog },
    data() {
        return {
            isLoading: false,
            detailForm: {
                repairId: null,
                jobNumber: "",
                wellNumber: "",
                contactUser: "",
                contactNumber: "",
                kitNumber: "",
                returnDate: "",
                customerName: "",
                repairDetailList: [],
                toolType: "MWD",
                repairCode:''
            },
            selectedRepairDetail: [],
            repairId: null,
        }
    },
    computed: {
        p_MwdInfoView(){
            return this.$checkBtnPermission('sys:mwd:info');
        },
        p_ReceiveRepair(){
            return this.$checkBtnPermission('sys:mwd:repair:receive');
        },
        p_CreateOrder(){
            return this.$checkBtnPermission('sys:mwd:add');
        }
    },
    async mounted(){
        this.repairId = this.$route.query.id;
        if(!this.repairId){
            return
        }
        this.getRepairInfo()
    },
    methods: {
        getRepairInfo() {
            return apiGetRepairInfo({ repairId: this.repairId }).then(async(res) => {
                this.detailForm = res.data.data;
                const deviceInfoList = await apiCheckSerialNumberList({serialNumberList: this.detailForm.repairDetailList.map(item=>(item.serialNumberUpdated || item.serialNumber))}).then(res=>{
                    return res.data.data || [];
                });
                this.detailForm.repairDetailList = this.detailForm.repairDetailList.map((item, index)=>{
                    return {...item, invalid: deviceInfoList[index].isValid==='invalid'}
                })
            });
        },
        handleSelectionChange(val){
            this.selectedRepairDetail = val
        },
        async onValidateSN(serialNumber){
            const res = await apiCheckSerialNumber({ serialNumber });
            const code = res.data.data;
            const valid = code == 1 || code == 0;
            return valid
        },
        onBatchReceive(){
            const notReceviedList = this.selectedRepairDetail.filter(item=>item.receiveStatus!="RECEIVED")
            if(!notReceviedList?.length){
                this.$message.error('请选择还未接收的仪器！')
                return
            }
            this.$refs.syncTableRef.showDialog(notReceviedList)
        },
        onBatchRevise(){
            if(!this.selectedRepairDetail?.length){
                this.$message.error('请选择仪器！')
                return
            }
            this.$refs.reviseDialogRef.showDialog(this.selectedRepairDetail)
        },
        onReceive(row){
            this.$refs.syncTableRef.showDialog([row])
        },
        onBatchCreate() {
            const noMwdIdList = this.selectedRepairDetail.filter(item=>!item.mwdId)
            if(!noMwdIdList?.length){
                this.$message.error('请选择还未维修的仪器！')
                return
            }
            this.$refs.orderDialogRef.showDialog(noMwdIdList)
        },
        onCreateOrder(row){
            this.$refs.orderDialogRef.showDialog([row])
        }
    },
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
.less-margin-form{
    .el-form-item {
        margin-bottom: 6px !important;
    }
}
</style>
