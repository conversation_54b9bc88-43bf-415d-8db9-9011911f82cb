<template>
    <el-dialog :visible.sync="isDialogVisible" width="600px" title="纠正序列号">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="serialNumber" label="序列号">
                <template slot-scope="scope">
                    <span>{{ scope.row.serialNumber }}</span>
                    <div v-if="scope.row.invalid && scope.row.serialNumber" style="color:red;font-size:10px;">无效序列号</div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="serialNumberUpdated" label="纠正序列号">
                <template slot-scope="scope">
                    <FuzzySelect type="DEVICE_SN" v-model="scope.row.serialNumberUpdated" />
                </template>
            </el-table-column>
            <el-table-column align="center" prop="invName" label="品名" width="140px"></el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiUpdateRepair } from '@/api/repair';
import FuzzySelect from "@/components/FuzzySelect/index.vue";
export default {
    name: "ReviseDialog",
    components: { FuzzySelect },
    data(){
        return {
            isDialogVisible: false,
            tableData: [],
        }
    },
    props: {
        detailForm: Object
    },
    methods: {
        async showDialog(selectedRepairList) {
            this.tableData = selectedRepairList;
            this.isDialogVisible = true;
        },
        onCancel(){
            this.$emit("update");
            this.isDialogVisible = false;
        },
        onConfirm(){
            apiUpdateRepair(this.detailForm).then(() => {
                this.$message.success("更新成功");
                this.$emit("update");
                this.isDialogVisible = false;
            })
        },
    }
}
</script>
