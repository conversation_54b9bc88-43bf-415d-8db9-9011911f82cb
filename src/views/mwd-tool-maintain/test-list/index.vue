<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>试用件列表</span>
                <el-button icon="el-icon-plus" type="primary" style="float:right" @click="onAdd" v-if="p_MwdTestAdd">添加试用件</el-button>
            </div>
            <!-- <div class="simple-line"></div> -->
            <el-form :model="searchForm" label-width="auto">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="试用物资名称">
                            <el-input @change="initData(true)" v-model="searchForm.invName" style="width: 100%;" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="试用结论">
                            <el-select @change="initData(true)" v-model="searchForm.testResult" style="width: 100%;" clearable>
                                <el-option :value="1" label="通过"></el-option>
                                <el-option :value="0" label="未通过"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="服役状态">
                            <el-select @change="initData(true)" v-model="searchForm.serviceStatus" style="width: 100%;" clearable>
                                <el-option value="START_SERVICE" label="开始服役"></el-option>
                                <el-option value="SERVICING" label="正在服役"></el-option>
                                <el-option value="END_SERVICE" label="结束服役"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                        <el-form-item label="部件序列号">
                            <el-input @change="initData(true)" v-model="searchForm.jobNumber" clearable></el-input>
                        </el-form-item>
                    </el-col> -->
                </el-row>
            </el-form>
            <el-table
                :data="tableData"
                stripe
                :header-cell-style="commmonTableHeaderCellStyle"
                height="calc(100vh - 250px)"
            >
                <el-table-column label="试用物资名称" fixed="left" width="150" prop="invName">
                </el-table-column>
                <el-table-column label="序列号" width="150" prop="serialNumber">
                </el-table-column>
                <el-table-column label="自带序列号" width="150" prop="merSerialNumber">
                </el-table-column>
                <el-table-column label="引进品牌" width="120" prop="importBrand">
                </el-table-column>
                <el-table-column label="引入目的" width="120" prop="testPurpose">
                </el-table-column>
                <el-table-column label="需要现场测试验证的点" width="200" prop="testContent">
                </el-table-column>
                <el-table-column label="试用计划安排" width="200" prop="testPlan">
                </el-table-column>
                <el-table-column label="试用结论" width="100" prop="testResult">
                    <template slot-scope="{row}">
                        <CustomTag tagType="TEST_RESULT" :tagValue="row.testResult" />
                    </template>
                </el-table-column>
                <el-table-column label="数量" width="60" align="center" prop="quantity">
                </el-table-column>
                <el-table-column label="服役状态" width="100" prop="quantity">
                    <template slot-scope="scope">
                        <CustomTag tagType="TEST_SERVICE_STATUS" :tagValue="scope.row.serviceStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="验收标准" width="200" prop="quantity" align="center">
                    <template slot-scope="{row}">
                        <span style="white-space: pre-line">
                            {{ getTestWarnStrategyStr(row)}}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="是否到达验收标准" width="140" prop="warn" align="center">
                    <template slot-scope="{row}">
                        <CustomTag tagType="TRUE_FALSE" :tagValue="row.warn" />
                    </template>
                </el-table-column>
                <el-table-column label="试用过程" width="120" prop="testProcess">
                </el-table-column>
                <el-table-column label="试用结论描述" width="120" prop="testResultDesc">
                </el-table-column>
                <el-table-column label="母体仪器类型" width="120" prop="parentInvName">
                </el-table-column>
                <el-table-column label="母体序列号" width="120" prop="parentSerialNumber">
                </el-table-column>
                <el-table-column label="总趟次" width="100" prop="totalRun">
                </el-table-column>
                <el-table-column label="总工作时长" width="100" prop="totalHour">
                </el-table-column>
                <el-table-column label="最高温度" width="100" prop="maxBht">
                </el-table-column>
                <el-table-column
                    prop="mwdNumber"
                    label="操作"
                    width="200"
                    fixed="right"
                    align="center"
                >
                    <template slot-scope="{row}">
                            <el-button @click="onUpdate(row)" type="text" v-if="p_MwdTestEdit">
                                编辑
                            </el-button>
                            <el-button @click="onViewHistory(row)" type="text">
                                历史追溯
                            </el-button>
                            <el-button @click="onExport(row)" type="text" v-if="p_MwdTestExport">
                                导出报告
                            </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
            <TestHistoryTrack ref="testHistoryTrack" />
        </div>
        <el-dialog top="10vh" :visible.sync="isDialogVisible" :title="dialogTitle">
            <el-form :model="testForm" ref="testForm" :rules="testFormRules" label-width="120px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item prop="invName" label="品名：">
                            <el-input v-model="testForm.invName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="序列号：">
                            <el-input placeholder="自动生成" disabled v-model="testForm.serialNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="quantity" label="数量：">
                            <el-input v-model="testForm.quantity"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-form-item label="自带序列号：">
                    <el-input v-model="testForm.merSerialNumber"></el-input>
                </el-form-item>
                <el-form-item label="引进品牌：">
                    <el-input v-model="testForm.importBrand"></el-input>
                </el-form-item>
                <div :class="{'warn-border': testForm.warnType&&testForm.warnType!=='NO_WARN'}" style="">
                    <el-form-item label="验收标准：" prop="warnType">
                        <el-select style="width:100%" v-model="testForm.warnType">
                            <el-option value="NO_WARN" label="无"></el-option>
                            <el-option value="RUN_WARN" label="趟次"></el-option>
                            <el-option value="HOUR_WARN" label="入井时长"></el-option>
                            <el-option value="RUN_AND_HOUR_WARN" label="趟次和入井时长"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="testForm.warnType==='RUN_WARN' || testForm.warnType==='RUN_AND_HOUR_WARN'" label="趟次：" prop="standardRun">
                        <InputNumber style="width:100%" align="left" v-model="testForm.standardRun"></InputNumber>
                    </el-form-item>
                    <el-form-item v-if="testForm.warnType==='HOUR_WARN' || testForm.warnType==='RUN_AND_HOUR_WARN'" label="入井时长：" prop="standardHour">
                        <InputNumber style="width:100%" align="left" v-model="testForm.standardHour"></InputNumber>
                    </el-form-item>
                </div>
                
                <el-form-item label="引入目的：">
                    <el-input type="textarea" v-model="testForm.testPurpose"></el-input>
                </el-form-item>
                <el-form-item label="需要现场测试验证的点：">
                    <el-input type="textarea" v-model="testForm.testContent"></el-input>
                </el-form-item>
                <el-form-item label="试用计划安排：">
                    <el-input type="textarea" v-model="testForm.testPlan"></el-input>
                </el-form-item>
                <el-form-item label="试用过程：">
                    <el-input type="textarea" v-model="testForm.testProcess"></el-input>
                </el-form-item>
                <el-form-item label="试用结论：">
                    <el-select style="width:100%" v-model="testForm.testResult">
                        <el-option :value="0" label="未通过"></el-option>
                        <el-option :value="1" label="通过"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="试用结论描述：">
                    <el-input type="textarea" v-model="testForm.testResultDesc"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="isDialogVisible=false">取消</el-button>
                <el-button type="primary" @click="onConfirm">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetTestList, apiAddTest, apiUpdateTestInfo, apiExportTest } from "@/api/mwd";
import { Form as ElForm } from "element-ui/types/element-ui";
import TestHistoryTrack from "../maintain/components/TestTrack.vue";
import { getTestWarnStrategyStr } from "@/utils/constant.mwd"
@Component({name: "TestList", components: {TestHistoryTrack}})
export default class extends Vue {
    private isDialogVisible = false;
    private dialogType = "ADD";
    private testForm: any = {
        invName: "我都差点",
        quantity: 1,
        description: "开始试用",
        warnType: "RUN_WARN",
        standardRun: 2,
        testPurpose: "",
        testContent: "",
        testPlan: "",
        testResultDesc: "",
        merSerialNumber: "",
        testProcess: "",
        importBrand: "",
    };
    private getAddInitTestForm(){
        return {
            invName: "",
            quantity: 1,
            description: "",
            warnType: "NO_WARN",
            standardRun: "",
            standardHour: "",
            testPurpose: "",
            testContent: "",
            testPlan: "",
            testResultDesc: "",
            merSerialNumber: "",
            testProcess: "",
            importBrand: "",
        }
    }
    private testFormRules: any = {
        invName: [
            { required: true, message: "请输入品名", trigger: "change" }
        ],
        quantity: [
            { required: true, message: "请输入数量", trigger: "change" }
        ],
        warnType: [
            { required: true, message: "请选择验收标准", trigger: "change" }
        ],
        standardRun: [
            { required: true, message: "请输入趟次", trigger: "change" }
        ],
        standardHour: [
            { required: true, message: "请输入入井时长", trigger: "change" }
        ]
    };
    private tableData: any[] = [];
    private total = 1;
    private currentPage = 1;
    private pageSize = 100;
    private searchForm:any = {
    }
    private getTestWarnStrategyStr = getTestWarnStrategyStr;
    get dialogTitle(){
        return this.dialogType === 'ADD' ? "新增试用件" : "编辑试用件"
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    get p_MwdTestEdit(){
        return this.$checkBtnPermission('sys:mwd:test:edit');
    }
    get p_MwdTestExport(){
        return this.$checkBtnPermission('sys:mwd:test:export');
    }
    get p_MwdTestAdd(){
        return this.$checkBtnPermission('sys:mwd:test:add');
    }
    async mounted() {
        this.searchForm.wellNumber = this.$route.query.wellNumber;
        await this.initData();
    }
    handleSelect(){
        this.initData()
    }
    async initData(bool=false) {
        if(bool){
            this.currentPage = 1;
        }
        apiGetTestList({
            serviceStatus: this.searchForm.serviceStatus || null,
            testResult: this.searchForm.testResult,
            invName: this.searchForm.invName || null,
        }, {
            current: this.currentPage,
            size: this.pageSize,
        }).then(res=>{
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        })
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    private onViewHistory({serialNumber, invName}) {
        (this.$refs.testHistoryTrack as any).showDialog({ serialNumber, invName });
    }
    private onExport(row) {
        const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        apiExportTest(row).then(res => {
            const blob = new Blob([res.data], {
                type: 'application/word;charset=UTF-8',
            });
            const link = document.createElement('a');
            link.style.display = 'none';
            link.href = window.URL.createObjectURL(blob);
            link.download = "试用件报告.xlsx";
            link.click();
            //释放内存
            link.remove(); // 下载完成移除元素
            window.URL.revokeObjectURL(link.href);
        }).finally(() => {
            loading.close();
        });
    }
    private onAdd(){
        this.dialogType = 'ADD';
        this.testForm = this.getAddInitTestForm();
        
        this.isDialogVisible = true;
        this.$nextTick(()=>{
            (this.$refs.testForm as ElForm).clearValidate();
        })
    }
    private async onConfirm(){
        const valid = await (this.$refs.testForm as ElForm).validate();
        if(valid){
            if(this.dialogType==='ADD'){
                apiAddTest(this.testForm).then(() =>{
                    this.isDialogVisible = false;
                    this.initData();
                });
            }else{
                apiUpdateTestInfo(this.testForm).then(() =>{
                    this.isDialogVisible = false;
                    this.initData();
                });
            }
        }
        this.isDialogVisible = false;
    }
    private onUpdate(row){
        this.dialogType = "UPDATE";
        this.testForm = { ...row }
        this.isDialogVisible = true;
    }
}
</script>
<style lang="scss" scoped>
    .warn-border {
        border: 1px dashed #DCE1E6;
        padding: 20px 10px 0 0;
        margin-bottom: 10px;
        margin-top: -10px;
    }
</style>