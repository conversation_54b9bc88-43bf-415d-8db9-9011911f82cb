<template>
    <div>
        <div class="tool-maintain-container" v-loading="loading">
            <div class="tool-card" style="position: relative">
                <div class="tool-card-title">
                    创建工单
                    <el-button
                        type="primary"
                        style="float: right"
                        @click="onAddWorkOrder"
                        v-if="p_MwdAdd"
                    >
                        确认创建
                    </el-button>
                </div>
                <el-form
                    :model="form"
                    :rules="topFormRules"
                    label-width="90px"
                    ref="topForm"
                >
                    <el-row style="margin-left: -18px">
                        <el-col :span="6">
                            <el-form-item label="工单号" prop="mwdNumber">
                                <el-input
                                    clearable
                                    style="width: 100%"
                                    v-model="form.mwdNumber"
                                >
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="井号">
                                <!-- <el-input v-model="form.wellNumber"></el-input> -->
                                <el-select
                                    v-model="form.wellNumber"
                                    filterable
                                    remote
                                    placeholder=""
                                    :remote-method="remoteWellNumberMethod"
                                    clearable
                                    style="width:100%"
                                >
                                    <el-option
                                    v-for="item in fuzzyWellNumberList"
                                    :key="item.value"
                                    :label="item.value"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业编号">
                                <el-input v-model="form.jobNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="kit箱编号">
                                <el-input v-model="form.kitNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="开始维修日期" label-width="110px">
                                <el-date-picker
                                    placement="bottom-start"
                                    style="width: 100%"
                                    v-model="form.startDate"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="tool-card-title">仪器清单</div>
                    <el-form
                        :model="workOrderMwdDetailList[0]"
                        label-width="90px"
                        style="padding-top: 10px; margin-left: -18px"
                        :rules="bottomFormRules"
                        ref="bottomForm"
                    >
                        <el-row>
                            <el-col :span="6">
                                <el-form-item prop="deviceType" label="仪器类型" size="normal">
                                    <FuzzySelect placeholder="" clearable type="DEVICE_TYPE" :init-list="deviceTypeList" v-model="workOrderMwdDetailList[0].deviceType" @change="onDeviceTypeChange($event)"></FuzzySelect>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="序列号" size="normal" prop="serialNumber">
                                    <el-autocomplete 
                                        v-model="workOrderMwdDetailList[0].serialNumber" 
                                        :fetch-suggestions="querySearchValidSN"
                                        style="width:100%"
                                        @select="onSelectSerialNumber" 
                                        value-key="serialNumber"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item prop="invName" label="仪器名称" size="normal">
                                    <el-input v-model="workOrderMwdDetailList[0].invName"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="故障类型" size="normal">
                                    <el-select
                                        clearable
                                        v-model="workOrderMwdDetailList[0].failureType"
                                        style="width: 100%"
                                        placeholder=""
                                    >
                                        <el-option
                                            v-for="item in failureTypeList"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="所有者" size="normal">
                                    <el-select
                                        clearable
                                        v-model="workOrderMwdDetailList[0].owner"
                                        style="width: 100%"
                                        placeholder=""
                                    >
                                        <el-option
                                            v-for="item in ownerTypeList"
                                            :key="item.id"
                                            :value="item.name"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="现场联系人" size="normal">
                                    <el-input
                                        v-model="workOrderMwdDetailList[0].contactUser"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="联系电话" size="normal">
                                    <el-input
                                        v-model="
                                            workOrderMwdDetailList[0].contactNumber
                                        "
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="入井时间" size="normal">
                                    <el-input
                                        v-model="workOrderMwdDetailList[0].inWellHour"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="循环时间" size="normal">
                                    <el-input
                                        v-model="workOrderMwdDetailList[0].circulateHrs"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="最高温度" size="normal">
                                    <el-input
                                        v-model="workOrderMwdDetailList[0].maxBht"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="入井趟次" size="normal">
                                    <el-select v-model="workOrderMwdDetailList[0].runList" multiple collapse-tags clearable style="width: 100%">
                                        <el-option v-for="item in 20" :key="item" :value="String(item)" :label="String(item)"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="返回原因" size="normal">
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        v-model="workOrderMwdDetailList[0].returnReason"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="备注" size="normal">
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        v-model="workOrderMwdDetailList[0].notes"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-form>
            </div>
        </div>
    </div>
</template>
<script>
import { apiAddMwdWorkOrder, apiCheckSerialNumber, apiGetMwdWorkOrderInfo } from "@/api/tool-mantain";
import getDict from "@/utils/getDict";
import { apiGetRepairInfoByCode } from "@/api/repair";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { mergeRun, splitRun, LUCIDA_DEVICE_TYPE_LIST } from "@/utils/constant.mwd"
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { apiGetWarehouseDeviceBatchInfo } from "@/api/warehouse";
export default {
    name: "WorkOrderRedirect",
    components: {
        FuzzySelect,
    },
    data(){
        return {
            failureTypeList: [],
            ownerTypeList: [],
            repairCode: "",
            repairForm: { repairDetailList: [] },
            selectedRepairDetailId: null,
            form: {
                mwdNumber: "",
                wellNumber: "",
                moDocNo: "",
                jobNumber: "",
                kitNumber: "",
                startDate: "",
                endDate: "",
            },
            fuzzyWellNumberList: [],
            workOrderMwdDetailList: this.getInitDetailList(),
            topFormRules: {
                mwdNumber: [
                    {
                        required: true,
                        validator: (_, __, callback) => {
                            if (!this.form.mwdNumber) {
                                callback(new Error("请填写工单号！"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "change",
                    },
                ],
            },
            bottomFormRules: {
                deviceType: [
                    {
                        required: true,
                        validator: (_, __, callback) => {
                            if (!this.workOrderMwdDetailList[0].deviceType) {
                                callback(new Error("请填写仪器类型！"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "change",
                    },
                ],
                invName: [
                    {
                        required: true,
                        validator: (_, __, callback) => {
                            if (!this.workOrderMwdDetailList[0].invName) {
                                callback(new Error("请填写仪器名称！"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "change",
                    },
                ],
                serialNumber: [
                ],
            },
            deviceTypeList: [],
            loading: false,
            isRedirect: true,
        }
    },
    computed: {
        p_MwdAdd() {
            return this.$checkBtnPermission(`sys:mwd:add`);
        },
    },
    beforeRouteEnter(to, from, next){
        // 会报路由error，但是不影响使用
        const mwdId = to.query.mwdId;
        if(mwdId){
            apiGetMwdWorkOrderInfo({mwdId}).then(res=>{
                const data = res.data.data || {};
                const deviceType = data.workOrderMwdDetailList?.[0]?.deviceType;
                const routerType = LUCIDA_DEVICE_TYPE_LIST.includes(deviceType) ? "twelllink" : "mwd";
                next({path: `/mwd-tool-maintain/${routerType}`, query: { mwdId }});
            }).catch(()=>{
                next(false)
            })
        }else{
            next()
        }
    },
    async mounted() {
        [this.failureTypeList, this.deviceTypeList, this.ownerTypeList] = await getDict([13, 17, 22]);
        this.repairCode = this.$route.query.repairCode;
        this.selectedRepairDetailId = Number(this.$route.query.repairDetailId);
        if (this.repairCode && this.selectedRepairDetailId) {
            await apiGetRepairInfoByCode({
                repairCode: this.repairCode,
            }).then((res) => {
                this.repairForm = res.data.data;
                const detailItem = this.repairForm.repairDetailList.find(
                    (item) => item.repairDetailId == this.selectedRepairDetailId
                );
                this.form.kitNumber = this.repairForm.kitNumber;
                this.form.jobNumber = this.repairForm.jobNumber;
                this.form.wellNumber = this.repairForm.wellNumber;
                this.form.kitNumber = this.repairForm.kitNumber;
                this.workOrderMwdDetailList[0].deviceType = Number(this.$route.query.deviceType) || null;
                this.form.mwdNumber = this.$route.query.mwdNumber || null;
                this.workOrderMwdDetailList[0].invName = detailItem.invName;
                this.workOrderMwdDetailList[0].serialNumber =
                    this.$route.query.serialNumber || detailItem.serialNumber || "";
                this.workOrderMwdDetailList[0].inWellHour = detailItem.inWellHour;
                this.workOrderMwdDetailList[0].circulateHrs =
                    detailItem.circulateHrs;
                this.workOrderMwdDetailList[0].maxBht = detailItem.maxBht;
                this.workOrderMwdDetailList[0].circulateBht =
                    detailItem.circulateBht;
                this.workOrderMwdDetailList[0].run = detailItem.run;
                this.workOrderMwdDetailList[0].runList = splitRun(detailItem.run);
                this.workOrderMwdDetailList[0].returnReason =
                    detailItem.returnReason;
                this.workOrderMwdDetailList[0].notes =
                    detailItem.note;
                this.workOrderMwdDetailList[0].contactNumber =
                    this.repairForm.contactNumber;
                this.workOrderMwdDetailList[0].contactUser =
                    this.repairForm.contactUser;
            });
        }
        const copyid = this.$route.query.copyid;
        if(copyid){
            apiGetMwdWorkOrderInfo({ mwdId: copyid }).then((res) => {
                const data = res.data.data || {};
                this.form.wellNumber = data.wellNumber;
                this.form.jobNumber = data.jobNumber;
                this.form.kitNumber = data.kitNumber;
                this.form.startDate = data.startDate;
                this.workOrderMwdDetailList[0].failureType = data.workOrderMwdDetailList[0].failureType;
                this.workOrderMwdDetailList[0].owner = data.workOrderMwdDetailList[0].owner;
                this.workOrderMwdDetailList[0].contactUser = data.workOrderMwdDetailList[0].contactUser;
                this.workOrderMwdDetailList[0].contactNumber = data.workOrderMwdDetailList[0].contactNumber;
                this.workOrderMwdDetailList[0].inWellHour = data.workOrderMwdDetailList[0].inWellHour;
                this.workOrderMwdDetailList[0].circulateHrs = data.workOrderMwdDetailList[0].circulateHrs;
                this.workOrderMwdDetailList[0].maxBht = data.workOrderMwdDetailList[0].maxBht;
                this.workOrderMwdDetailList[0].run = data.workOrderMwdDetailList[0].run;
                this.workOrderMwdDetailList[0].runList = splitRun(data.workOrderMwdDetailList[0].run);
                this.workOrderMwdDetailList[0].returnReason = data.workOrderMwdDetailList[0].returnReason;
                this.workOrderMwdDetailList[0].notes = data.workOrderMwdDetailList[0].notes;
            });
        }
    },
    methods: {
        remoteWellNumberMethod(query){
            const form = new FormData();
            form.append("wellNumberKey", query || "");
            apiWellListFuzzy(form).then(res=>{
                const data = res.data.data || [];
                this.fuzzyWellNumberList = data.map(item=>({value:item.wellNumber}))
            })
        },
        getInitDetailList() {
            return [
                {
                    serialNumber: null,
                    deviceType: null,
                    invName: null,
                    owner: null,
                    contactUser: null,
                    contactNumber: null,
                    inWellHour: null,
                    circulateHrs: null,
                    failure: null,
                    failureType: null,
                    repairLevel: null,
                    repairUser: null,
                    checkedUser: null,
                    approveUser: null,
                    run: null,
                    runList: null,
                    maxBht: null,
                    laborHours: null,
                    customerName: null,
                    repairStartDate: null,
                    repairEndDate: null,
                    returnReason: null,
                    rootReason: null,
                    repairAction: null,
                    notes: null,
                },
            ];
        },
        onSelectSerialNumber(item){
            this.workOrderMwdDetailList[0].deviceType = item.deviceType;
            this.workOrderMwdDetailList[0].invName = item.invName;
            this.workOrderMwdDetailList[0].owner = item.owner;
        },
        querySearchValidSN(query, cb){
            apiGetWarehouseDeviceBatchInfo({
                serialNumber:query,
                deviceTypeList:this.workOrderMwdDetailList[0].deviceType ? [this.workOrderMwdDetailList[0].deviceType] : undefined,
            }).then(res=>{
                const data = res.data.data || [];
                cb(data)
            })
        },
        async onAddWorkOrder() {
            const valid1 = await this.$refs.topForm.validate()
                .catch(() => {
                    return Promise.resolve(false);
                });
            const valid2 = await this.$refs.bottomForm.validate()
                .catch(() => {
                    return Promise.resolve(false);
                });
            if (!valid1 || !valid2) {
                return;
            }
            let valid = false;
            const serialNumber = this.workOrderMwdDetailList[0].serialNumber;
            if(serialNumber){
                const res = await apiCheckSerialNumber({ serialNumber });
                const code = res.data.data;
                valid = code == 1 || code == 0;
            }
            if (!valid) {
                this.$confirm("当前序列号无效，还要继续创建吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                }).then(() => {
                    this.onCreateOrder()
                })
            }else{
                this.onCreateOrder()
            }
        },
        onCreateOrder(){
            this.workOrderMwdDetailList[0].run = mergeRun(this.workOrderMwdDetailList[0].runList);
            this.form.workOrderMwdDetailList = this.workOrderMwdDetailList;
            apiAddMwdWorkOrder(this.form).then((res) => {
                const mwdId = res.data.data.mwdId;
                this.$message.success("创建成功");
                this.$router.push(`/redirect/mwd-tool-maintain/maintain?mwdId=${mwdId}`)
            });
        },
        onDeviceTypeChange(item) {
            let tmp = this.deviceTypeList.find((map) => map.id == item.id);
            let invName = this.workOrderMwdDetailList[0].invName
            this.workOrderMwdDetailList[0].serialNumber = null;
            if(!invName){
                const deviceType = tmp.id == 16015 ? "" : tmp.name;
                this.workOrderMwdDetailList[0].invName = deviceType;
            }
        
        },
    }
}
</script>

<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
    
    .highlighted {
        padding: 2px;
        border: 1px solid rgb(67, 86, 152);
    }
}
</style>

<style lang="scss" scoped>
.tool-maintain-container {
    padding: 20px 40px;
    padding-right: 100px;
    margin: 12px;
    background: white;
    overflow: hidden;
    .tool-maintain-title {
        font-size: 16px;
        font-weight: 600;
    }
    .tool-card-subtitle {
        padding-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        color: rgb(67, 86, 152);
        margin-left: -12px;
        &::before {
            display: inline-block;
            margin-right: 6px;
            content: "-";
        }
    }
    .is-process {
        color: rgb(67, 86, 152) !important;
        font-weight: normal !important;
        .el-step__icon {
            border-color: rgb(67, 86, 152) !important;
        }
    }
    .active-step {
        .is-process {
            color: rgb(67, 86, 152) !important;
            font-weight: bold !important;
            .el-step__icon {
                border-color: rgb(67, 86, 152) !important;
            }
        }
    }
}
.el-table__expanded-cell {
    background: rgb(235, 238, 245);
}
.el-table__expanded-cell:hover {
    background: rgb(235, 238, 245) !important;
}
.active-step .el-step__title {
    color: rgb(67, 86, 152) !important;
    font-weight: bold;
}
</style>