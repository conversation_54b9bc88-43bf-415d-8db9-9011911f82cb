<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">交接单</div>
            <el-form :model="searchForm" label-width="70px" inline>
                <el-form-item label="交接日期">
                    <el-date-picker
                        placement="bottom-start"
                        clearable
                        @change="onSearch"
                        value-format="yyyy-MM-dd"
                        v-model="searchForm.receiveDaterange"
                        class="fixed-separator-date-picker"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="交接状态" label-width="100px">
                    <el-select
                        style="width: calc(100%)"
                        v-model="searchForm.handoverStatus"
                        @change="onSearch"
                        clearable
                    >
                        <el-option value="RECEIVED" label="已接收"></el-option>
                        <el-option value="HANDOVER" label="未接收"></el-option>
                        <el-option value="IGNORE" label="已忽略"></el-option>
                    </el-select>
                </el-form-item>
                <span style="float: right">
                    <el-button
                        type="primary"
                        @click="onExport"
                    >导出</el-button>
                </span>
            </el-form>
            <el-table 
                :header-cell-style="commmonTableHeaderCellStyle" 
                :data="tableData" 
                :default-sort="{
                    prop:'handoverDate',
                    order:'descending'
                }" 
                @sort-change="onSortChange"
                @selection-change="handleSelectionChange"
                height="calc(100% - 170px)"
            >
                <el-table-column
                    label="交接时间"
                    prop="handoverDate"
                    width="200px"
                    align="center"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    label="工单号"
                    min-width="140"
                    prop="mwdNumber"
                    align="center"
                >
                    <template slot-scope="{row}">
                        <router-link tag="a" style="color: blue" :to="`/mwd-tool-maintain/maintain?mwdId=${row.businessId}`" v-if="p_MwdInfoView">{{ row.businessNumber }}</router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="品名"
                    prop="invName"
                    align="center"
                    min-width="120"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    min-width="120"
                >
                    <template slot-scope="{row}">
                        <span style="color: blue;cursor: pointer;" @click="onDeviceInfo(row)">
                            {{ row.serialNumber }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="交接人"
                    prop="handoverUserName"
                    min-width="120"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="状态"
                    prop="receiveUserName"
                    align="center"
                    min-width="120"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RECEIVE_STATUS" v-if="scope.row.handoverStatus === 'RECEIVED'" tagValue="RECEIVED" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else-if="scope.row.handoverStatus === 'IGNORE'" tagValue="IGNORE" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else tagValue="SUBMITTED" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收人"
                    prop="receiveUserName"
                    align="center"
                    min-width="120"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receivedDate"
                    align="center"
                    width="200px"
                    sortable="custom"
                ></el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <InfoDialog ref="infoDialogRef" />
        <el-dialog width="500px" title="导出交接单" :visible.sync="isExportDialogVisible">
            <el-form :model="exportForm" label-width="80px">
                <el-form-item label="交接时间">
                    <el-date-picker
                        style="width: calc(100%)"
                        placement="bottom-start"
                        clearable
                        value-format="yyyy-MM-dd"
                        v-model="exportForm.daterange"
                        class="fixed-separator-date-picker"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="isExportDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onConfirmExport">导出</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiExportHandover, apiGetHandoverList } from "@/api/handover";
import InfoDialog from "./infoDialog.vue";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({ components: { InfoDialog } })
export default class extends Vue {
    private searchForm: any = {
        handoverType: "MWD"
    };
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 100;
    private total = 0;
    private orderType = "desc";
    private orderBy = "handoverDate";
    private isExportDialogVisible = false;
    private exportForm: any = {
        daterange: null
    };
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private async mounted() {
        this.getHandoverList();
    }
    private getHandoverList() {
        apiGetHandoverList({
            ...this.searchForm,
            orderType: this.orderType,
            orderBy: this.orderBy
        }, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }
    private onReceive(row){
        (this.$refs.syncTableRef as any).showDialog([row])
    }
    private onExport(){
        this.$confirm("确认导出未接收的交接单？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                this.onConfirmExport();
            })
            .catch(() => {});
    }
    private onConfirmExport(){
        const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        apiExportHandover({}).then((res)=>{
            if(!res.data){
                this.$message.error("暂无数据")
                return
            }
            const blob = new Blob([res.data], {
                type: "application/word;charset=UTF-8",
            });
            const fileName = `交接单.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);
            this.isExportDialogVisible = false;
        }).finally(()=>{
            loading.close();
        })
    }
    private onSearch() {
        const receiveDaterange = this.searchForm.receiveDaterange;

        if (receiveDaterange?.length) {
            this.searchForm.startDate = receiveDaterange[0];
            this.searchForm.endDate = receiveDaterange[1];
        } else {
            this.searchForm.startDate = undefined;
            this.searchForm.endDate = undefined;
        }

        Object.keys(this.searchForm).forEach((key) => {
            let value = this.searchForm[key];
            if (value === "" || value === null) {
                this.searchForm[key] = undefined;
            }
        });
        this.currentPage = 1;
        this.getHandoverList();
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getHandoverList();
    }
    private onSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.getHandoverList();
    }
    private onBatchReceive(){
        if(!this.selectedRelateItemList?.length){
            this.$message.error("请先选择需要接收的交接单!")
            return
        }
        (this.$refs.syncTableRef as any).showDialog(this.selectedRelateItemList)
    }
    
    private handleSelectionChange(val){
        this.selectedRelateItemList = val;
    }
    private isRowSelectable(row){
        return row.handoverStatus === 'HANDOVER'
    }
    private onDeviceInfo(row){
        (this.$refs.infoDialogRef as any).showDialog(row)
    }
}
</script>
<style lang="scss" scoped></style>
