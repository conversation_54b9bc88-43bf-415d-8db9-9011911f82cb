<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">工单查询</div>
            <el-row class="flex-row" :gutter="10">
                <el-col :span="20">
                    <el-form :model="searchForm" label-width="70px">
                        <el-row class="flex-row" :gutter="20" :style="`height: ${isFilterCollapsed ? '50px' : '160px'};overflow: hidden; transition: height 0.25s;`">
                            <el-col :span="6">
                                <el-form-item label="内部单号">
                                    <el-input
                                        v-model="searchForm.mwdNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>

                            <el-col :span="6">
                                <el-form-item label="仪器分类">
                                    <FuzzySelect placeholder="" multiple clearable type="DEVICE_TYPE" v-model="searchForm.deviceTypeList" @change="onSearchWorkOrder"></FuzzySelect>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="序列号">
                                    <el-autocomplete
                                        style="width: calc(100%)"
                                        v-model="searchForm.serialNumber"
                                        :fetch-suggestions="querySN"
                                        placeholder="请输入内容"
                                        @select="onSearchWorkOrder"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        @clear="onSearchWorkOrder"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修状态">
                                    <el-select
                                        style="width:calc(100%)"
                                        v-model="searchForm.finishList"
                                        @change="onSearchWorkOrder"
                                        multiple
                                        clearable
                                    >
                                        <el-option
                                            label="完成"
                                            :value="1"
                                        ></el-option>
                                        <el-option
                                            label="未完成"
                                            :value="0"
                                        ></el-option>
                                        <el-option
                                            label="滞留"
                                            :value="-1"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="6">
                                <el-form-item label="作业号">
                                    <el-input
                                        v-model="searchForm.jobNumber"
                                        @change="onSearchWorkOrder"
                                        style="width: calc(100% - 100px)"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="6">
                                <el-form-item label="接收日期">
                                    <el-date-picker
                                        style="width: calc(100%)"
                                        placement="bottom-start"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        value-format="yyyy-MM-dd"
                                        v-model="searchForm.receiveDaterange"
                                        class="fixed-separator-date-picker"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="完成日期">
                                    <el-date-picker
                                        style="width: calc(100%)"
                                        placement="bottom-start"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        value-format="yyyy-MM-dd"
                                        v-model="searchForm.finishDaterange"
                                        class="fixed-separator-date-picker"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                    >
                                    </el-date-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="项目号(井号)" label-width="100px">
                                    <el-autocomplete
                                        style="width: calc(100%)"
                                        v-model="searchForm.wellNumber"
                                        :fetch-suggestions="querySearchAsync"
                                        placeholder="请输入内容"
                                        @select="onSearchWorkOrder"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        @clear="onSearchWorkOrder"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="出厂序列状态" label-width="100px">
                                    <el-select
                                        style="width: calc(100%)"
                                        v-model="searchForm.riskTypeList"
                                        @change="onSearchWorkOrder"
                                        multiple
                                        clearable
                                    >
                                        <el-option
                                            v-for="item in riskTypeList"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="kit箱">
                                    <el-input
                                        v-model="searchForm.kitNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <!-- 不要用id, 用name -->
                                <el-form-item label="所有者">
                                    <el-autocomplete
                                        style="width: calc(100%)"
                                        v-model="searchForm.owner"
                                        :fetch-suggestions="querySearchOwnerAsync"
                                        placeholder="请输入内容"
                                        @select="onSearchWorkOrder"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        @clear="onSearchWorkOrder"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="故障类型">
                                    <el-select
                                        style="width: calc(100%)"
                                        v-model="searchForm.failureTypeList"
                                        @change="onSearchWorkOrder"
                                        multiple
                                        clearable
                                    >
                                        <el-option
                                            v-for="item in failureTypeList"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="失效部件分类" label-width="100px">
                                    <el-select
                                        style="width: calc(100%)"
                                        v-model="searchForm.failureComponentTypeList"
                                        @change="onSearchWorkOrder"
                                        multiple
                                        clearable
                                    >
                                        <el-option
                                            v-for="item in failureComponentTypeList"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        
                        </el-row>
                    </el-form>
                </el-col>
                <el-col :span="4">
                    <i style="margin-right: 8px; cursor: pointer" :title="isFilterCollapsed ? '展开搜索条件' : '折叠搜索条件'" :class="isFilterCollapsed ? `el-icon-arrow-right` : `el-icon-arrow-down`" @click="toggleFilterCollapse"></i>
                    <el-button
                        type="primary"
                        @click="onAddWorkOrder"
                        v-if="$checkBtnPermission(`sys:mwd:add`)"
                        >新增</el-button
                    >
                    <el-button
                        type="primary"
                        @click="onClickExportWorkOrder"
                        v-if="$checkBtnPermission(`sys:mwd:export`)"
                        >导出</el-button
                    >
                    <el-popover
                        placement="bottom"
                        trigger="manual"
                        v-model="isTotalInfoPopoverVisible"
                    >
                        <div class="total-info">
                            <div style="margin-bottom:10px">
                                <span style="font-size:14px;color:#1f2d3d;font-weight:700">总最高温度(℃)：</span>{{ statsInfo.maxBht }}
                            </div>
                            <div style="margin-bottom:10px">
                                <span style="font-size:14px;color:#1f2d3d;font-weight:700">总循环时间(h)：</span>{{ statsInfo.totalCirculateHrs }}
                            </div>
                            <div style="margin-bottom:10px">
                                <span style="font-size:14px;color:#1f2d3d;font-weight:700">总入井时间(h)：</span>{{ statsInfo.totalInWellHrs }}
                            </div>
                        </div>
                        <i class="el-icon-info" style="margin-left: 6px;cursor: pointer" v-if="isFilterCollapsed" slot="reference" @click="isTotalInfoPopoverVisible = !isTotalInfoPopoverVisible"></i>
                    </el-popover>
                    <div class="total-info" :class="{collapsed: isFilterCollapsed}" style="">
                        <div style="margin-bottom:10px">
                            <span style="font-size:14px;color:#1f2d3d;font-weight:700">总最高温度(℃)：</span>{{ statsInfo.maxBht }}
                        </div>
                        <div style="margin-bottom:10px">
                            <span style="font-size:14px;color:#1f2d3d;font-weight:700">总循环时间(h)：</span>{{ statsInfo.totalCirculateHrs }}
                        </div>
                        <div style="margin-bottom:10px">
                            <span style="font-size:14px;color:#1f2d3d;font-weight:700">总入井时间(h)：</span>{{ statsInfo.totalInWellHrs }}
                        </div>
                    </div>
                </el-col>
            </el-row>
            <el-table :data="tableData" @sort-change="onSortChange" v-loading="tableLoading" :height="`calc(100vh - ${isFilterCollapsed ? `280px` : '370px'})`" style="transition: height 0.25s;">
                <el-table-column
                    label="内部单号"
                    min-width="140"
                    prop="mwdNumber"
                    align="center"
                    fixed="left"
                    sortable="custom"
                ></el-table-column>
                <el-table-column
                    label="仪器类型"
                    prop="deviceTypeStr"
                    align="center"
                    width="120px"
                ></el-table-column>
                <el-table-column
                    label="仪器名称"
                    prop="invName"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修状态"
                    prop="finish"
                    width="120px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="FINISH" :tagValue="scope.row.finish" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="维修耗时"
                    prop="laborHours"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="项目号(井号)"
                    prop="wellNumber"
                    min-width="120"
                    align="center"
                ></el-table-column>
                
                <el-table-column
                    label="kit箱"
                    prop="kitNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="出厂序列状态"
                    prop="riskType"
                    width="120"
                    align="center"
                >
                    <template slot-scope="scope">
                        {{ getRiskTypeLabel(scope.row.riskType) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="所有者"
                    prop="owner"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="故障类型"
                    prop="failureType"
                    align="center"
                    width="100"
                >
                    <template slot-scope="scope">
                        {{ getFailureTypeLabel(scope.row.failureType) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="失效部件分类"
                    prop="failureComponentType"
                    min-width="150"
                    align="center"
                >
                    <template slot-scope="scope">
                        {{ getFailureComponentTypeLabel(scope.row.failureComponentType) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="返回原因"
                    prop="returnReason"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <el-table-column
                    label="根本原因"
                    prop="rootReason"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <el-table-column
                    label="方案&执行措施"
                    prop="repairAction"
                    align="center"
                    min-width="160"
                >
                    <template slot-scope="scope">
                        <div v-html="scope.row.repairAction"></div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="车间发现"
                    prop="findings"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <el-table-column
                    label="入井时间"
                    prop="inWellHour"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="循环时间"
                    prop="circulateHrs"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="入井趟次"
                    prop="run"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最高温度"
                    prop="maxBht"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="完成日期"
                    prop="endDate"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="接收日期"
                    prop="receiveDate"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后编辑人"
                    prop="lastModifiedByStr"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后修改时间"
                    prop="lastModifiedDate"
                    min-width="170"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    :width="operatorWidth"
                    v-if="p_mwdInfo||p_CreateHandover||p_mwdDelete"
                    fixed="right"
                >
                    <!--  v-if="$checkBtnPermission(`sys:mwd:workorder`)" -->
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_mwdInfo"
                            type="text"
                            @click="onDetail(scope.row)"
                        >
                            查看
                        </el-button>
                        <el-button
                            v-if="p_CreateHandover"
                            type="text"
                            :disabled="scope.row.finish!==1||!canHandover(scope.row.handoverStatus)"
                            :title="scope.row.finish===1?'':'未完成的工单不能交接'"
                            @click="onHandover(scope.row)"
                        >
                            {{ scope.row.handoverStatus ? (scope.row.handoverStatus === 'HANDOVER' ? '交接中' : '已交接' ) : '交接' }}
                        </el-button>
                        <el-button
                            v-if="p_mwdDelete"
                            :disabled="scope.row.finish===1"
                            :title="scope.row.finish===1?'已完成的工单不能删除':''"
                            type="text"
                            @click="onDelete(scope.row)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog :visible.sync="isExportDialogVisible" width="600px">
            接收日期：
            <el-date-picker
                placement="bottom-start"
                clearable
                style="width:calc(100% - 100px)"
                value-format="yyyy-MM-dd"
                v-model="exportDaterange"
                class="fixed-separator-date-picker"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
            >
            </el-date-picker>
            <template #footer>
                <el-button @click="isExportDialogVisible=false">取消</el-button>
                <el-button @click="onExportWorkOrder" type="primary">导出</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiDeleteMwdWorkOrder, apiExportMwdWorkOrder, apiGetMwdWorkOrderList, apiGetMwdWorkOrderOwnerList } from "@/api/tool-mantain";
import getDict from "@/utils/getDict";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { apiGetSNFuzzyList, apiGetMwdDeviceStats } from "@/api/mwd";
import { apiCreateHandover } from "@/api/handover";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({components: {FuzzySelect}})
export default class extends Vue {
    private tableLoading = false;
    private searchForm: any = {
        deviceTypeList: [],
        riskTypeList: [],
        failureComponentTypeList: [],
        failureTypeList: [],
        finishList: [],
    };
    private tableData: any[] = [];
    private deviceTypeList: any[] = [];
    private failureTypeList: any[] = [];
    private ownerTypeList: any[] = [];
    private failureComponentTypeList: any[] = [];
    private currentPage = 1;
    private pageSize = 100;
    private total = 0;
    private isExportDialogVisible = false;
    private exportDaterange = null;
    private statsInfo:any = {};
    private orderType = "";
    private orderBy = "";
    private isTotalInfoPopoverVisible = false;
    private riskTypeList = [
        { value: "RISK", label: "风险" },
        { value: "PRODUCE", label: "生产" },
        { value: "TEST", label: "试验" },
        { value: "SCRAP", label: "报废" },
    ];
    private ownerList: any[] = [];
    private isFilterCollapsed = false
    get p_mwdInfo(){
        return this.$checkBtnPermission(`sys:mwd:info`)
    }
    get p_mwdDelete(){
        return this.$checkBtnPermission(`sys:mwd:workorder:delete`)
    }
    get p_CreateHandover(){
        return this.$checkBtnPermission(`sys:mwd:handover`)
    }
    get operatorWidth(){
        return [this.p_CreateHandover,this.p_mwdDelete,this.p_mwdInfo].filter(item=>item).length * 52 + 'px'
    }
    private toggleFilterCollapse(){
        this.isFilterCollapsed = !this.isFilterCollapsed;
        this.isTotalInfoPopoverVisible = false

    }
    private canHandover(handoverStatus) {
        return handoverStatus !== 'HANDOVER' && handoverStatus !== 'RECEIVED'
    }
    private onHandover(row) {
        const { handoverStatus, mwdId } = row;
        if(!this.canHandover(handoverStatus)){
            return
        }
        this.$confirm("确认交接?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            const form = new FormData();
            form.append("idList", [mwdId].toString());
            form.append("type", "MWD");
            apiCreateHandover(form).then(()=>{
                this.$message.success("交接成功！");
                this.getMwdWorkOrderList();
            })
        });
    }
    private async activated() {
        this.getMwdWorkOrderList();
        this.getOwnerList();
        [this.deviceTypeList, this.failureTypeList, this.failureComponentTypeList, this.ownerTypeList] = await getDict([17, 13, 14, 22]);
    }
    private async getOwnerList() {
        await apiGetMwdWorkOrderOwnerList({}).then((res) => {
            this.ownerList = res.data.data || [].filter(item=>!!item);
        });
    }
    private getRiskTypeLabel(key) {
        const item = this.riskTypeList.find(item=>item.value === key);
        return item ? item.label : "";
    }
    private getFailureTypeLabel(key) {
        const item = this.failureTypeList.find(item=>item.id === key);
        return item ? item.name : "";
    }
    private getFailureComponentTypeLabel(key) {
        const item = this.failureComponentTypeList.find(item=>item.id === key);
        return item ? item.name : "";
    }
    private getMwdWorkOrderList() {
        this.tableLoading = true;
        apiGetMwdDeviceStats(this.searchForm).then(res=>{
            this.statsInfo = res.data.data;
        });
        const form = {...this.searchForm}
        form.failureComponentTypeList = form.failureComponentTypeList?.length ? form.failureComponentTypeList : undefined;
        form.failureTypeList = form.failureTypeList?.length ? form.failureTypeList : undefined;
        form.deviceTypeList = form.deviceTypeList?.length ? form.deviceTypeList : undefined;
        form.riskTypeList = form.riskTypeList?.length ? form.riskTypeList : undefined;
        form.finishList = form.finishList?.length ? form.finishList : undefined;
        apiGetMwdWorkOrderList({
            ...form,
            orderType: this.orderType,
            orderBy: this.orderBy
        }, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        }).finally(()=>{
            this.tableLoading = false;
        });
    }
    private onDelete(row: any){
        if(row.inWellHour || row.circulateHrs || row.maxBht){
            this.$message.error("该工单不允许删除，若要删除请联系管理员!");
            return;
        }
        const mwdId = row.mwdId;
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteMwdWorkOrder({ mwdId}).then(() => {
                    this.getMwdWorkOrderList();
                });
            })
            .catch(() => {});
    }
    private onDetail(row: any) {
        // const lucidaDeviceTypeList = [16027, 16028, 16029];
        // const routeType = lucidaDeviceTypeList.includes(row.deviceType) ? "twelllink" : "maintain";
        this.$router.push(`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`);
    }
    private onAddWorkOrder() {
        this.$router.push("/mwd-tool-maintain/maintain");
    }
    private onClickExportWorkOrder(){
        this.$confirm("确认导出当前条件下的工单?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(()=>{
            const loading = this.$loading({
                lock: true,
                text: "处理中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            const form = {...this.searchForm}
            form.failureComponentTypeList = form.failureComponentTypeList?.length ? form.failureComponentTypeList : undefined;
            form.failureTypeList = form.failureTypeList?.length ? form.failureTypeList : undefined;
            form.deviceTypeList = form.deviceTypeList?.length ? form.deviceTypeList : undefined;
            form.riskTypeList = form.riskTypeList?.length ? form.riskTypeList : undefined;
            form.finishList = form.finishList?.length ? form.finishList : undefined;
            apiExportMwdWorkOrder(form).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `MWD工单.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        })
    }
    private onExportWorkOrder(){
        apiExportMwdWorkOrder(this.searchForm).then((res) => {
            if(!res.data){
                this.$message.error("暂无数据")
                return
            }
            const blob = new Blob([res.data], {
                type: "application/word;charset=UTF-8",
            });
            const fileName = `MWD工单.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);
            this.isExportDialogVisible = false;
        })
    }
    
    handleSelect(){
        this.onSearchWorkOrder()
    }
    private async querySearchOwnerAsync(qs, cb) {
        if(!this.ownerList.length){
            await this.getOwnerList();
        }
        const data = qs ? this.ownerList.filter(item => item.toLowerCase().includes(qs.toLowerCase())) : this.ownerList;
        cb(data.map(item => ({ value: item })));
        // const form = new FormData();
        // form.append("name", qs || "");
        // apiGetOwnerList(form).then(res => {
        //     const data = res.data.data || [];
        //     cb(data.map(item => ({ value: item })))
        // })
    }
    private querySN(qs,cb){
        const form = new FormData();
        form.append("serialNumber", qs || "");
        apiGetSNFuzzyList(form).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item})))
        })
    }
    private querySearchAsync(qs, cb){
        const form = new FormData();
        form.append("wellNumberKey", qs || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item.wellNumber})))
        })
    }
    private onSearchWorkOrder() {
        const receiveDaterange = this.searchForm.receiveDaterange;

        if (receiveDaterange?.length) {
            this.searchForm.startTime = receiveDaterange[0];
            this.searchForm.endTime = receiveDaterange[1];
        } else {
            this.searchForm.startTime = undefined;
            this.searchForm.endTime = undefined;
        }
        const finishDaterange = this.searchForm.finishDaterange;

        if (finishDaterange?.length) {
            this.searchForm.finishStartTime = finishDaterange[0];
            this.searchForm.finishEndTime = finishDaterange[1];
        } else {
            this.searchForm.finishStartTime = undefined;
            this.searchForm.finishEndTime = undefined;
        }
        Object.keys(this.searchForm).forEach((key) => {
            let value = this.searchForm[key];
            if (value === "" || value === null) {
                this.searchForm[key] = undefined;
            }
        });
        this.currentPage = 1;
        this.getMwdWorkOrderList();
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getMwdWorkOrderList();
    }
    private onSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.getMwdWorkOrderList();
    }
}
</script>
<style lang="scss" scoped>
.total-info {
    transition: height 0.25s;
    overflow: hidden;
    margin-top: 14px;
    display: inline-block;
    border: 2px solid #ff4949;
    padding: 10px 8px 0;
    min-width: 200px;
    height: 105px;
    box-sizing: border-box;
    &.collapsed {
        border: none;
        height: 0;
    }
}
</style>
<style lang="scss" scoped>
.flex-row.el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>
