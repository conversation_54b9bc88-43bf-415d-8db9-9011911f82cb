<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">流转历史</div>
            </div>
            <el-form :model="searchForm" inline label-width="auto" style="margin-top: 10px; width: 100%;">
                <el-form-item label="仪器类型">
                    <FuzzySelect placeholder="" clearable type="DEVICE_TYPE" v-model="searchForm.deviceType"></FuzzySelect>
                </el-form-item>
                <el-form-item label="序列号">
                    <FuzzySelect :rest-params="{deviceType: searchForm.deviceType}" type="DEVICE_SN" @change="initData" v-model="searchForm.serialNumber" />
                </el-form-item>
                <el-form-item label="时间区间">
                    <el-date-picker
                        clearable
                        value-format="yyyy-MM-dd"
                        v-model="searchForm.daterange"
                        @change="initData"
                        class="fixed-separator-date-picker"
                        style="width: 300px"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>

            </el-form>
            <div class="circulation-info-container">
                <div class="circulation-info">
                    <div class="title">总服役次数：</div>
                    <div class="content">{{ info.serveTotalCount }}</div>
                </div>
                <div class="circulation-info">
                    <div class="title">总入井时间：</div>
                    <div class="content">{{ info.totalInWellHrs }}</div>
                </div>
                <div class="circulation-info">
                    <div class="title">总循环时间：</div>
                    <div class="content">{{ info.totalCirculateHrs }}</div>
                </div>
                <div class="circulation-info">
                    <div class="title">最高温度：</div>
                    <div class="content">{{ info.maxBht }}</div>
                </div>
            </div>
            <Circulation ref="circulationRef" style="height: calc(100vh - 260px);" />
        </div>
    </div>
</template>

<script>
import { apiGetCirculation } from "@/api/tool-track";
import getDict from "@/utils/getDict";
import Circulation from "@/components/Circulation/index.vue";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
export default {
    name: "DeviceCirculation",
    components: { FuzzySelect, Circulation },
    data() {
        return {
            isLoading: false,
            searchForm: {
                // deviceId: 204,
                transferType: "",
                serialNumber: "",
                daterange: [],
                startTime: null,
                endTime: null,
            },
            info: {
                maxBht: null,
                totalCirculateHrs: null,
                totalInWellHrs: null,
                serveTotalCount: null,
                serviceHistoryList: []
            }
        };
    },
    computed: {
    },
    mounted() {
        getDict([20]);
        if(this.searchForm.serialNumber) {
            this.initData();
        }
    },
    methods: {
        initData() {
            if(!this.searchForm.serialNumber){
                return
            }
            if(this.searchForm.daterange && this.searchForm.daterange.length!=0){
                this.searchForm.startTime = this.searchForm.daterange[0] + " 00:00:00";
                this.searchForm.endTime = this.searchForm.daterange[1] + " 23:59:59";
            } else {
                this.searchForm.startTime = null;
                this.searchForm.endTime = null;
            }
            apiGetCirculation(this.searchForm)
                .then((res) => {
                    this.info = res.data.data || [];
                    const serviceHistoryList = this.info.serviceHistoryList || [];
                    if(!serviceHistoryList || serviceHistoryList.length === 0) {
                        this.$message.error("没有查询到相关流转历史数据");
                    }
                    this.$nextTick(() => {
                        this.$refs.circulationRef.initData(serviceHistoryList);
                    });
                })
        },

    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
<style lang="scss" scoped>
.circulation-info-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 10px;
    max-width: 800px;
    width: 100%;
    .circulation-info {
        display: flex;
        width: 25%;
        margin-bottom: 10px;
        .title {
            font-weight: bold;
            color: #606266;
        }
        .content {
            color: #409eff;
        }
    }
}
</style>