<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="核心部件信息"
        width="800px"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="品名: " prop="invName">
                        <el-input v-model="detailForm.invName" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="序列号: " prop="serialNumber">
                        <el-input v-model="detailForm.serialNumber" disabled></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="固件版本号: ">
                        <el-input v-model="detailForm.versionNumber"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="最高温度: " prop="repairDate">
                        <el-input v-model="detailForm.maxBht"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="修正最高温度: " prop="kitNumber">
                        <el-input v-model="detailForm.reviseMaxBht"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="风险值: ">
                        <el-input v-model="detailForm.riskValue" disabled></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="总入井时间: " prop="totalInWellHrs">
                        <el-input v-model="detailForm.totalInWellHrs"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="修正总入井时间: " prop="reviseTotalHours" label-width="120px">
                        <el-input v-model="detailForm.reviseTotalHours"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="风险类型: ">
                        <el-select
                            style="width: calc(100%)"
                            v-model="detailForm.riskType"
                            disabled
                        >
                            <el-option
                                v-for="item in riskTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="总循环时间: " prop="totalCirculateHrs">
                        <el-input v-model="detailForm.totalCirculateHrs"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注: " prop="note">
                        <el-input v-model="detailForm.note"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <el-button @click="detailDialogVisible=false">取消</el-button>
            <el-button type="primary" @click="onConfirm">确认接收</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiReceiveHandover } from "@/api/handover";
import { apiUpdateComponent } from "@/api/mwd";
import { apiGetServiceHistoryDetailList } from "@/api/tool-track";
import { riskTypeList } from "@/utils/constant";
export default {
    name: "RmaReceiveDialog",
    data(){
        return {
            detailForm: {},
            detailDialogVisible: false,
            riskTypeList,
        }
    },
    methods: {
        showDialog(handoverInfo) {
            const {invName, serialNumber, id} = handoverInfo;
            this.id = id;
            apiGetServiceHistoryDetailList({invName, serialNumber}, { current: 1, size: 999 }).then((res) => {
                const data = res.data.data?.records || [];
                const target = data.find(item => (item.serialNumber == serialNumber && item.invName == invName));
                if(!target){
                    this.$message.error("未找到核心部件数据");
                    return;
                }
                this.detailForm = target;
                this.detailDialogVisible = true;
            })
        },
        receiveOrder(){
            const form = new FormData();
            form.append("idList", [this.id].toString());
            return apiReceiveHandover(form);
        },
        updateInfo(){
            return apiUpdateComponent(this.detailForm)
        },
        onConfirm(){
            Promise.all([this.receiveOrder(), this.updateInfo()]).then(()=>{
                this.$message.success("接收成功");
                this.detailDialogVisible = false;
                this.$emit("update")
            })
        }
    }
}
</script>
<style lang="scss"></style>
