<template>
    <el-dialog destroy-on-close :visible.sync="isDialogVisible" title="详情" width="70%">
        <el-tabs v-if="isDialogVisible" v-model="activeName" style="margin-top:-20px">
            <el-tab-pane label="仪器工作历史" name="history" style="min-height:200px">
                <el-table v-if="activeName==='history'" :data="historyTableData">
                    <el-table-column prop="wellNumber" label="井号"></el-table-column>
                    <el-table-column prop="jobNumber" align="center" label="作业号"></el-table-column>
                    <el-table-column prop="circulateHrs" align="center" label="循环时间"></el-table-column>
                    <el-table-column prop="inWellHour" align="center" label="入井时间"></el-table-column>
                    <el-table-column prop="maxBht" align="center" label="最高温度"></el-table-column>
                    <el-table-column prop="run" align="center" label="入井趟次"></el-table-column>
                    <el-table-column
                        prop="mwdNumber"
                        label="工单号"
                        align="center"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                    v-if="p_MwdInfoView"
                                >
                                    {{ row.mwdNumber }}
                                </router-link>
                            <span v-else>{{ row.mwdNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="historyTotal"
                    :page-size="pageSize"
                    @current-change="onHistoryCurrentChange"
                    :current-page="historyCurrentPage"
                ></el-pagination>
            </el-tab-pane>
            <el-tab-pane label="当前核心部件" name="core" style="min-height:200px">
                <el-table v-if="activeName==='core'" :data="coreTableData">
                    <el-table-column prop="invName" label="品名"></el-table-column>
                    <el-table-column prop="serialNumber" align="center" label="序列号">
                        <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/mwd-tool-maintain/partservice?serialNumber=${row.serialNumber}`"  
                                v-if="p_PartListView"
                            >
                                {{ row.serialNumber }}
                            </router-link>
                        <span v-else>{{ row.serialNumber }}</span>
                    </template>
                    </el-table-column>
                    <el-table-column prop="versionNumber" align="center" label="固件版本号"></el-table-column>
                    <el-table-column prop="riskType" align="center" label="风险类型">
                        <template slot-scope="scope">
                            <span>{{ getRiskTypeStr(scope.row.riskType) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="maxBht"
                        label="最高温度(℃)"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="totalCirculateHrs"
                        label="总循环时长(h)"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="totalInWellHrs"
                        label="总入井时长(h)"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="serveTotalCount"
                        label="总服役次数"
                        align="center"
                    ></el-table-column>
                </el-table>
                <!-- <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="coreTotal"
                    :page-size="pageSize"
                    @current-change="onCoreCurrentChange"
                    :current-page="coreCurrentPage"
                ></el-pagination> -->
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetMwdWorkOrderList } from "@/api/tool-mantain";
import { apiCoreList } from "@/api/tool-track";
import { getRiskTypeStr } from "@/utils/constant";
import { Component, Vue } from "vue-property-decorator";
@Component({ name: "PartDetail" })
export default class extends Vue {
    private activeName = "history";
    private isDialogVisible = false;
    private historyTableData:any[] = [];
    private coreTableData:any[] = [];
    private pageSize = 10;
    private historyCurrentPage = 1;
    private historyTotal = 0;
    private coreCurrentPage = 1;
    private coreTotal = 0;
    private getRiskTypeStr = getRiskTypeStr
    get p_PartListView(){
        return this.$checkBtnPermission('sys:partservice:view');
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private async showDialog(row:any){
        if(!row.serialNumber){
            return;
        }
        this.activeName = "history";
        this.coreCurrentPage = 1;
        this.historyCurrentPage = 1;
        this.row = row;
        await this.getHistoryList();
        await this.getCoreHistoryList();
        this.isDialogVisible = true;
        
    }
    private getHistoryList(){
       return apiGetMwdWorkOrderList({deviceType: this.row.deviceType, serialNumber: this.row.serialNumber}, {current:this.historyCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.historyTableData = data.records || [];
            this.historyTotal = data.total;
        })
    }
    private onHistoryCurrentChange(currentPage: number) {
        this.historyCurrentPage = currentPage;
        this.getHistoryList();
    }
    private getCoreHistoryList(){
        return apiCoreList({deviceId: this.row.deviceId}).then(res=>{
            const data = res.data.data || [];
            this.coreTableData = data;
            // this.coreTableData = data.records || [];
            // this.coreTotal = data.total;
        })
    }
    private onCoreCurrentChange(currentPage: number) {
        this.coreCurrentPage = currentPage;
        this.getCoreHistoryList();
    }
    
    
}
</script>