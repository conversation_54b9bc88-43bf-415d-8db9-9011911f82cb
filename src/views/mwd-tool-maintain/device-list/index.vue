<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器列表</div>
            <el-form :model="searchForm" label-width="70px">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="仪器类型">
                            <FuzzySelect placeholder="" clearable type="DEVICE_TYPE" v-model="searchForm.deviceType" @change="getCurrentStockList(true)"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="序列号" label-width="60px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="getCurrentStockList(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-button style="float: right; margin-right: 25px;" type="primary" @click="onExportRssMaintainHistory">旋导维保记录</el-button>
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" height="calc(100vh - 250px)">
                <el-table-column prop="deviceTypeStr" label="仪器类型">
                </el-table-column>
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                ></el-table-column>
                <el-table-column label="状态" prop="status" align="center" width="200px">
                    <template slot-scope="scope">
                        <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="scope.row.status"></CustomTag>
                    </template>
                </el-table-column>
                <el-table-column prop="riskType" label="风险类型" align="center" width="120px">
                    <template slot-scope="scope">
                        <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType"></CustomTag>
                    </template>
                </el-table-column>
                
                
                <el-table-column
                    prop="maxBht"
                    label="最高温度(℃)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="totalCirculateHrs"
                    label="总循环时长(h)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="totalInWellHrs"
                    label="总入井时长(h)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="serveTotalCount"
                    label="总服役次数"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="parentInvName"
                    label="最后操作工单"
                    align="center"
                >
                    <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                v-if="p_MwdInfoView"
                            >
                                {{ row.lastMwdNumber }}
                            </router-link>
                        <span v-else>{{ row.lastMwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="100px"
                    align="center"
                    v-if="p_DeviceDetail"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="onDetail(scope.row)"
                            type="text"
                        >
                            详情
                        </el-button>
                        <router-link 
                            tag="a"
                            :to="`/mwd-tool-maintain/structure?deviceId=${scope.row.deviceId}`"
                            target="_blank"
                            style="color: #004e7e"
                            v-if="showStructrue(scope.row)"
                        >
                            结构
                        </router-link>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <DeviceDetailDialog ref="detailDialog" />
        <RssMaintainHistoryDialog ref="rssMaintainHistoryDialogRef" />
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiGetServiceHistoryList } from "@/api/tool-track";
import DeviceDetailDialog from "./detail.vue"
import { apiGetDeviceStatisticList } from "@/api/mwd";
import { getRiskTypeStr } from "@/utils/constant";
import getDict from "@/utils/getDict";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { LUCIDA_DEVICE_TYPE_LIST } from "@/utils/constant.mwd";
import RssMaintainHistoryDialog from "./rssMaintainHistoryDialog.vue";
@Component({ components: { DeviceDetailDialog, FuzzySelect, RssMaintainHistoryDialog }})
export default class extends Vue {
    private searchForm: any = {
        deviceType: null,
        serialNumber: null,
    };
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 50;
    private total = 0;
    private inventoryClassList: any[] = [];
    private detailForm: any = {};
    private isDetailDialogVisible: boolean = false;
    private showDetail = false;
    private maxBht = "";
    private totalCirculateHrs = "";
    private totalInWellHrs = "";
    private serveTotalCount = "";
    private isDialogVisible = false;
    private currentDetailPage = 1;
    private detailTotal = 0;
    private detailTableData: any[] = [];
    private getRiskTypeStr = getRiskTypeStr;
    private deviceTypeList:any [] = [];
    get p_DeviceDetail(){
        return this.$checkBtnPermission('sys:mwd:devicelist:detail');
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private async mounted() {
        [this.deviceTypeList] = await getDict([17]);
        const { type, sn } = this.$route.query;
        this.searchForm.deviceType = type ?  Number(type) : undefined;
        this.searchForm.serialNumber = sn || undefined;
        this.getCurrentStockList();
    }
    private showStructrue(row){
        const deviceType = row.deviceType;
        return LUCIDA_DEVICE_TYPE_LIST.includes(deviceType)
    }
    
    private getDeviceTypeStr(deviceType) {
        return (
            this.deviceTypeList.find((item) => item.id == deviceType)?.name ||
            ""
        );
    }
    
    private async onDetail(row){
        (this.$refs.detailDialog as any).showDialog(row)
    }
    private getCurrentDetailStockList() {
        const form = { invName:this.detailForm.invName, serialNumber: this.detailForm.serialNumber }
        return apiGetServiceHistoryList(form, {
            current: this.currentDetailPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.detailTableData = data.records || [];
            this.detailTotal = data.total;
        });
    }

    private onCurrentDetailChange(currentPage: number) {
        this.currentDetailPage = currentPage;
        this.getCurrentDetailStockList();
    }
    private getCurrentStockList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const invName = this.searchForm.invName || undefined;
        const serialNumber = this.searchForm.serialNumber || undefined;
        const deviceType = this.searchForm.deviceType || undefined;
        apiGetDeviceStatisticList({invName,serialNumber,deviceType}, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
    private onClickDetail(scope: any) {
        this.detailForm = scope.row;
        this.isDetailDialogVisible = true;
    }
    private onStructure(row: any) {
        this.$router.push(`/mwd-tool-maintain/structure?deviceId=${row.deviceId}`);
    }
    private onExportRssMaintainHistory() {
        (this.$refs.rssMaintainHistoryDialogRef as any).showDialog();
    }
}
</script>
<style lang="scss" scoped></style>
