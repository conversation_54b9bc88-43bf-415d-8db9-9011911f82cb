<template>
    <el-dialog :visible.sync="isDialogVisible" title="旋导维保记录" width="400px" :close-on-click-modal="false">
        <el-form :model="form" label-width="auto" :rules="formRules" ref="formRef">
            <el-form-item label="SU" prop="suSerialNumber">
                <FuzzySelect type="DEVICE_SN" :rest-params="{deviceType: 16029}" v-model="form.suSerialNumber"></FuzzySelect>
            </el-form-item>
            <el-form-item label="MWD" prop="mwdSerialNumber">
                <FuzzySelect type="DEVICE_SN" :rest-params="{deviceType: 16028}" v-model="form.mwdSerialNumber"></FuzzySelect>
            </el-form-item>
            <el-form-item label="LCP" prop="lcpSerialNumber">
                <FuzzySelect type="DEVICE_SN" :rest-params="{deviceType: 16027}" v-model="form.lcpSerialNumber"></FuzzySelect>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onConfirm">确认导出</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiExportRssMaintainHistory } from "@/api/tool-mantain";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
export default {
    name: "RssMaintainHistoryDialog",
    components: {
        FuzzySelect
    },
    data() {
        return {
            isDialogVisible: false,
            form: {
                lcpSerialNumber: null,
                mwdSerialNumber: null,
                suSerialNumber: null
            },
            formRules: {
                lcpSerialNumber: [{ required: true, message: "请选择LCP序列号", trigger: "change" }],
                mwdSerialNumber: [{ required: true, message: "请选择MWD序列号", trigger: "change" }],
                suSerialNumber: [{ required: true, message: "请选择SU序列号", trigger: "change" }]
            },
        };
    },
    methods: {
        showDialog() {
            this.form = {
                lcpSerialNumber: null,
                mwdSerialNumber: null,
                suSerialNumber: null
            };
            this.isDialogVisible = true;
        },
        async onConfirm() {
            const valid = await this.$refs.formRef.validate();
            if (!valid) {
                return;
            }
            const loading = this.$loading({
                lock: true,
                text: "处理中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            apiExportRssMaintainHistory(this.form)
                .then((res) => {
                    this.loadingExportExcelAll = false;
                    const blob = new Blob([res.data], {
                        type: "application/word;charset=UTF-8",
                    });
                    const date = new Date().Format('yyyy-MM-dd');
                    const fileName = `${date}维保记录（LCP${this.form.lcpSerialNumber}+MWD${this.form.mwdSerialNumber}+SU${this.form.suSerialNumber}）.xlsx`;
                    const link = document.createElement("a");
                    link.style.display = "none";
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    link.remove();
                    window.URL.revokeObjectURL(link.href);
                    this.isDialogVisible = false;
                })
                .finally(() => {
                    loading.close();
                });
            
        }
    }
};
</script>