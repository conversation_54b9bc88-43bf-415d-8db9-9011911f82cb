<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器结构 - {{ deviceTree.invName }} - {{ deviceTree.serialNumber }}</div>
            <div style="overflow: hidden;margin-bottom: 10px;">
                <span style="float: right;">
                    <el-button type="primary" @click="onTable" v-if="showType==='tree'">表格显示</el-button>
                    <span v-else>
                        <el-button type="primary" @click="onTableExpandAll">全部展开</el-button>
                        <el-button type="primary" @click="onTree">树状显示</el-button>
                    </span>
                </span>
            </div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="tableData"
                row-key="id"
                :default-expand-all="false"
                ref="treeTableRef"
                v-if="showType === 'table'"
            >
                <el-table-column
                    prop="invName"
                    label="名称"
                    width="400">
                </el-table-column>
                <el-table-column
                    prop="level"
                    label="等级"
                    align="center"
                    width="60">
                </el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="lastAssemblyDate"
                    label="上次更换时间"
                    align="center"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="versionNumber"
                    label="版本"
                    align="center"
                    width="80">
                </el-table-column>
                <el-table-column
                    prop="riskType"
                    label="风险类型(风险值)"
                    align="center"
                    width="140"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                        <span v-if="scope.row.riskValue"> ({{ scope.row.riskValue }})</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="maxBht"
                    label="最高温度"
                    align="center"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="reviseMaxBht"
                    label="修正最高温度"
                    align="center"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="totalInWellHrs"
                    label="总入井时间"
                    align="center"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="reviseTotalHours"
                    label="修正总入井时间"
                    align="center"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="note"
                    label="厂家/备注"
                    align="center">
                </el-table-column>
            </el-table>
            <LucidaTreeGraph style="height: calc(100vh - 140px);margin-top: -46px;" :template="treeGraphTemplate" :actualData="treeGraphActualData" ref="lucidaTreeGraphRef" v-else />
        </div>
    </div>
</template>
<script>
import { apiGetLucidaTemplate, apiGetLucidaTree } from "@/api/lucida";
import LucidaTreeGraph from "@/components/LucidaTreeGraph";
import { mergeCompleteTemplateWithPartData } from "@/components/LucidaTreeGraph/utils/nodes";
import { lucidaDeviceTypeMap } from "@/utils/constant.mwd";

export default {
    components: { LucidaTreeGraph },
    data(){
        return {
            partListType: 'in',
            showType: 'table', // table, tree
            treeGraphActualData: null,
            treeGraphTemplate: null,
            tableData: [],
            deviceTree: {}
        }
    },
    mounted() {
        const deviceId = this.$route.query.deviceId;
        apiGetLucidaTree({deviceId}).then(res1=>{
            this.deviceTree = res1.data.data;
            let templateType = lucidaDeviceTypeMap[this.deviceTree.deviceType]?.template;
            this.treeGraphActualData = this.deviceTree;
            apiGetLucidaTemplate({ templateType }).then(res2=>{
                this.treeGraphTemplate = res2.data.data;
                this.tableData = mergeCompleteTemplateWithPartData(this.handleTreeData(this.treeGraphTemplate), this.deviceTree).children;
            })
        })
    },
    methods: {
        handleTreeData(data) {
            let count = 0;
            const handleFunc = (item) => {
                item.id = 'nodeid' + count++;
                if (item.children) {
                    item.children.forEach((child) => {
                        handleFunc(child, item);
                    });
                }
                return item;
            };
            return handleFunc(data);
        },

        onTree(){
            this.showType = 'tree';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('READ');
            })
        },
        onTable(){
            this.showType = 'table';
        },
        onTableExpandAll() {
            // 对表格数据进行遍历，展开每一行
            this.tableData.forEach(row => {
                this.$refs.treeTableRef.toggleRowExpansion(row, true);
                // 如果有子节点，递归展开
                if (row.children && row.children.length > 0) {
                    this.expandAllRows(row.children);
                }
            });
        },

        // 添加一个辅助方法用于递归展开所有子节点
        expandAllRows(rows) {
            rows.forEach(row => {
                this.$refs.treeTableRef.toggleRowExpansion(row, true);
                if (row.children && row.children.length > 0) {
                    this.expandAllRows(row.children);
                }
            });
        }
    }
}
</script>