<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
              <div class="app-card-title">钻井液管理</div>
            </div>
                <el-select v-model="filterForm.wellNumber" clearable  placeholder="请选择">
                    <el-option v-for="item in wellList"  :key="item.wellNumber" :label="item.wellNumber"  :value="item.wellNumber"></el-option>
                </el-select>
                <el-button icon="el-icon-search" type="primary" style="margin-left:20px;" @click="onFilter">查询</el-button>
                <el-button icon="el-icon-plus" type="primary" @click="onAddRecord" style="float:right;">新增记录</el-button>
            <el-table :data="tableData"  stripe style="margin-top:10px;">
                <el-table-column  label="井号" prop="wellNumber"></el-table-column>
                <el-table-column label="Run" prop="sequence"></el-table-column>
                <el-table-column label="井深" prop="wellDepth"></el-table-column>
                <el-table-column label="层位" prop="layer"></el-table-column>
                <!--begin 水基:常规性能-->
                <el-table-column label="常规性能"  v-if="wellType=='水基'">
                    <el-table-column  prop="density" label="密度"></el-table-column>
                    <el-table-column  prop="funnelViscosity" label="漏斗粘度"></el-table-column>
                    <el-table-column  prop="API" label="API失水"></el-table-column>
                    <el-table-column  prop="mudCake" label="泥饼"></el-table-column>
                    <el-table-column  prop="name" label="含沙"></el-table-column>
                    <el-table-column  prop="name" label="摩阻系数"></el-table-column>
                    <el-table-column  prop="name" label="PH值"></el-table-column>
                </el-table-column>
                <!--end-->
                 <!--begin 水基:流变参数-->
                <el-table-column label="流变参数"  v-if="wellType=='水基'">
                    <el-table-column   label="静切力Pa">
                        <el-table-column  prop="firstChop" label="初切"></el-table-column>
                        <el-table-column  prop="finalChop" label="终切"></el-table-column>
                    </el-table-column>
                    <el-table-column  prop="plansticViscosity" label="塑性粘度"></el-table-column>
                    <el-table-column  prop="dynamicShear" label="动切力"></el-table-column>
                    <el-table-column  prop="nValue" label="n值"></el-table-column>
                    <el-table-column  prop="kValue" label="k值"></el-table-column>
                </el-table-column>
                <!--end-->
                <!--begin 油基:常规性能-->
                <el-table-column label="常规性能" v-if="wellType=='油基'">
                    <el-table-column  prop="density" label="密度"></el-table-column>
                    <el-table-column  prop="funnelViscosity" label="漏斗粘度"></el-table-column>
                    <el-table-column  prop="CaCl2" label="氯化钙含量(水相中)"></el-table-column>
                    <el-table-column  prop="cement" label="过量石灰"></el-table-column>
                    <el-table-column  prop="sand" label="含沙"></el-table-column>
                    <el-table-column  prop="HTHP" label="HTHP失水ml"></el-table-column>
                    <el-table-column  prop="lowDensitySolid" label="低密度固相含量"></el-table-column>
                </el-table-column>
                <!--end-->
                  <!--begin 油基:流变参数-->
                 <el-table-column label="流变参数"  v-if="wellType=='油基'">
                    <el-table-column  prop="name" label="静切力Pa">
                        <el-table-column  prop="firstChop" label="初切"></el-table-column>
                        <el-table-column  prop="finalChop" label="终切"></el-table-column>
                    </el-table-column>
                    <el-table-column  prop="plansticViscosity" label="塑性粘度"></el-table-column>
                    <el-table-column  prop="dynamicShear" label="动切力"></el-table-column>
                    <el-table-column  prop="ф600/300" label="ф 600/300"></el-table-column>
                    <el-table-column  prop="ф6/3" label="ф 6/3"></el-table-column>
                </el-table-column>
                <!--end-->
                <el-table-column label="固含(%)" prop="province"></el-table-column>
                 <!--水基-->
                <el-table-column label="澎润土含量(g/L)" prop="province"  v-if="wellType=='水基'"></el-table-column>
                <!--begin 油基-->
                <el-table-column label="破乳电压(V)" prop="province"  v-if="wellType=='油基'"></el-table-column>
                <el-table-column label="油水比(O:W)" prop="province"  v-if="wellType=='油基'"></el-table-column>
                <!--end-->
                <el-table-column label="操作" width="110">
                    <template slot-scope="scope">
                        <el-button
                            icon="el-icon-edit"
                            size="medium" circle
                            style="padding:6px 6px"
                            type="success"
                            @click="onEdit(scope)"
                            >
                        </el-button>
                        <el-button
                            icon="el-icon-delete"
                            size="medium" circle
                            style="padding:6px 6px"
                            type="danger"
                            @click="onDelete(scope)"
                            >
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
          <el-dialog  width="400px"  :visible.sync="showDialog" :title="dialogTitle" :close-on-click-modal="false">
            <el-form :model="editform" label-width="100px">
                <el-form-item prop="wellNumber" label="井号：">
                    <el-input
                        style="width:90%"
                        v-model="editform.wellNumber"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="run" label="Run：">
                    <el-input
                        style="width:90%"
                        v-model="editform.run"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="layer" label="层位：">
                    <el-input
                        style="width:90%"
                        v-model="editform.layer"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="layer" label="密度：">
                    <el-input
                        style="width:90%"
                        v-model="editform.density"
                    ></el-input>
                </el-form-item>
                 <el-form-item prop="funnelViscosity" label="漏斗粘度：">
                    <el-input
                        style="width:90%"
                        v-model="editform.funnelViscosity"
                    ></el-input>
                </el-form-item>
               <el-form-item prop="CaCl2" label="氯化钙含量：">
                    <el-input
                        style="width:90%"
                        v-model="editform.CaCl2"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="cement" label="过量石灰：">
                    <el-input
                        style="width:90%"
                        v-model="editform.cement"
                    ></el-input>
                </el-form-item>
                  <el-form-item prop="sand" label="含沙：">
                    <el-input
                        style="width:90%"
                        v-model="editform.sand"
                    ></el-input>
                </el-form-item>
                 <el-form-item prop="HTHP" label="HTHP失水：">
                    <el-input
                        style="width:90%"
                        v-model="editform.HTHP"
                    ></el-input>
                </el-form-item>
               <el-form-item prop="lowDensitySolid" label="低密度固相含量%：">
                    <el-input
                        style="width:90%"
                        v-model="editform.lowDensitySolid"
                    ></el-input>
                </el-form-item>
                  <el-form-item prop="firstChop" label="静力切/初切：">
                    <el-input
                        style="width:90%"
                        v-model="editform.firstChop"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ConstantModule } from "@/store/modules/constant";
var appData = require('./data/data.json')
@Component({})
export default class extends Vue {
    filterForm={
        wellNumber:''
    };
    editform={

    }
    dialogTitle="新增记录";
    showDialog=false;
    wellType="油基";
    wellList:any[]=[];
    tableData=[];
    mounted() {
             this.getWellList()
             //this.getDrillFluidData();
    }
       
      /**
     * 获取井列表
     * */
    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }
    getDrillFluidData(){
        if(this.filterForm.wellNumber=="")return;

        let tmpData=appData.filter(obj=>obj.wellNumber==this.filterForm.wellNumber);
        if(tmpData.length==0)return;

        this.wellType=tmpData[0].wellType;
        this.tableData=tmpData;
    }
    onAddRecord(){

    }
    onFilter(){
        this.getDrillFluidData()
    }
}
</script>
