<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
              <div class="app-card-title">钻井液管理</div>
            </div>
                <el-select v-model="filterForm.wellNumber" clearable  placeholder="请选择">
                    <el-option v-for="item in wellList"  :key="item.wellNumber" :label="item.wellNumber"  :value="item.wellNumber"></el-option>
                </el-select>
                <el-button icon="el-icon-plus" type="primary" style="margin-left:20px;" @click="onFilter">查询</el-button>
                <el-row :gutter="20" style="margin-top:20px;">
                    <el-col :span="12" shadow="hover">
                        <el-card>
                           <div id="main1" style="height:360px;"></div>
                         </el-card>
                   </el-col>
                    <el-col :span="12">
                        <el-card shadow="hover">
                          <div id="main2" style="height:360px;"></div>
                        </el-card>
                    </el-col>
                </el-row>
                 <el-row :gutter="20" style="margin-top:20px;">
                    <el-col :span="12" shadow="hover">
                        <el-card>
                           <div id="main3" style="height:360px;"></div>
                         </el-card>
                   </el-col>
                    <el-col :span="12">
                        <el-card shadow="hover">
                          <div id="main4" style="height:360px;"></div>
                        </el-card>
                    </el-col>
                </el-row>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import * as echarts from 'echarts';
import { ConstantModule } from "@/store/modules/constant";
var appData = require('./data/data.json')
@Component({})
export default class extends Vue {
    filterForm={
        wellNumber:''
    };
    wellType="油基";
    wellList:any[]=[];
    tableData=[];
    mounted() {
       this.getWellList()
    }
       
      /**
     * 获取井列表
     * */
    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }
    getDrillFluidData(){
        if(this.filterForm.wellNumber=="")return;

        let tmpData=appData.filter(obj=>obj.wellNumber==this.filterForm.wellNumber);
        if(tmpData.length==0)return;
        this.wellType=tmpData[0].wellType;
        this.tableData=tmpData;
    }
    onFilter(){
        this.getDrillFluidData();
        let arr1=[],arr2=[],arr3=[],arr4=[];
        //recordTime
        for(let i=0;i<this.tableData.length;i++){
            arr1.push([this.tableData[i].recordTime,this.tableData[i].density]);
            arr2.push([this.tableData[i].recordTime,this.tableData[i].funnelViscosity]);
            arr3.push([this.tableData[i].recordTime,this.tableData[i].sand]);
            arr4.push([this.tableData[i].recordTime,this.tableData[i].density]);
        }
            this.initChart1(arr1);
            this.initChart2(arr2);
            this.initChart3(arr3);
            this.initChart4(arr4);
    }
    initChart1(data:any){
     
            var chartDom = document.getElementById('main1');
            var myChart = echarts.init(chartDom);
            var option;

            option = {
               title: {left: 'center',text: '密度'},
               tooltip: {trigger: 'axis',
                  position: function (pt) {return [pt[0], '10%'];}
               },
               xAxis: {
                 type: 'category',boundaryGap: false},
                 yAxis: {type: 'value',boundaryGap: [0, '10%']},
                 grid: {left: '1%',right: '1%',bottom: '1%',top:'10%',containLabel: true},
                 series: [
                    {
                        type: 'line',
                        smooth: 0.6,
                        symbol: 'none',
                        lineStyle: {
                            color: '#5470C6',width:2},
                        data: data
                    }
                ]
            };

            option && myChart.setOption(option);
    }
    initChart2(data:any){
            var chartDom = document.getElementById('main2');
            var myChart = echarts.init(chartDom);
            var option;

            option = {
               title: {left: 'center',text: '漏斗粘度'},
               tooltip: {trigger: 'axis',
                  position: function (pt) {return [pt[0], '10%'];}
               },
               xAxis: {
                 type: 'category',boundaryGap: false},
                 yAxis: {type: 'value',boundaryGap: [0, '10%']},
                 grid: {left: '1%',right: '1%',bottom: '1%',top:'10%',containLabel: true},
                 series: [
                    {
                        type: 'line',
                        smooth: 0.6,
                        symbol: 'none',
                        lineStyle: {
                            color: '#5470C6',width:2},
                       data: data
                    }
                ]
            };

            option && myChart.setOption(option);
    }
    initChart3(data:any){
            var chartDom = document.getElementById('main3');
            var myChart = echarts.init(chartDom);
            var option;

            option = {
               title: {left: 'center',text: '含沙'},
               tooltip: {trigger: 'axis',
                  position: function (pt) {return [pt[0], '10%'];}
               },
               xAxis: {
                 type: 'category',boundaryGap: false},
                 yAxis: {type: 'value',boundaryGap: [0, '10%']},
                 grid: {left: '1%',right: '1%',bottom: '1%',top:'10%',containLabel: true},
                 series: [
                    {
                        type: 'line',
                        smooth: 0.6,
                        symbol: 'none',
                        lineStyle: {
                            color: '#5470C6',width:2},
                      data: data
                    }
                ]
            };

            option && myChart.setOption(option);
    }
    initChart4(data:any){
            var chartDom = document.getElementById('main4');
            var myChart = echarts.init(chartDom);
            var option;

            option = {
               title: {left: 'center',text: '油水比'},
               tooltip: {trigger: 'axis',
                  position: function (pt) {return [pt[0], '10%'];}
               },
               xAxis: {
                 type: 'category',boundaryGap: false},
                 yAxis: {type: 'value',boundaryGap: [0, '10%']},
                 grid: {left: '1%',right: '1%',bottom: '1%',top:'10%',containLabel: true},
                 series: [
                    {
                        type: 'line',
                        smooth: 0.6,
                        symbol: 'none',
                        lineStyle: {
                            color: '#5470C6',width:2},
                       data: data
                    }
                ]
            };

            option && myChart.setOption(option);
    }
}
</script>
