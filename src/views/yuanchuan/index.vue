<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">远传测试</div>
            </div>
            <div class="simple-line"></div>
            <div>
                <el-input v-model="ip" placeholder="ip" style="width: 200px;"></el-input> : <el-input v-model="port" placeholder="port" style="width: 100px;"></el-input>
                <i class="el-icon-success" v-if="isConnected" style="font-size: 20px;color: green;margin-left: 16px;"></i>
                <i class="el-icon-error" style="font-size: 20px;color: red;margin-left: 16px;" v-else></i>
            </div>
            <div style="margin-top: 20px;">
                <el-radio v-model="type" label="tcp">TCP</el-radio>
                <el-radio v-model="type" label="udp">UDP</el-radio>
                <el-button type="danger" @click="disconnect" :disabled="!isConnected">断开</el-button><el-button type="primary" @click="connect" :disabled="isConnected">连接</el-button>
                <el-button type="danger" @click="clearMsg">清空数据</el-button>
            </div>
            <div>
                <h3>数据</h3>
                <p v-for="item in msgArr" v-html="item"></p>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({})
export default class extends Vue {
    private ip = '*************'
    private port = 9000
    private type = 'tcp' // tcp | udp
    private myWs:WebSocket | null = null
    private isConnected = false
    private msgArr:any[] = []
    private connect(){
        const wsPath = `ws://${this.ip}:${this.port}`
        this.myWs = new WebSocket(wsPath)
        const _this = this;
        const heartCheck:any = {
            timeout: 5000,
            timeoutObj: null,
            reset(){
                this.timeoutObj && clearInterval(this.timeoutObj);
                return this;
            },
            start(){
                this.timeoutObj = setInterval(function(){
                    _this.myWs?.send('HeartBeat')
                }, this.timeout)
            }
        }
        //监听是否连接成功
        this.myWs.onopen = ()=> {
             console.log('ws连接状态：' +this.myWs?.readyState);
             //连接成功则发送一个数据
             this.myWs?.send('连接成功');
             this.msgArr.unshift(`<span style='color:green'>连接成功`)
             this.isConnected = true
             heartCheck.reset().start();
          }
          
        //接听服务器发回的信息并处理展示
        this.myWs.onmessage = (data :any)=> {
            const msg = data.data
             if(msg === 'HeartBeat'){
                return 
             }
             this.msgArr.unshift(msg)    
         }
         
         //监听连接关闭事件
         this.myWs.onclose = ()=>{
            //监听整个过程中websocket的状态
            console.log('ws连接状态：' + this.myWs?.readyState);
            this.msgArr.unshift(`<span style='color:red'>断开成功</span>`)
            this.isConnected = false
            heartCheck.reset()
         }
         
         //监听并处理error事件
         this.myWs.onerror = function(error) {
            console.log(error);
         }
    }
    private sendMsg(msg){
        this.myWs?.send('hello')
    }
    private clearMsg(){
        this.msgArr = []
    }
    private disconnect(){
        this.myWs?.close?.()
    }
    private beforeDestroy() {
      if (this.myWs) {
        this.myWs.close?.();
        this.myWs = null;
      }
    }
}
</script>
