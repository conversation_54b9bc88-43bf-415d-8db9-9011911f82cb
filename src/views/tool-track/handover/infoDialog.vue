<template>
    <el-dialog :visible.sync="isDialogVisible" title="仪器信息">
        <el-form :model="infoForm" label-width="120px">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="品名">
                        <span>{{ infoForm.invName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="序列号">
                        <span>{{ infoForm.serialNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="接收/发出日期">
                        <span>{{ infoForm.receivedSendDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="状态">
                        <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="infoForm.status"></CustomTag>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="所在Kit箱">
                        <span>{{ infoForm.kitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="风险类型">
                        <CustomTag tagType="RISK_TYPE" :tagValue="infoForm.riskType" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="资产所属">
                        <span>{{ infoForm.owner }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="地点">
                        <span>{{ infoForm.location }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="infoForm.deviceType===16004">
                    <el-form-item label="完工日期">
                        <span>{{ infoForm.finishDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注">
                        <span>{{ infoForm.note }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>
<script>
import { apiGetDeviceInfo } from '@/api/tools';
import { riskTypeList } from '@/utils/constant';
export default{
    name: "InfoDialog",
    data(){
        return {
            isDialogVisible: false,
            riskTypeList,
            infoForm: {}
        }
    },
    methods: {
        async showDialog(info){
            await apiGetDeviceInfo({serialNumber: info.serialNumber}).then(res => {
                this.infoForm = res.data.data || {};
            })
            this.isDialogVisible = true
        }
    }
}
</script>