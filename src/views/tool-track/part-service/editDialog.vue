<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        :title="`${isAdd?'新增':'编辑'}部件信息`"
        width="900px"
    >
        <el-form ref="detailForm" :rules="formRules" :model="detailForm" label-width="80px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="品名: " prop="invName">
                        <FuzzySelect @change="onInvNameChange" :disabled="!isAdd" type="MWD_INVNAME" v-model="detailForm.invName" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="序列号: " prop="serialNumber">
                        <el-input :disabled="!isAdd" v-model="detailForm.serialNumber"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="固件版本号: " prop="versionNumber" label-width="100px">
                        <el-input v-model="detailForm.versionNumber"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="厂家: " prop="manufacturer">
                        <el-input v-model="detailForm.manufacturer"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="detailForm.invName==='Core'">
                    <el-form-item label="温度类型: " prop="tempType" label-width="90px">
                        <el-select v-model="detailForm.tempType" style="width: 100%;" clearable>
                            <el-option v-for="item in tempTypeList" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="isAdd">
                    <el-form-item label="最高温度: " prop="maxBht">
                        <InputNumber style="width: 100%;" align="left" v-model="detailForm.maxBht"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="isAdd">
                    <el-form-item label="总循环时间: " prop="totalCirculateHrs" label-width="100px">
                        <InputNumber style="width: 100%;" align="left" v-model="detailForm.totalCirculateHrs"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="isAdd">
                    <el-form-item label="总入井时间: " prop="totalInWellHrs" label-width="100px">
                        <InputNumber style="width: 100%;" align="left" v-model="detailForm.totalInWellHrs"></InputNumber>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="8" v-if="isAdd">
                    <el-form-item label="修正总入井时间: " prop="reviseTotalHours" label-width="120px">
                        <InputNumber style="width: 100%;" align="left" v-model="detailForm.reviseTotalHours"></InputNumber>
                    </el-form-item>
                </el-col> -->
                <el-col :span="8">
                    <el-form-item label="入库时间: " prop="stockInDate">
                        <el-date-picker
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.stockInDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注: " prop="note">
                        <el-input type="textarea" v-model="detailForm.note"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onConfirm">
                确 认
            </el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { Component, Prop, Vue } from "vue-property-decorator";
import { ElForm } from "element-ui/types/form";
import { apiAddComponent, apiUpdateComponent } from "@/api/mwd"
@Component({ components : { FuzzySelect } })
export default class extends Vue {
    @Prop({ default: () => [] }) tempTypeList!: any[];
    @Prop({ default: () => [] }) invNameList!: any[];
    private isDialogVisible = false;
    private dialogType = "ADD"
    private detailForm: any = {};
    get formRules() {
        return {
            invName : this.isAdd ? [{required:true, message: "请填写品名", trigger:'blur'}] : [],
            serialNumber: this.isAdd ? [{required:true, message: "请填写序列号", trigger:'blur'}] : [],
        }
    }
    get isAdd(){
        return this.dialogType === "ADD";
    }
    private getInitForm(): any {
        return {
            invName: null,
            serialNumber: null,
            versionNumber: null,
            riskType: null,
            manufacturer: null,
            tempType: null,
            maxBht: 0,
            totalCirculateHrs: 0,
            totalInWellHrs: 0,
            stockInDate: null,
            note: null
        };
    }
    private showDialog(type, row:any={}) {
        this.dialogType = type;
        if(type === 'ADD'){
            this.detailForm = {...this.getInitForm()};
            this.detailForm.invName = row?.invName || null;
            this.detailForm.invCode = row?.invCode || null;
        }else{
            this.detailForm = {...row};
        }
        this.isDialogVisible = true;
        this.$nextTick(()=>{
            (this.$refs.detailForm as ElForm).clearValidate();
        })
    }
    private onConfirm(){
        if(this.isAdd){
            apiAddComponent(this.detailForm).then(()=>{
                this.$message.success("操作成功");
                this.isDialogVisible = false;
                this.$emit("getCurrentStockList", true);
            })
        }else{
            apiUpdateComponent(this.detailForm).then(()=>{
                this.$message.success("操作成功");
                this.isDialogVisible = false;
                this.$emit("getCurrentStockList");
            })
        }
    }
    private onInvNameChange(item){
        if(item){
            this.detailForm.invName = item.invName;
            this.detailForm.invCode = item.invCode;
        }else{
            this.detailForm.invName = null;
            this.detailForm.invCode = null;
        }
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
