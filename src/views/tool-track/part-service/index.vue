<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">部件列表</div>
            <el-form :model="searchForm" label-width="40px">
                <el-row :gutter="20">
                    <el-col :span="20">
                        <el-col :span="6">
                            <el-form-item label="品名">
                                <el-autocomplete
                                    style="width: calc(100%)"
                                    v-model="searchForm.invName"
                                    :fetch-suggestions="remoteMethodInvName"
                                    placeholder="请输入内容"
                                    clearable
                                    @select="getCurrentStockList(true)"
                                    @change="getCurrentStockList(true)"
                                ></el-autocomplete>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="序列号" label-width="60px">
                                <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="getCurrentStockList(true)">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="温度类型" label-width="80px">
                                <el-select v-model="searchForm.tempTypeName" style="width: 100%;" clearable @change="getCurrentStockList(true)">
                                    <el-option v-for="item in tempTypeList" :key="item" :label="item" :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="厂家" label-width="60px">
                                <el-input style="width: calc(100%)" clearable v-model="searchForm.manufacturer" @change="getCurrentStockList(true)">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="风险类型" label-width="70px">
                                <el-select
                                    style="width: calc(100%)"
                                    v-model="searchForm.riskType"
                                    @change="getCurrentStockList(true)"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in riskTypeList"
                                        :key="item.value"
                                        :value="item.value"
                                        :label="item.label"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="入库日期" label-width="70px">
                                <el-date-picker
                                    style="width: calc(100%)"
                                    placement="bottom-start"
                                    clearable
                                    @change="getCurrentStockList(true)"
                                    value-format="yyyy-MM-dd"
                                    v-model="searchForm.stockInDaterange"
                                    class="fixed-separator-date-picker"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-col>
                    <el-col :span="4">
                        <el-button type="primary" @click="onAdd" v-if="p_PartAdd" style="float: right">
                            新增部件
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table @sort-change="onSortChange" :data="tableData" height="calc(100vh - 320px)" :header-cell-style="commmonTableHeaderCellStyle" v-loading="tableLoading">
                <el-table-column
                    prop="invName"
                    label="品名"
                    fixed="left"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                    fixed="left"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="versionNumber"
                    label="固件版本号"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="riskType"
                    label="风险类型"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="stockInDate"
                    label="入库日期"
                    align="center"
                    width="100px"
                    sortable="custom"
                >
                    <template slot-scope="scope">
                        <span :title="moreThanFourYears(scope.row.stockInDate)?'入库时间大于4年':''" :class="{'four-year-warning': moreThanFourYears(scope.row.stockInDate)}">
                            {{ scope.row.stockInDate }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="riskValue"
                    label="风险值"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="manufacturer"
                    label="厂家"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="tempType"
                    label="温度类型"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="maxBht"
                    label="最高温度(℃)"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="reviseMaxBht"
                    label="修正最高温度(℃)"
                    align="center"
                    width="130"
                ></el-table-column>
                <el-table-column
                    prop="totalCirculateHrs"
                    label="总循环时间(h)"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="totalInWellHrs"
                    label="总入井时间(h)"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="reviseTotalHours"
                    label="修正总入井时间(h)"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="serveTotalCount"
                    label="总服役次数"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="parentInvName"
                    label="母件品名"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="parentSerialNumber"
                    label="母件序列号"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="note"
                    label="备注"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="140px"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="onEdit(scope.row)"
                            type="text"
                            v-if="p_PartUpdate"
                            >编辑</el-button
                        >
                        <el-button
                            @click="onDetail(scope.row)"
                            type="text"
                            >详情</el-button
                        >
                        <router-link 
                            tag="a"
                            :to="`/mwd-tool-maintain/partservice/structure?componentId=${scope.row.componentId}`"
                            target="_blank"
                            style="color: #004e7e"
                        >
                            结构
                        </router-link>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <EditDialog :invNameList="invNameList" :tempTypeList="tempTypeList" @getCurrentStockList="getCurrentStockList" ref="editDialog" />
        <DetailDialog ref="detailDialog" />
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiGetServiceHistoryDetailList } from "@/api/tool-track";
import { apiGetInvNameFuzzyList } from "@/api/tools";
import {  apiGetTempTypeList } from "@/api/mwd"
import { riskTypeList } from "@/utils/constant";
import EditDialog from "./editDialog.vue";
import DetailDialog from "./detailDialog.vue"
import dayjs from "dayjs";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({
    components: { EditDialog, DetailDialog }
})
export default class extends Vue {
    private searchForm: any = {};
    private tableData: any[] = [];
    private tableLoading = false;
    private currentPage = 1;
    private pageSize = 50;
    private total = 0;
    private detailForm: any = {};
    private currentDetailPage = 1;
    private tempTypeList:any[] = []
    private invNameList:string[] = [];
    private riskTypeList = riskTypeList;
    get p_PartAdd(){
        return this.$checkBtnPermission(`sys:partservice:add`)
    }
    get p_PartUpdate(){
        return this.$checkBtnPermission(`sys:partservice:edit`)
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    private mounted() {
        const serialNumber = decodeURIComponent(this.$route.query.serialNumber as string || "")
        if(serialNumber){
            this.searchForm.serialNumber = serialNumber;
        }
        this.getTempTypeList();
        this.getCurrentStockList();
        this.getInvNameList();
        const { event, invName, invCode } = this.$route.query;
        if(event === 'add'){
            (this.$refs.editDialog as any).showDialog("ADD", { invName, invCode })
        }
    }
    private moreThanFourYears(targetDate){
        return dayjs(targetDate).add(4,'year').isBefore(dayjs())
    }
    getInvNameList() {
        apiGetInvNameFuzzyList(
            { toolType: "MWD" }
        ).then((res) => {
            this.invNameList = res.data?.data || [];
        });
    }
    private getTempTypeList() {
        apiGetTempTypeList({}).then(res=>{
            this.tempTypeList = res.data?.data || [];
        })
    }
    private remoteMethodInvName(queryString: any, cb) {
        apiGetInvNameFuzzyList(
            { invName: queryString, toolType: "MWD" }
        ).then((res) => {
            const data = res.data?.data || [];
            cb(data.map((item) => ({value: item.invName})));
        });
    }
    
    private async onDetail(row){
        (this.$refs.detailDialog as any).showDialog(row)
    }
    private async onStructure(row){
        this.$router.push(
            { 
                path: `/mwd-tool-maintain/partservice/structure`,
                query: {
                    componentId: row.componentId,
                } 
            })
    }
    private onEdit(row){
        (this.$refs.editDialog as any).showDialog("EDIT", row)
    }
    private onAdd(){
        (this.$refs.editDialog as any).showDialog("ADD")
    }
    private getCurrentStockList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const form = {
            invName: this.searchForm.invName || undefined,
            serialNumber: this.searchForm.serialNumber || undefined,
            tempTypeName: this.searchForm.tempTypeName || undefined,
            manufacturer: this.searchForm.manufacturer || undefined,
            riskType: this.searchForm.riskType || undefined,
            startDate: this.searchForm.stockInDaterange?.[0] || undefined,
            endDate: this.searchForm.stockInDaterange?.[1] || undefined,
            orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
            orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
        }
        this.tableLoading = true;
        apiGetServiceHistoryDetailList(form, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        }).finally(() => {
            this.tableLoading = false;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
    
    private onSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.getCurrentStockList();
    }
}
</script>
<style lang="scss" scoped>
.four-year-warning {
    color: red;
    font-weight: bold;
}
</style>
