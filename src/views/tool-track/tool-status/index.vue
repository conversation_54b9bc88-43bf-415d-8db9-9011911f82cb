<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">物料状态查询</div>
            <el-form :model="searchForm" label-width="100">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="品名">
                            <el-input
                                v-model.trim="searchForm.invName"
                                @change="getCurrentStockList(true)"
                                style="width: calc(100% - 100px)"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="序列号">
                            <el-input
                                v-model.trim="searchForm.serialNumber"
                                @change="getCurrentStockList(true)"
                                style="width: calc(100% - 100px)"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="4">
                        <el-button type="primary" @click="getCurrentStockList(true)">查询</el-button>
                    </el-col> -->
                </el-row>
            </el-form>
            <el-table :data="tableData">
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                ></el-table-column>
                <el-table-column
                    prop="warehouseName"
                    label="仓库"
                ></el-table-column>
                <el-table-column prop="binName" label="库位"></el-table-column>
                <el-table-column prop="status" label="状态">
                    <template slot-scope="{ row }">
                        <span>{{
                            toolStatusMap[row.status] || row.status
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" v-if="p_Info || p_History">
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_Info"
                            type="text"
                            @click="onClickDetail(scope)"
                            >其它信息</el-button
                        >
                        <el-button
                            v-if="p_History"
                            type="text"
                            @click="onGotoToolStatus(scope.row)"
                            >流转历史</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog
            title="其它信息"
            :visible.sync="isDetailDialogVisible"
            width="600px"
        >
            <el-form :model="detailForm" label-width="100px">
                <el-row v-if="this.infoShow != null">
                    <el-col :span="12">
                        <el-form-item label="弯度: ">
                            <span>{{
                                detailForm.camberType
                                    ? CAMBER_TYPE[detailForm.camberType]
                                    : "未知"
                            }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="扣型: ">
                            <span>{{ detailForm.claspType }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="度数: ">
                            <span>{{ detailForm.angle }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最大外径: ">
                            <span>{{ detailForm.odMax }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="耐温: ">
                            <span>{{ detailForm.endureTemperature }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="属性: ">
                            <span>{{ detailForm.mudType }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否带扶正器: ">
                            <span>{{ detailForm.takeStb }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注: ">
                            <span>{{ detailForm.comment }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div v-else>暂无数据</div>
            </el-form>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import {
    apiGetCurrentStockList,
    apiGetDetail,
    apiGetInventoryClassList,
} from "@/api/tool-track";
import { CAMBER_TYPE } from "@/utils/constant";
import router from "@/router";
import getDict from "@/utils/getDict";

@Component({})
export default class extends Vue {
    private searchForm: any = {};
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private inventoryClassList: any[] = [];
    private detailForm: any = {};
    private isDetailDialogVisible: boolean = false;
    private CAMBER_TYPE = CAMBER_TYPE;
    private toolStatusMap: any = {};
    private infoShow: any = {};
    get p_Info() {
        return this.$checkBtnPermission("sys:toolstatus:info");
    }
    get p_History() {
        return this.$checkBtnPermission("sys:toolstatus:history");
    }
    private async mounted() {
        this.getInventoryClassList();
        let [toolStatusList]: any[] = await getDict([8]);
        let tmp = {};
        toolStatusList.forEach((item) => {
            tmp[item.id] = item.name;
        });
        this.toolStatusMap = tmp;
        await this.getCurrentStockList();
    }
    private getInventoryClassList() {
        // TODO: 太多了，300多条
        apiGetInventoryClassList({}).then((res) => {
            this.inventoryClassList = res?.data?.data || [];
        });
    }
    private getCurrentStockList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const params: any = {
            invClassCode: this.searchForm.invClassCode || undefined,
            invName: this.searchForm.invName || undefined,
            invCode: this.searchForm.invCode || undefined,
            serialNumber: this.searchForm.serialNumber || undefined,
        };
        return apiGetCurrentStockList(params, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
    private onClickDetail(scope: any) {
        this.detailForm = scope.row;

        apiGetDetail({
            invCode: this.detailForm.invCode,
            serialNumber: this.detailForm.serialNumber,
        }).then((res) => {
            this.infoShow = res.data.data.takeStb;
            let content = res.data.data;

            //弯度
            this.detailForm.camberType = content.camberType;
            //扣型
            this.detailForm.claspType = content.claspType
                ? content.claspType
                : "";
            //度数
            this.detailForm.angle = content.angle ? content.angle : "";
            //最大外径
            this.detailForm.odMax = content.odMax ? content.odMax : "";
            //耐温
            this.detailForm.endureTemperature = content.endureTemperature
                ? content.endureTemperature
                : "";
            //属性
            this.detailForm.mudType = content.mudType ? content.mudType : "";
            //是否带扶正器
            this.detailForm.takeStb = content.takeStb ? content.takeStb : "";
            //备注
            this.detailForm.comment = content.comment ? content.comment : "";

            this.isDetailDialogVisible = true;
        });
        console.log();
    }

    private onGotoToolStatus(rows: any) {
        if (this.$route.path === "/tooltrack/toolstatus") {
            //仪器使用情况
            router.push(
                `/tooltrack/tooltrack?invName=${rows.invName}&invCode=${rows.invCode}&serialNumber=${rows.serialNumber}`
            );
        } else {
            router.push("/tooltrack/toolstatus");
        }
    }
}
</script>
<style lang="scss" scoped></style>
