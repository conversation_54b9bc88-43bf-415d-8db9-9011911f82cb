<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器使用统计</div>
            <el-form :model="searchForm" label-width="80px">
                <el-row>
                    <el-col :span="4">
                        <el-form-item label="仪器类型">
                            <el-select v-model.trim="searchForm.toolType">
                                <el-option
                                    v-for="item in toolTypeList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="仪器种类">
                            <el-select
                                clearable
                                :disabled="toolType !== 'UNDER_WELL'"
                                v-model="searchForm.type"
                            >
                                <el-option
                                    v-for="item in underWellToolTypeList"
                                    :key="item.typeValue"
                                    :label="item.typeLabel"
                                    :value="item.typeValue"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="品名">
                            <el-autocomplete
                                v-model.trim="searchForm.invName"
                                :fetch-suggestions="querySearchInvName"
                                clearable
                            >
                                <template slot-scope="{ item }">
                                    <div style="padding: 2px 0 4px">
                                        <span>{{ item.invName }}</span>
                                    </div>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="序列号">
                            <el-autocomplete
                                v-model.trim="searchForm.serialNumber"
                                :fetch-suggestions="querySearchSerialNumber"
                                clearable
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="时间区间">
                            <el-date-picker
                                clearable
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.daterange"
                                class="fixed-separator-date-picker"
                                style="width: 100%"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="2">
                        <el-button
                            @click="getCurrentStockList"
                            type="primary"
                            style="margin-left: 10px"
                        >
                            查询
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData">
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                ></el-table-column>
                <el-table-column
                    prop="wellNumber"
                    label="井号"
                ></el-table-column>
                <el-table-column
                    prop="circulateBht"
                    label="循环温度"
                ></el-table-column>
                <el-table-column
                    prop="maxBht"
                    label="最高温度"
                ></el-table-column>
                <el-table-column
                    prop="total"
                    label="入井时长"
                ></el-table-column>
                <el-table-column
                    prop="circulateHrs"
                    label="循环时长"
                ></el-table-column>
                <el-table-column prop="run" label="趟钻"></el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import Vue from "vue";
import { apiGetDeviceUsage } from "@/api/tool-mantain";
import { apiGetInventoryList, apiGetSerialNumberList } from "@/api/tool-track";
@Component({})
export default class extends Vue {
    private searchForm: any = {
        type: "",
        invName: "",
        serialNumber: "",
        startTime: "",
        endTime: "",
    };
    private tableData: any[] = [];
    private toolTypeList: any[] = [
        {
            value: "MWD",
            label: "MWD",
        },
        {
            value: "UNDER_WELL",
            label: "井下工具",
        },
    ];
    private underWellToolTypeList: any[] = [
        {
            typeValue: "drill",
            typeLabel: "钻头",
        },
        {
            typeValue: "screw",
            typeLabel: "螺杆",
        },
        {
            typeValue: "kn",
            typeLabel: "震击器",
        },
        {
            typeValue: "wp",
            typeLabel: "水利",
        },
    ];
    get toolType() {
        return this.searchForm.toolType;
    }
    @Watch("toolType")
    onToolTypeChange() {
        this.searchForm.type = "";
    }

    private mounted() {
        const { invName, serialNumber, startTime, endTime } =
            this.$route.query || {};
        this.searchForm.invName = invName;
        this.searchForm.serialNumber = serialNumber;
        this.searchForm.daterange = [startTime, endTime];
    }
    private querySearchInvName(invName: string, cb: any) {
        const params: any = { invName: invName || "" };
        apiGetInventoryList(params).then((response) => {
            let list = response.data?.data || [];
            list = list.map((item) => {
                item.value = item.invName;
                return item;
            });
            cb(list);
        });
    }

    private querySearchSerialNumber(queryString: string, cb: any) {
        apiGetSerialNumberList({
            serialNumber: queryString,
        }).then((res) => {
            let list: any[] = res.data?.data || [];
            cb(
                list.map((item) => {
                    return { value: item };
                })
            );
        });
    }

    private getCurrentStockList() {
        if (
            this.searchForm.invName === "" ||
            this.searchForm.serialNumber === ""
        ) {
            this.$message.error("请填写品名, 序列号, 时间");
            return;
        }
        if (
            this.searchForm.toolType === "UNDER_WELL" &&
            !this.searchForm.type
        ) {
            this.$message.error("请选择仪器种类！");
            return;
        }
        const daterange = this.searchForm.daterange;
        if (daterange?.length) {
            this.searchForm.startTime = daterange[0];
            this.searchForm.endTime = daterange[1];
        } else {
            this.searchForm.startTime = undefined;
            this.searchForm.endTime = undefined;
        }
        const params: any = {
            invName: this.searchForm.invName || "",
            serialNumber: this.searchForm.serialNumber || "",
            type: this.searchForm.type || undefined,
            toolType: this.searchForm.toolType || "",
            startTime: this.searchForm.startTime || "",
            endTime: this.searchForm.endTime || "",
        };
        apiGetDeviceUsage(params).then((res) => {
            this.tableData = res.data.data || [];
        });
    }
}
</script>
<style lang="scss" scoped></style>
