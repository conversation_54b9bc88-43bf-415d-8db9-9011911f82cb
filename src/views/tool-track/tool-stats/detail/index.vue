<template>
    <div class="app-container">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">
                    <span>{{ getDeviceTypeStr(this.deviceType) }}</span>
                    <el-button
                        v-if="p_AddResource"
                        @click="onAdd"
                        type="primary"
                        icon="el-icon-plus"
                        style="margin-left:10px"
                    >
                        添加
                    </el-button>
                </div>
                <router-link
                    to="/tooltrack/toolstats"
                    style="float: right; color: blue; font: 15px normal"
                >
                    <i class="el-icon-back"></i> 返回
                </router-link>
            </div>
            <el-form :model="searchForm" label-width="auto" style="margin-top:10px">
                <el-form-item label="序列号：">
                    <el-input v-model="searchForm.serialNumber" @change="getWarehouseToolList(true)" style="width:200px"></el-input>
                </el-form-item>
            </el-form>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="tableData"
                row-key="id"
                stripe
                highlight-current-row
            >
                <el-table-column prop="deviceType" label="仪器类型	">
                    <template slot-scope="scope">
                        {{ getDeviceTypeStr(scope.row.deviceType) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="invName"
                    label="仪器名称"
                ></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                ></el-table-column>
                <el-table-column prop="status" label="状态"></el-table-column>
                <el-table-column
                    prop="receivedDate"
                    label="接收日期"
                ></el-table-column>
                <el-table-column
                    prop="quantity"
                    label="规格/配置"
                ></el-table-column>
                <el-table-column prop="location" label="地点"></el-table-column>
                <el-table-column prop="owner" label="所属"></el-table-column>
                <el-table-column prop="notes" label="备注"></el-table-column>
                <el-table-column
                    v-if="p_EditResource || p_DeleteResource"
                    label="操作"
                    width="180"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_EditResource"
                            @click="onEdit(scope)"
                            type="success"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-edit"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-if="p_DeleteResource"
                            @click="onDelete(scope)"
                            type="danger"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog
            :title="dialogTitle"
            :close-on-click-modal="false"
            :visible.sync="isDialogVisible"
            width="600px"
        >
            <el-form
                :model="form"
                ref="form"
                :rules="resourceRules"
                label-width="140px"
                :inline="false"
                size="normal"
            >
                <el-form-item prop="deviceType" label="仪器类型">
                    <el-select style="width: 90%" v-model="form.deviceType">
                        <el-option
                            v-for="item in deviceTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="invName" label="仪器名称">
                    <el-input
                        style="width: 90%"
                        v-model="form.invName"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="serialNumber" label="序列号：">
                    <el-input
                        style="width: 90%"
                        v-model="form.serialNumber"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="status" label="状态：">
                    <el-input
                        style="width: 90%"
                        v-model="form.status"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="receivedDate" label="接收时间：">
                    <el-date-picker
                        placement="bottom-start"
                        style="width: 90%"
                        v-model="form.receivedDate"
                        type="date"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item prop="quantity" label="规格/配置：">
                    <el-input
                        style="width: 90%"
                        v-model="form.quantity"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="location" label="地点：">
                    <el-input
                        style="width: 90%"
                        v-model="form.location"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="owner" label="所属：">
                    <el-input
                        style="width: 90%"
                        v-model="form.owner"
                    ></el-input>
                </el-form-item>
                <el-form-item prop="notes" label="备注：">
                    <el-input
                        type="textarea"
                        style="width: 90%"
                        v-model="form.notes"
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm"> 确认 </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { Form as ElForm } from "element-ui";
import {
    apiAddWarehouseToolList,
    apiUpdateWarehouseToolList,
    apiDeleteWarehouseToolList,
} from "@/api/warehouse";
import getDict from "@/utils/getDict";
import { apiGetToolByDeviceType } from "@/api/tool-track";
const statusMap = {
    atRig: "上井",
    ready: "车间可用",
    repair: "维修",
    other: "其它",
};
@Component({
    name: "ToolStatsDetail",
})
export default class extends Vue {
    private searchForm:any = {serialNumber:""};
    private deviceType: number|null = null;
    private tableData: any[] = [];
    private categoryList: any[] = [];
    private isDialogVisible: boolean = false;
    private dialogTitle: string = "";
    private form: any = this.getInitForm();
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private resourceRules: any = {};
    private deviceTypeList: any[] = [];
    // 权限
    get p_AddResource() {
        return this.$checkBtnPermission("sys:tooltrack:toolstats:detail:add");
    }
    get p_EditResource() {
        return this.$checkBtnPermission("sys:tooltrack:toolstats:detail:edit");
    }
    get p_DeleteResource() {
        return this.$checkBtnPermission("sys:tooltrack:toolstats:detail:delete");
    }
    async created() {
        this.deviceType = Number(this.$route.params.tool);
        this.status = this.$route.params.type;
        [this.deviceTypeList] = await getDict([19]);
        this.getWarehouseToolList();
    }
    private getInitForm() {
        return {
            deviceType: this.deviceType,
            invName: "",
            serialNumber: "",
            status: "",
            receivedDate: "",
            quantity: "",
            location: "",
            owner: "",
            notes: "",
        };
    }
    private getDeviceTypeStr(deviceType) {
        return this.deviceTypeList.find((item) => item.id == deviceType)?.name || "";
    }
    private getWarehouseToolList(init=false) {
        if(init){
            this.currentPage = 1;
        }
        this.searchForm.serialNumber = this.searchForm.serialNumber || undefined;
        return apiGetToolByDeviceType(
            { deviceType: this.deviceType, status: statusMap[this.status] || null,...this.searchForm },
            { current: this.currentPage, size: this.pageSize }
        ).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getWarehouseToolList();
    }
    private onConfirm() {
        (this.$refs.form as ElForm).validate(async (valid) => {
            try {
                if (this.dialogTitle === "新增仪器") {
                    await apiAddWarehouseToolList(this.form);
                } else {
                    await apiUpdateWarehouseToolList(this.form);
                }
                await this.getWarehouseToolList();
                this.$message.success("操作成功！");
                this.isDialogVisible = false;
            } catch {
                this.$message.error("操作失败，请重试！");
            }
        });
    }
    private onCancel() {
        this.isDialogVisible = false;
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除该仪器？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                const form = new FormData();
                form.append("deviceId", scope.row.deviceId);
                apiDeleteWarehouseToolList(form)
                    .then(async () => {
                        await this.getWarehouseToolList();
                        this.$message.success("删除成功！");
                    })
                    .catch((err) => {
                        this.$message.error(err.msg);
                    });
            })
            .catch((err) => {});
    }
    private async onEdit(scope: any) {
        this.dialogTitle = "编辑仪器信息";
        this.form = scope.row;
        this.isDialogVisible = true;
    }

    private async onAdd() {
        this.dialogTitle = "新增仪器";
        this.form = this.getInitForm();
        this.isDialogVisible = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }
}
</script>
