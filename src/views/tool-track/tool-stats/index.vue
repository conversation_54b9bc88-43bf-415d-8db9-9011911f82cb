<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="loading">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                <span>仓库盘点</span>
                <el-button style="float:right" type="primary" @click="onExport">
                    导出Excel
                </el-button>
            </div>
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 20px"
                v-if="showType === 'TABLE'"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column label="工具/仪器类型" prop="deviceType">
                    <template slot-scope="{row}">
                        {{getDeviceTypeStr(row.deviceType)}}
                    </template>
                </el-table-column>

                <el-table-column label="车间可用" prop="ready">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/tooltrack/toolstats/${row.deviceType}/ready`"
                            style="cursor: pointer; color: blue"
                            v-if="p_detail"
                        >
                            {{ row.ready }}
                        </router-link>
                        <span v-else>{{ row.ready }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="上井" prop="atRig">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/tooltrack/toolstats/${row.deviceType}/atRig`"
                            style="cursor: pointer; color: blue"
                            v-if="p_detail"
                        >
                            {{ row.atRig }}
                        </router-link>
                        <span v-else>{{ row.atRig }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="维修" prop="repair">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/tooltrack/toolstats/${row.deviceType}/repair`"
                            style="cursor: pointer; color: blue"
                            v-if="p_detail"
                        >
                            {{ row.repair }}
                        </router-link>
                        <span v-else>{{ row.repair }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="其它" prop="other">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/tooltrack/toolstats/${row.deviceType}/other`"
                            style="cursor: pointer; color: blue"
                            v-if="p_detail"
                        >
                            {{ row.other }}
                        </router-link>
                        <span v-else>{{ row.other }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="总计" prop="sum">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/tooltrack/toolstats/${row.deviceType}/sum`"
                            style="cursor: pointer; color: blue"
                            v-if="p_detail"
                        >
                            {{ row.sum }}
                        </router-link>
                        <span v-else>{{ row.sum }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div
                class="chart-container"
                style="display: flex; flex-wrap: wrap"
                v-if="showType === 'PIE'"
            >
                <el-card
                    class="chart"
                    style="
                        width: 30%;
                        padding-bottom: 25%;
                        height: 0;
                        margin: 0 3% 3% 0;
                        position: relative;
                    "
                    v-for="item in tableData"
                    :key="item.deviceType"
                >
                    <div
                        :id="item.deviceType + 'chart'"
                        :ref="item.deviceType + 'chart'"
                        style="
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            left: 0;
                        "
                    ></div>
                </el-card>
            </div>
            <div
                class="stack-bar"
                style="width: 100%; height: 600px"
                v-if="showType === 'STACK_BAR'"
            >
                <div
                    id="stackbar"
                    ref=""
                    style="width: 100%; height: 100%"
                ></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { getPieOption, getStackBarOption } from "./option";
import * as echarts from "echarts";
import { apiGetToolByDeviceTypeList, apiExportWarehouse } from "@/api/tool-track";
import getDict from "@/utils/getDict";
@Component({})
export default class extends Vue {
    private loading = false;
    private showType = "TABLE"; //TABLE PIE STACK_BAR
    private tableData: any[] = [];
    private deviceTypeList: any[] = [];
    get p_detail() {
        return this.$checkBtnPermission("sys:tooltrack:toolstats:detail:view");
    }
    @Watch("showType")
    onWatchShowType(nv) {
        switch (nv) {
            case "TABLE":
            case "PIE":
                this.$nextTick(() => {
                    this.drawPieCharts();
                });
            case "STACK_BAR":
                this.$nextTick(() => {
                    this.drawStackBarChart();
                });
        }
    }
    async mounted() {
        [this.deviceTypeList] = await getDict([19])
        this.getToolList();
        window.addEventListener("resize", this.refreshCharts);
    }
    private onExport(){
        this.loading = true;
        apiExportWarehouse({}).then(res=>{
            if(!res.data){
                this.$message.error("暂无数据")
                return
            }
            const blob = new Blob([res.data], {
                type: "application/word;charset=UTF-8",
            });
            const fileName = `仪器盘点.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);
        }).finally(()=>{
            this.loading = false;
        })
    }
    getDeviceTypeStr(type){
        return this.deviceTypeList.find(item=>item.id==type)?.name||''
    }
    private getToolList() {
        apiGetToolByDeviceTypeList({nameList:this.deviceTypeList.map(item=>item.id)}).then((res) => {
            this.tableData = res.data?.data || [];
        });
    }
    private refreshCharts() {
        const chartRefList = [
            "螺杆chart",
            "近钻头chart",
            "水力振荡器chart",
            "绝缘短节chart",
            "stackbar",
        ];
        chartRefList.forEach((item) => {
            if (this[item]) {
                this[item].resize();
            }
        });
        // const chartDoms: any = document.querySelectorAll(
        //     chartRefList.map((item) => "#" + item).join(",")
        // );
        // console.log(chartDoms);
        // chartDoms.forEach((dom) => {
        //     if (dom) {
        //         echarts.init(dom).resize();
        //     }
        // });
    }
    private drawPieCharts() {
        this.tableData.forEach((item) => {
            const deviceType = item.deviceType;
            const chartDom = document.getElementById(
                `${deviceType}chart`
            ) as HTMLElement;
            if (!chartDom) {
                return;
            }
            const chart = echarts.init(chartDom);
            this[`${deviceType}chart`] = chart;
            const option = getPieOption();
            option.title.text = item.deviceType;
            option.title.subtext = item.total;
            option.series[0].data = [
                {
                    name: "车间可用",
                    value: item.available,
                },
                {
                    name: "在用",
                    value: item.occupied,
                },
                {
                    name: "维修",
                    value: item.maintain,
                },
            ];
            chart.setOption(option);
        });
    }
    private drawStackBarChart() {
        const chartDom = document.getElementById("stackbar") as HTMLElement;
        if (!chartDom) {
            return;
        }
        const chart = echarts.init(chartDom);
        this.stackbar = chart;
        const option = getStackBarOption();
        option.yAxis.data = this.tableData.map((item) => item.deviceType);
        option.series = [
            {
                name: "车间可用",
                type: "bar",
                stack: "total",
                label: {
                    show: true,
                },
                emphasis: {
                    focus: "series",
                },
                data: this.tableData.map((item) => item.available),
            },
            {
                name: "在用",
                type: "bar",
                stack: "total",
                label: {
                    show: true,
                },
                emphasis: {
                    focus: "series",
                },
                data: this.tableData.map((item) => item.occupied),
            },
            {
                name: "维修",
                type: "bar",
                stack: "total",
                label: {
                    show: true,
                },
                emphasis: {
                    focus: "series",
                },
                data: this.tableData.map((item) => item.maintain),
            },
            // 堆叠顶部显示总数
            {
                name: "总计",
                type: "bar",
                stack: "",
                label: {
                    normal: {
                        show: true,
                        position: "right",
                    },
                },
                z: -1,
                barGap: "-100%",
                emphasis: {
                    focus: "series",
                },
                data: this.tableData.map((item) => item.total),
            },
        ];
        option.legend.data = ["车间可用", "在用", "维修"];
        chart.setOption(option);
    }
    private beforeDestroy() {
        window.removeEventListener("resize", this.refreshCharts);
    }
}
</script>
<style lang="scss" scoped>
.el-dropdown-link {
    color: blue;
    cursor: pointer;
}
.switch-dropdown {
    .active {
        color: blue;
    }
    svg {
        margin-right: 6px;
    }
}
</style>
