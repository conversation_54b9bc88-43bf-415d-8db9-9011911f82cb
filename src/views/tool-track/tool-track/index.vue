<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器流转历史</div>
            <el-form :model="searchForm">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="品名" label-width="40px">
                            <el-input
                                v-model.trim="searchForm.invName"
                                @change="getCurrentStockList(true)"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="序列号" label-width="70px">
                            <el-input
                                v-model.trim="searchForm.serialNumber"
                                @change="getCurrentStockList(true)"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-button
                            type="primary"
                            @click="onGetNewData"
                            style="margin-left: 20px"
                        >
                            同步ERP数据
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData">
                <el-table-column
                    prop="docNo"
                    label="调拨单号"
                ></el-table-column>
                <el-table-column
                    prop="docName"
                    label="单据类型"
                ></el-table-column>
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                ></el-table-column>
                <el-table-column
                    prop="invStd"
                    label="规格"
                ></el-table-column>
                <el-table-column
                    prop="fromWarehouseName"
                    label="From仓库"
                ></el-table-column>
                <el-table-column
                    prop="fromBinName"
                    label="From库位"
                ></el-table-column>
                <el-table-column
                    prop="toWarehouseName"
                    label="To仓库"
                ></el-table-column>
                <el-table-column
                    prop="toBinName"
                    label="To库位"
                ></el-table-column>
                <el-table-column width="100px" prop="toStatus" label="状态变化">
                    <template slot-scope="scope">
                        {{ getToolStatus(scope.row.toStatus) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="residenceTime"
                    label="停留时间"
                ></el-table-column>
                <el-table-column
                    width="180px"
                    prop="approveDate"
                    label="审批时间"
                ></el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import {
    apiGetDeviceHistoryList,
    apiGetInventoryClassList,
} from "@/api/tool-track";
import getDict from "@/utils/getDict";
import { apiSyncAllData, apiSyncData, apiSyncGroupData } from "@/api/erp";
@Component({})
export default class extends Vue {
    private searchForm: any = {
        invClassCode: "",
        invName: "",
        invCode: "",
        serialNumber: "",
    };
    private tableData: any[] = [];
    private inventoryClassList: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private toolStatusDict: any[] = [];
    private async mounted() {
        //参数
        if (this.$route.query) {
            this.searchForm.invName = this.$route.query.invName;
            this.searchForm.invCode = this.$route.query.invCode;
            this.searchForm.serialNumber = this.$route.query.serialNumber;
        }
        this.getCurrentStockList(true);
        this.getInventoryClassList();
        [this.toolStatusDict] = await getDict([8]);
    }
    private getToolStatus(id: any) {
        return this.toolStatusDict.find((item) => item[id])?.name || id;
    }
    private getInventoryClassList() {
        // TODO: 太多了，300多条
        apiGetInventoryClassList({}).then((res) => {
            this.inventoryClassList = res?.data?.data || [];
        });
    }
    onGetNewData() {
        let form = new FormData() as any;
        form.append("taskGroup", [
            "ERP_INVENTORY_SYNC_TASK",
            "ERP_TRANSFER_DOC_SYNC_TASK",
            "ERP_WAREHOUSE_SYNC_TASK",
            "ERP_BIN_SYNC_TASK",
        ]);
        apiSyncGroupData(form)
            .then((res) => {
                // let {data,message,code}=res;
                if (res.data.code == 200) {
                    this.$message.success("同步成功");
                    this.getCurrentStockList(true);
                }
            })
            .catch((err) => {});
    }
    private getCurrentStockList(flag = false) {
        let params: any = {};
        if (flag) {
            this.currentPage = 1;
        }
        params = {
            invClassCode: this.searchForm.invClassCode || undefined,
            invName: this.searchForm.invName || undefined,
            invCode: this.searchForm.invCode || undefined,
            serialNumber: this.searchForm.serialNumber || undefined,
        };
        apiGetDeviceHistoryList(params, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
}
</script>
<style lang="scss" scoped></style>
