/* 
    node    group   -> shape
                    -> keyShape
    edge    group   -> shape
*/
// enum
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 200;
const BOX_HEIGHT = 50;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#5b8ff9';
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "red"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;

// TODO: edge方向
export const dataGenerator = (data) => {
    const stdData = handleData1(data);
    console.log(stdData);
    const nodes = [];
    const edges = [];
    const rowLen = stdData.length;

    let curNodeIndex = 0;

    for (let i = 0; i < rowLen; i++) {
        const rowData = stdData[i];
        for (let j = 0; j < rowData.length; j++) {
            const curNodeId = `node-${curNodeIndex}`;
            const prevNodeId = `node-${curNodeIndex-1}`
            const rowItem = rowData[j];
            nodes.push({ 
                ...rowItem,
                id: curNodeId,
                x: INIT_X + (BOX_WIDTH + COL_GUTTER) * j,
                y: INIT_Y + (BOX_HEIGHT + ROW_GUTTER) * (rowLen - i),
                anchorPoints: [ANCHOR.LEFT, ANCHOR.RIGHT]
            });
            if(!rowItem.isStart){
                edges.push({ 
                    ...rowItem,
                    label: `${prevNodeId} - ${rowItem.status} - ${curNodeId}`,
                    source: prevNodeId,
                    target: curNodeId,
                    sourceAnchor: 1,
                    targetAnchor: 0,
                });
            }
            curNodeIndex++;
        }
    }
    return { nodes, edges }
}

export const defaultNodeGenerator = (cfg, group) => {
    const { isStart, isEnd } = cfg;
    // 从内到外add
    const point_offset = 2 * BOX_BORDER_WIDTH - POINT_RADIUS / 2;
    const point_cfg = {
        r: POINT_RADIUS,
        fill: POINT_COLOR,
        stroke: POINT_COLOR,
    }
    
    if(!isStart){
        // 左侧
        group.addShape('circle', {
            attrs: {
                x: -point_offset,
                y: BOX_HEIGHT / 2,
                ...point_cfg
            },
            name: 'default-start-point'
        })
    }
    if(!isEnd){
        // 右侧
        group.addShape('circle', {
            attrs: {
                x: BOX_WIDTH + point_offset,
                y: BOX_HEIGHT / 2,
                ...point_cfg
            },
            name: 'default-end-point'
        })
    }
    /* 
        keyshape
            anchorPoints(边的两端)相对于这个
            group中的其它shape的位置都基于它, 所以它的(x, y)应该取原点
    */ 
    const node = group.addShape('rect', {
        attrs: {
            x: 0,
            y: 0,
            width: BOX_WIDTH,
            height: BOX_HEIGHT,
            fill: BOX_FILL_COLOR,
            lineWidth: BOX_BORDER_WIDTH,
            stroke: BOX_BORDER_COLOR,
            ...(cfg.style || {}),
        },
        name: 'key-rect-shape'
    })
    return node
}

/* 
    TODO:

    highlight   ✔
    arrow       ✔
    cursor      ✔
    click       ✔
    hover       ✔
    long text
    contextmenu ✔     
    animation   ✔
    要关联的cfg
*/
export const defaultEdgeGenerator = (cfg, group) => {
    const { startPoint, endPoint } = cfg;
    const { x: startX, y: startY } = startPoint;
    const { x: endX, y: endY } = endPoint;
    // label
    const label_font_size = 14;
    const label_offset_y = label_font_size / 2 + 4; // 4 是label和线的间距
    group.addShape('text', {
        attrs: {
            text: cfg.label,
            x: (startX + endX) / 2,
            y: (startY + endY) / 2 - label_offset_y, // 在线中间的上方
            fontSize: label_font_size,
            textAlign: 'center',
            textBaseline: 'middle',
            cursor: 'pointer',
            fill: 'red',
            position: 'middle',
        },
        name: 'default-edge-text-shape',
    });
    // line
    let edge;
    const edge_cfg = {
        lineWidth: LINE_WIDTH,
        stroke: LINE_COLOR,
    }
    if(cfg.isCycleStart){
        // s形线
        const sPath = [
            ['M', startX, startY],
            ['C', startX + 200, startY - 100, endX - 200, endY + 100, endX, endY], // TODO: 贝塞尔曲线通用策略
        ];
        edge = group.addShape('path', {
            attrs: {
                path: sPath,
                lineDash: [10, 5],
                ...edge_cfg,
            },
            name: 's-path-shape',
        });
    }else{
        // 直线
        edge = group.addShape('path', {
            attrs: {
                path: [['M', startX, startY], ['L', endX, endY]],
                ...edge_cfg,
            },
            name: 'l-path-shape',
        });
    }
    return edge;
}

/* 
    [{status}] -> [[{status, isStart, isEnd, isCycleStart, isCycleEnd}]]
*/
function handleData(data){
    const ret = [];
    let curRowIndex = 0;
    let curRowData = [];
    for (let i = 0, len = data.length; i < len; i++) {
        const item = { ...data[i] };
        const isStart = i === 0;
        const isEnd = i === len - 1;
        const isReady = item.status === 'READY';
        item.isStart = isStart;
        item.isEnd = isEnd;
        if(isReady){
            curRowData.push({ ...item, isCycleEnd: !isStart });
            if(!isEnd&&!isStart){
                ret.push(curRowData);
                curRowIndex++;
                curRowData = [];
                curRowData.push({ ...item, isCycleStart: true });
            }
        }else{
            curRowData.push({ ...item });
        }
        if(isEnd){
            ret.push(curRowData);
        }
    }
    return ret;
}
export const testData = [
    {status: 'READY'}, {status: 'ATRIG'}, {status: 'REPAIR'},
    {status: 'READY'}, {status: 'ATRIG'},
    {status: 'READY'}, {status: 'REPAIR'},
    {status: 'READY'}
]


function handleData1(data){
    const ret = [];
    let curRowIndex = 0;
    let curRowData = [];
    for (let i = 0, len = data.length; i < len; i++) {
        const item = { ...data[i] };
        const isStart = i === 0;
        const isEnd = i === len - 1;
        const isReady = item.status === 19003;
        item.isStart = isStart;
        item.isEnd = isEnd;
        if(isReady){
            curRowData.push({ ...item, isCycleEnd: !isStart });
            if(!isEnd&&!isStart){
                ret.push(curRowData);
                curRowIndex++;
                curRowData = [];
                curRowData.push({ ...item, isCycleStart: true });
            }
        }else{
            curRowData.push({ ...item });
        }
        if(isEnd){
            ret.push(curRowData);
        }
    }
    return ret;
}
export const testData2 = [
    {
      "historyId": 66,
      "deviceId": 1616,
      "status": 19003,
      "location": "Chengdu Shop",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-07 09:53:24.0",
      "circulateType": "HANDOVER",
      "businessNumber": null,
      "businessId": 66,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:53:24.000+00:00",
      "updateTime": "2025-05-07T01:53:24.000+00:00"
    },
    {
      "historyId": 65,
      "deviceId": 1616,
      "status": 19002,
      "location": "Chengdu Shop",
      "jobId": 283,
      "reportCoverId": null,
      "wellNumber": "测试井1",
      "date": "2025-05-07 09:52:43.0",
      "circulateType": "WORK_ORDER_MWD",
      "businessNumber": "流转测试_wlj_0507_1",
      "businessId": 6055,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:52:43.000+00:00",
      "updateTime": "2025-05-07T01:52:43.000+00:00"
    },
    {
      "historyId": 64,
      "deviceId": 1616,
      "status": 19002,
      "location": "Chengdu Shop",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-07 09:52:16.0",
      "circulateType": "REPAIR",
      "businessNumber": "RPMWD20250507095116",
      "businessId": 133,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:52:16.000+00:00",
      "updateTime": "2025-05-07T01:52:16.000+00:00"
    },
    {
      "historyId": 63,
      "deviceId": 1616,
      "status": 19001,
      "location": "测试井1",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-07 09:47:23.0",
      "circulateType": "WAREHOUSE",
      "businessNumber": null,
      "businessId": 41,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:47:23.000+00:00",
      "updateTime": "2025-05-07T01:47:23.000+00:00"
    },
    {
      "historyId": 61,
      "deviceId": 1616,
      "status": 19003,
      "location": "Chengdu Shop",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-07 09:46:13.0",
      "circulateType": "HANDOVER",
      "businessNumber": null,
      "businessId": 65,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:46:13.000+00:00",
      "updateTime": "2025-05-07T01:46:13.000+00:00"
    },
    {
      "historyId": 60,
      "deviceId": 1616,
      "status": 19001,
      "location": "测试井1",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": "",
      "date": "2025-05-07 09:45:01.0",
      "circulateType": "WORK_ORDER_MWD",
      "businessNumber": "流转测试_wlj_0507",
      "businessId": 6054,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-07T01:45:01.000+00:00",
      "updateTime": "2025-05-07T01:45:01.000+00:00"
    },
    {
      "historyId": 51,
      "deviceId": 1616,
      "status": 19001,
      "location": "测试井1",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-06 13:11:12.0",
      "circulateType": "DEMAND_ORDER",
      "businessNumber": "RQ20250506131047",
      "businessId": 6,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-06T05:11:12.000+00:00",
      "updateTime": "2025-05-06T05:11:12.000+00:00"
    },
    {
      "historyId": 50,
      "deviceId": 1616,
      "status": 19003,
      "location": null,
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-06 11:09:30.0",
      "circulateType": "WAREHOUSE",
      "businessNumber": null,
      "businessId": 41,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-06T03:09:30.000+00:00",
      "updateTime": "2025-05-06T03:09:30.000+00:00"
    },
    {
      "historyId": 47,
      "deviceId": 1616,
      "status": 19001,
      "location": "煤层气项目",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-06 11:03:24.0",
      "circulateType": "TRANSFER_ORDER",
      "businessNumber": "T20250506093952048",
      "businessId": 6,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-06T03:03:24.000+00:00",
      "updateTime": "2025-05-06T03:03:24.000+00:00"
    },
    {
      "historyId": 46,
      "deviceId": 1616,
      "status": 19001,
      "location": "测试井1",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-06 11:01:48.0",
      "circulateType": "DEMAND_ORDER",
      "businessNumber": "RQ20250506110121",
      "businessId": 5,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-06T03:01:48.000+00:00",
      "updateTime": "2025-05-06T03:01:48.000+00:00"
    },
    {
      "historyId": 44,
      "deviceId": 1616,
      "status": 19001,
      "location": "测试井1",
      "jobId": null,
      "reportCoverId": null,
      "wellNumber": null,
      "date": "2025-05-06 10:52:34.0",
      "circulateType": "DEMAND_ORDER",
      "businessNumber": "RQ20250506094306",
      "businessId": 4,
      "circulateHrs": null,
      "maxBht": null,
      "inWellHour": null,
      "createTime": "2025-05-06T02:52:34.000+00:00",
      "updateTime": "2025-05-06T02:52:34.000+00:00"
    }
  ]