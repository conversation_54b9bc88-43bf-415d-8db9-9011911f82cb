<template>
    <div id="container">

    </div>
</template>
<script>
import G6 from '@antv/g6';
import { defaultNodeGenerator, defaultEdgeGenerator, dataGenerator, testData } from './utils'
/* 
  data: {
    nodes: [nodesCfg],
    edges: [edgesCfg],
  } 
*/

export default {
    data(){
        return {}
    },
    mounted() {
        const container = document.getElementById('container');
        const width = container.scrollWidth;
        const height = container.scrollHeight || 800;
        
        G6.registerNode('default-node', {
          // drawShape: defaultNodeGenerator,
          drawShape: function drawShape(cfg, group) {
      const style = {
        x: 0,
        y: 0,
        r: 50,
        ...cfg.style
      };
      const shape = group.addShape('circle', {
        attrs: style,
        // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
        name: 'key-shape',
      });
      // 绘制节点里面的小圆。点击这个小圆会显示tooltip
      group.addShape('circle', {
        attrs: {
          x: 0,
          y: -30,
          r: 10,
          fill: '#096dd9',
          cursor: 'pointer',
        },
        // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
        name: 'circle-shape',
      });
      return shape;
    },
        });
        G6.registerEdge('default-edge', {
          draw: defaultEdgeGenerator,
          // animation
          // afterDraw(cfg, group) {
          //   const shape = group.get('children')[1];
          //   let index = 0;
          //   shape.animate(
          //     () => {
          //       index++;
          //       if (index > 9) {
          //         index = 0;
          //       }
          //       const res = {
          //         lineDash: [10, 5],
          //         lineDashOffset: -index,
          //       };
          //       // returns the modified configurations here, lineDash and lineDashOffset here
          //       return res;
          //     },
          //     {
          //       repeat: true, // whether executes the animation repeatly
          //       duration: 10000, // the duration for executing once
          //     },
          //   );
          // },
        });
        // tooltip
        const tooltip = new G6.Tooltip({
          offsetX: 10,
          offsetY: 10,
          itemTypes: ['edge'],
          getContent: e => {
            const dom = document.createElement('div');
            dom.style.width = 'fit-content';
            const model = e.item.getModel();
            dom.innerText = model.label;
            return dom;
          },
          // 在这里判断要不要显示tooltip
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        })
        // contextmenu
        const contextmenu = new G6.Menu({
          getContent(evt) {
            return `<div style="cursor: pointer">${evt.item.getType()}</div>`;
          },
          handleMenuClick: (target, item) => {
            console.log(target, item.getModel());
          },
          offsetX: 16 + 10,
          offsetY: 0,
          // 在哪些类型的元素上响应
          itemTypes: ['edge'],
          // 在这里判断要不要显示contextmenu
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        });
        // graph
        const graph = new G6.Graph({
          container: 'container',
          width,
          height,
          fitCenter: true,
          plugins: [tooltip, contextmenu],
          modes: {
            default: ['zoom-canvas', 'drag-canvas'],
          },
          defaultNode: {
            type: 'default-node',
            style: {
              fill: '#DEE9FF',
              stroke: '#5B8FF9',
            },
            labelCfg: {
              style: {
                fontSize: 12,
              },
            },
          },
          nodeStateStyles: {
            selected: {
              stroke: 'red',
            },
          },
          defaultEdge: {
            type: 'default-edge',
          },
        });
        graph.on('node:click', function (event) {
          const { item } = event;
          graph.setItemState(item, 'selected', true);
          console.log(event)
        });
        graph.on('default-edge-text-shape:click', (evt)=>{
          console.log(evt)
        })
        graph.data(dataGenerator(testData));
        graph.render();
        if (typeof window !== 'undefined')
          window.onresize = () => {
            if (!graph || graph.get('destroyed')) return;
            if (!container || !container.scrollWidth || !container.scrollHeight) return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };

    }
}
</script>