<template>
    <div id="container">

    </div>
</template>
<script>
import G6 from '@antv/g6';
import { defaultNodeGenerator, defaultEdgeGenerator, dataGenerator, testData, testData2 } from './utils'
/* 
  data: {
    nodes: [nodesCfg],
    edges: [edgesCfg],
  } 
*/
export default {
    data(){
        return {}
    },
    mounted() {
        const container = document.getElementById('container');
        const width = container.scrollWidth;
        const height = container.scrollHeight || 800;
        
        G6.registerNode('default-node', {
          draw: defaultNodeGenerator,
          // setState: (stateName, stateValue, node)=>{
          //   const keyShape = node.getKeyShape();
          //   switch (stateName) {
          //     case 'selected':
          //       if (stateValue) {
          //         keyShape.attr({fill:'#3182ce'});
          //       } else {
          //         keyShape.attr({fill:'#f5f5f5'});
          //       }
          //       break;
          //     default:
          //   }
          // }
        }, 'rect'); // 指定要扩展的内置节点类型, 类似继承, 否则要实现其他需要的方法
        G6.registerEdge('default-edge', {
          draw: defaultEdgeGenerator,
          // animation
          // afterDraw(cfg, group) {
          //   const shape = group.get('children')[1];
          //   let index = 0;
          //   shape.animate(
          //     () => {
          //       index++;
          //       if (index > 9) {
          //         index = 0;
          //       }
          //       const res = {
          //         lineDash: [10, 5],
          //         lineDashOffset: -index,
          //       };
          //       // returns the modified configurations here, lineDash and lineDashOffset here
          //       return res;
          //     },
          //     {
          //       repeat: true, // whether executes the animation repeatly
          //       duration: 10000, // the duration for executing once
          //     },
          //   );
          // },
        });
        // tooltip
        const tooltip = new G6.Tooltip({
          offsetX: 10,
          offsetY: 10,
          itemTypes: ['edge'],
          getContent: e => {
            const dom = document.createElement('div');
            dom.style.width = 'fit-content';
            const model = e.item.getModel();
            dom.innerText = model.label;
            return dom;
          },
          // 在这里判断要不要显示tooltip
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        })
        // contextmenu
        const contextmenu = new G6.Menu({
          getContent(evt) {
            return `<div style="cursor: pointer">${evt.item.getType()}</div>`;
          },
          handleMenuClick: (target, item) => {
            console.log(target, item.getModel());
          },
          offsetX: 16 + 10,
          offsetY: 0,
          // 在哪些类型的元素上响应
          itemTypes: ['edge'],
          // 在这里判断要不要显示contextmenu
          shouldBegin: (e) => {
            return e.target.cfg.name === 'default-edge-text-shape';
          },
        });
        // graph
        const graph = new G6.Graph({
          container: 'container',
          width,
          height,
          fitCenter: true,
          plugins: [tooltip, contextmenu],
          modes: {
            default: ['zoom-canvas', 'drag-canvas'],
          },
          defaultNode: {
            type: 'default-node',
          },
          nodeStateStyles: {
            abc: {
              fill: 'red',
            },
            'ABCD:REPAIR': {
              stroke: 'yellow',
            },
          },
          defaultEdge: {
            type: 'default-edge',
          },
        });
        const generatedData = dataGenerator(testData2);
        console.log(generatedData);
        graph.data(generatedData);
        graph.render();
        graph.on('node:click', function (event) {
          const { item } = event;
          graph.setItemState(item, 'selected', true);
        });
        graph.on('canvas:click', () => {
          graph.getNodes().forEach((node)=>{
            graph.clearItemStates(node, 'selected');
          })
        });
        graph.on('default-edge-text-shape:click', (evt)=>{
          console.log(evt)
        })
        if (typeof window !== 'undefined')
          window.onresize = () => {
            if (!graph || graph.get('destroyed')) return;
            if (!container || !container.scrollWidth || !container.scrollHeight) return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };

    }
}
</script>