// NOTE: 全局默认树的子节点字段为children
// 炫技: 为了防止collapsed字段被占用, 设置了ANTV_TREE_COLLPSED_FLAG为折叠标志字段
// TODO: 拆卸组装
// TODO: 幽灵节点
// TODO: initTreeData -> default_level -> ANTV_TREE_COLLPSED
// TODO: 展开层级(1/all)
// TODO: 批量更新节点
// NOTE: 展开后要重新设置状态...
import G6 from "@antv/g6";
import { get } from "js-cookie";

export const ANTV_TREE_COLLPSED_FLAG = "ANTV_TREE_COLLPSED_FLAG"
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 200;
const BOX_HEIGHT = 50;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#ccc';
const BOX_RADIUS = 4;
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "red"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;

export const NODE_COLLAPSE_STATUS = 'NODE_COLLAPSED';
export const DISASSEMBLE_NODE = 'DISASSEMBLE_NODE';
export const GHOST_NODE = 'GHOST_NODE';
export const NODE_TYPE = 'NODE_TYPE';

// 生成节点唯一id的一个方法
export const getId = cfg => cfg.id ? `data-${cfg.id}` : `template-${cfg.templateId}`

const nodes = {
    "tree-default-node": {
        options: {
            style: {
                'main-rect': {
                    rect: {
                        default: {
                            width: BOX_WIDTH,
                            height: BOX_HEIGHT,
                            lineWidth: BOX_BORDER_WIDTH,
                            fontSize: 12,
                            radius: BOX_RADIUS,
                            stroke: BOX_BORDER_COLOR,
                            fill: BOX_FILL_COLOR
                        },
                        [GHOST_NODE]: {
                            lineDash: [5, 5]
                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        }
                    },
                    text: {
                        default: {
                            textAlign: "left",
                            textBaseline: "bottom",
                        },
                        [GHOST_NODE]: {

                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        }
                    }
                },
                'collapse-rect': {
                    rect: {
                        default: {
                            width: 16,
                            height: 16,
                            stroke: "rgba(0, 0, 0, 0.25)",
                            cursor: "pointer",
                            fill: "#fff",
                        },
                        [GHOST_NODE]: {
                            lineDash: [5, 5]
                        },
                        [DISASSEMBLE_NODE]: {
                            fill: "red"
                        }
                    },
                    text: {
                        default: {
                            textAlign: "center",
                            textBaseline: "middle",
                            fontSize: 16,
                            cursor: "pointer",
                            fill: "rgba(0, 0, 0, 0.25)",
                        },
                        [GHOST_NODE]: {},
                        [DISASSEMBLE_NODE]: {
                        }
                    }
                }
            }
        },
        draw(cfg, group) {
            // 主节点配置
            const mainRectDefaultStyle = this.options.style['main-rect'].rect.default;
            let nodeType = null;
            if(cfg.templateId){
                nodeType = GHOST_NODE;
            }
            const mainRectCustomStyle = nodeType ? this.options.style['main-rect'].rect[nodeType] : {};
            // 声明主节点中心在长方形的中心
            const nodeOrigin = {
                x: -mainRectDefaultStyle.width / 2,
                y: -mainRectDefaultStyle.height / 2,
            };
            const rect = group.addShape("rect", {
                attrs: {
                    x: nodeOrigin.x,
                    y: nodeOrigin.y,
                    ...mainRectDefaultStyle,
                    ...mainRectCustomStyle
                },
                name: "main-rect"
            });
            // 文字基本样式
            const mainRectDefaultTextStyle = this.options.style['main-rect'].text.default;
            const mainRectCustomTextStyle = cfg.nodeType ? this.options.style['main-rect'].text[cfg.nodeType] : {};


            // collapse图标
            const collapseRectDefaultStyle = this.options.style['collapse-rect'].rect.default;
            // collapse文字基本样式
            const collapseRectDefaultTextStyle = this.options.style['collapse-rect'].text.default;
            if (
                cfg.children &&
                cfg.children.length
            ) {
                group.addShape("rect", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2 - 8,
                        y: -10,
                        ...collapseRectDefaultStyle
                    },
                    name: "collapse-rect",
                    modelId: getId(cfg),
                });
                group.addShape("text", {
                    attrs: {
                        x: mainRectDefaultStyle.width / 2,
                        y: -1,
                        text: cfg[ANTV_TREE_COLLPSED_FLAG] ? "+" : "-",
                        ...collapseRectDefaultTextStyle
                    },
                    name: "collapse-text",
                    modelId: getId(cfg),
                });
            }
            return rect;
        },
        setState(name, value, item){
            if (name === NODE_COLLAPSE_STATUS) {
                const group = item.getContainer();
                const collapseText = group.find(
                    (e) => e.get("name") === "collapse-text"
                );
                if (collapseText) {
                    if (!value) {
                        collapseText.attr({ text: "-", });
                    } else {
                        collapseText.attr({ text: "+", });
                    }
                }
            }
            if(name === NODE_TYPE) {
                const mainRectDefaultStyle = this.options.style['main-rect'].rect.default;
                const mainRectCustomStyle = value ? this.options.style['main-rect'].rect[value] : {};
                const mainRectConfig = {
                    ...mainRectDefaultStyle,
                    ...mainRectCustomStyle
                };
                const collapseRectDefaultStyle = this.options.style['collapse-rect'].rect.default;
                const collapseRectCustomStyle = value ? this.options.style['collapse-rect'].rect[value] : {};
                const collapseRectConfig = {
                   ...collapseRectDefaultStyle,
                   ...collapseRectCustomStyle
                };
                const group = item.getContainer();
                const collapseRect = group.find(
                    (e) => e.get("name") === "collapse-rect"
                );
                collapseRect&&collapseRect.attr({ ...collapseRectConfig })
                const mainRect = group.find(
                    (e) => e.get("name") === "main-rect"
                );
                mainRect&&mainRect.attr({ ...mainRectConfig })
            }
        },
    }
}

// string, string[], {name: alias}[]
export default function registerCustomNodes(nodeList) {
    if(typeof nodeList==="string"){
        nodeList = [nodeList];
    }
    nodeList.forEach(item => {
        let name, alias;

        if (typeof item === 'string') {
            // 直接传递的节点名
            name = item;
            alias = item;
        } else if (typeof item === 'object' && item !== null) {
            [[name, alias]] = Object.entries(item);
        }

        if (nodes[name]) {
            // 这里默认了是rect, 可能需要维护
            G6.registerNode(alias, nodes[name], 'rect');
        } else {
            console.warn(`Node type "${name}" is not defined.`);
        }
    });
}

export const collapseNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLPSED_FLAG] = true;
    graph.layout();
    graph.setItemState(item, NODE_COLLAPSE_STATUS, true);
}

export const expandNode = (item, graph) => {
    const model = item.getModel();
    model[ANTV_TREE_COLLPSED_FLAG] = false;
    if (model.children) {
        model.children.forEach((child) => {
            child[ANTV_TREE_COLLPSED_FLAG] = true;
        });
    }
    graph.layout();
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);
}

export const expandNodeAll = (item, graph) => {
    const model = item.getModel();
    const expandAll = (m) => {
        m[ANTV_TREE_COLLPSED_FLAG] = false;
        if (m.children) {
            m.children.forEach((child) => {
                expandAll(child);
            });
        }
    };
    expandAll(model);
    graph.layout();
    graph.setItemState(item, NODE_COLLAPSE_STATUS, false);

}