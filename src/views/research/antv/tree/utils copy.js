/* 
    node    group   -> shape
                    -> keyShape
    edge    group   -> shape
*/
// enum
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 200;
const BOX_HEIGHT = 50;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#5b8ff9';
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "red"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;

// TODO: edge方向
export const dataGenerator = (data) => {
    const stdData = handleData(data);
    const nodes = [];
    const edges = [];
    const rowLen = stdData.length;

    let curNodeIndex = 0;

    for (let i = 0; i < rowLen; i++) {
        const rowData = stdData[i];
        for (let j = 0; j < rowData.length; j++) {
            const curNodeId = `node-${curNodeIndex}`;
            const prevNodeId = `node-${curNodeIndex-1}`
            const rowItem = rowData[j];
            nodes.push({ 
                ...rowItem,
                id: curNodeId,
                x: INIT_X + (BOX_WIDTH + COL_GUTTER) * j,
                y: INIT_Y + (BOX_HEIGHT + ROW_GUTTER) * (rowLen - i),
                anchorPoints: [ANCHOR.LEFT, ANCHOR.RIGHT]
            });
            if(!rowItem.isStart){
                edges.push({ 
                    ...rowItem,
                    label: `${prevNodeId} - ${rowItem.status} - ${curNodeId}`,
                    source: prevNodeId,
                    target: curNodeId,
                    sourceAnchor: 1,
                    targetAnchor: 0,
                });
            }
            curNodeIndex++;
        }
    }
    return { nodes, edges }
}

export const defaultNodeGenerator = (cfg, group) => {
    const { isStart, isEnd } = cfg;
    // 从内到外add
    const point_offset = 2 * BOX_BORDER_WIDTH - POINT_RADIUS / 2;
    const point_cfg = {
        r: POINT_RADIUS,
        fill: POINT_COLOR,
        stroke: POINT_COLOR,
    }
    
    if(!isStart){
        // 左侧
        group.addShape('circle', {
            attrs: {
                x: -point_offset,
                y: BOX_HEIGHT / 2,
                ...point_cfg
            },
            name: 'default-start-point'
        })
    }
    if(!isEnd){
        // 右侧
        group.addShape('circle', {
            attrs: {
                x: BOX_WIDTH + point_offset,
                y: BOX_HEIGHT / 2,
                ...point_cfg
            },
            name: 'default-end-point'
        })
    }
    /* 
        keyshape
            anchorPoints(边的两端)相对于这个
            group中的其它shape的位置都基于它, 所以它的(x, y)应该取原点
    */ 
    const node = group.addShape('rect', {
        attrs: {
            x: 0,
            y: 0,
            width: BOX_WIDTH,
            height: BOX_HEIGHT,
            fill: BOX_FILL_COLOR,
            lineWidth: BOX_BORDER_WIDTH,
            stroke: BOX_BORDER_COLOR,
            ...(cfg.style || {}),
        },
        name: 'key-rect-shape'
    })
    return node
}

/* 
    TODO:

    highlight   ✔
    arrow       ✔
    cursor      ✔
    click       ✔
    hover       ✔
    long text
    contextmenu ✔     
    animation   ✔
    要关联的cfg
*/
export const defaultEdgeGenerator = (cfg, group) => {
    const { startPoint, endPoint } = cfg;
    const { x: startX, y: startY } = startPoint;
    const { x: endX, y: endY } = endPoint;
    // label
    const label_font_size = 14;
    const label_offset_y = label_font_size / 2 + 4; // 4 是label和线的间距
    group.addShape('text', {
        attrs: {
            text: cfg.label,
            x: (startX + endX) / 2,
            y: (startY + endY) / 2 - label_offset_y, // 在线中间的上方
            fontSize: label_font_size,
            textAlign: 'center',
            textBaseline: 'middle',
            cursor: 'pointer',
            fill: 'red',
            position: 'middle',
        },
        name: 'default-edge-text-shape',
    });
    // line
    let edge;
    const edge_cfg = {
        lineWidth: LINE_WIDTH,
        stroke: LINE_COLOR,
    }
    if(cfg.isCycleStart){
        // s形线
        const sPath = [
            ['M', startX, startY],
            ['C', startX + 200, startY - 100, endX - 200, endY + 100, endX, endY], // TODO: 贝塞尔曲线通用策略
        ];
        edge = group.addShape('path', {
            attrs: {
                path: sPath,
                lineDash: [10, 5],
                ...edge_cfg,
            },
            name: 's-path-shape',
        });
    }else{
        // 直线
        edge = group.addShape('path', {
            attrs: {
                path: [['M', startX, startY], ['L', endX, endY]],
                ...edge_cfg,
            },
            name: 'l-path-shape',
        });
    }
    return edge;
}

/* 
    [{status}] -> [[{status, isStart, isEnd, isCycleStart, isCycleEnd}]]
*/
function handleData(data){
    const ret = [];
    let curRowIndex = 0;
    let curRowData = [];
    for (let i = 0, len = data.length; i < len; i++) {
        const item = { ...data[i] };
        const isStart = i === 0;
        const isEnd = i === len - 1;
        const isReady = item.status === 'READY';
        item.isStart = isStart;
        item.isEnd = isEnd;
        if(isReady){
            curRowData.push({ ...item, isCycleEnd: !isStart });
            if(!isEnd&&!isStart){
                ret.push(curRowData);
                curRowIndex++;
                curRowData = [];
                curRowData.push({ ...item, isCycleStart: true });
            }
        }else{
            curRowData.push({ ...item });
        }
        if(isEnd){
            ret.push(curRowData);
        }
    }
    return ret;
}
export const testData = [
    {status: 'READY'},{status: 'ATRIG'}, {status: 'REPAIR'},
    {status: 'READY'}, {status: 'ATRIG'},
    {status: 'READY'}, {status: 'REPAIR'},
    {status: 'READY'}
]


// TODO: initTreeData -> default_level -> ANTV_TREE_COLLPSED

// NODE

// EDGE