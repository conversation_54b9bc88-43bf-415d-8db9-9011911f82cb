<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">生产监控</div>
            <div
                class="process-container"
                style="display:flex;height:calc(100% - 40px)"
            >
                <div
                    class="left"
                    style="flex:160px 0 0;border-right:1px solid #dee0e3;height:100%"
                >
                    <div
                        class="list"
                        v-for="item in productionTaskList"
                        :key="item.produceId"
                    >
                        <div
                            class="list-item"
                            :class="{ active: item.produceId === produceId }"
                            @click="onClickTask(item.produceId)"
                        >
                            <span :title="item.produceName">
                                {{ item.produceName }}
                            </span>
                        </div>
                    </div>
                </div>

                <div style="flex:1 0 0;padding-left:20px" v-if="produceId > 0">
                    <div id="container" style="height:100%"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import G6, { TreeGraph, ModelConfig, IGroup, Item, Minimap } from "@antv/g6";
import insertCss from "insert-css";
import {
    apiGetProductionTaskInfo,
    apiGetProductionTaskListByCondition,
} from "@/api/process";
import { props, colors, color } from "./mockdata";
const DATE_NOW = new Date(new Date().Format("yyyy-MM-dd")).getTime();
export default {
    data(){
        return {
            productionTaskList: [],
            produceId: 0,
            graph: null,
        }
    },
    mounted() {
        
        this.getProductionTaskList();
        insertCss(`
            .g6-component-tooltip {
                background-color: rgba(0,0,0, 0.65);
                padding: 10px;
                box-shadow: rgb(174, 174, 174) 0px 0px 10px;
                width: fit-content;
                color: #fff;
                border-radius = 4px;
            };
        `);
    
    },
    methods: {
        async getData(produceId = this.produceId) {
            await apiGetProductionTaskInfo({ produceId }).then((res) => {
                let data = res.data.data || [];
                data.children = data.childList;
                delete data.childList;
                function mapData(childList) {
                    for (let i = 0, len = childList.length; i < len; i++) {
                        childList[i].id = `${childList[i].produceId}`;
                        if (childList[i].childList) {
                            childList[i].children = childList[i].childList;
                            delete childList[i].childList;
                            mapData(childList[i].children);
                        }
                    }
                }
                mapData(data.children);
                props.data = data;
            });
        },
        async onClickTask(produceId = this.produceId) {
            this.produceId = produceId;
            await this.getData();
            this.drawChart();
        },
        async getProductionTaskList(
            condition = {},
            cs = { current: 1, size: 100000 }
        ) {
            await apiGetProductionTaskListByCondition(condition, cs).then((res) => {
                this.productionTaskList = res.data.data.records;
            });
        },
        initContainer() {
            this.container = document.getElementById("container");
            this.width = this.container.scrollWidth;
            this.height = this.container.scrollHeight || 500;
        },
        registerFun() {
            G6.registerNode(
                "flow-rect",
                {
                    shapeType: "flow-rect",
                    draw(cfg, group) {
                        let {
                            produceName = "",
                            actualEndDate,
                            actualStartDate,
                            planStartDate,
                            planEndDate,
                            directorName,
                            produceDesc,
                            produceStatus,
                            priority,
                            variableName,
                            variableValue,
                            variableUp,
                            label,
                            collapsed,
                            currency,
                            status,
                            progress,
                        } = cfg;

                        let planStartDateTime = new Date(planEndDate).getTime();
                        let planEndDateTime = new Date(planEndDate).getTime();
                        let actualEndDateTime = new Date(actualEndDate).getTime();
                        let actualStartDateTime = new Date(
                            actualStartDate
                        ).getTime();
                        let inTimeStatus = "NORMAL";
                        if (actualEndDate) {
                            inTimeStatus =
                                planStartDateTime - actualEndDateTime < 0
                                    ? "EXCEED"
                                    : "NORMAL";
                        } else {
                            inTimeStatus =
                                planStartDateTime - DATE_NOW < 0
                                    ? "EXCEED"
                                    : "NORMAL";
                        }
                        if (produceStatus == 3) {
                            inTimeStatus = "REJECT";
                        }
                        let progressStatus = "WORKING";
                        if (progress >= 100) {
                            progressStatus = "COMPLETED";
                        } else {
                            progressStatus = "WORKING";
                        }
                        if (inTimeStatus === "EXCEED") {
                            progressStatus = "EXCEED";
                        }

                        status = "B";
                        progress = (progress || 0) / 100;
                        const grey = "#CED4D9";
                        // 逻辑不应该在这里判断
                        const rectConfig = {
                            width: 232,
                            height: 90,
                            lineWidth: 1,
                            fontSize: 12,
                            fill: color[`${inTimeStatus}_BG`],
                            radius: 4,
                            stroke: grey,
                            opacity: 1,
                        };
                        // 声明节点中心在长方形的中心
                        const nodeOrigin = {
                            x: -rectConfig.width / 2,
                            y: -rectConfig.height / 2,
                        };

                        const textConfig = {
                            textAlign: "left",
                            textBaseline: "bottom",
                        };

                        const rect = group.addShape("rect", {
                            attrs: {
                                x: nodeOrigin.x,
                                y: nodeOrigin.y,
                                ...rectConfig,
                                fill: color[`${inTimeStatus}_BG`],
                            },
                        });
                        const rectBBox = rect.getBBox();

                        // label title
                        group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: 12 + nodeOrigin.x,
                                y: 20 + nodeOrigin.y,
                                text:
                                    produceName.length > 16
                                        ? produceName.substr(0, 16) + "..."
                                        : produceName,
                                fontSize: 12,
                                opacity: 0.85,
                                fill: "#000",
                                cursor: "pointer",
                            },
                            // 用于在e.target中作区分
                            produceName: "name-shape",
                        });
                        // 预计时间
                        group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: 12 + nodeOrigin.x,
                                y: 36 + nodeOrigin.y,
                                text: `预计时间：${planStartDate}-${planEndDate}`,
                                fontSize: 12,
                                opacity: 0.85,
                                fill: "#000",
                                cursor: "pointer",
                            },
                            // 用于在e.target中作区分
                            produceName: "name-shape",
                        });
                        // 预计时间
                        group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: 12 + nodeOrigin.x,
                                y: 52 + nodeOrigin.y,
                                text: `实际时间：${actualStartDate}-${actualEndDate ||
                                    "至今"}`,
                                fontSize: 12,
                                opacity: 0.85,
                                fill: "#000",
                                cursor: "pointer",
                            },
                            // 用于在e.target中作区分
                            produceName: "name-shape",
                        });
                        // price
                        const price = group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: 12 + nodeOrigin.x,
                                y: rectBBox.maxY - 12,
                                text: label,
                                fontSize: 16,
                                fill: "#000",
                                opacity: 0.85,
                            },
                        });

                        // label currency
                        group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: price.getBBox().maxX + 5,
                                y: rectBBox.maxY - 12,
                                text: currency,
                                fontSize: 12,
                                fill: "#000",
                                opacity: 0.75,
                            },
                        });

                        // percentage
                        const percentText = group.addShape("text", {
                            attrs: {
                                ...textConfig,
                                x: rectBBox.maxX - 8,
                                y: rectBBox.maxY - 12,
                                text: `完成率：${(progress * 100).toFixed(2)}%`,
                                fontSize: 12,
                                textAlign: "right",
                                fill: colors[status],
                            },
                        });

                        // bottom line background
                        const bottomBackRect = group.addShape("rect", {
                            attrs: {
                                x: nodeOrigin.x,
                                y: rectBBox.maxY - 4,
                                width: rectConfig.width,
                                height: 4,
                                radius: [
                                    0,
                                    0,
                                    rectConfig.radius,
                                    rectConfig.radius,
                                ],
                                fill: "#E0DFE3",
                            },
                        });

                        // bottom percent
                        const bottomRect = group.addShape("rect", {
                            attrs: {
                                x: nodeOrigin.x,
                                y: rectBBox.maxY - 4,
                                width: progress * rectBBox.width,
                                height: 4,
                                radius: [0, 0, 0, rectConfig.radius],
                                fill: color[`${progressStatus}_LINE`],
                            },
                        });

                        // collapse rect
                        // 看情况添加+-符号
                        if (
                            cfg.children &&
                            cfg.children.length
                        ) {
                            group.addShape("rect", {
                                attrs: {
                                    x: rectConfig.width / 2 - 8,
                                    y: -8,
                                    width: 16,
                                    height: 16,
                                    stroke: "rgba(0, 0, 0, 0.25)",
                                    cursor: "pointer",
                                    fill: "#fff",
                                },
                                name: "collapse-back",
                                modelId: cfg.id,
                            });

                            // collpase text
                            group.addShape("text", {
                                attrs: {
                                    x: rectConfig.width / 2,
                                    y: -1,
                                    textAlign: "center",
                                    textBaseline: "middle",
                                    text: collapsed ? "+" : "-",
                                    fontSize: 16,
                                    cursor: "pointer",
                                    fill: "rgba(0, 0, 0, 0.25)",
                                },
                                name: "collapse-text",
                                modelId: cfg.id,
                            });
                        }

                        // this.drawLinkPoints(cfg, group);
                        return rect;
                    },
                    update(cfg, item) {
                        const group = item.getContainer();
                        // this.updateLinkPoints(cfg, group);
                    },
                    setState(name, value, item) {
                        if (name === "collapse") {
                            const group = item.getContainer();
                            const collapseText = group.find(
                                (e) => e.get("name") === "collapse-text"
                            );
                            if (collapseText) {
                                if (!value) {
                                    collapseText.attr({
                                        text: "-",
                                    });
                                } else {
                                    collapseText.attr({
                                        text: "+",
                                    });
                                }
                            }
                        }
                    },
                },
                "rect"
            );

            G6.registerEdge(
                "line-dash",
                {
                },
                "cubic"
            );
        },
        initGraph(data) {
            if (!data) {
                return;
            }
            const { config } = props;
            const minimap = new Minimap({ size: [300, 300] });
            const tooltip = new G6.Tooltip({
                // offsetX and offsetY include the padding of the parent container
                offsetX: 20,
                offsetY: 30,
                // the types of items that allow the tooltip show up
                // 允许出现 tooltip 的 item 类型
                itemTypes: ["node"],
                // custom the tooltip's content
                // 自定义 tooltip 内容
                getContent: (e) => {
                    const outDiv = document.createElement("div");
                    //outDiv.style.padding = '0px 0px 20px 0px';
                    const model = e.item.getModel();
                    const {
                        produceName,
                        directorName,
                        produceDesc,
                        priority,
                        produceStatus,
                    } = model;
                    outDiv.innerHTML = `任务名称：${produceName}<br/>
                        责任人：${directorName}<br/>
                        任务描述信息：${produceDesc}<br/>
                        任务级别：${require("./enum").priorityList[priority]}<br/>
                        任务状态：${
                            require("./enum").taskStatus[produceStatus]
                        }<br/>`;
                    return outDiv;
                },
                shouldBegin: (e) => {
                    console.log(e);
                    if (e.target.get("name")) return false;
                    return true;
                },
            });
            this.graph = new G6.TreeGraph({
                container: "container",
                ...this.defaultConfig,
                ...config,
                animate: false,
                plugins: [tooltip],
            });
            // if (typeof onInit === "function") {
            //     onInit(this.graph);
            // }
            this.graph.data(data);
            this.graph.render();
            this.graph.zoom(config.defaultZoom || 1);
            // 给+-号添加点击事件
            const handleCollapse = (e) => {
                const target = e.target;
                const id = target.get("modelId");
                const item = this.graph.findById(id);
                const nodeModel = item.getModel();
                nodeModel.collapsed = !nodeModel.collapsed;
                this.graph.layout();
                this.graph.setItemState(
                    item,
                    "collapse",
                    nodeModel.collapsed
                );
            };
            this.graph.on("collapse-text:click", (e) => {
                handleCollapse(e);
            });
            this.graph.on("collapse-back:click", (e) => {
                handleCollapse(e);
            });
        },
        drawChart() {
            if (!this.container) {
                this.initContainer();
            }
            this.defaultConfig = {
                width: this.width,
                height: this.height,
                modes: {
                    default: ["zoom-canvas", "drag-canvas"],
                },
                fitView: true,
                animate: true,
                defaultNode: {
                    type: "flow-rect",
                },
                defaultEdge: {
                    type: "line-dash",
                    style: {
                        stroke: "#CED4D9",
                    },
                },
                layout: {
                    type: "indented",
                    direction: "LR",
                    dropCap: false,
                    indent: 300,
                    getHeight: () => {
                        return 80;
                    },
                },
            };
            this.registerFun();
            const { data } = props;
            if (this.graph) {
                this.graph.destroy();
            }
            this.initGraph(data);
            if (typeof window !== "undefined")
                window.onresize = () => {
                    if (!this.graph || this.graph.get("destroyed")) return;
                    if (
                        !this.container ||
                        !this.container.scrollWidth ||
                        !this.container.scrollHeight
                    )
                        return;
                    this.graph.changeSize(
                        this.container.scrollWidth,
                        this.container.scrollHeight
                    );
                };
        }
    }
}
</script>
<style lang="scss">
.g6-minimap {
    position: absolute;
    right: 100px;
    top: 100px;
    background-color: #fff;
}
</style>
<style lang="scss" scoped>
.list {
    cursor: pointer;
    font-size: 14px;
    padding-right: 20px;
    .list-item {
        box-sizing: border-box;
        padding: 5px 2px;
        line-height: 20px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        span {
            width: 140px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .list-item:hover {
        background-color: #eff0f1;
    }

    .active {
        background-color: #ebf3ff;
        color: #37f;
    }
}
</style>
