
export const ANTV_TREE_COLLPSED_FLAG = "ANTV_TREE_COLLPSED_FLAG"
export const ANCHOR = {
    LEFT: [0, 0.5],
    RIGHT: [1, 0.5],
    TOP: [0.5, 0],
    BOTTOM: [0.5, 1],
    CENTER: [0.5, 0.5],
}
// config
const BOX_WIDTH = 200;
const BOX_HEIGHT = 50;
const BOX_BORDER_WIDTH = 2;
const BOX_BORDER_COLOR = '#ccc';
const BOX_RADIUS = 4;
const HILIGHT_BOX_BORDER_COLOR = '#ee99ee'
const BOX_FILL_COLOR = '#dee9ff';

const POINT_RADIUS = 4;
const POINT_COLOR = 'red';

const LINE_WIDTH = 2;
const LINE_COLOR = "red"

const COL_GUTTER = 200;
const ROW_GUTTER = 120;

const INIT_X = 100;
const INIT_Y = 100;
// NODE
export const defaultNodeGenerator = (cfg, group) => {
    let {
        produceName = "",
    } = cfg;

    // 逻辑不应该在这里判断
    const rectConfig = {
        width: BOX_WIDTH,
        height: BOX_HEIGHT,
        lineWidth: BOX_BORDER_WIDTH,
        fontSize: 12,
        radius: BOX_RADIUS,
        stroke: BOX_BORDER_COLOR,
    };
    // 声明节点中心在长方形的中心
    const nodeOrigin = {
        x: -rectConfig.width / 2,
        y: -rectConfig.height / 2,
    };

    const textConfig = {
        textAlign: "left",
        textBaseline: "bottom",
    };

    const rect = group.addShape("rect", {
        attrs: {
            x: nodeOrigin.x,
            y: nodeOrigin.y,
            ...rectConfig,
        },
        name: "default-rect"
    });
    // label title
    group.addShape("text", {
        attrs: {
            ...textConfig,
            x: 12 + nodeOrigin.x,
            y: 20 + nodeOrigin.y,
            text:
                produceName.length > 16
                    ? produceName.substr(0, 16) + "..."
                    : produceName,
            fontSize: 12,
            opacity: 0.85,
            fill: "#000",
            cursor: "pointer",
        },
        // 用于在e.target中作区分
        name: "default-text",
    });
    // 看情况添加+-符号
    if (
        cfg.children &&
        cfg.children.length
    ) {
        group.addShape("rect", {
            attrs: {
                x: rectConfig.width / 2 - 8,
                y: -10,
                width: 16,
                height: 16,
                stroke: "rgba(0, 0, 0, 0.25)",
                cursor: "pointer",
                fill: "#fff",
            },
            name: "collapse-rect",
            modelId: cfg.id,
        });
        group.addShape("text", {
            attrs: {
                x: rectConfig.width / 2,
                y: -1,
                textAlign: "center",
                textBaseline: "middle",
                text: cfg[ANTV_TREE_COLLPSED_FLAG] ? "+" : "-",
                fontSize: 16,
                cursor: "pointer",
                fill: "rgba(0, 0, 0, 0.25)",
            },
            name: "collapse-text",
            modelId: cfg.id,
        });
    }
    return rect;
}
// EDGE
export const defaultEdgeGenerator = (cfg, group) => {
    
}