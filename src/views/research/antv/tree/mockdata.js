export const mockData = {
    "produceId": 25,
    "produceCode": "PRODUCE20211018133154",
    "produceType": "自研产品",
    "parentId": -1,
    "leaf": 1,
    "ancestors": null,
    "produceName": "手机制造-ZJ",
    "produceDesc": null,
    "priority": 0,
    "director": 8,
    "directorName": "龚小东",
    "produceStatus": 4,
    "progress": 87.5,
    "receiptCode": null,
    "planStartDate": "2021-10-01",
    "planEndDate": "2021-10-31",
    "actualStartDate": null,
    "actualEndDate": null,
    "children": [
        {
            "produceId": 26,
            "produceCode": "647469043321806848",
            "produceType": null,
            "parentId": 25,
            "leaf": 1,
            "ancestors": 25,
            "produceName": "芯片制造",
            "produceDesc": "制造芯片，随意发挥。",
            "priority": 0,
            "director": 13,
            "directorName": "范晓梅Linda Fan",
            "produceStatus": 2,
            "progress": 100,
            "receiptCode": null,
            "planStartDate": "2021-10-01",
            "planEndDate": "2021-10-03",
            "actualStartDate": null,
            "actualEndDate": null,
            "id": "26",
            "nodeType": "ghost",
            "children": [
                {
                    "produceId": 27,
                    "produceCode": "647469043326001152",
                    "produceType": null,
                    "parentId": 26,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "CPU",
                    "produceDesc": "好好工作，实现买房自由",
                    "priority": 0,
                    "director": 13,
                    "directorName": "范晓梅Linda Fan",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-01",
                    "planEndDate": "2021-10-01",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "27"
                },
                {
                    "produceId": 28,
                    "produceCode": "647469043330195456",
                    "produceType": null,
                    "parentId": 26,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "电池",
                    "produceDesc": "",
                    "priority": 0,
                    "director": 13,
                    "directorName": "范晓梅Linda Fan",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-02",
                    "planEndDate": "2021-10-03",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "28"
                },
                {
                    "produceId": 47,
                    "produceCode": "647798151377461248",
                    "produceType": null,
                    "parentId": 26,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "铜线",
                    "produceDesc": "铜线连接",
                    "priority": 0,
                    "director": 8,
                    "directorName": "龚小东",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-04",
                    "planEndDate": "2021-10-04",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "47"
                }
            ]
        },
        {
            "produceId": 29,
            "produceCode": "647469043346972672",
            "produceType": null,
            "parentId": 25,
            "leaf": 1,
            "ancestors": 25,
            "produceName": "外壳制造",
            "produceDesc": "",
            "priority": 0,
            "director": 7,
            "directorName": "文浩",
            "produceStatus": 2,
            "progress": 100,
            "receiptCode": null,
            "planStartDate": "2021-10-05",
            "planEndDate": "2021-10-12",
            "actualStartDate": null,
            "actualEndDate": null,
            "id": "29",
            "children": [
                {
                    "produceId": 30,
                    "produceCode": "647469043351166976",
                    "produceType": null,
                    "parentId": 29,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "生产玻璃片",
                    "produceDesc": "",
                    "priority": 0,
                    "director": 7,
                    "directorName": "文浩",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-05",
                    "planEndDate": "2021-10-07",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "30"
                },
                {
                    "produceId": 31,
                    "produceCode": "647469043355361280",
                    "produceType": null,
                    "parentId": 29,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "模型铸造",
                    "produceDesc": "",
                    "priority": 0,
                    "director": 7,
                    "directorName": "文浩",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-07",
                    "planEndDate": "2021-10-09",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "31"
                }
            ]
        },
        {
            "produceId": 32,
            "produceCode": "647469043359555584",
            "produceType": null,
            "parentId": 25,
            "leaf": 1,
            "ancestors": 25,
            "produceName": "显示器制造",
            "produceDesc": "",
            "priority": 0,
            "director": 7,
            "directorName": "文浩",
            "produceStatus": 1,
            "progress": 50,
            "receiptCode": null,
            "planStartDate": "2021-10-07",
            "planEndDate": "2021-10-07",
            "actualStartDate": null,
            "actualEndDate": null,
            "id": "32",
            "children": [
                {
                    "produceId": 33,
                    "produceCode": "647469043363749888",
                    "produceType": null,
                    "parentId": 32,
                    "leaf": 1,
                    "ancestors": 25,
                    "produceName": "液晶显示器",
                    "produceDesc": "",
                    "priority": 0,
                    "director": 8,
                    "directorName": "龚小东",
                    "produceStatus": 1,
                    "progress": 0,
                    "receiptCode": null,
                    "planStartDate": "2021-10-07",
                    "planEndDate": "2021-10-09",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "id": "33",
                    "children": [
                        {
                            "produceId": 58,
                            "produceCode": "648214559517188096",
                            "produceType": null,
                            "parentId": 33,
                            "leaf": 0,
                            "ancestors": 25,
                            "produceName": "购买液晶显示屏",
                            "produceDesc": null,
                            "priority": 0,
                            "director": 7,
                            "directorName": "文浩",
                            "produceStatus": 0,
                            "progress": 0,
                            "receiptCode": null,
                            "planStartDate": "2021-10-13",
                            "planEndDate": "2021-10-13",
                            "actualStartDate": "2021-10-14",
                            "actualEndDate": "2021-10-20",
                            "children": [
                                {
                                    "produceId": 99,
                                    "produceCode": "648214559517188888",
                                    "produceType": null,
                                    "parentId": 33,
                                    "leaf": 0,
                                    "ancestors": 25,
                                    "produceName": "购买液晶显示屏",
                                    "produceDesc": null,
                                    "priority": 0,
                                    "director": 7,
                                    "directorName": "文浩",
                                    "produceStatus": 0,
                                    "progress": 0,
                                    "receiptCode": null,
                                    "planStartDate": "2021-10-13",
                                    "planEndDate": "2021-10-13",
                                    "actualStartDate": "2021-10-14",
                                    "actualEndDate": "2021-10-20",
                                    "children": null,
                                    "id": "99"
                                }
                            ],
                            "id": "58"
                        }
                    ]
                },
                {
                    "produceId": 34,
                    "produceCode": "647469043367944192",
                    "produceType": null,
                    "parentId": 32,
                    "leaf": 0,
                    "ancestors": 25,
                    "produceName": "测试显示效果",
                    "produceDesc": "",
                    "priority": 0,
                    "director": 13,
                    "directorName": "范晓梅Linda Fan",
                    "produceStatus": 2,
                    "progress": 100,
                    "receiptCode": null,
                    "planStartDate": "2021-10-08",
                    "planEndDate": "2021-10-10",
                    "actualStartDate": null,
                    "actualEndDate": null,
                    "children": null,
                    "id": "34"
                }
            ]
        },
        {
            "produceId": 35,
            "produceCode": "647469043372138496",
            "produceType": null,
            "parentId": 25,
            "leaf": 0,
            "ancestors": 25,
            "produceName": "组装",
            "produceDesc": "",
            "priority": 0,
            "director": 13,
            "directorName": "范晓梅Linda Fan",
            "produceStatus": 2,
            "progress": 100,
            "receiptCode": null,
            "planStartDate": "2021-10-01",
            "planEndDate": "2021-10-12",
            "actualStartDate": null,
            "actualEndDate": null,
            "children": null,
            "id": "35"
        }
    ]
}
export const color = {
    NORMAL_BG: 'rgb(255,255,255)',
    COMPLETED_LINE: 'rgb(2,240,140)',
    WORKING_LINE: 'rgb(2,167,240)',
    REJECT_BG: 'rgb(247,247,201)',
    EXCEED_BG: 'rgb(234,167,175)',
    EXCEED_LINE: 'rgb(217,0,27)'
}
export const colors = {
    B: '#5B8FF9',
    R: '#F46649',
    Y: '#EEBC20',
    G: '#5BD8A6',
    DI: '#A7A7A7',
};

//  组件props
export const props = {
    data: mockData,
    config: {
        padding: [20, 50],
        defaultLevel: 3,
        defaultZoom: 0.7,
        modes: { default: ['zoom-canvas', 'drag-canvas'] },
    },
};

export const props2 = {
    data: {
        id: '1',
        name: 'root',
        sort: 1,
        children: [
            {
                id: '2',
                name: 'child1',
                sort: 1,
                children: [
                    {
                        id: '4',
                        name: 'child1-1',
                        sort: 1,
                    },
                    {
                        id: '5',
                        name: 'child1-2',
                        sort: 2,
                    },
                ],
            },
            {
                id: '3',
                name: 'child2',
                sort: 2,
                children: [
                    {
                        id: '6',
                        name: 'child2-2',
                        sort: 2,
                    },
                ]
            },
        ],
    },
    templateData: {
        templateId: 'template_id_1',
        templateName: '模板_root',
        sort: 1,
        children: [
            {
                templateId: 'template_id_2',
                templateName: '模板_child1',
                sort: 1,
                children: [
                    {
                        templateId: 'template_id_4',
                        templateName: '模板_child1_1',
                        sort: 1,
                    },
                    {
                        templateId: 'template_id_5',
                        templateName: '模板_child1_2',
                        sort: 2,
                    },
                ],
            },
            {
                templateId: 'template_id_3',
                templateName: '模板_child2',
                sort: 2,
                children: [
                    {
                        templateId: 'template_id_6',
                        templateName: '模板_child2_1',
                        sort: 1,
                        children: [
                            {
                                templateId: 'template_id_6_1',
                                templateName: '模板_child2_1_1',
                                sort: 1,
                            },
                            {
                                templateId: 'template_id_6_2',
                                templateName: '模板_child2_1_2',
                                sort: 2,
                            }
                        ]
                    },
                    {
                        templateId: 'template_id_7',
                        templateName: '模板_child2_2',
                        sort: 2,
                        children: [
                            {
                                templateId: 'template_id_7_1',
                                templateName: '模板_child2_2_1',
                                sort: 1,
                            },
                            {
                                templateId: 'template_id_7_2',
                                templateName: '模板_child2_2_2',
                                sort: 2,
                            }
                        ]
                    }
                ]
            },
        ]
    }
}

// 以props2.templateData解构为标准, 递归合并props.data数据
export const mergeData = (templateData, data) => {
    if (!templateData) return null;
    
    // 创建一个以 level 为 key 的映射，方便查找 data 中的节点
    const dataMap = new Map((data?.children || []).map(child => [child.level, child]));
    
    // 递归构造新的 data 结构
    function buildNode(templateNode) {
        const existingNode = dataMap.get(templateNode.level) || {};
        
        return {
            id: existingNode.id || templateNode.templateId, // 保留原 id，否则用 templateId 代替
            level: templateNode.level,
            children: templateNode.children?.map(buildNode) || [] // 递归构造 children
        };
    }
    
    return buildNode(templateData);
}

export const mergeData2 = (templateData, data) => {
    let res = { ...data };
    if(templateData.children){
        if(!data.children || data.children.length===0){
            res.children = templateData.children;
        }else{
            res.children = templateData.children.map((templateChild) => {
                const target = data.children.find(child => child.sort === templateChild.sort);
                if(target){
                    return mergeData2(templateChild, target);
                }else{
                    return {...templateChild, isGhostHead: true};
                }
            });
        }
    }
    return res;
}


export const dddddata = {
    "id": "data-1",
    "name": "root",
    "sort": 1,
    "children": [
        {
            "id": "data-2",
            "name": "child1",
            "sort": 1,
            "children": [
                {
                    "id": "data-4",
                    "name": "child1-1",
                    "sort": 1,
                    "x": 600,
                    "y": 0,
                    "depth": 2,
                    "type": "tree-default-node",
                    "style": {}
                },
                {
                    "id": "data-5",
                    "name": "child1-2",
                    "sort": 2,
                    "x": 600,
                    "y": 116,
                    "depth": 2,
                    "type": "tree-default-node",
                    "style": {}
                }
            ],
            "x": 300,
            "y": 0,
            "depth": 1,
            "type": "tree-default-node",
            "style": {}
        },
        {
            "id": "data-3",
            "name": "child2",
            "sort": 2,
            "children": [
                {
                    "templateId": "template_id_6",
                    "templateName": "模板_child2_1",
                    "sort": 1,
                    "children": [
                        {
                            "templateId": "template_id_6_1",
                            "templateName": "模板_child2_1_1",
                            "sort": 1,
                            "id": "template-template_id_6_1",
                            "x": 900,
                            "y": 232,
                            "depth": 3,
                            "type": "tree-default-node",
                            "style": {},
                            "ANTV_TREE_COLLPSED_FLAG": true
                        },
                        {
                            "templateId": "template_id_6_2",
                            "templateName": "模板_child2_1_2",
                            "sort": 2,
                            "id": "template-template_id_6_2",
                            "x": 900,
                            "y": 348,
                            "depth": 3,
                            "type": "tree-default-node",
                            "style": {},
                            "ANTV_TREE_COLLPSED_FLAG": true
                        }
                    ],
                    "isGhostHead": true,
                    "id": "template-template_id_6",
                    "x": 600,
                    "y": 232,
                    "depth": 2,
                    "type": "tree-default-node",
                    "style": {},
                    "ANTV_TREE_COLLPSED_FLAG": false
                },
                {
                    "id": "data-6",
                    "name": "child2-2",
                    "sort": 2,
                    "children": [
                        {
                            "templateId": "template_id_7_1",
                            "templateName": "模板_child2_2_1",
                            "sort": 1,
                            "id": "template-template_id_7_1",
                            "x": 900,
                            "y": 464,
                            "depth": 3,
                            "type": "tree-default-node",
                            "style": {}
                        },
                        {
                            "templateId": "template_id_7_2",
                            "templateName": "模板_child2_2_2",
                            "sort": 2,
                            "id": "template-template_id_7_2",
                            "x": 900,
                            "y": 580,
                            "depth": 3,
                            "type": "tree-default-node",
                            "style": {}
                        }
                    ],
                    "x": 600,
                    "y": 464,
                    "depth": 2,
                    "type": "tree-default-node",
                    "style": {}
                }
            ],
            "x": 300,
            "y": 232,
            "depth": 1,
            "type": "tree-default-node",
            "style": {}
        }
    ],
    "x": 0,
    "y": 0,
    "depth": 0,
    "type": "tree-default-node",
    "style": {}
}