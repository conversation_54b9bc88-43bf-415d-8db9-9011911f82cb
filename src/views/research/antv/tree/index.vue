<template>
  <div class="app-container" style="height: 100%; overflow: hidden">
      <div class="app-card" style="height: 100%;">
          <div id="container" style="height:100%"></div>
      </div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import insertCss from "insert-css";
import { props, props2, mergeData,mergeData2, color, dddddata } from "./mockdata";
import { defaultNodeGenerator, ANTV_TREE_COLLPSED_FLAG } from "./utils";
import registerCustomNodes, { NODE_TYPE, NODE_COLLAPSE_STATUS, DISASSEMBLE_NODE, GHOST_NODE, expandNode, collapseNode, expandNodeAll, getId } from "./utils/nodes";

const originMergedData = mergeData2(props2.templateData, props2.data);
let mergedData;
registerCustomNodes('tree-default-node');
export default {
    data(){
        return {
            graph: null,
            matrix: null, // 如果有值，则保持视图位置
        }
    },
    async mounted() {
        mergedData = this.handleData(originMergedData);
        console.log(mergedData)
        insertCss(`
            .g6-component-tooltip {
                background-color: rgba(0,0,0, 0.65);
                padding: 10px;
                box-shadow: rgb(174, 174, 174) 0px 0px 10px;
                width: fit-content;
                color: #fff;
                border-radius = 4px;
            };
        `);
        this.$nextTick(()=>{
            this.drawChart();
        })
    },
    methods: {
        handleData(data) {
            // 预处理数据, 递归将数据中的id字段以getId方法生成, TODO: modelId的生成应该注释掉
            const handleId = (item) => {
                item.id = getId(item);
                if (item.children) {
                    item.children.forEach((child) => {
                        handleId(child);
                    });
                }
                return item;
            };
            return handleId(data);
        },
        initContainer() {
            this.container = document.getElementById("container");
            this.width = this.container.scrollWidth;
            this.height = this.container.scrollHeight || 500;
        },
        registerFun() {
            // G6.registerNode(
            //     "flow-rect",
            //     {
            //         shapeType: "flow-rect",
            //         draw: defaultNodeGenerator,
            //         setState(name, value, item) {
            //             if (name === NODE_COLLAPSE_STATUS) {
            //                 const group = item.getContainer();
            //                 const collapseText = group.find(
            //                     (e) => e.get("name") === "collapse-text"
            //                 );
            //                 if (collapseText) {
            //                     if (!value) {
            //                         collapseText.attr({ text: "-", });
            //                     } else {
            //                         collapseText.attr({ text: "+", });
            //                     }
            //                 }
            //             }
            //         },
            //     },
            //     "rect"
            // );
        },

        initGraph(data) {
            if (!data) {
                return;
            }
            const { config } = props;
            const tooltip = new G6.Tooltip({
                // offsetX and offsetY include the padding of the parent container
                offsetX: 20,
                offsetY: 30,
                // 允许出现 tooltip 的 item 类型
                itemTypes: ["node"],
                // 自定义 tooltip 内容
                getContent: (e) => {
                    const outDiv = document.createElement("div");
                    //outDiv.style.padding = '0px 0px 20px 0px';
                    const model = e.item.getModel();
                    const {
                        produceName,
                        directorName,
                        produceDesc,
                        priority,
                        produceStatus,
                    } = model;
                    outDiv.innerHTML = `任务名称：${produceName}<br/>
                        责任人：${directorName}<br/>
                        任务描述信息：${produceDesc}<br/>
                        任务级别：${require("./enum").priorityList[priority]}<br/>
                        任务状态：${
                            require("./enum").taskStatus[produceStatus]
                        }<br/>`;
                    return outDiv;
                },
                shouldBegin: (e) => {
                    if (e.target.get("name")) return false;
                    return true;
                },
            });
            const contextmenu = new G6.Menu({
                getContent(e) {
                    const model = e.item.getModel();
                    // 是否有子节点
                    const hasChildren = !!(model.children && model.children.length > 0);
                    // 是否折叠状态
                    const isCollapsed = !!model[ANTV_TREE_COLLPSED_FLAG];
                    // 递归判断是否是全部展开的状态
                    const checkAllExpanded = (m) => {
                        if (!m.children || m.children.length === 0) {
                            return true;
                        }
                        return m.children.every((child) => {
                            return !child[ANTV_TREE_COLLPSED_FLAG] && checkAllExpanded(child);
                        });
                    };
                    // 是否是拆卸状态
                    const isDisassemble = model.nodeType === DISASSEMBLE_NODE;
                    const allExpandDiv = `<div style="cursor: pointer" name="ALL_EXPAND">全部展开</div>`;
                    const expandDiv = `<div style="cursor: pointer" name="EXPAND">展开</div>`;
                    const collapseDiv = `<div style="cursor: pointer" name="COLLAPSE">折叠</div>`;
                    const disassembleDiv = `<div style="cursor: pointer" name="DISASSEMBLE">拆卸</div>`;
                    const cancelDisassembleDiv = `<div style="cursor: pointer" name="CANCEL_DISASSEMBLE">取消拆卸</div>`;
                    let content = "";
                    if (hasChildren) {
                        if (isCollapsed) {
                            content = expandDiv + allExpandDiv;
                        } else {
                            // if (checkAllExpanded(model)) {
                            //     content = collapseDiv;
                            // } else {
                            //     content = collapseDiv + allExpandDiv;
                            // }
                            content = collapseDiv;
                        }
                    }
                    content += isDisassemble ? cancelDisassembleDiv : disassembleDiv;
                    return content;
                },
                handleMenuClick: (target, item) => {
                    const name = target.getAttribute("name");
                    const model = item.getModel();
                    switch(name) {
                        case "EXPAND":
                            expandNode(item, this.graph);
                            break;
                        case "COLLAPSE":
                            collapseNode(item, this.graph);
                            break;
                        case "ALL_EXPAND":
                            expandNodeAll(item, this.graph);
                            break;
                        case "DISASSEMBLE":
                            // 假拆卸, 将该节点状态置为待拆卸, 等待最终确认
                            // 确认时只需要遍历所有节点，找到待拆卸的节点id，调用拆卸接口，然后重新获取数据并渲染即可
                            // TODO: 重新渲染前后可能有画面闪烁 --- changeData方法调用的可行性
                            model.nodeType = DISASSEMBLE_NODE;
                            this.graph.setItemState(item, NODE_TYPE, DISASSEMBLE_NODE);
                            // const parentData = this.graph.findDataById(model.id);
                            // if (parentData.children) {
                            //     parentData.children = [];
                            //     this.matrix = this.graph.getGroup().getMatrix();
                            //     this.graph.changeData();
                            // }
                            console.log(this.graph.save())
                            break;
                        case "CANCEL_DISASSEMBLE":
                            model.nodeType = DISASSEMBLE_NODE;
                            this.graph.setItemState(item, NODE_TYPE, '');
                            // const parentData = this.graph.findDataById(model.id);
                            // if (parentData.children) {
                            //     parentData.children = [];
                            //     this.matrix = this.graph.getGroup().getMatrix();
                            //     this.graph.changeData();
                            // }
                            break;
                            
                    }
                },
                offsetX: 16 + 10,
                offsetY: 0,
                // 在哪些类型的元素上响应
                itemTypes: ['node'],
                // 在这里判断要不要显示contextmenu
                shouldBegin: (e) => {
                    return e.target.cfg.name === 'main-rect';
                },
            });
            this.graph = new G6.TreeGraph({
                container: "container",
                ...this.defaultConfig,
                ...config,
                customMode: "READ",
                animate: false,
                plugins: [tooltip, contextmenu],
            });
            this.graph.set('instance', this.graph); // 存储 graph 实例
            this.graph.data(data);
            this.graph.on('afterrender', (e) => {
                const nodes = this.graph.getNodes();
                nodes.forEach(node=>{
                    const model = node.getModel();
                    this.graph.setItemState(node, NODE_TYPE, model.nodeType || 'default');
                });
                this.matrix && graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            })
            this.graph.render();
            // 以保证第一个节点高度为40为标准, 设置整个画布的初始zoom
            const firstNode = this.graph.getNodes()[0];
            if (firstNode) {
                const bbox = firstNode.getBBox();
                console.log(bbox)
                const height = bbox.maxY - bbox.minY;
                this.graph.zoom(40 / height);
            }
            // 因为没有配置fitView, 这里手动平移一下, 否则默认的左上角是应该是第一个节点的中心
            this.graph.translate(100, 50);
            
            // 给+-号添加点击事件
            const handleCollapse = (e) => {
                const item = e.item;
                const model = item.getModel();
                model[ANTV_TREE_COLLPSED_FLAG] ? expandNode(item, this.graph) : collapseNode(item, this.graph);
            };
            this.graph.on("collapse-text:click", handleCollapse);
            this.graph.on("collapse-rect:click", handleCollapse);
            this.graph.on("main-rect:click", (e)=>{
                // const item = e.item;

                // const nodeId = item.get('id');
                // const model = item.getModel();
                // const children = model.children;
                // if (!children || children.length === 0) {
                //     const parentData = this.graph.findDataById(nodeId);
                //     if (!parentData.children) {
                //         parentData.children = [];
                //     }
                //     // 如果childData是一个数组，则直接赋值给parentData.children
                //     // 如果是一个对象，则使用parentData.children.push(obj)
                //     parentData.children = childrenData;
                //     this.matrix = this.graph.getGroup().getMatrix();
                //     this.graph.changeData();
                // }
            });
            this.graph.on("viewportchange", (e) => {
                this.matrix && this.graph.getGroup().setMatrix(this.matrix);
                this.matrix = null;
            });
        },
        drawChart() {
            if (!this.container) {
                this.initContainer();
            }
            this.defaultConfig = {
                width: this.width,
                height: this.height,
                modes: {
                    default: [
                        // 重定义折叠标志
                        {
                            type: "collapse-expand",
                            onChange: function onChange(item, collapsed) {
                                const data = item.get("model");
                                data[ANTV_TREE_COLLPSED_FLAG] = collapsed;
                                return true;
                            },
                        },
                        "drag-canvas",
                        "zoom-canvas",
                    ]
                },
                // fitView: true, // 因为主动初始化了zoom, 这个不能开启
                animate: true,
                defaultNode: {
                    type: "tree-default-node",
                },
                defaultEdge: {
                    type: 'cubic-horizontal',
                    style: {
                    stroke: '#CED4D9',
                    },
                },
                layout: {
                    type: "indented",
                    direction: "LR",
                    dropCap: false,
                    indent: 300,
                    getHeight: () => {
                        return 80;
                    },
                    // 重定义折叠标志
                    getChildren: (model) => {
                        return model[ANTV_TREE_COLLPSED_FLAG] ? [] : model.children;
                    },
                },
            };
            this.registerFun();
            const { data } = props;
            if (this.graph) {
                this.graph.destroy();
            }
            this.initGraph(dddddata);
            if (typeof window !== "undefined")
                window.onresize = () => {
                    if (!this.graph || this.graph.get("destroyed")) return;
                    if (
                        !this.container ||
                        !this.container.scrollWidth ||
                        !this.container.scrollHeight
                    )
                        return;
                    this.graph.changeSize(
                        this.container.scrollWidth,
                        this.container.scrollHeight
                    );
                };
        }
    }
}
</script>
<style lang="scss">
.g6-minimap {
position: absolute;
right: 100px;
top: 100px;
background-color: #fff;
}
</style>
