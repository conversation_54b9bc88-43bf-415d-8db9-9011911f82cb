<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div id="container" style="height:100%"></div>
        </div>
    </div>
</template>
<script>
import G6 from "@antv/g6";
import insertCss from "insert-css";
import { props, color } from "./mockdata";
export default {
  data(){
      return {
          graph: null,
      }
  },
  async mounted() {
        insertCss(`
            .g6-component-tooltip {
                background-color: rgba(0,0,0, 0.65);
                padding: 10px;
                box-shadow: rgb(174, 174, 174) 0px 0px 10px;
                width: fit-content;
                color: #fff;
                border-radius = 4px;
            };
        `);
        this.$nextTick(()=>{
            this.drawChart();
        })
  },
  methods: {
      initContainer() {
          this.container = document.getElementById("container");
          this.width = this.container.scrollWidth;
          this.height = this.container.scrollHeight || 500;
      },
      registerFun() {
          G6.registerNode(
              "flow-rect",
              {
                  shapeType: "flow-rect",
                  draw(cfg, group) {
                      let {
                          produceName = "",
                          collapsed,
                          progress,
                      } = cfg;

                      let inTimeStatus = "NORMAL";
                      progress = (progress || 0) / 100;
                      const grey = "#CED4D9";
                      // 逻辑不应该在这里判断
                      const rectConfig = {
                          width: 232,
                          height: 90,
                          lineWidth: 1,
                          fontSize: 12,
                          fill: color[`${inTimeStatus}_BG`],
                          radius: 4,
                          stroke: grey,
                          opacity: 1,
                      };
                      // 声明节点中心在长方形的中心
                      const nodeOrigin = {
                          x: -rectConfig.width / 2,
                          y: -rectConfig.height / 2,
                      };

                      const textConfig = {
                          textAlign: "left",
                          textBaseline: "bottom",
                      };

                      const rect = group.addShape("rect", {
                          attrs: {
                              x: nodeOrigin.x,
                              y: nodeOrigin.y,
                              ...rectConfig,
                              fill: color[`${inTimeStatus}_BG`],
                          },
                      });
                      const rectBBox = rect.getBBox();

                      // label title
                      group.addShape("text", {
                          attrs: {
                              ...textConfig,
                              x: 12 + nodeOrigin.x,
                              y: 20 + nodeOrigin.y,
                              text:
                                  produceName.length > 16
                                      ? produceName.substr(0, 16) + "..."
                                      : produceName,
                              fontSize: 12,
                              opacity: 0.85,
                              fill: "#000",
                              cursor: "pointer",
                          },
                          // 用于在e.target中作区分
                          produceName: "name-shape",
                      });
                      // 看情况添加+-符号
                      if (
                          cfg.children &&
                          cfg.children.length
                      ) {
                          group.addShape("rect", {
                              attrs: {
                                  x: rectConfig.width / 2 - 8,
                                  y: -10,
                                  width: 16,
                                  height: 16,
                                  stroke: "rgba(0, 0, 0, 0.25)",
                                  cursor: "pointer",
                                  fill: "#fff",
                              },
                              name: "collapse-back",
                              modelId: cfg.id,
                          });
                          group.addShape("text", {
                              attrs: {
                                  x: rectConfig.width / 2,
                                  y: -1,
                                  textAlign: "center",
                                  textBaseline: "middle",
                                  text: collapsed ? "+" : "-",
                                  fontSize: 16,
                                  cursor: "pointer",
                                  fill: "rgba(0, 0, 0, 0.25)",
                              },
                              name: "collapse-text",
                              modelId: cfg.id,
                          });
                      }
                      return rect;
                  },
                  update(cfg, item) {
                      const group = item.getContainer();
                      // this.updateLinkPoints(cfg, group);
                  },
                  setState(name, value, item) {
                      if (name === "collapse") {
                          const group = item.getContainer();
                          const collapseText = group.find(
                              (e) => e.get("name") === "collapse-text"
                          );
                          if (collapseText) {
                              if (!value) {
                                  collapseText.attr({ text: "-", });
                              } else {
                                  collapseText.attr({ text: "+", });
                              }
                          }
                      }
                  },
              },
              "rect"
          );

          G6.registerEdge("line-dash", {}, "cubic");
      },
      initGraph(data) {
          if (!data) {
              return;
          }
          const { config } = props;
          const tooltip = new G6.Tooltip({
              // offsetX and offsetY include the padding of the parent container
              offsetX: 20,
              offsetY: 30,
              // 允许出现 tooltip 的 item 类型
              itemTypes: ["node"],
              // 自定义 tooltip 内容
              getContent: (e) => {
                  const outDiv = document.createElement("div");
                  //outDiv.style.padding = '0px 0px 20px 0px';
                  const model = e.item.getModel();
                  const {
                      produceName,
                      directorName,
                      produceDesc,
                      priority,
                      produceStatus,
                  } = model;
                  outDiv.innerHTML = `任务名称：${produceName}<br/>
                      责任人：${directorName}<br/>
                      任务描述信息：${produceDesc}<br/>
                      任务级别：${require("./enum").priorityList[priority]}<br/>
                      任务状态：${
                          require("./enum").taskStatus[produceStatus]
                      }<br/>`;
                  return outDiv;
              },
              shouldBegin: (e) => {
                  if (e.target.get("name")) return false;
                  return true;
              },
          });
          this.graph = new G6.TreeGraph({
              container: "container",
              ...this.defaultConfig,
              ...config,
              animate: false,
              plugins: [tooltip],
          });
          this.graph.data(data);
          this.graph.render();
          this.graph.zoom(config.defaultZoom || 1);
          // 给+-号添加点击事件
          const handleCollapse = (e) => {
              const target = e.target;
              const id = target.get("modelId");
              const item = this.graph.findById(id);
              const nodeModel = item.getModel();
              nodeModel.collapsed = !nodeModel.collapsed;
              this.graph.layout();
              this.graph.setItemState(item, "collapse", nodeModel.collapsed);
          };
          this.graph.on("collapse-text:click", handleCollapse);
          this.graph.on("collapse-back:click", handleCollapse);
      },
      drawChart() {
          if (!this.container) {
              this.initContainer();
          }
          this.defaultConfig = {
              width: this.width,
              height: this.height,
              modes: {
                  default: ["zoom-canvas", "drag-canvas"],
              },
              fitView: true,
              animate: true,
              defaultNode: {
                  type: "flow-rect",
              },
              defaultEdge: {
                  type: "line-dash",
                  style: {
                      stroke: "#CED4D9",
                  },
              },
              layout: {
                  type: "indented",
                  direction: "LR",
                  dropCap: false,
                  indent: 300,
                  getHeight: () => {
                      return 80;
                  },
              },
          };
          this.registerFun();
          const { data } = props;
          if (this.graph) {
              this.graph.destroy();
          }
          this.initGraph(data);
          if (typeof window !== "undefined")
              window.onresize = () => {
                  if (!this.graph || this.graph.get("destroyed")) return;
                  if (
                      !this.container ||
                      !this.container.scrollWidth ||
                      !this.container.scrollHeight
                  )
                      return;
                  this.graph.changeSize(
                      this.container.scrollWidth,
                      this.container.scrollHeight
                  );
              };
      }
  }
}
</script>
<style lang="scss">
.g6-minimap {
  position: absolute;
  right: 100px;
  top: 100px;
  background-color: #fff;
}
</style>
