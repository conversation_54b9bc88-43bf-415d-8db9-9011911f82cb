<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">工具统计</div>
            <el-card shadow="always" style="margin-bottom:20px">
                <div class="chart-table-container">
                    <div
                        class="top-line"
                        style="border-bottom:1px solid #eee;padding-bottom:10px"
                    >
                        年份：
                        <el-date-picker
                            @change="onYearChange"
                            v-model="year"
                            type="year"
                            placeholder="选择年"
                            format="yyyy"
                            value-format="yyyy"
                        >
                        </el-date-picker>
                    </div>
                    <div
                        id="stackChart"
                        style="width:100%;height:100%;padding:20px 0"
                    ></div></div
            ></el-card>
            <el-card shadow="always" style="margin-bottom:20px">
                <div class="chart-table-container">
                    <div
                        class="top-line"
                        style="border-bottom:1px solid #eee;padding-bottom:10px"
                    >
                        月份：
                        <el-date-picker
                            v-model="month"
                            @change="onMonthChange"
                            type="month"
                            placeholder="选择月"
                            format="yyyy-MM"
                            value-format="yyyy-MM"
                        >
                        </el-date-picker>
                    </div>
                    <div
                        id="treeChart"
                        style="width:100%;height:100%;padding:20px 0"
                    ></div></div
            ></el-card>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import * as echarts from "echarts";
import { option as stackOption } from "./stack";
import { option as treeOption } from "./tree";
import { AppModule } from "@/store/modules/app";
import {
    apiGetToolStandingBookStatisticsByMonth,
    apiGetToolStandingBookStatisticsByHours,
} from "@/api/ledger";
@Component({})
export default class extends Vue {
    private year: any = "";
    private month: any = "";
    private form: any = {};
    private showChart = false;
    private confirmDate: string[] = ["", ""];
    private targetLine = "";
    private chart1: any = null;
    private chart2: any = null;
    private mounted() {
        this.chart1 = echarts.init(
            document.getElementById(`stackChart`) as HTMLDivElement
        );
        this.chart2 = echarts.init(
            document.getElementById(`treeChart`) as HTMLDivElement
        );
        this.resizeFun = () => {
            if (this.chart1) {
                this.chart1.resize({ width: "auto" });
            }
            if (this.chart2) {
                this.chart2.resize({ width: "auto" });
            }
        };
        this.sidebarEl = document.getElementsByClassName(
            "sidebar-container"
        )[0];
        this.sidebarEl.addEventListener("transitionend", this.resizeFun);
        window.addEventListener("resize", this.resizeFun);
        this.year = new Date().Format("yyyy");
        this.month = new Date().Format("yyyy-MM");
        this.renderChart1();
        this.renderChart2();
    }

    get isSidebarOpened() {
        return AppModule.sidebar.opened;
    }
    onYearChange() {
        this.renderChart1();
    }
    onMonthChange() {
        this.renderChart2();
    }
    private renderChart1() {
        if (!this.year) return;

        apiGetToolStandingBookStatisticsByMonth({ time: this.year }).then(
            (res) => {
                const data = res.data.data || [];
                stackOption.xAxis[0].data = data.map(
                    (item: any) => new Date(item.time).Format("M") + "月"
                );
                stackOption.series[0].data = data.map(
                    (item: any) => item.screw_REPAIR
                );
                stackOption.series[1].data = data.map(
                    (item: any) => item.screw_ASSEMBLY
                );
                stackOption.series[2].data = data.map(
                    (item: any) => item.capsule_ASSEMBLY
                );
                stackOption.series[3].data = data.map(
                    (item: any) => item.atbit_ASSEMBLY
                );
                stackOption.series[4].data = data.map(
                    (item: any) => item.insulation_ASSEMBLY
                );
                stackOption.series[5].data = data.map((item: any) => {
                    return {
                        name: item.total,
                        value: item.water_MAINTAIN,
                    };
                });
                console.log(stackOption, "???");
                this.chart1.setOption(stackOption, true);
            }
        );
    }
    private renderChart2() {
        if (!this.month) return;

        apiGetToolStandingBookStatisticsByHours({ time: this.month }).then(
            (res) => {
                treeOption.series[0].data = [
                    {
                        name: "工时统计",
                        children: (res.data.data || []).map((item: any) => {
                            item.name += ": " + item.operateHour + "h";
                            return item;
                        }),
                    },
                ];

                this.chart2.setOption(treeOption);
            }
        );
    }
    private onSearch() {}
    private beforeDestroy() {
        window.removeEventListener("resize", this.resizeFun);
        if (this.sidebarEl) {
            this.sidebarEl.removeEventListener("transitionend", this.resizeFun);
        }
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.chart-table-container {
    width: 100%;
    height: 620px;
}
</style>
