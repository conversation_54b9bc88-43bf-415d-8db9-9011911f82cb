<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">井下工具工单详情</div>
            <el-form ref="form" :model="formData" label-width="90px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item
                            prop="businessType"
                            required
                            label="业务类型"
                        >
                            <el-select
                                v-model="formData.businessType"
                                style="width:100%"
                                clearable
                            >
                                <el-option
                                    v-for="(item, key) in BusinessTypeMap"
                                    :key="key"
                                    :value="key"
                                    :label="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="钻具通知单:">
                            <el-select
                                disabled
                                style="width:100%"
                                v-model="formData.repairId"
                            >
                                <el-option
                                    v-for="item in repairList"
                                    :key="item.repairCode"
                                    :label="item.repairCode"
                                    :value="item.repairId"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <!-- <el-button style="margin-left:10px" type="primary">
                        关联返修单
                    </el-button> -->
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="井号">
                            <el-input
                                :disabled="!!formData.repairId"
                                style="width:100%"
                                v-model="formData.wellNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="jobNum">
                            <el-input
                                :disabled="!!formData.repairId"
                                style="width:100%"
                                v-model="formData.jobNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="接收日期">
                            <el-date-picker
                                style="width:100%"
                                v-model="formData.dateReceived"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="WorkNum">
                            <el-input
                                :disabled="!!formData.repairId"
                                style="width:100%"
                                v-model="formData.workNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="border:1px dashed #ccc;padding:20px">
                <el-button
                    style="margin-bottom:10px"
                    type="primary"
                    @click="onAddProcessFlowRow"
                >
                    新增工具
                </el-button>
                <el-table
                    ref="expandTable"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    class="expand-table"
                    :data="formData.detailList"
                >
                    <el-table-column type="expand">
                        <template slot-scope="expandScope">
                            <div style="padding:10px 40px">
                                <el-form label-width="120px">
                                    <el-row :gutter="10">
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="零件名称"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.invName
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="零件号"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.invCode
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="序列号"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .serialNumber
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="型号"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.model
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="循环温度"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row
                                                            .circulateBht
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="最高温度"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row.maxBht
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="入井时长"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row.hour
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="循环时间"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row
                                                            .circulateHrs
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="泥浆类型"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.mudType
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="弯度"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row.angle
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="耐温"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row
                                                            .endureTemperature
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="扣型"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .claspType
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="扶正器尺寸/类型"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row.stbSize
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="最大外径"
                                                size="normal"
                                            >
                                                <input-number
                                                    align="left"
                                                    style="width:100%;"
                                                    v-model="
                                                        expandScope.row.odMax
                                                    "
                                                ></input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="返修原因"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .returnReason
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="开始维修日期"
                                                size="normal"
                                                required
                                            >
                                                <el-date-picker
                                                    clearable
                                                    style="width:100%"
                                                    value-format="yyyy-MM-dd"
                                                    format="yyyy-MM-dd"
                                                    v-model="
                                                        expandScope.row
                                                            .repairStartDate
                                                    "
                                                >
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="维修完成日期"
                                                size="normal"
                                            >
                                                <el-date-picker
                                                    clearable
                                                    style="width:100%"
                                                    value-format="yyyy-MM-dd"
                                                    format="yyyy-MM-dd"
                                                    v-model="
                                                        expandScope.row
                                                            .repairEndDate
                                                    "
                                                >
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="备注"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.notes
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>

                                <el-button
                                    type="primary"
                                    @click="onAddAllocateRow(expandScope)"
                                >
                                    新增
                                </el-button>
                                <el-table
                                    style="margin-top:10px;width:50%"
                                    :data="expandScope.row.operateList"
                                    border
                                >
                                    <el-table-column
                                        prop="operateUserName"
                                        label="操作人"
                                    >
                                        <template slot-scope="scope">
                                            <span
                                                v-if="
                                                    expandScope.$index !==
                                                        expandScopeEditRowIndex ||
                                                        allocateEditObject.editRowIndex !==
                                                            scope.$index
                                                "
                                            >
                                                {{ scope.row.operateUserName }}
                                            </span>
                                            <el-cascader
                                                v-else
                                                @change="onOperateChange(scope)"
                                                :show-all-levels="false"
                                                style="width:90%"
                                                v-model="
                                                    scope.row.operateUserId
                                                "
                                                :options="userTreeList"
                                                :props="{
                                                    value: 'id',
                                                    label: 'name',
                                                    emitPath: false,
                                                }"
                                            ></el-cascader>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        prop="operateHour"
                                        label="工时"
                                    >
                                        <template slot-scope="scope">
                                            <span
                                                v-if="
                                                    expandScope.$index !==
                                                        expandScopeEditRowIndex ||
                                                        allocateEditObject.editRowIndex !==
                                                            scope.$index
                                                "
                                            >
                                                {{ scope.row.operateHour }}
                                            </span>
                                            <el-input
                                                v-else
                                                v-model="scope.row.operateHour"
                                            ></el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="100">
                                        <template slot-scope="scope">
                                            <span
                                                v-if="
                                                    expandScope.$index !==
                                                        expandScopeEditRowIndex ||
                                                        allocateEditObject.editRowIndex !==
                                                            scope.$index
                                                "
                                            >
                                                <el-button
                                                    type="text"
                                                    @click="
                                                        onEditAllocateRow(
                                                            scope,
                                                            expandScope
                                                        )
                                                    "
                                                >
                                                    编辑
                                                </el-button>
                                                <el-button
                                                    type="text"
                                                    @click="
                                                        onDeleteAllocateRow(
                                                            scope,
                                                            expandScope
                                                        )
                                                    "
                                                >
                                                    删除
                                                </el-button>
                                            </span>
                                            <span v-else>
                                                <el-button
                                                    type="text"
                                                    @click="
                                                        onSaveAllocateRow(scope)
                                                    "
                                                >
                                                    保存
                                                </el-button>
                                                <el-button
                                                    type="text"
                                                    @click="
                                                        onCancelAllocateRow(
                                                            scope,
                                                            expandScope
                                                        )
                                                    "
                                                >
                                                    取消
                                                </el-button>
                                            </span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invName" label="零件名称">
                    </el-table-column>
                    <el-table-column prop="invCode" label="零件号">
                    </el-table-column>
                    <el-table-column prop="serialNumber" label="序列号">
                    </el-table-column>
                    <el-table-column prop="model" label="型号">
                    </el-table-column>
                    <el-table-column
                        prop="repairStartDate"
                        label="开始维修日期"
                    >
                    </el-table-column>
                    <el-table-column prop="repairEndDate" label="维修完成日期">
                    </el-table-column>
                    <el-table-column prop="notes" label="备注">
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                @click="onDeleteProcessFlowRow(scope)"
                            >
                                删除
                            </el-button>
                            <el-button
                                type="text"
                                @click="onProcessTool(scope)"
                            >
                                操作仪器
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div style="float:right;margin:20px">
                <el-button type="primary" @click="onConfirm">
                    保存工单
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import {
    apiGetToolStandingBookInfo,
    apiSaveToolStandingBook,
} from "@/api/ledger";
import { Table as ElTable } from "element-ui";
import { apiGetRepairUnderList } from "@/api/repair";
import { BusinessTypeMap } from "./constant";
import { Form as ElForm } from "element-ui";
import { validateItemList } from "./validateItem";
import { ConstantModule } from "@/store/modules/constant";
@Component({})
export default class extends Vue {
    private repairList: any[] = [];
    private businessOrderId = "";
    private formData: any = { detailList: [] };
    private expandScopeEditRowIndex = -1;
    private isProcessDialogVisible = false;
    private isDialogVisible = false;
    private form: any = {};
    private userTreeList: any[] = [];
    private procesFlowEditObject = { editRow: false, editRowIndex: -1 };
    private processFlowPreObject: any = {};
    private allocatePreObject: any = {};
    private allocateEditObject = { editRow: false, editRowIndex: -1 };
    private BusinessTypeMap = BusinessTypeMap;
    private async created() {
        this.toolStandBookId = this.$route.query?.id;
        this.userTreeList = this.userTreeList = await ConstantModule.getUserTreeList();
        this.procesFlowEditObject = { editRow: false, editRowIndex: -1 };
        this.allocateEditObject = { editRow: false, editRowIndex: -1 };
        await this.getProcessInfo();
        this.isDialogVisible = true;
        this.getRepairUnderList();
    }
    private getInitDetailVo() {
        return {
            angle: "",
            circulateBht: "",
            circulateHrs: "",
            claspType: "",
            endureTemperature: "",
            hour: "",
            maxBht: "",
            model: "",
            mudType: "",
            notes: "",
            odMax: "",
            invCode: "",
            invName: "",
            repairEndDate: "",
            repairStartDate: "",
            returnReason: "",
            serialNumber: "",
            stbSize: "",
            toolDetailId: "",
            toolStandBookId: "",
        };
    }
    private onRepairIdChange(repairId: string) {}
    getRepairUnderList() {
        apiGetRepairUnderList({}).then((res) => {
            this.repairList = res.data.data || [];
        });
    }
    private async showDialog(scope: any) {
        this.toolStandBookId = scope.row.toolStandBookId;
        this.procesFlowEditObject = { editRow: false, editRowIndex: -1 };
        this.allocateEditObject = { editRow: false, editRowIndex: -1 };
        await this.getProcessInfo();
        this.isDialogVisible = true;
        this.getRepairUnderList();
    }
    private async getProcessInfo() {
        await apiGetToolStandingBookInfo({
            toolStandBookId: this.toolStandBookId,
        }).then((res) => {
            this.formData = res.data.data;
        });
    }
    private validItem(targetList: any[], validateList: any[]): boolean {
        return targetList.every((item1) =>
            validateList.every((item2) => item1[item2] || item1[item2] == 0)
        );
    }
    private onConfirm() {
        (this.$refs.form as ElForm).validate((valid) => {
            if (valid) {
                const itemValid = this.validItem(
                    this.formData.detailList || [],
                    validateItemList
                );
                if (itemValid) {
                    apiSaveToolStandingBook(this.formData).then(() => {
                        this.$emit("getStandingBookList");
                        this.isDialogVisible = false;
                    });
                } else {
                    this.$message.error("工具列表中有必填项未填");
                }
            }
        });
    }
    // 工序信息业务
    private onCheckUserChange(scope: any) {
        scope.row.checkUserName = this.findNameById(scope.row.checkUserId);
    }
    private findNameById(id: string) {
        let flag = false;
        let ret = "";
        function recursiveGetName(children: any[]) {
            for (let i = 0, len = children.length; i < len; i++) {
                if (flag) {
                    break;
                }
                if (children[i].id == id) {
                    flag = true;
                    ret = children[i].name;
                }
                if (children[i].children && !flag) {
                    recursiveGetName(children[i].children);
                }
            }
        }
        recursiveGetName(this.userTreeList);
        return ret;
    }
    private onAddProcessFlowRow() {
        if (!this.formData.detailList) {
            this.$set(this.formData, "detailList", []);
        }
        this.formData.detailList.push(this.getInitDetailVo());
        this.processFlowPreObject = {};
        this.procesFlowEditObject.editRow = true;
        this.procesFlowEditObject.editRowIndex =
            this.formData.detailList.length - 1;
    }

    private onEditProcessFlowRow(scope: any) {
        this.processFlowPreObject = { ...scope.row };
        this.procesFlowEditObject.editRowIndex = scope.$index;
    }
    private onProcessTool(scope: any) {
        this.$router.push(`/downholetool/assemble`);
    }
    private onDeleteProcessFlowRow(scope: any) {
        this.formData.detailList.splice(scope.$index, 1);
    }

    private onSaveProcessFlowRow(scope: any) {
        this.procesFlowEditObject.editRowIndex = -1;
    }
    private async onCancelProcessFlowRow(scope: any) {
        if (Object.keys(this.processFlowPreObject).length) {
            this.formData.detailList[scope.$index] = {
                ...this.processFlowPreObject,
            };
        } else {
            this.formData.detailList.pop();
        }
        this.procesFlowEditObject.editRowIndex = -1;
    }

    // 人员分配业务
    private onOperateChange(scope: any) {
        scope.row.operateUserName = this.findNameById(scope.row.operateUserId);
    }
    private onAddAllocateRow(expandScope: any) {
        if (this.allocateEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.allocatePreObject = {};
        if (!expandScope.row.operateList) {
            this.$set(expandScope.row, "operateList", []);
            this.$nextTick(() => {
                (this.$refs.expandTable as ElTable).toggleRowExpansion(
                    expandScope.row,
                    true
                );
            });
        }
        expandScope.row.operateList.push({
            operateHour: "",
            operateUserId: "",
            operateUserName: "",
        });
        this.allocateEditObject.editRow = true;
        this.expandScopeEditRowIndex = expandScope.$index;
        this.allocateEditObject.editRowIndex =
            expandScope.row.operateList.length - 1;
    }

    private onEditAllocateRow(scope: any, expandScope: any) {
        if (this.allocateEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.allocatePreObject = { ...scope.row };
        this.allocateEditObject.editRowIndex = scope.$index;
        this.expandScopeEditRowIndex = expandScope.$index;
    }

    private onDeleteAllocateRow(scope: any, expandScope: any) {
        if (this.allocateEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        expandScope.row.operateList.splice(scope.$index, 1);
    }

    private onSaveAllocateRow() {
        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
    }

    private onCancelAllocateRow(scope: any, expandScope: any) {
        if (Object.keys(this.allocatePreObject).length) {
            scope.row = {
                ...this.allocatePreObject,
            };
        } else {
            this.formData.detailList[expandScope.$index].operateList.pop();
        }

        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
        this.allocatePreObject = {};
    }
}
</script>
<style lang="scss">
.expand-table {
    .el-table__cell.el-table__expanded-cell {
        padding: 0;
        // background: #f5f7fa;
    }
    .el-table__expanded-cell:hover {
        // background-color: #f5f7fa !important;
    }
}
</style>
