<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">工具台账</div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="4">
                        <el-form-item label="查询月份" size="normal">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM"
                                format="yyyy-MM"
                                v-model="form.month"
                                type="month"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="业务类型" size="normal">
                            <el-select
                                v-model="form.businessType"
                                style="width:100%"
                                clearable
                            >
                                <el-option
                                    v-for="(item, key) in BusinessTypeMap"
                                    :key="key"
                                    :value="key"
                                    :label="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="" size="normal" label-width="0">
                            <el-button type="primary" @click="onSearch"
                                >查询</el-button
                            >
                            <el-button type="primary" @click="onAdd"
                                >新增</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column prop="workNumber" width="200" label="WorkNum">
                </el-table-column>
                <el-table-column prop="wellNumber" label="服务井号">
                </el-table-column>
                <el-table-column prop="jobNumber" label="作业号">
                </el-table-column>
                <el-table-column prop="businessType" label="业务类型">
                    <template slot-scope="scope">
                        {{ BusinessTypeMap[scope.row.businessType] }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="dateReceived"
                    label="接收时间"
                ></el-table-column>
                <el-table-column label="操作" width="220">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            style="margin-right:10px"
                            @click="onDetail(scope)"
                        >
                            详情
                        </el-button>
                        <el-popover
                            placement="top"
                            :ref="`popover-${scope.$index}`"
                        >
                            <p>确认删除？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onDelete(scope)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button
                                style="margin-right:10px"
                                slot="reference"
                                type="text"
                            >
                                删除
                            </el-button>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                style="margin-top:20px"
                :hide-on-single-page="true"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <update-batch-dialog
            :userTreeList="userTreeList"
            ref="updateBatchDialog"
        >
        </update-batch-dialog>
        <add-batch-dialog
            :userTreeList="userTreeList"
            @getStandingBookList="getStandingBookList"
            ref="addBatchDialog"
        ></add-batch-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import UpdateBatchDialog from "./updateBatchDialog.vue";
import AddBatchDialog from "./addBatchDialog.vue";
import {
    apiGetToolStandingBookList,
    apiDeleteToolStandingBook,
} from "@/api/ledger";
import { BusinessTypeMap } from "./constant";
import { ConstantModule } from "@/store/modules/constant";
@Component({ components: { UpdateBatchDialog, AddBatchDialog } })
export default class extends Vue {
    private total = 1;
    private currentPage = 1;
    private BusinessTypeMap = BusinessTypeMap;
    private businessType = "";
    private tableData = [];
    private userTreeList: any[] = [];
    private processFlowName: string = "";
    private partNumber = "";
    private confirmDate = "";
    private form: any = {};
    private async mounted() {
        this.getStandingBookList();
        this.userTreeList = await ConstantModule.getUserTreeList();
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getStandingBookList();
    }
    private onDetail(scope: any) {
        // (this.$refs.updateBatchDialog as any).showDialog(scope);
        this.$router.push(`/tool-ledger/tool-ledger-detail?id=${scope.row.toolStandBookId}`)
    }
    private onAdd() {
        (this.$refs.addBatchDialog as any).showDialog("ADD");
    }
    private async getStandingBookList(
        condition: any = this.form,
        cs: any = { current: this.currentPage, size: 10 }
    ) {
        await apiGetToolStandingBookList(condition, cs).then((res) => {
            this.tableData = res.data.data.records;
            this.total = res.data.data.total;
        });
    }
    private onDelete(scope: any) {
        apiDeleteToolStandingBook({
            toolStandBookId: scope.row.toolStandBookId,
        }).then(() => {
            this.getStandingBookList();
        });
    }
    private onSearch() {
        this.currentPage = 1;
        this.getStandingBookList(this.form);
    }
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
</style>
