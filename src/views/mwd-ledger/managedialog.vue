<template>
    <el-dialog
        :close-on-click-modal="false"
        top="1vh"
        width="80%"
        :visible.sync="isDialogVisible"
    >
        <el-form :model="baseInfo" label-width="90px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="返修单:">
                        <el-select
                            :disabled="workOrderId"
                            clearable
                            style="width:100%"
                            v-model="businessOrderId"
                            @change="onRepairIdChange"
                        >
                            <el-option
                                v-for="item in repairList"
                                :key="item.repairCode"
                                :label="item.repairCode"
                                :value="item.repairId"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="16">
                    <!-- <el-button style="margin-left:10px" type="primary">
                        关联返修单
                    </el-button> -->
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="jobNum:">
                        <el-input
                            :disabled="businessOrderId>0"
                            v-model="baseInfo.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="井号:">
                        <el-input
                            :disabled="businessOrderId>0"
                            v-model="baseInfo.wellNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱编号:">
                        <el-input
                            :disabled="businessOrderId>0"
                            v-model="baseInfo.kitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人:">
                        <el-input
                            :disabled="businessOrderId>0"
                            v-model="baseInfo.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="接收日期:">
                        <el-date-picker
                            style="width:100%"
                            v-model="baseInfo.dateReceived"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="完成日期:">
                        <el-date-picker
                            style="width:100%"
                            v-model="baseInfo.dateCompleted"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-tabs v-model="activeTab">
                <el-tab-pane
                    label="仪器清单"
                    name="first"
                    style="min-height:635px"
                >
                    <el-button type="primary" @click="onAddRepairListRow">
                        新增行
                    </el-button>
                    <el-table :data="repairInfoList" max-height="600">
                        <el-table-column type="expand">
                            <template slot-scope="expandScope">
                                <el-form
                                    :model="expandScope.row"
                                    label-width="100px"
                                >
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="工具类型"
                                                size="normal"
                                            >
                                                <el-select
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .invClassCode
                                                    "
                                                >
                                                    <el-option
                                                        v-for="item in ToolTypeList"
                                                        :key="item.invClassCode"
                                                        :label="
                                                            item.invClassName
                                                        "
                                                        :value="
                                                            item.invClassCode
                                                        "
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="序列号"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .serialNumber
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="维修人"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.sponsor
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="维修工时"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .laborHours
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="所有者"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .assertOwner
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="现场联系人"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .contactUser
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="维修开始日"
                                                size="normal"
                                            >
                                                <el-date-picker
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .repairStartDate
                                                    "
                                                    type="date"
                                                    format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd"
                                                ></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="预计完成日"
                                                size="normal"
                                            >
                                                <el-date-picker
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .estimatedCompDate
                                                    "
                                                    type="date"
                                                    format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd"
                                                ></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="实际完成日"
                                                size="normal"
                                            >
                                                <el-date-picker
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .actualFinishDate
                                                    "
                                                    type="date"
                                                    format="yyyy-MM-dd"
                                                    value-format="yyyy-MM-dd"
                                                ></el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="状态"
                                                size="normal"
                                            >
                                                <el-select
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .maintenanceStatus
                                                    "
                                                >
                                                    <el-option
                                                        v-for="item in MaintenanceStatusList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="故障类型"
                                                size="normal"
                                            >
                                                <el-select
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .failureType
                                                    "
                                                >
                                                    <el-option
                                                        v-for="item in FailureTypeList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="失效部件分类"
                                                size="normal"
                                            >
                                                <el-select
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .failedPartLabel
                                                    "
                                                >
                                                    <el-option
                                                        v-for="item in FailureComponentTypeList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id"
                                                    ></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="入井时间"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.hour
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="循环时间"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row
                                                            .circulateHrs
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="最高温度"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.maxBht
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="入井趟次"
                                                size="normal"
                                            >
                                                <el-input
                                                    v-model="
                                                        expandScope.row.runCount
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="描述信息"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .description
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="返修原因"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .returnReason
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>

                                        <el-col :span="6">
                                            <el-form-item
                                                label="根本原因"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .rootCause
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="入厂部件"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .loadInMainSn
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="滞超原因"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .overdueReasons
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="解决方案"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row.actions
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="出厂部件"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row
                                                            .loadOutMainSn
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                label="车间方案"
                                                size="normal"
                                            >
                                                <el-input
                                                    type="textarea"
                                                    :autosize="{ minRows: 3 }"
                                                    v-model="
                                                        expandScope.row.findings
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </template>
                        </el-table-column>
                        <el-table-column prop="invClassName" label="工具类型">
                            <template slot-scope="scope">
                                {{
                                    findInvNameByInvCode(scope.row.invClassCode)
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="serialNumber" label="序列号">
                        </el-table-column>
                        <el-table-column prop="sponsor" label="维修人员">
                        </el-table-column>
                        <el-table-column prop="description" label="描述信息">
                        </el-table-column>
                        <el-table-column prop="maintenanceStatus" label="状态">
                            <template slot-scope="scope">
                                {{
                                    findMaintenanceStatusById(
                                        scope.row.maintenanceStatus
                                    )
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="laborHours" label="工时">
                        </el-table-column>
                        <el-table-column
                            prop="repairStartDate"
                            label="维修开始日"
                        >
                        </el-table-column
                        ><el-table-column
                            prop="estimatedCompDate"
                            label="预计完成日"
                        >
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="onDeleteRepairListRow(scope)"
                                >
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane
                    label="替换清单"
                    name="second"
                    style="min-height:635px"
                >
                    <el-button type="primary" @click="onAddReplaceListRow">
                        新增行
                    </el-button>
                    <el-table :data="replacedInfoList" max-height="600">
                        <el-table-column prop="partNumber" label="P/N">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.partNumber }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.partNumber"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="chineseDescription"
                            label="中文描述"
                        >
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.chineseDescription }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.chineseDescription"
                                ></el-input>
                            </template>
                        </el-table-column>

                        <el-table-column
                            prop="englishDescription"
                            label="英文描述"
                        >
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.englishDescription }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.englishDescription"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="quantity" label="数量">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.quantity }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.quantity"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="parentSerialNumber"
                            label="父级工具"
                        >
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.parentSerialNumber }}
                                </span>
                                <el-select
                                    v-else
                                    v-model="scope.row.parentSerialNumber"
                                    placeholder="父级工具序列号"
                                >
                                    <el-option
                                        v-for="item in repairSerialNumberList"
                                        :key="item"
                                        :label="item"
                                        :value="item"
                                    ></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="notes" label="备注">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    {{ scope.row.notes }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.notes"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        replaceListEditObject.editRowIndex !==
                                            scope.$index
                                    "
                                >
                                    <el-button
                                        type="text"
                                        @click="onEditReplaceListRow(scope)"
                                    >
                                        编辑
                                    </el-button>
                                    <el-button
                                        type="text"
                                        @click="onDeleteReplaceListRow(scope)"
                                    >
                                        删除
                                    </el-button>
                                </span>
                                <span v-else>
                                    <el-button
                                        type="text"
                                        @click="onSaveReplaceListRow(scope)"
                                    >
                                        保存
                                    </el-button>
                                    <el-button
                                        type="text"
                                        @click="onCancelReplaceListRow(scope)"
                                    >
                                        取消
                                    </el-button>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </el-form>
        <span slot="footer">
            <el-button type="primary" @click="onConfirm">确认</el-button>
            <el-button @click="onCancel">取消</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import {
    apiGetMwdWorkOderInfo,
    apiSaveMwdWorkOderInfo,
    getInventoryClassList,
} from "@/api/ledger";
import { apiGetKitBoxList } from "@/api/kitBox";
import getDict from "@/utils/getDict";
import { IBusinessType } from "@/utils/constant";
import { apiGetRepairMWDList } from "@/api/repair";
import { validateItemList } from "./validateItem";
import { ConstantModule } from "@/store/modules/constant";
@Component({})
export default class extends Vue {
    private businessOrderId: any = null;
    private repairList: any[] = [];
    private wellList: any[] = [];
    private kitBoxList: any[] = [];
    private workOrderId: any = null;
    private parentSerialNumber = "";
    private ToolTypeList: any[] = [];
    private MaintenanceStatusList: IBusinessType[] = []; // 12
    private FailureTypeList: IBusinessType[] = []; // 13
    private FailureComponentTypeList: IBusinessType[] = []; // 14
    private editType = "ADD";
    private baseInfo: any = {};
    private repairInfoList: any[] = [];
    private replacedInfoList: any[] = [];
    private isDialogVisible = false;
    private form = {};
    private replaceListEditObject = { editRow: false, editRowIndex: -1 };
    private replaceListPreObject: any = {};
    private activeTab = "first";
    get repairSerialNumberList() {
        let snSet = new Set(
            this.repairInfoList.map((item) => item.serialNumber)
        );
        return [...snSet];
    }
    private async mounted() {}
    private onRepairIdChange(repairId: string) {
        //alert(this.businessOrderId)
        let idx = this.repairList.findIndex(
            (item) => item.repairId == repairId
        );
        let temp = this.repairList[idx];
        this.baseInfo.workOrderNumber = temp.repairCode;
        this.baseInfo.jobNumber = temp.jobNumber;
        this.baseInfo.wellNumber = temp.wellNumber;
        this.baseInfo.kitNumber = temp.kitNumber;
        this.baseInfo.workOrderNumber = temp.repairCode;
    }
    private async showDialog(scope: any) {
        // this.workOrderId = scope.row?.workOrderId;
        this.workOrderId = scope.row?.workOrderId;
        [
            this.MaintenanceStatusList,
            this.FailureTypeList,
            this.FailureComponentTypeList,
        ] = await getDict([12, 13, 14]);
        await getInventoryClassList({}).then((res) => {
            this.ToolTypeList = res.data.data || [];
        });
        this.getMwdRepairList();
        if (this.workOrderId) {
            await apiGetMwdWorkOderInfo({
                workOrderId: this.workOrderId,
            }).then((res) => {
                this.businessOrderId = res.data.data.businessOrderId;
                this.baseInfo = res.data.data.baseInfo;
                this.repairInfoList = res.data.data.repairInfoList || [];
                this.replacedInfoList = res.data.data.replacedInfoList || [];
            });
        } else {
            this.baseInfo = {};
            this.repairInfoList = [];
            this.replacedInfoList = [];
        }
        this.getKitList();
        this.getWellList();
        this.isDialogVisible = true;
    }
    getKitList() {
        apiGetKitBoxList({}).then((res) => {
            this.kitBoxList = res.data.data || [];
        });
    }
    getMwdRepairList() {
        apiGetRepairMWDList({}).then((res) => {
            this.repairList = res.data.data || [];
        });
    }
    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }
    private validItem(targetList: any[], validateList: any[]): boolean {
        return targetList.every((item1) =>
            validateList.every((item2) => item1[item2] || item1[item2] == 0)
        );
    }
    onConfirm() {
        const itemValid = this.validItem(
            this.repairInfoList || [],
            validateItemList
        );
        if (itemValid) {
            apiSaveMwdWorkOderInfo({
                businessOrderId: this.businessOrderId,
                businessOrderType: "REPAIR_ORDER",
                baseInfo: this.baseInfo,
                repairInfoList: this.repairInfoList,
                replacedInfoList: this.replacedInfoList,
            }).then(() => {
                this.$emit("updateList");
                this.isDialogVisible = false;
            });
        } else {
            this.$message.error("仪器清单中有必填项未填");
        }
    }
    onCancel() {
        this.isDialogVisible = false;
    }
    // 仪器清单业务
    onAddRepairListRow() {
        this.repairInfoList.push({
            invClassCode: "",
            serialNumber: "",
            maintenanceStatus: "",
            loadInMainSn: "",
            loadOutMainSn: "",
            assertOwner: "",
            sponsor: "",
            repairStartDate: "",
            estimatedCompDate: "",
            overdueReasons: "",
            returnReason: "",
            findings: "",
            rootCause: "",
            actions: "",
            hour: "",
            circulateHrs: "",
            maxBht: "",
            runCount: "",
            failureType: "",
            completedFr: "",
            failedPartLabel: "",
            eaStatus: "",
            responsibility: "",
            notes: "",
            laborHours: "",
            contactUser: "",
            actualFinishDate: "",
            description: "",
        });
    }
    findInvNameByInvCode(invClassCode: string) {
        if (invClassCode) {
            let idx = this.ToolTypeList.findIndex(
                (item: any) => item.invClassCode == invClassCode
            );
            if (idx < 0) {
                return "";
            } else {
                return this.ToolTypeList[idx].invClassName;
            }
        } else {
            return "";
        }
    }
    private findMaintenanceStatusById(id: number) {
        if (id) {
            let idx = this.MaintenanceStatusList.findIndex(
                (item) => item.id == id
            );
            if (idx < 0) {
                return "";
            } else {
                return this.MaintenanceStatusList[idx].name;
            }
        } else {
            return "";
        }
    }
    onDeleteRepairListRow(scope: any) {
        this.repairInfoList.splice(scope.$index, 1);
    }
    // 替换清单业务
    private onAddReplaceListRow() {
        if (this.replaceListEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.replacedInfoList.push({
            partNumber: "",
            quantity: "",
            chineseDescription: "",
            englishDescription: "",
            parentSerialNumber: this.parentSerialNumber,
            maintenancePaperwork: null,
            notes: null,
        });
        this.replaceListPreObject = {};
        this.replaceListEditObject.editRow = true;
        this.replaceListEditObject.editRowIndex =
            this.replacedInfoList.length - 1;
    }

    private onEditReplaceListRow(scope: any) {
        if (this.replaceListEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.replaceListPreObject = { ...scope.row };
        this.replaceListEditObject.editRowIndex = scope.$index;
    }

    private onDeleteReplaceListRow(scope: any) {
        if (this.replaceListEditObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.replacedInfoList.splice(scope.$index, 1);
    }

    private onSaveReplaceListRow(scope: any) {
        this.replaceListEditObject.editRowIndex = -1;
    }
    private async onCancelReplaceListRow(scope: any) {
        if (Object.keys(this.replaceListPreObject).length) {
            this.replacedInfoList[scope.$index] = {
                ...this.replaceListPreObject,
            };
        } else {
            this.replacedInfoList.pop();
        }
        this.replaceListEditObject.editRowIndex = -1;
    }
}
</script>
