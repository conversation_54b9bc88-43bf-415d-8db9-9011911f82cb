<template>
    <el-card shadow="always" style="margin-bototm:20px">
        <div class="chart-table-container">
            <div
                class="top-line"
                style="border-bottom:1px solid #eee;padding-bottom:10px;position:relative"
            >
                Target Line(%)：
                <el-input
                    style="width:100px"
                    size="mini"
                    v-model="targetLine"
                    @change="onTargetLineChange"
                ></el-input>
                <span
                    style="position:absolute;left:50%;transform: translate(-50%);font-size:18px;font-weight:800"
                >
                    {{ chartKey }}
                </span>
                <el-button
                    style="float:right;margin-right:10px"
                    type="text"
                    @click="showChart = !showChart"
                    >{{ showChart ? "切换表" : "切换图" }}</el-button
                >
            </div>

            <el-table v-if="!showChart" :data="tableData" style="width:100%">
                <el-table-column align="center" prop="time" label="time">
                </el-table-column>
                <el-table-column align="center" prop="pm" label="PM">
                </el-table-column>
                <el-table-column align="center" prop="df" label="DF">
                </el-table-column>
                <el-table-column align="center" prop="sf" label="SF">
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="rate"
                    label="Ratio of DF"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="targetLine"
                    label="Target Line"
                    width="110"
                >
                    <template slot-scope="{}">{{ targetLine }}</template>
                </el-table-column>
            </el-table>
            <div
                v-if="showChart"
                :id="`standing-book-${chartKey}`"
                style="width:100%;height:100%;padding:20px 0"
            ></div></div
    ></el-card>
</template>
<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";
import Vue from "vue";
import * as echarts from "echarts";
import { option } from "./option";
import { AppModule } from "@/store/modules/app";
@Component({})
export default class extends Vue {
    private form: any = {};
    private showChart = false;
    private confirmDate: string[] = ["", ""];
    private targetLine = "";
    private chart: any = null;
    @Prop({ default: "" }) chartKey!: string;
    @Prop({ default: () => [] }) tableData!: any[];
    private mounted() {
        this.resizeFun = () => {
            if (this.chart && this.showChart) {
                this.chart.resize({ width: "auto" });
            }
        };
        this.sidebarEl = document.getElementsByClassName(
            "sidebar-container"
        )[0];
        this.sidebarEl.addEventListener("transitionend", this.resizeFun);
        window.addEventListener("resize", this.resizeFun);
    }

    get isSidebarOpened() {
        return AppModule.sidebar.opened;
    }
    @Watch("showChart") watchShowChart(nv: boolean) {
        if (nv) {
            this.$nextTick(() => {
                this.renderChart();
            });
        }
    }
    onTargetLineChange() {
        this.renderChart();
    }
    private renderChart() {
        this.chart = echarts.init(
            document.getElementById(
                `standing-book-${this.chartKey}`
            ) as HTMLDivElement
        );
        option.xAxis[0].data = this.tableData.map((item) => item.time);
        option.series[0].data = this.tableData.map((item) => item.pm);
        option.series[1].data = this.tableData.map((item) => item.df);
        option.series[2].data = this.tableData.map((item) => item.sf);
        option.series[3].data = this.tableData.map((item) => item.rate);
        option.series[4].data = this.tableData.map(
            (item) => Number(this.targetLine) / 100
        );
        this.chart.setOption(option);
    }
    private onSearch() {}
    private beforeDestroy() {
        window.removeEventListener("resize", this.resizeFun);
        if (this.sidebarEl) {
            this.sidebarEl.removeEventListener("transitionend", this.resizeFun);
        }
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.chart-table-container {
    width: 100%;
    height: 620px;
}
</style>
