<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">MWD台账管理</div>
            <div class="simple-line"></div>
            <el-form ref="searchForm" label-width="100px">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="WorkNo: " prpo="wordNumber">
                            <el-input v-model.trim="searchForm.workOrderNumber" placeholder="请填写" clearable
                                      style="width:180px">
                            
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="井号: " prop="invClassCode">
                            <el-select v-model="searchForm.wellNumber" clearable placeholder="请选择井"
                                       style="width:180px">
                                <el-option v-for="(item, key) in wellList"
                                           :key="`wellId` + key"
                                           :label="item.wellNumber"
                                           :value="item.wellNumber"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="接受日期">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.dateReceived"
                                type="date"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label-width="0">
                            <el-button style="margin-left:30px" type="primary" @click="onSearch"
                            >查询
                            </el-button>
                            <el-button style="margin-left:30px" type="primary" @click="onAdd"
                            >新增
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="simple-line"></div>
            <el-table
                :data="infoList"
                row-key="produceId"
                :expand-row-keys="defaultExpandRowKeys"
                :row-class-name="tableRowClassName"
                @expand-change="test"
                ref="table"
                :header-cell-style="commmonTableHeaderCellStyle">
                <el-table-column>
                    <template slot="header">WorkNo</template>
                    <template slot-scope="scope">
                        <span>{{ scope.row.workOrderNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber">
                </el-table-column>
                <el-table-column
                    label="kit箱子"
                    prop="kitNumber">
                </el-table-column>
                <el-table-column
                    label="接收日期"
                    prop="dateReceived">
                </el-table-column>
                <el-table-column
                    label="完成日期"
                    prop="dateCompleted">
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="text"
                                   style="margin-right:10px"
                                   @click="onDetail(scope)">
                            详情
                        </el-button>
                        <el-button type="text" @click="onDelete(scope)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <manage-dialog @updateList="getMwdOrderList" ref="manageDialog"></manage-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiMwdOrderDelete, apiMwdOrderList, apiMwdStandingBook } from "@/api/mwd";
import ManageDialog from "./managedialog.vue";
import { ConstantModule } from "@/store/modules/constant";

@Component({ components: { ManageDialog } })
export default class extends Vue {
    private total = 1;
    private currentPage = 1;
    private searchForm = {
        workOrderNumber: "",
        wellNumber: "",
        dateReceived: "",
    };
    private typeList: any[] = [
        { id: "REPAIR_ORDER", businessOrderType: "返修单" }, { id: 2, businessOrderType: "调拨单" }, {
            id: 3,
            businessOrderType: "失效报告单",
        }, { id: 4, businessOrderType: "需求单" },
    ];
    
    private changeBusiness() {
        this.getMwdOrderList();
    }
    
    private businessOrderType = "REPAIR_ORDER";
    private defaultExpandRowKeys: any[] = [];
    private wellList: any[] = [];
    private infoList: any[] = [];
    private pager = {
        total: 0, size: 10, current: 1,
    };
    
    async mounted() {
        this.wellList = await ConstantModule.getWellList()
        this.getMwdOrderList();
    }
    
    private getMwdOrderList() {
        let params: any = { current: this.currentPage, size: 10 };
        let detail: any = {};
        if (this.businessOrderType != "")
            detail.businessOrderType = this.businessOrderType;
        if (this.searchForm.workOrderNumber != "")
            detail.workOrderNumber = this.searchForm.workOrderNumber;
        if (this.searchForm.wellNumber != "")
            detail.wellNumber = this.searchForm.wellNumber;
        if (this.searchForm.dateReceived != "") {
            detail.dateReceived = this.searchForm.dateReceived;
        }
        apiMwdOrderList(params, detail).then((response) => {
            let { code, message, data } = response.data;
            this.infoList = data.records;
            this.total = data.total;
        }).catch((err) => {
            console.log(err);
        });
    }
    
    private onSearch() {
        this.currentPage = 1;
        this.getMwdOrderList();
    }
    
    private test(row: any) {
        apiMwdStandingBook({ businessOrderId: this.workOrderId, businessOrderType: "REPAIR_ORDER" }).then((res) => {
            row.detailList = res.data.data.repairInfoList;
        });
    }
    
    
    private tableRowClassName({ row }: any) {
        if (
            row.produceId ==
            this.defaultExpandRowKeys[this.defaultExpandRowKeys.length - 1]
        ) {
            return "highlight-row";
        }
        return "";
    }
    
    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiMwdOrderDelete({ workOrderId: scope.row.workOrderId }).then(() => {
                    this.getMwdOrderList();
                });
            })
            .catch((err) => {
                console.log(err);
            });
    }
    
    
    private onCurrentChange(val: any) {
        this.pager.current = val;
        this.getMwdOrderList();
    }
    
    private onDetail(scope: any) {
        (this.$refs.manageDialog as any).showDialog(scope);
    }
    
    private onAdd() {
        (this.$refs.manageDialog as any).showDialog({});
    }
}
</script>
<style lang="scss" scoped>
</style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}

.demo-table-expand {
    font-size: 0;
}

.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}

.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
}

</style>
