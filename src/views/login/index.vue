<template>
  <div class="login-container">
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      autocomplete="on"
      size="small"
      label-position="left"
    >
      <!-- <div class="title-container">
        
        <h3 class="title">
          系统登录
        </h3>
      </div> -->
      <div class="logo-container">
        <div class="logo">
        </div>
      </div>
      <div class="title-container">
        <div class="title">
          达坦综合管理平台
        </div>
      </div>
      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon name="user" />
        </span>
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
        />
      </el-form-item>

      <el-tooltip
        v-model="capsTooltip"
        content="大写锁定已打开"
        placement="right"
        manual
      >
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon name="password" />
          </span>
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="on"
            @keyup.native="checkCapslock"
            @blur="capsTooltip = false"
            @keyup.enter.native="handleLogin"
          />
          <span
            class="show-pwd"
            @click="showPwd"
          >
            <svg-icon :name="passwordType === 'password' ? 'eye-off' : 'eye-on'" />
          </span>
        </el-form-item>
      </el-tooltip>
      <div style="width:100%;display:flex;justify-content:center">
          <el-button
            :loading="loading"
            type="primary"
            class="login-btn"
            style=""
            @click.native.prevent="handleLogin"
          >
            登 录
          </el-button>
      </div>

    </el-form>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Route } from 'vue-router'
import { Form as ElForm, Input } from 'element-ui'
import { UserModule } from '@/store/modules/user'
import { isValidUsername } from '@/utils/validate'
@Component({
  name: 'Login',
  components: {
  }
})
export default class extends Vue {
  private redirect = '';
  private otherQuery: any = {}
  @Watch('$route', { immediate: true })
  onWatchRoute(route){
    const query = route.query
    if (query) {
      this.redirect = query.redirect
      this.otherQuery = this.getOtherQuery(query)
    }
  }
  private validatePassword = (rule: any, value: string, callback: Function) => {
    if (value.length < 1) {
      callback(new Error('请输入密码'))
    } else {
      callback()
    }
    callback()
  }

  private loginForm = {
    username: '',
    password: ''
  }

  private loginRules = {
    username: [{ required:true, message: '请输入用户名', }],
    password: [{ validator: this.validatePassword, }]
  }

  private passwordType = 'password'
  private loading = false
  private capsTooltip = false

  mounted() {
    if (this.loginForm.username === '') {
      (this.$refs.username as Input).focus()
    } else if (this.loginForm.password === '') {
      (this.$refs.password as Input).focus()
    }
  }
  private inputTest(a:any){
    console.log(a)
  }
  private checkCapslock(e: KeyboardEvent) {
    const { key } = e
    this.capsTooltip = key !== null && key !== undefined && key.length === 1 && (key >= 'A' && key <= 'Z')
  }

  private showPwd() {
    if (this.passwordType === 'password') {
      this.passwordType = ''
    } else {
      this.passwordType = 'password'
    }
    this.$nextTick(() => {
      (this.$refs.password as Input).focus()
    })
  }

  private handleLogin() {
    (this.$refs.loginForm as ElForm).validate(async(valid: boolean) => {
      if (valid) {
        this.loading = true
        try{
          let form = new FormData();
          form.append('username',this.loginForm.username)
          form.append('password', this.loginForm.password)
          await UserModule.Login(form)
          this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
          .catch(err => {
            console.warn(err)
          })
          this.loading = false
        }catch(err){
          console.log(err)
          this.loading = false
        }

        // Just to simulate the time of the request

      } else {
        return false
      }
    })
  }
  
  private getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
}
</script>

<style lang="scss">

// input:-webkit-autofill{
//    -webkit-transition-delay: 999999s;
//    -webkit-transition: color 999999s ease-out, background-color 999999s ease-out;
// }

// References: https://www.zhangxinxu.com/wordpress/2018/01/css-caret-color-first-line/
@supports (-webkit-mask: none) and (not (cater-color: $loginCursorColor)) {
  .login-container .el-input {
    //input { color: $loginCursorColor; }
    //input::first-line { color: $lightGray; }
  }
}

.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      height: 47px;
      background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $lightGray;
      //caret-color: $loginCursorColor;
      -webkit-appearance: none;

      &:-webkit-autofill {
        background-color: transparent;
        -webkit-transition-delay: 999999s;
        -webkit-transition: color 999999s ease-out, background-color 999999s ease-out;
        // box-shadow: 0 0 0px 1000px $loginBg inset !important;
        // -webkit-text-fill-color: #fff !important;
      }
    }
  }
  .el-form-item__error{
    padding-left: 40px;
  }
  .el-form-item {
    // border: 1px solid #dcdfe6;
    //background: rgba(0, 0, 0, 0.1);
    // border-radius: 5px;
    box-shadow: 0px 0px 29px 0px #008BFF inset;
    border-radius: 22px;
    //color: #454545;
  }
}
</style>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  // background-color: $loginBg;
  background: url('./bg.png');
  background-size: cover;
  background-position: bottom left;
  .login-form {
    position: relative;
    width: 460px;
    max-width: 100%;
    padding: 35px 75px 35px 75px;
    margin: 100px auto;
    overflow: hidden;
    // background: white;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 4px 5px 6px 15px;
    color: $darkGray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  // .title-container {
  //   position: relative;

  //   .title {
  //     font-size: 26px;
  //     //color: $lightGray;
  //     margin: 0px auto 40px auto;
  //     text-align: center;
  //     font-weight: bold;
  //   }
  // }
  .title-container {
    position: relative;
    width: 100%;
    margin-top: 46px;
    margin-bottom: 76px;
    .title {
      width: 100%;
      text-align: center;
      height: 24px;
      font-size: 24px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 36px;
      letter-spacing: 1px;
    }
  }
  .logo-container {
    position: relative;

    .logo {
      width: 100%;
      height: 76px;
      background: url('./logo.png') no-repeat center center;
      background-size: contain;
    }
  }
  .login-btn{
    width: 140px;
    height: 44px;
    background-color: #0057A0;
    border-radius: 22px;
    margin:20px;
    border:none;
    &:hover{
      background-color: #176aad;
    }
    &:active{
      border:1px solid #DCDFE6;
    }
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 9px;
    font-size: 16px;
    color: $darkGray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}
</style>
