<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="loading">
        <div class="app-card" style="height: 100%">
            <!-- <div style="display: flex;justify-content: space-between;">
                <div class="base-info-container" style="display: inline-block;width: calc(100% - 200px);">
                    <span class="base-info-item">
                        <span class="base-info-title">工单号:</span>
                        <span class="base-info-content">{{ originForm.eaNumber }}</span>
                    </span>
                    <span class="base-info-item">
                        <span class="base-info-title">部件品号-品名:</span>
                        <span class="base-info-content">{{ detailForm.invCode }} - {{ detailForm.invName }}</span>
                    </span>
                    <span class="base-info-item">
                        <span class="base-info-title">部件序列号:</span>
                        <span class="base-info-content" :title="isValid ? '' : '无效序列号'" :style="`color: ${ isValid ? '' : 'red' }`">{{ detailForm.serialNumber }}</span>
                    </span>
                </div>
                <div style="display: inline-block;">
                    <div class="update-info" style="color: grey; font-size: 14px;line-height:20px">
                        <div>
                            最近编辑人: {{ detailForm.lastModifiedByStr }}
                        </div>
                        <div>
                            更新于: {{ detailForm.lastModifiedDate}}
                        </div>
                    </div>
                </div>
            </div> -->
            
            <div>
                <div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 10px;overflow: hidden;">
                    <span style="font-size: 18px; font-weight: bold;">
                        工单号：{{ originForm.eaNumber }}
                    </span>
                    <el-button size="small" type="primary" @click="onFinishWorkOrder">
                        完成工单
                    </el-button>
                </div>
                <div style="font-size: 14px; height: 40px;line-height: 40px;background-color: #f5f5f5;padding: 0 10px;margin-bottom: 10px;">
                    <span>
                        <span style="color: #999">
                            部件品号-品名：
                        </span>
                        <span style="color: #333">
                            {{ detailForm.invCode }} - {{ detailForm.invName }}
                        </span>
                    </span>
                    <span style="margin-left: 20px;">
                        <span style="color: #999">
                            部件序列号：
                        </span>
                        <span :title="isValid ? '' : '无效序列号, 不会有树结构相关操作'" :style="`color: ${ isValid ? '#333' : 'red' }`">{{ detailForm.serialNumber }}</span>
                    </span>
                    <span style="float: right;">
                        <span>
                            <span style="color: #999">
                                最近编辑人：
                            </span>
                            <span style="color: #333">
                                {{ detailForm.lastModifiedByStr }}
                            </span>
                        </span>
                        
                        <span style="margin-left: 20px;">
                            <span style="color: #999">
                                更新于：
                            </span>
                            <span style="color: #333">
                                {{ detailForm.lastModifiedDate}}
                            </span>
                        </span>
                    </span>
                </div>
            </div>
            <el-tabs v-model="activePane" @tab-click="onClickTab" class="tree-workorder-tabs">
                <el-tab-pane
                    v-for="item in componentList"
                    :key="item.componentName"
                    :name="item.componentName"
                    :label="item.name"
                >
                </el-tab-pane>
            </el-tabs>
            <div style="padding: 0px 0px;;">
                <component
                    v-if="currentComponent"
                    ref="componentRef"
                    :is="currentComponent"
                    :failureTypeList="failureTypeList"
                    :failureComponentTypeList="failureComponentTypeList"
                    :failureReasonTypeList="failureReasonTypeList"
                    :deviceTypeList="deviceTypeList"
                    :ownerTypeList="ownerTypeList"
                    :detailForm="detailForm"
                    :originForm="originForm"
                    :inTree="inTree"
                    :outTree="outTree"
                    :treeGraphTemplate="treeGraphTemplate"
                    :componentBaseData="componentBaseData"
                    @getData="getWorkOrderInfo"
                    @update="updateInfo"
                />
            </div>
        </div>
    </div>
</template>
<script>
import BaseInfo from './BaseInfo.vue';
import WorkOrderTree from './WorkOrderTree.vue';
import Assemble from './Assemble.vue';
import PartList from './PartList.vue';
import Exchange from './Exchange.vue';
import Labor from './Labor.vue';
import MaintainCheck from './MaintainCheck.vue';
import FileManage from './FileManage.vue';
import getDict from '@/utils/getDict';
import { apiGetEaOrderInfo, apiGetPartTemplate, apiUpdateEaWorkOrder, apiFinishEaWorkOrder } from '@/api/ea';
import { mergeRun, splitRun } from '@/utils/constant.mwd';
import { apiGetServiceHistoryDetailList } from '@/api/tool-track';
export default {
    components: {
        BaseInfo,
        WorkOrderTree,
        Assemble,
        PartList,
        Exchange,
        Labor,
        FileManage,
        MaintainCheck,
    },
    data(){
        return {
            loading: false,
            currentComponent: BaseInfo,
            failureTypeList: [],
            failureComponentTypeList: [],
            failureReasonTypeList: [],
            deviceTypeList: [],
            ownerTypeList: [],
            originForm: {},
            finish: 0,
            detailForm: {},
            eaComponentAssembleRecord: {
                inTree: {},
                outTree: {},
            },
            treeGraphTemplate: null,
            isValid: false,
            componentBaseData: {},
            activePane: 'BaseInfo',
        }
    },
    computed: {
        componentList(){
            return [
                { name: '基本信息', componentName: 'BaseInfo', component: BaseInfo },
                { name: '工单树', componentName: 'WorkOrderTree', component: WorkOrderTree, needValidate: true },
                { name: '部件清单', componentName: 'PartList', component: PartList, needValidate: true },
                { name: '维修检查', componentName: 'MaintainCheck', component: MaintainCheck },
                { name: '拆卸组装', componentName: 'Assemble', component: Assemble, needValidate: true },
                { name: '更换清单', componentName: 'Exchange', component: Exchange },
                { name: '工时统计', componentName: 'Labor', component: Labor },
            ].filter(item=>!item.needValidate || (item.needValidate&&this.isValid))
        },
        inTree(){
            return this.eaComponentAssembleRecord.inTree?.children?.[0] || {}
        },
        outTree(){
            return this.eaComponentAssembleRecord.outTree?.children?.[0] || {}
        },
    },
    async mounted() {
        this.eaId = Number(this.$route.query.eaId);
        this.loading = true;
        try {
            [
                this.failureTypeList,
                this.failureComponentTypeList,
                this.failureReasonTypeList,
                this.deviceTypeList,
                this.ownerTypeList
            ] = await getDict([13, 14, 15, 17, 22]);
            if (this.eaId) {
                await this.getWorkOrderInfo();
            }
        } finally {
            this.loading = false;
        }
    },
    methods: {
        onClickTab(item) {
            this.currentComponent = this.componentList.find(c=>c.componentName===item.name).component;
            this.activePane = item.name;
            console.log(item)
        },
        getComputedProps(item){
            if(!this.isValid){
                if(item.componentName === 'Assemble'){
                    return {
                        disabled: true,
                        title: '无效序列号无法进行相关操作'
                    }
                }
            }
            return {}
        },
        validateSn({invName, serialNumber}){
            apiGetServiceHistoryDetailList({invName, serialNumber}, {size:100,current:1}).then(res=>{
                const data = res.data.data?.records || [];
                this.isValid = !!data.find(item=>item.invName === invName && item.serialNumber === serialNumber);
            })
        },
        async onClickComponent(item) {
            const componentRef = this.$refs.componentRef;
            if(componentRef && componentRef.isEdit) {
                await componentRef.onCancel&&componentRef.onCancel();
            }
            this.currentComponent = item.component;
        },
        getWorkOrderInfo(cb) {
            return apiGetEaOrderInfo({ eaId: this.eaId }).then((res) => {
                const data = res.data.data || {};
                data.workOrderEaDetailList[0].runList = splitRun(data.workOrderEaDetailList[0].run);
                this.originForm = data;
                this.finish = data.finish;
                this.detailForm = data.workOrderEaDetailList?.[0] || {};
                this.eaComponentAssembleRecord = data.eaComponentAssembleRecord || {};
                this.componentBaseData = data.eaRepairCheckData.componentBaseData || {}; 
                this.validateSn(this.detailForm)               
                apiGetPartTemplate({invCode: this.detailForm.invCode}).then(res=>{
                    this.treeGraphTemplate = res.data.data || null;
                })
                cb && cb();
            });
        },
        async updateInfo(){
            // 工时
            this.originForm.repairMemberList = this.originForm.repairMemberList.filter(item=>item.memberId&&item.name);
            // 处理入井趟次列表为字符串
            this.originForm.workOrderEaDetailList[0].run = mergeRun(this.originForm.workOrderEaDetailList[0].runList);
            // update
            apiUpdateEaWorkOrder(this.originForm).then(async () => {
                await this.getWorkOrderInfo();
                this.$message.success("更新成功！");
            });
        },
        onFinishWorkOrder() {
            this.$confirm("确认完成工单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiFinishEaWorkOrder({
                    eaId: this.eaId,
                    finish: 1,
                }).then(async ()=>{
                    this.$message.success("操作成功！");
                    await this.getWorkOrderInfo();
                });
            });
            
        }
        
    }
}
</script>

<style lang="scss" scoped>
.base-info-container{
    font-size: 16px;
    .base-info-item {
        margin-right: 60px;
        .base-info-title{
            font-weight: 600;
            margin-right: 12px;
        }
        
    }
}
.operators {
    position: fixed;
    right: 30px;
    top: calc(50% - 97px);
    display: flex;
    flex-direction: column;
    justify-content: center;

    padding: 6px 0;
    margin: 2px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .operator {
        line-height: 30px;
        padding: 0 10px;
        margin: 0;
        font-size: 14px;
        text-align: center;
        color: #606266;
        cursor: pointer;
        outline: none;
        &:hover {
            background-color: #ecf5ff;
            color: #66b1ff;
        }
    }
}
.operator-cover {
    position: fixed;
    right: 30px;
    top: calc(50% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    color: white;
    padding-left: 6px;
    font-size: 14px;
    line-height: 2;
    width: 26px;
    height: 80px;
    background: rgba(67, 86, 152, 0.8);
    cursor: pointer;
    user-select: none;
    .operator {
        font-size: 14px;
        line-height: 1.4;
        border: 1px solid grey;
        padding: 4px;
    }
}
.step-container {
    display: flex;
    justify-content: space-between;

    .step {
        width: 140px;
        height: 60px;
        line-height: 58px;
        text-align: center;
        font-size: 18px;
        border: 1px solid #ccc;
        font-weight: 500;
        border-radius: 16px;
        cursor: pointer;
        user-select: none;
        &.active {
            background: pink;
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
    
    .highlighted {
        padding: 2px;
        border: 1px solid rgb(67, 86, 152);
    }
}
.tree-workorder-tabs{
    .el-tabs__header{
        margin: 0 0 10px;
    }
}
</style>
<style lang="scss" scoped>
.tool-maintain-container {
    padding: 20px 40px;
    padding-right: 100px;
    margin: 12px;
    background: white;
    overflow: hidden;
    .tool-maintain-title {
        font-size: 16px;
        font-weight: 600;
    }
    .tool-card-subtitle {
        padding-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        color: rgb(67, 86, 152);
        margin-left: -12px;
        &::before {
            display: inline-block;
            margin-right: 6px;
            content: "-";
        }
    }
    .is-process {
        color: rgb(67, 86, 152) !important;
        font-weight: normal !important;
        .el-step__icon {
            border-color: rgb(67, 86, 152) !important;
        }
    }
    .active-step {
        .is-process {
            color: rgb(67, 86, 152) !important;
            font-weight: bold !important;
            .el-step__icon {
                border-color: rgb(67, 86, 152) !important;
            }
        }
    }
}
.el-table__expanded-cell {
    background: rgb(235, 238, 245);
}
.el-table__expanded-cell:hover {
    background: rgb(235, 238, 245) !important;
}
.active-step .el-step__title {
    color: rgb(67, 86, 152) !important;
    font-weight: bold;
}
</style>