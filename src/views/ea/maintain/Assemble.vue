<template>
    <div style="height: 100%;position: relative;">
        <LucidaTreeGraph :template="treeGraphTemplate" :actualData="treeGraphActualData" ref="lucidaTreeGraphRef" />
        <div style="position: absolute;top: 0;right:0px">
             <template v-if="status === 'READ'">
                <el-button @click="assemble" type="primary">组装</el-button>
                <el-button @click="disassemble" type="primary">拆卸</el-button>
             </template>
            <template v-else>
                <el-button @click="onCancelOperate">取消</el-button>
                <el-button @click="onConfirmOperate" type="primary">{{ `确认${ status=='ASSEMBLE' ? '组装' : '拆卸' }` }}</el-button>
            </template>
        </div>
        <CreateEaWorkOrderDialog ref="createEaWorkOrderDialogRef" />
    </div>
</template>
<script>
import { ASSEMBLE_NODE, DISASSEMBLE_NODE, GHOST_NODE, pruneTree, defaultFieldsToRemove } from "@/components/LucidaTreeGraph/utils/nodes";
import { deepCopyTree, findFirstNode, findNodesWithParents } from "@/utils/tree";
import LucidaTreeGraph from "@/components/LucidaTreeGraph";
import { apiAssembleEa } from "@/api/ea";
import { apiUpdateComponent } from "@/api/mwd";
import { apiGetLucidaTreeByNode } from "@/api/lucida";
import CreateEaWorkOrderDialog from "./CreateEaWorkOrderDialog.vue";
export default {
    components: {
        LucidaTreeGraph,
        CreateEaWorkOrderDialog
    },
    props: {
        outTree: {
            type: Object,
            default: null
        },
        treeGraphTemplate: {
            type: Object,
            default: null
        },
        detailForm: {
            type: Object,
            default: () => ({}),
        },
        originForm: {
            type: Object,
            default: () => ({}),
        },
    },
    data(){
        return {
            status: 'READ',
            treeGraphActualData: null,
        }
    },
    computed: {
        p_CreateEaWorkOrder(){
            return this.$checkBtnPermission(`sys:ea:create:all`)
        }
    },
    async mounted() {
        this.treeGraphActualData = deepCopyTree(this.outTree);
        this.$nextTick(()=>{
            this.$refs.lucidaTreeGraphRef.initData('READ');
        })
    },
    methods: {
        read(){
            this.status = 'READ';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('READ');
            })
        },
        assemble(){
            this.status = 'ASSEMBLE';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('ASSEMBLE');
            })
        },
        disassemble(){
            this.status = 'DISASSEMBLE';
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('DISASSEMBLE');
            })
        },
        onCancelOperate(){
            this.read();
        },
        onConfirmOperate(){
            // 拆卸的时候直接删除parentComponentId，组装的时候要关联相应parentComponentId，也要处理sort字段，这个字段数据最好从模板数据中得到
            // 业务上第二层的数据都没有parentComponentId，要单独处理，而且要过滤掉GHOST_NODE
            // TODO: 抽象出组件逻辑，尽量不要在这里操作组件内部数据
            const graphData = this.$refs.lucidaTreeGraphRef.graph.save();
            let batchUpdateList;
            if(this.status === 'ASSEMBLE'){
                let list = findNodesWithParents(graphData, node => node.isAssembleHead);
                batchUpdateList = list.map(({node, parent})=>{
                    return {
                        componentId: node.componentId,
                        parentComponentId: parent.componentId,
                        sort: node.sort,
                    }
                })
            }
            if(this.status === 'DISASSEMBLE'){
                let list = findNodesWithParents(graphData, node => node.isDisassembleHead);
                console.log(list);
                batchUpdateList = list.map(({node})=>{
                    return {
                        componentId: node.componentId,
                        parentComponentId: null,
                    }
                })
            }
            Promise.all(batchUpdateList.map(item=>{
                return apiUpdateComponent(item)
            })).then(async ()=>{
                const originPartTree = await apiGetLucidaTreeByNode({componentId: graphData.componentId}).then(res => res.data.data);
                const partTree = findFirstNode(originPartTree, item => item.serialNumber === graphData.serialNumber);
                const eaComponentAssembleRecord = this.originForm.eaComponentAssembleRecord;
                eaComponentAssembleRecord.outTree.children[0] = partTree;
                apiAssembleEa(eaComponentAssembleRecord).then(async() => {
                    this.$message.success(`${this.status==='DISASSEMBLE'?'拆卸':'组装'}成功`);
                    if(this.p_CreateEaWorkOrder && this.status==='DISASSEMBLE'){
                        const disassembleHeadList = findNodesWithParents(graphData, data => data.nodeType === DISASSEMBLE_NODE && data.isDisassembleHead)
                        this.$refs.createEaWorkOrderDialogRef.showDialog(disassembleHeadList.map(item => item.node), this.originForm);
                    }
                    this.$emit('getData', ()=>{
                        this.$nextTick(()=>{
                            this.treeGraphActualData = deepCopyTree(this.outTree);
                            this.read();
                        })
                    });
                })
            })
            
        },
    }
}
</script>
  