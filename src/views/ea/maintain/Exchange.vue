<template>
    <div>
        <div style="margin-bottom:10px;overflow: hidden;">
            <el-button key="9527" type="primary" @click="isEdit = true" v-if="!isEdit" style="float:right;">编辑</el-button>
            <template v-else>
                <el-button @click="addRow()" type="primary" style="margin-right:10px">首行插入</el-button>
                <span style="float:right">
                    <!-- <el-autocomplete
                        popper-class="my-autocomplete"
                        style="margin-right:10px;width:180px"
                        v-model="bomInvName"
                        :fetch-suggestions="querySearchAsyncBom"
                        placeholder="请输入BOM并选择"
                        @select="handleSelectBom"
                        @clear="bomId=null"
                        clearable
                        value-key="invName"
                        ref="bom"
                    >
                        <template slot-scope="{ item }">
                            <div class="name">{{ item.invName }}</div>
                            <span class="code">{{ item.invCode }}</span>
                        </template>
                    </el-autocomplete>
                    <el-button @click="onOpenRelateDialog('bom')" type="primary" style="margin-right:10px;">关联BOM</el-button>
                    <el-input
                        style="margin-right:10px;width:180px"
                        v-model="eaNumber"
                        placeholder="请输入工单号"
                        clearable
                    ></el-input>
                    <el-button @click="onOpenRelateDialog('mwd')" type="primary" style="margin-right:10px;">关联工单号</el-button>
                    <el-autocomplete
                        style="margin-right:10px;width:180px"
                        v-model="pickOrderNumber"
                        :fetch-suggestions="querySearchAsync"
                        placeholder="请输入领料单"
                        clearable
                        ref="pickorder"
                    ></el-autocomplete>
                    <el-button @click="onOpenRelateDialog('pickorder')" type="primary">关联领料单</el-button> -->
                    <el-button @click="onCancel">取消</el-button>
                    <el-button @click="onSave" type="primary">保存</el-button>
                </span>
            </template>
        </div>
        
        <el-table :data="tableData" border>
            <el-table-column prop="invName" label="品名">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invName" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invName}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="invCode" label="品号">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invCode" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invCode}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="invStd" label="规格">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invStd" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invStd}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.quantity" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.quantity}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="unitName" label="单位">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.unitName" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.unitName}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width:100%" class="tail-input" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.note}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog custom-class="bom-dialog" :title="dialogTitle" :visible.sync="isDialogVisible">
            <div style="display:flex; justify-content: space-between;align-items:center">
                <el-button type="text" @click="revertSelection" style="margin-right: 20px">反选</el-button>
                <span style="margin-right:40px">已选部件种类：{{selectedRelateItemList.length}}/{{relateItemList.length}}</span>
            </div>
            <el-table @row-click="onClickRow" max-height="500px" ref="relateItemListTable" :data="relateItemList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column prop="invName" width="200" label="品名">
                </el-table-column>
                <el-table-column prop="invCode" width="120" label="品号">
                </el-table-column>
                <el-table-column prop="invStd" label="规格">
                </el-table-column>
                <el-table-column prop="quantity" width="60" label="数量">
                </el-table-column>
                <el-table-column prop="unitName" width="60" label="单位">
                </el-table-column>
            </el-table>
            <template #footer>
                <el-button type="plain" @click="isDialogVisible=false">取消</el-button>
                <el-button type="primary" @click="onClickRelate">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { apiGetFuzzyPickOrderList } from "@/api/erp";
import { apiGetBomDetail, apiGetBomFuzzylist } from "@/api/mwd";
export default {
    data() {
        return {
            addRowItem: {serialNumber:'',invName:'',invCode:'',invStd:'',quantity:'',unitName:'',note:''},
            bomInvName: "",
            pickOrderNumber: "",
            eaNumber: "",
            isDialogVisible: false,
            selectedRelateItemList:[],
            relateItemList:[],
            dialogType: "bom", // pickorder | mwd | bom
            isEdit: false
        };
    },
    props: {
        originForm: {
            type: Object,
            default: () => {},
        },
    },
    computed: {
        tableData(){
            return this.originForm.eaRepairCheckData?.checkData?.replaceItemList || []
        },
        deviceType(){
            return this.originForm?.workOrderEaDetailList?.[0].deviceType
        },
        dialogTitle(){
            switch(this.dialogType){
                case 'pickorder':
                    return `关联领料单 —— ${this.pickOrderNumber}`
                case'mwd':
                    return `关联工单号 —— ${this.eaNumber}`
                case 'bom':
                    return `关联BOM —— ${this.bomInvName}`
                default:
                    return '关联'
            }
        }
    },
    watch: {
        "originForm.eaNumber"(val) {
            this.eaNumber = val
        }
    },
    methods: {
        onCancel(){
            this.isEdit = false;
            this.$emit("getData");
        },
        onSave(){
            this.isEdit = false;
            this.originForm.eaRepairCheckData.checkData.replaceItemList = this.tableData;
            this.$emit("update");
        },
        deleteCell(scope) {
            let index = scope.$index;
            this.tableData.splice(index, 1);
        },
        addRow(scope) {
            if (scope) {
                let index = scope.$index;
                this.tableData.splice(index + 1, 0, {
                    ...this.addRowItem,
                });
            } else {
                this.tableData.unshift({
                    ...this.addRowItem,
                });
            }
        },
        onClickRelate(){
            if(!this.selectedRelateItemList.length){
                this.$message.error('没有选择部件');
                return;
            }
            const data = this.selectedRelateItemList.map(item=>{
                let tmp = {...this.addRowItem};
                tmp.serialNumber = item.serialNumber;
                tmp.quantity = item.quantity;
                tmp.invName = item.invName;
                tmp.invCode = item.invCode;
                tmp.invStd = item.invStd;
                tmp.unitName = item.unitName;
                tmp.note = item.note || "";
                return tmp;
            })
            this.tableData.push(...data)
            switch (this.dialogType) {
                case "pickorder":
                    this.pickOrderNumber = ""
                    break;
                case "mwd":
                    this.eaNumber = "";
                    break;
                case "bom":
                    this.bomId = "";
                    this.bomInvName = "";
                    break;
                default:
                    break;
            }
            this.isDialogVisible = false;
            
        },
        handleSelectBom(item){
            this.bomId = item.bomId;
        },
        querySearchAsyncBom(qs, cb){
            apiGetBomFuzzylist({invName: qs}).then(res=>{
                const data = res.data.data || [];
                cb(data)
            })
        },
        querySearchAsync(qs, cb){
            const form = new FormData();
            form.append("docNo", qs);
            apiGetFuzzyPickOrderList(form).then(res=>{
                const data = res.data.data || [];
                cb(data.map(item=>({value:item})))
            })
        },
        getRelateBomItemList() {
            return new Promise((resolve,reject)=>{
                    apiGetBomDetail({bomId:this.bomId, deviceType:this.deviceType}).then(res=>{
                        resolve(res?.data?.data || []);
                    }).catch(err=>{
                        reject(err);
                    })
                })
        },
        getRelatePickorderItemList() {
            return new Promise((resolve,reject)=>{
                    apiGetPickOrderList({issueReceiptDocNo: this.pickOrderNumber, deviceType:this.deviceType}).then(res=>{
                        resolve(res?.data?.data || []);
                    }).catch(err=>{
                        reject(err);
                    })
                })
        },
        getRelateMwdItemList() {
            return new Promise((resolve,reject)=>{
                    apiGetPickOrderList({moDocNo: this.eaNumber, deviceType:this.deviceType}).then(res=>{
                        resolve(res?.data?.data || []);
                    }).catch(err=>{
                        reject(err);
                    })
                })
        },
        async onOpenRelateDialog(dialogType) {
            this.dialogType = dialogType;
            try{
                switch (dialogType) {
                    case "pickorder":
                        if(!this.pickOrderNumber){
                            this.$message.error("请填写领料单号！");
                            return;
                        }
                        this.relateItemList = await this.getRelatePickorderItemList();
                        break;
                    case "mwd":
                        if(!this.eaNumber){
                            this.$message.error("请填写工单号！");
                            return;
                        }
                        this.relateItemList = await this.getRelateMwdItemList();
                        break;
                    case "bom":
                        if(!this.bomId){
                            this.$message.error("请填写BOM名称！");
                            return;
                        }
                        this.relateItemList = await this.getRelateBomItemList();
                        break;
                    default:
                        break;
                }
                this.selectedRelateItemList = [];
                this.isDialogVisible = true;
            }catch (e) {
                console.log(e)
            }
            
        },
        handleSelectionChange(val){
            this.selectedRelateItemList = val;
        },
        revertSelection(){
            this.relateItemList.forEach(item=>{
                (this.$refs.relateItemListTable).toggleRowSelection(item)
            })
        },
        onClickRow(row){
            (this.$refs.relateItemListTable).toggleRowSelection(row)
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
<style lang="scss">
.bom-dialog .el-dialog__body{
    padding-top: 6px
}
</style>
<style lang="scss">
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .code {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .code {
      color: #ddd;
    }
  }
}
</style>