<template>
    <div>
        <el-button type="primary"  @click="onClickUpload">上传</el-button>
        <FileManage style="margin-top: 10px" :mode="'EDIT'" ref="fileManageRef" bussinessType="EA" :bussinessId="eaId" />
    </div>
</template>
<script>
import FileManage from "@/components/FileManage/index.vue";
export default {
    components: {
        FileManage,
    },
    props: {
        eaId: Number,
    },
    methods: {
        onClickUpload(){
            this.$refs.fileManageRef.onUpload();
        }
    }
}

</script>