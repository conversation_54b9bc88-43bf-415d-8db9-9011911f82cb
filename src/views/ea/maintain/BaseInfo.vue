<template>
    <div style="">
        <span style="float: right">
            <span v-if="!isEdit">
                <el-button type="primary" @click="isEdit = true">编辑</el-button>
            </span>
            <span v-else>
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSave">保存</el-button>
            </span>
        </span>
        <div class="tool-card-title" style="margin-left: 0;">部件信息</div>
        <el-form :model="detailForm" label-width="90px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="部件序列号">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.serialNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="部件品号">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.invCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="部件名称">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.invName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="6">
                    <el-form-item label="部件等级">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.level"
                        ></el-input>
                    </el-form-item>
                </el-col> -->
                <el-col :span="6">
                    <el-form-item label="是否属于最后一级" label-width="130px">
                        <el-select disabled v-model="detailForm.isLeaf" placeholder="" style="width: 100%;">
                            <el-option label="是" :value="true"></el-option>
                            <el-option label="否" :value="false"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="母体仪器类型" label-width="100px">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.parentDeviceTypeStr"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="母体序列号">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.parentSerialNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="母体品名">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.parentInvName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总循环时间">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.componentTotalCirculateHrs"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="最高温度">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.componentMaxBht"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="修正最高温度" label-width="100px">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.componentReviseMaxBht"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总入井时间">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.componentTotalInWellHrs"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="修正总入井时间" label-width="110px">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="版本号">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.versionNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="风险类型">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.riskType"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="风险值">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.riskValue"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
                

        <div class="tool-card-title" style="margin-left: 0;">作业信息</div>
        <el-form :model="detailForm" label-width="70px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="井号">
                        <el-input :disabled="true" v-model="originForm.wellNumber"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业编号">
                        <el-input
                            :disabled="true"
                            v-model="originForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱">
                        <el-input
                            :disabled="true"
                            v-model="originForm.kitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人" label-width="84px">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                
                <el-col :span="6">
                    <el-form-item label="入井时间">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.inWellHour"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="循环时间">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.circulateHrs"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="最高温度">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.maxBht"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="入井趟次">
                        <el-input
                            :disabled="true"
                            v-model="detailForm.run"
                        ></el-input>
                        
                        <!-- <el-select v-else v-model="detailForm.runList" multiple collapse-tags clearable style="width: 100%">
                            <el-option v-for="item in 20" :key="item" :value="String(item)" :label="String(item)">
                            </el-option>
                        </el-select> -->
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="tool-card-title" style="margin-left: 0;">维修信息</div>
        <el-form :model="detailForm" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="上级维修单号">
                        <el-input
                            disabled
                            v-if="originForm.parentEaId"
                            v-model="originForm.parentEaNumber"
                        ></el-input>
                        <el-input
                            disabled
                            v-if="originForm.parentMwdId"
                            v-model="originForm.parentMwdNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="开始维修日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.startDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="预计完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.estimatedEndDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="实际完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.endDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>

                <el-col :span="6">
                    <el-form-item label="维修状态">
                        <el-select
                            disabled
                            v-model="originForm.finish"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option label="完成" :value="1"></el-option>
                            <el-option label="未完成" :value="0"></el-option>
                            <el-option label="滞留" :value="-1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="故障类型">
                        <el-select
                            :disabled="!isEdit"
                            clearable
                            v-model="detailForm.failureType"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in failureTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="失效部件分类" label-width="100px">
                        <el-select
                            :disabled="!isEdit"
                            clearable
                            v-model="detailForm.failureComponentType"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in failureComponentTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="主要失效原因分类" label-width="130px">
                        <el-select
                            :disabled="!isEdit"
                            clearable
                            v-model="detailForm.failureReasonType"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in failureReasonTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                
                <el-col :span="6">
                    <el-form-item label="出厂序列状态" label-width="100px">
                        <el-select
                            :disabled="!isEdit"
                            clearable
                            v-model="detailForm.riskType"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in riskTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修人员">
                        <el-input
                            disabled
                            v-model="detailForm.repairUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修耗时">
                        <el-input
                            disabled
                            v-model="detailForm.laborHours"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修报告编制人" label-width="110px">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.editor"
                        ></el-input>
                    </el-form-item>
                </el-col>
                
                <el-col :span="6">
                    <el-form-item label="维修等级">
                        <el-select v-model="detailForm.repairLevel" :disabled="!isEdit" style="width: 100%;" placeholder="">
                            <el-option v-for="item in 5" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="返回原因" label-width="70px">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.returnReason"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="车间检查发现" label-width="100px">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.findings"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="备注">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.notes"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import { riskTypeList } from '@/utils/constant';

export default {
    props: {
        detailForm: {
            type: Object,
            default: () => ({}),
        },
        originForm: {
            type: Object,
            default: () => ({}),
        },
        failureTypeList: {
            type: Array,
            default: () => [],
        },
        failureComponentTypeList: {
            type: Array,
            default: () => [],
        },
        failureReasonTypeList: {
            type: Array,
            default: () => [],
        },
        deviceTypeList: {
            type: Array,
            default: () => [],
        },
        ownerTypeList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            riskTypeList,
            isEdit: false,
        };
    },
    methods: {
        onCancel(){
            this.isEdit = false;
            this.$emit('getData');
        },
        onSave(){
            this.$emit('update');
            this.isEdit = false;

        }
    },
}
</script>