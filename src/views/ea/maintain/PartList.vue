<template>
    <div style="height: calc(100% - 100px);">
        <div>
            <el-button size="small" :type="partListType==='in' ? 'primary': 'default'" @click="partListType='in'">入场清单</el-button>
            <el-button size="small" :type="partListType==='out' ? 'primary': 'default'" @click="partListType='out'">出场清单</el-button>
            <span style="float: right;">
                <el-button-group size="mini" class="part-btn-group" v-if="showType==='tree'">
                    <el-button key="9527" size="mini" type="plain" @click="onTable" title="表格显示">
                        <svg-icon name="table-list" width="16px" height="16px" color="#004e7e"></svg-icon>
                    </el-button>
                </el-button-group>
                <el-button-group size="mini" class="part-btn-group" v-else>
                    <el-button key="27149" size="mini" type="plain" @click="onTableExpandAll" title="展开所有">
                        <svg-icon name="expand" width="16px" height="16px" color="#004e7e"></svg-icon>
                    </el-button>
                    <el-button key="8848" size="mini" type="plain" @click="onTree" title="树状显示">
                        <svg-icon name="tree" width="16px" height="16px" color="#004e7e"></svg-icon>
                    </el-button>
                </el-button-group>
            </span>
        </div>
        <el-table
            :header-cell-style="commmonTableHeaderCellStyle"
            style="margin-top: 10px;"
            :data="tableData"
            row-key="id"
            :default-expand-all="false"
            ref="treeTableRef"
            v-if="showType === 'table'"
        >
            <el-table-column
                prop="invName"
                label="名称"
                width="400">
            </el-table-column>
            <el-table-column
                prop="level"
                label="等级"
                align="center"
                width="60">
            </el-table-column>
            <el-table-column
                prop="serialNumber"
                label="序列号"
                align="center"
                width="120">
            </el-table-column>
            <el-table-column
                prop="lastAssemblyDate"
                label="上次更换时间"
                align="center"
                width="120">
            </el-table-column>
            <el-table-column
                prop="versionNumber"
                label="版本"
                align="center"
                width="80">
            </el-table-column>
            <el-table-column
                prop="riskType"
                label="风险类型(风险值)"
                align="center"
                width="140"
            >
                <template slot-scope="scope">
                    <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                    <span v-if="scope.row.riskValue"> ({{ scope.row.riskValue }})</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="maxBht"
                label="最高温度"
                align="center"
                width="100">
            </el-table-column>
            <el-table-column
                prop="reviseMaxBht"
                label="修正最高温度"
                align="center"
                width="120">
            </el-table-column>
            <el-table-column
                prop="totalInWellHrs"
                label="总入井时间"
                align="center"
                width="100">
            </el-table-column>
            <el-table-column
                prop="reviseTotalHours"
                label="修正总入井时间"
                align="center"
                width="120">
            </el-table-column>
            <el-table-column
                prop="note"
                label="厂家/备注"
                align="center">
            </el-table-column>
        </el-table>
        <LucidaTreeGraph style="margin-top: 10px;height: calc(100vh - 290px);" :template="treeGraphTemplate" :actualData="treeGraphActualData" ref="lucidaTreeGraphRef" v-else />
    </div>
</template>
<script>
import LucidaTreeGraph from "@/components/LucidaTreeGraph";
import { deepCopyTree } from "@/utils/tree";
import { mergeCompleteTemplateWithPartData } from "@/components/LucidaTreeGraph/utils/nodes";
export default {
    components: {
        LucidaTreeGraph
    },
    props: {
        inTree: {
            type: Object,
            default: () => ({children: []})
        },
        outTree: {
            type: Object,
            default: () => ({children: []})
        },
        treeGraphTemplate: {
            type: Object,
            default: null
        },
    },
    data(){
        return {
            partListType: 'in',
            showType: 'table', // table, tree
            treeGraphActualData: null,
            tableData: [],
        }
    },
    computed: {
        tree(){
            return this.partListType === 'in' ? this.inTree : this.outTree;
        }
    },
    watch: {
        partListType:{
            handler(){
                this.tableData = [mergeCompleteTemplateWithPartData(this.handleTreeData(this.treeGraphTemplate), this.tree)];
                if(this.showType === 'table'){
                    this.$nextTick(()=>{
                        this.$refs.treeTableRef.toggleRowExpansion(this.tableData[0], true);
                    })
                }
                if(this.showType === 'tree'){
                    this.$nextTick(()=>{
                        this.onTree();
                    })
                }
            },
            immediate: true,
        }
    },
    mounted() {
    },
    methods: {
        handleTreeData(data) {
            let count = 0;
            const handleFunc = (item) => {
                item.id = 'nodeid' + count++;
                if (item.children) {
                    item.children.forEach((child) => {
                        handleFunc(child, item);
                    });
                }
                return item;
            };
            return handleFunc(data);
        },

        onTree(){
            this.showType = 'tree';
            this.treeGraphActualData = deepCopyTree(this.tree);
            this.$nextTick(()=>{
                this.$refs.lucidaTreeGraphRef.initData('READ');
            })
        },
        onTable(){
            this.showType = 'table';
            this.$nextTick(()=>{
                this.$refs.treeTableRef.toggleRowExpansion(this.tableData[0], true);
            })
        },
        onTableExpandAll() {
            // 对表格数据进行遍历，展开每一行
            this.tableData.forEach(row => {
                this.$refs.treeTableRef.toggleRowExpansion(row, true);
                // 如果有子节点，递归展开
                if (row.children && row.children.length > 0) {
                    this.expandAllRows(row.children);
                }
            });
        },

        // 添加一个辅助方法用于递归展开所有子节点
        expandAllRows(rows) {
            rows.forEach(row => {
                this.$refs.treeTableRef.toggleRowExpansion(row, true);
                if (row.children && row.children.length > 0) {
                    this.expandAllRows(row.children);
                }
            });
        }
    }
}
</script>
<style lang="scss">
.part-btn-group{
    .el-button--mini{
        padding: 7px 7px;
    }
}
</style>