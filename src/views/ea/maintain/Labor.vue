<template>
    <div>
        <div style="margin-bottom:10px; overflow: hidden;">
            <el-button @click="isEdit = true" type="primary" style="float: right;" v-if="!isEdit">编辑</el-button>
            <template v-else>
                <el-button @click="addRow()" type="primary" v-if="isEdit">首行插入</el-button>
                <span style="float: right;">
                    <el-button @click="onCancel">取消</el-button>
                    <el-button @click="onSave" type="primary">保存</el-button>
                </span>
            </template>
        </div>
        <el-table :data="tableData" border>
            <el-table-column prop="serialNumber" label="员工姓名" width="200px" :key="Math.random()">
                <template slot="header">
                    <el-tooltip v-if="isEdit" class="item" effect="dark" content="输入关键字后请务必选择姓名, 否则该条数据将无法录入" placement="top-start">
                        <span>员工姓名<i class="el-icon-info"></i></span>
                    </el-tooltip>
                    <span v-else>员工姓名</span>
                </template>
                <template slot-scope="scope">
                    <el-autocomplete
                        popper-class="my-autocomplete"
                        v-model="scope.row.name"
                        :fetch-suggestions="querySearch"
                        placeholder="请输入员工姓名并选择"
                        @select="handleSelect($event, scope)"
                        v-if="isEdit"
                    >
                        <template slot-scope="{ item }">
                            <div class="name">{{ item.name }}</div>
                        </template>
                    </el-autocomplete>
                    <span v-else>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="laborHours" label="工时" width="200px">
                <template slot-scope="scope">
                    <InputNumber v-model="scope.row.laborHours" v-if="isEdit" style="width:100%"></InputNumber>
                    <span v-else>{{scope.row.laborHours}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.notes" style="width:100%" class="tail-input" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.notes}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { apiGetMemberList } from "@/api/users";
export default {
    name: "Labor",
    props: {
        originForm: {
            type: Object,
            default: ()=>{}
        }
    },
    data() {
        return {
            addRowItem: {mwdId:"",memberId:'',name:'',laborHours:'',notes:''},
            isEdit: false
        }
    },
    computed: {
        tableData() {
            return this.originForm.repairMemberList || []
        },
        mwdId() {
            return this.originForm.mwdId
        }
    },
    methods: {
        onCancel(){
            this.isEdit = false;
            this.$emit("getData");
        },
        onSave(){
            this.isEdit = false;
            this.originForm.repairMemberList = this.tableData;
            this.$emit("update");
        },
        deleteCell(scope) {
            let index = scope.$index;
            this.tableData.splice(index, 1);
        },
        addRow(scope) {
            if (scope) {
                let index = scope.$index;
                this.tableData.splice(index + 1, 0, {
                    ...this.addRowItem,mwdId: this.mwdId
                });
            } else {
                this.tableData.unshift({
                    ...this.addRowItem,mwdId: this.mwdId
                });
            }
        },
        querySearch(qs, cb) {
            apiGetMemberList({name:qs}).then(res=>{
                cb(res.data.data?.memberInfoList || [])
            })
        },
        handleSelect(item, scope) {
            const {row, $index} = scope;
            const index = this.tableData.findIndex(td=>(td.memberId==item.memberId)&&td.name);
            if(index>=0&&index!==$index){
                this.$message.error('列表已经有该员工！')
                return;
            }
            row.name = item.name;
            row.memberId = item.memberId;
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
