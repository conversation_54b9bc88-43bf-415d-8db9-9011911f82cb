<template>
    <div>
        <!-- <div>
            <el-button
                :type="currentField === item.field ? 'primary' : 'default'"
                v-for="item in tabList"
                :key="item.field"
                @click="onClickTab(item)"
            >
                {{ item.title }}
            </el-button>
            <span style="float: right;">
                <span v-if="!isEdit">
                    <el-button type="primary" @click="isEdit = true">编辑</el-button>
                </span>
                <span v-else>
                    <el-button @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="onSave">保存</el-button>
                </span>
            </span>
        </div>
        <quill-editor
            class="lucida-quill-editor"
            style="width: 100%; margin-top: 10px;"
            ref="quillEditorRef"
            v-model="faultDiagnosis[currentField]"
            :options="options"
            v-if="isEdit"
        />
        <ImageViewer style="height: calc(100vh - 260px);overflow: auto;" v-html="faultDiagnosis[currentField]" v-else></ImageViewer> -->
        <span style="float: right">
            <span v-if="!isEdit">
                <el-button type="primary" @click="isEdit = true">编辑</el-button>
            </span>
            <span v-else>
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onSave">保存</el-button>
            </span>
        </span>
        <div class="tool-card-title" style="margin-left: 0px;padding-bottom: 10px;">参数调整</div> 
        <el-table :data="tableData" class="ea-para-table" :show-header="false" border :span-method="arraySpanMethod">
            <el-table-column label="名称" prop="col0" align="center"></el-table-column>
            <!-- <el-table-column label="等级" prop="col1" align="center"></el-table-column> -->
            <el-table-column label="序列号" prop="col2" align="center"></el-table-column>
            <el-table-column label="是否属于最后一级" prop="col3" align="center"></el-table-column>
            <el-table-column label="当前版本" prop="col4" align="center">
                <!-- <template #default="scope">
                    <el-input v-if="isEdit && scope.$index === 1" v-model="scope.row.col4" size="small" style="width: 100%;"></el-input>
                    <span v-else>{{ scope.row.col4 }}</span>
                </template> -->
            </el-table-column>
            <el-table-column label="序列号" prop="col5" align="center">
                <template #default="scope">
                    <template v-if="isEdit">
                        <el-input v-if="scope.$index === 3" v-model="componentBaseData.reviseMaxBht" size="mini" style="width: 100%;"></el-input>
                        <el-select 
                            clearable
                            v-else-if="scope.$index === 5"
                            v-model="componentBaseData.updateRiskType"
                            style="width: 100%"
                            size="mini" 
                        >
                            <el-option
                                v-for="item in riskTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                        <span v-else>{{ scope.row.col5 }}</span>
                    </template>
                    <span v-else>{{ scope.row.col5 }}</span>
                </template>
            </el-table-column>
            <el-table-column label="更新版本" prop="col6" align="center">
                <template #default="scope">
                    <template v-if="isEdit">
                        <el-input v-if="scope.$index === 1" v-model="componentBaseData.updateVersionNumber" size="mini" style="width: 100%;"></el-input>
                        <span v-else>{{ scope.row.col6 }}</span>
                    </template>
                    <span v-else>{{ scope.row.col6 }}</span>
                </template>
            </el-table-column>
            <el-table-column label="序列号" prop="col7" align="center">
                <template #default="scope">
                    <template v-if="isEdit">
                        <el-input v-if="scope.$index === 3" v-model="componentBaseData.reviseTotalHours" size="mini" style="width: 100%;"></el-input>
                        <el-input v-else-if="scope.$index === 5" v-model="componentBaseData.updateRiskValue" size="mini" style="width: 100%;"></el-input>
                        <span v-else>{{ scope.row.col7 }}</span>
                    </template>
                    <span v-else>{{ scope.row.col7 }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div class="tool-card-title" style="margin-left: 0px;padding-bottom: 10px;">维修检查</div> 
        <el-form :model="faultDiagnosis" label-position="top" class="lucida-form">
            <el-row :gutter="20">
                <el-col :span="12" v-for="item in tabList" :key="item.field">
                    <el-form-item :label="item.title">
                        <quill-editor
                            class="lucida-quill-editor"
                            style="width: 100%;"
                            :ref="item.field"
                            v-model="faultDiagnosis[item.field]"
                            :options="getSetting(item.field)"
                            @focus="onEditorFocus(item.field)"
                            v-if="isEdit"
                        />
                        <ImageViewer class="lucida-quill-div" v-html="faultDiagnosis[item.field]" v-else></ImageViewer>
                    </el-form-item>
                </el-col>
            </el-row>
            
        </el-form>
        <form
            action
            method="post"
            enctype="multipart/form-data"
            id="uploadFormMulti"
        >
            <input
                style="display: none"
                id="ghostUploader"
                type="file"
                name="file"
                multiple
                accept="image/jpg,image/jpeg,image/png,image/gif"
                @change="uploadImg()"
            />
        </form>
        <div class="tool-card-title" style="margin-left: 0px;padding-bottom: 10px;">文件管理</div>
        <FileManage :eaId="originForm.eaId" />
    </div>
</template>
<script>
import { apiUploadFile } from '@/api/file';
import ImageViewer from "@/components/ImageViewer/index.vue";
import FileManage from './FileManage.vue';
import { riskTypeList } from '@/utils/constant';
export default {
    name: "MaintainCheck",
    components: {
        ImageViewer, FileManage
    },
    props: {
        originForm: {
            type: Object,
            default: () => ({eaRepairCheckData:{checkData:{faultDiagnosis:{}},componentBaseData:{}}}),
        },
        detailForm: {
            type: Object,
            default: () => ({}),
        },
    },
    data(){
        return {
            riskTypeList,
            isEdit: false,
            currentField: 'preliminaryTestResult',
            tabList: [
                {
                    name: '',
                    field: 'preliminaryTestResult',
                    title: '入场初步检查'
                },
                {
                    name: '',
                    field: 'testResult',
                    title: '检测与测试结果'
                },
                {
                    name: '',
                    field: 'dataAnalysis',
                    title: '下载数据分析'
                },
                {
                    name: '',
                    field: 'repairAdvice',
                    title: '维修措施'
                },
                {
                    name: '',
                    field: 'rootReasonAndImproveMethod',
                    title: '根本原因与改善方法'
                },
            ],
            options: {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: [["image"]],
                        handlers: {
                            image: () => {
                                let quill = this.$refs.quillEditorRef[0].quill;
                                const ghostUploaderDom = document.getElementById("ghostUploader");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click(); 
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    }
                },
            }
                
        }
    },
    computed: {
        faultDiagnosis(){
            return this.originForm?.eaRepairCheckData?.checkData?.faultDiagnosis || {};
        },
        componentBaseData(){
            return this.originForm?.eaRepairCheckData?.componentBaseData || {};
        },
        tableData(){
            return [{
                    col0: '名称',
                    col1: '等级',
                    col2: '序列号',
                    col3: '是否属于最后一级',
                    col4: '当前版本',
                    col5: '',
                    col6: '更新版本',
                    col7: '',
                },
                {
                    col0: this.detailForm.invName,
                    col1: this.detailForm.level,
                    col2: this.detailForm.serialNumber,
                    col3: this.detailForm.isLeaf ? '是' : '否',
                    col4: this.componentBaseData.versionNumber,
                    col5: '',
                    col6: this.componentBaseData.updateVersionNumber,
                    col7: '',
                },
                {
                    col0: '',
                    col1: '',
                    col2: '',
                    col3: '',
                    col4: '最高温度',
                    col5: this.componentBaseData.maxBht,
                    col6: '总入井时间',
                    col7: this.componentBaseData.totalInWellHrs,
                },
                {
                    col0: '',
                    col1: '',
                    col2: '',
                    col3: '',
                    col4: '修正最高温度',
                    col5: this.componentBaseData.reviseMaxBht,
                    col6: '修正总入井时间',
                    col7: this.componentBaseData.reviseTotalHours,
                },
                {
                    col0: '',
                    col1: '',
                    col2: '',
                    col3: '',
                    col4: '当前风险类型',
                    col5: this.getRiskTypeStr(this.componentBaseData.riskType),
                    col6: '当前风险值',
                    col7: this.componentBaseData.riskValue,
                },
                {
                    col0: '',
                    col1: '',
                    col2: '',
                    col3: '',
                    col4: '更新风险类型',
                    col5: this.getRiskTypeStr(this.componentBaseData.updateRiskType),
                    col6: '更新风险值',
                    col7: this.componentBaseData.updateRiskValue,
                },
            ]
        }
    },
    methods: {
        getRiskTypeStr(riskType){
            return this.riskTypeList.find(item => item.value === riskType)?.label || '';
        },
        arraySpanMethod({ row, column, rowIndex, columnIndex }){
            // 合并第1,2,3,4列的第二行到第六行
            if (columnIndex <=2) {
                if(rowIndex === 0){
                    return [1, 1];
                }
                if(rowIndex === 1){
                    return [5, 1];
                }else{
                    return [0, 0];
                }
            }
            // 合并第1,2行的第六列和第七列
            if(rowIndex === 0 || rowIndex === 1){
                if(columnIndex === 3 || columnIndex === 5){
                    return [1, 2];
                }else if(columnIndex === 4 || columnIndex === 6){
                    return [0, 0];
                }else{
                    return [1, 1]
                }
            }
            return [1, 1];

        },
        getSetting(editorRef) {
            return {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: ["image"],
                        handlers: {
                            image: () => {
                                this.currentEditor = editorRef;
                                let quill = this.$refs[editorRef][0].quill;
                                const ghostUploaderDom = document.getElementById("ghostUploader");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click(); 
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    }
                },
            };
        },
        onCancel(){
            this.isEdit = false;
            this.$emit('getData');
        },
        onSave(){
            this.$emit('update');
            this.isEdit = false;

        },
        uploadImg() {
            let quill = this.$refs[this.currentEditor][0].quill;
            let files = document.getElementById("ghostUploader").files;
            Promise.all(Array.from(files).map(file=>{
                const form = new FormData();
                form.append("file", file);
                return apiUploadFile(form).then(res=>{
                    return res.data.data;
                })
            })).then((urls) => {
                urls.forEach((url) => {
                    quill.insertEmbed(this.insertPosition, "image", url);
                    this.insertPosition += 1;
                });
                quill.setSelection(this.insertPosition);
            });
        },
        onClickTab(item) {
            this.currentField = item.field;
        },
        
        handlePateImage(event){
            const quill = this.$refs[this.currentEditor][0].quill;
            var items = (event.clipboardData || event.originalEvent.clipboardData)
                .items;
            for (let index in items) {
                var item = items[index];
                if (item.kind === "file") {
                    event.preventDefault();
                    var file = item.getAsFile();
                    const form = new FormData();
                    form.append("file", file);
                    apiUploadFile(form).then((res) => {
                        quill.insertEmbed(this.insertPosition, "image", res.data.data);
                        quill.setSelection(this.insertPosition + 1);
                    });
                }
            }
        },
        onEditorFocus(editorRef) {
            this.currentEditor = editorRef;
            const quill = this.$refs[editorRef][0].quill;
            quill.root.addEventListener("paste", this.handlePateImage);
        }
    }
}
</script>
<style lang="scss">
.lucida-quill-editor{
    .ql-toolbar{
        padding: 0;
    }
    .ql-container{
        height: 360px;
    }
}
.lucida-form{
    .el-form-item__label{
        padding: 0;
    }
}
.ea-para-table.el-table .cell{
    min-height: 18px;
}
</style>
<style lang="scss">
.lucida-quill-div{
    height: 400px;
    overflow: auto;
    background-color: #fff;
    line-height: 1.3;
    p{
        margin: 0;
    }
    img{
        max-width: calc(100%) !important;
    }
}
</style>