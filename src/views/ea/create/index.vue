<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <span style="float: right">
                <el-button type="primary" @click="onCreate">确认创建</el-button>
            </span>
            <div class="tool-card-title" style="margin-left: 0;">工单信息</div>
            <el-form :model="originForm" label-width="60px" :rules="originFormRules" ref="originFormRef">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="工单号" prop="eaNumber" label-width="80px">
                            <el-input v-model="originForm.eaNumber"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tool-card-title" style="margin-left: 0;">部件信息</div>
            <el-form :model="detailForm" label-width="90px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="部件序列号">
                            <CustomAutocomplete
                                v-model="detailForm.serialNumber"
                                placeholder=""
                                @select="onSelectSerialNumber"
                                @change="onClearCompoenentInfo"
                                clearable
                                style="width: 100%;"
                                value-key="serialNumber"
                                type="COMPONENT_INFO"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="部件品号" label-width="110px">
                            <el-input v-model="detailForm.invCode" @change="onClearCompoenentInfo"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="部件名称" label-width="110px">
                            <el-input v-model="detailForm.invName" @change="onClearCompoenentInfo"></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                        <el-form-item label="部件等级">
                            <el-input v-model="detailForm.level"></el-input>
                        </el-form-item>
                    </el-col> -->
                    <!-- <el-col :span="6">
                        <el-form-item label="是否属于最后一级" label-width="130px">
                            <el-select disabled v-model="detailForm.isLeaf" placeholder="" style="width: 100%;">
                                <el-option label="是" :value="true"></el-option>
                                <el-option label="否" :value="false"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <!-- <el-col :span="6">
                        <el-form-item label="母体仪器类型" label-width="100px">
                            <el-select
                                style="width: 100%"
                                v-model="detailForm.parentDeviceType"
                                placeholder=""
                                disabled
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="母体序列号">
                            <el-input disabled v-model="detailForm.parentSerialNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="母体品名">
                            <el-input disabled v-model="detailForm.parentInvName"></el-input>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="6">
                        <el-form-item label="总循环时间">
                            <el-input disabled v-model="detailForm.componentTotalCirculateHrs"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="最高温度">
                            <el-input disabled v-model="detailForm.componentMaxBht"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="修正最高温度" label-width="100px">
                            <el-input disabled v-model="detailForm.componentReviseMaxBht"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="总入井时间">
                            <el-input disabled v-model="detailForm.componentTotalInWellHrs"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="修正总入井时间" label-width="110px">
                            <el-input disabled v-model="detailForm.componentReviseTotalHrs"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="版本号">
                            <el-input disabled v-model="detailForm.versionNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="风险类型">
                            <el-select 
                                clearable
                                v-model="detailForm.riskType"
                                disabled
                                style="width: 100%"
                                placeholder=""
                            >
                                <el-option
                                    v-for="item in riskTypeList"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="风险值">
                            <el-input disabled v-model="detailForm.riskValue"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
                    

            <div class="tool-card-title" style="margin-left: 0;">作业信息</div>
            <el-form :model="detailForm" label-width="70px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="井号">
                            <FuzzyWellInput style="width: 100%" v-model="originForm.wellNumber" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业编号">
                            <el-input
                                v-model="originForm.jobNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="kit箱">
                            <el-input
                                v-model="originForm.kitNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系人" label-width="84px">
                            <el-input
                                v-model="detailForm.contactUser"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="6">
                        <el-form-item label="入井时间">
                            <el-input
                                v-model="detailForm.inWellHour"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="循环时间">
                            <el-input
                                v-model="detailForm.circulateHrs"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="最高温度">
                            <el-input
                                v-model="detailForm.maxBht"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入井趟次">
                            <el-input
                                v-if="!isEdit"
                                v-model="detailForm.run"
                            ></el-input>
                            
                            <el-select v-else v-model="detailForm.runList" multiple collapse-tags clearable style="width: 100%">
                                <el-option v-for="item in 20" :key="item" :value="String(item)" :label="String(item)">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tool-card-title" style="margin-left: 0;">维修信息</div>
            <el-form :model="detailForm" label-width="80px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="维修等级">
                            <el-select v-model="detailForm.repairLevel" style="width: 100%;" placeholder="">
                                <el-option v-for="item in 5" :key="item" :label="item" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="返回原因" label-width="70px">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 6 }"
                                v-model="detailForm.returnReason"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="备注">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 6 }"
                                v-model="detailForm.notes"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
</template>
<script>
import { apiAddEaWorkOrder } from '@/api/ea';
import { apiGetServiceHistoryDetailList } from '@/api/tool-track';
import { riskTypeList } from '@/utils/constant';
import FuzzyWellInput from "@/components/FuzzyWellInput/index.vue";
import { apiGetComponentFuzzyList } from '@/api/mwd';
import CustomAutocomplete from "@/components/CustomAutocomplete/index.vue";
import getDict from '@/utils/getDict';
// WLJ_TEST_10360829   COMPENSATION,LENGTH,ASSY,F/MWD,6.75LBRSS
export default {
    components: {
        FuzzyWellInput,
        CustomAutocomplete
    },
    data() {
        return {
            riskTypeList,
            deviceTypeList:[],
            isEdit: false,
            fuzzyWellNumberList: [],
            originForm: {
                eaNumber: null,
                wellNumber: null,
                jobNumber: null,
                kitNumber: null,
            },
            detailForm: {
                runList: [],
                run: null,
                parentDeviceType: null,
                parentSerialNumber: null,
                parentInvName: null,
                componentTotalCirculateHrs: null,
                componentMaxBht: null,
                componentReviseMaxBht: null,
                componentTotalInWellHrs: null,
                componentReviseTotalHrs: null,
                versionNumber: null,
                riskType: null,
                riskValue: null,
                contactUser: null,
                contactNumber: null,
                invName: null,
                invCode: null,
                serialNumber: null,
            },
            wellNumberList:[],
            originFormRules: {
                eaNumber: [
                    { required: true, message: '请输入工单号', trigger: 'blur' },
                ],
            }
        };
    },
    async mounted(){
        [this.deviceTypeList] = await getDict([18]);
    },
    methods: {
        querySearchSerialNumber(qs, cb){
            apiGetComponentFuzzyList({serialNumber: qs}).then((res)=>{
                cb(res.data.data || []);
            })
        },
        onSelectSerialNumber(data){
            this.detailForm.parentDeviceType = data.parentDeviceType;
            this.detailForm.parentSerialNumber = data.parentSerialNumber;
            this.detailForm.parentInvName = data.parentInvName;
            this.detailForm.componentTotalCirculateHrs = data.totalCirculateHrs;
            this.detailForm.componentMaxBht = data.maxBht;
            this.detailForm.componentReviseMaxBht = data.reviseMaxBht;
            this.detailForm.componentTotalInWellHrs = data.totalInWellHrs;
            this.detailForm.componentReviseTotalHrs = data.reviseTotalHours;
            this.detailForm.versionNumber = data.versionNumber;
            this.detailForm.riskType = data.riskType;
            this.detailForm.riskValue = data.riskValue;
            this.detailForm.invName = data.invName;
            this.detailForm.invCode = data.invCode;
        },
        onClearCompoenentInfo(){
            this.detailForm.parentDeviceType = null;
            this.detailForm.parentSerialNumber = null;
            this.detailForm.parentInvName = null;
            this.detailForm.componentTotalCirculateHrs = null;
            this.detailForm.componentMaxBht = null;
            this.detailForm.componentReviseMaxBht = null;
            this.detailForm.componentTotalInWellHrs = null;
            this.detailForm.componentReviseTotalHrs = null;
            this.detailForm.versionNumber = null;
            this.detailForm.riskType = null;
            this.detailForm.riskValue = null;
            // this.detailForm.invName = null;
            // this.detailForm.invCode = null;
        },
        async onCreate(){
            const valid = await this.$refs.originFormRef.validate();
            if(!valid) return;
            const form = {...this.originForm, workOrderEaDetailList: [this.detailForm]};
            apiAddEaWorkOrder(form).then((res)=>{
                const eaId = res.data.data.eaId;
                this.$router.push({path: '/ea/detail', query: {eaId}});
                this.$message.success('创建成功');
            })
        },
        getPartInfo(){
            if(this.detailForm.invCode && this.detailForm.serialNumber){
                apiGetServiceHistoryDetailList({invCode: this.detailForm.invCode, serialNumber: this.detailForm.serialNumber}, {size:10, current:1}).then((res)=>{
                    const data = res.data.data?.records?.[0] || {};
                    console.log(data);
                    this.detailForm.parentDeviceType = data.parentDeviceType;
                    this.detailForm.parentSerialNumber = data.parentSerialNumber;
                    this.detailForm.parentInvName = data.parentInvName;
                    this.detailForm.componentTotalCirculateHrs = data.totalCirculateHrs;
                    this.detailForm.componentMaxBht = data.maxBht;
                    this.detailForm.componentReviseMaxBht = data.reviseMaxBht;
                    this.detailForm.componentTotalInWellHrs = data.totalInWellHrs;
                    this.detailForm.componentReviseTotalHrs = data.reviseTotalHours;
                    this.detailForm.versionNumber = data.versionNumber;
                    this.detailForm.riskType = data.riskType;
                    this.detailForm.riskValue = data.riskValue;
                    this.detailForm.invName = data.invName;
                })
            }
        },
    },
}
</script>
<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
    
    .highlighted {
        padding: 2px;
        border: 1px solid rgb(67, 86, 152);
    }
}
</style>