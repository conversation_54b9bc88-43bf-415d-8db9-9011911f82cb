<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">工单查询</div>
            <el-row :gutter="10">
                <el-col :span="22">
                    <el-row :gutter="20">
                        <el-form :model="searchForm" label-width="70px">
                            <el-col :span="6">
                                <el-form-item label="EA工单号" label-width="80px">
                                    <el-input
                                        v-model="searchForm.eaNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <!-- <el-col :span="6">
                                <el-form-item label="部件名称">
                                    <el-input
                                        v-model="searchForm.invName"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col> -->
                            <el-col :span="6">
                                <el-form-item label="序列号">
                                    <el-input
                                        v-model="searchForm.serialNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="井号">
                                    <el-input
                                        v-model="searchForm.wellNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修状态">
                                    <el-select
                                        style="width:calc(100%)"
                                        v-model="searchForm.finish"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    >
                                        <el-option
                                            label="完成"
                                            :value="1"
                                        ></el-option>
                                        <el-option
                                            label="未完成"
                                            :value="0"
                                        ></el-option>
                                        <el-option
                                            label="滞留"
                                            :value="-1"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-form>
                    </el-row>
                </el-col>
                <el-col :span="2" v-if="p_AddEaWorkOrder">
                    <el-button
                        type="primary"
                        @click="onAddWorkOrder"
                        style="float: right"
                        >新增</el-button
                    >
                </el-col>
            </el-row>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                @sort-change="onSortChange"
                v-loading="tableLoading"
                :height="`calc(100vh - 240px)`"
                style="transition: height 0.25s;"
            >
                <el-table-column
                    label="工单号"
                    min-width="200"
                    prop="eaNumber"
                    align="center"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    label="部件名称"
                    prop="invName"
                    align="center"
                    width="200px"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    width="120px"
                ></el-table-column>
                <el-table-column
                    label="维修状态"
                    prop="finish"
                    width="120px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            v-if="scope.row.finish == 1"
                            type="success"
                            effect="dark"
                        >
                            完成
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="danger"
                            v-if="scope.row.finish == 0"
                        >
                            未完成
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="warning"
                            v-if="scope.row.finish == -1"
                        >
                            滞留
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    label="上级工单号"
                    min-width="200"
                    align="center"
                >
                    <template slot-scope="scope">
                        <template v-if="scope.row.parentEaId">
                            <router-link
                                tag="a"
                                style="color: blue;margin-left: 20px;width: 70px;"
                                :to="`/ea/detail?eaId=${scope.row.parentEaId}`"
                                target="_blank"
                                v-if="p_EaWorkOrderInfo"
                            >
                                {{ scope.row.parentEaNumber }}
                            </router-link>
                            <span v-else>
                                {{ scope.row.parentEaNumber }}
                            </span>
                        </template>
                        <template v-if="scope.row.parentMwdId">
                            <router-link
                                tag="a"
                                style="color: blue;margin-left: 20px;width: 70px;"
                                :to="`/mwd-tool-maintain/maintain?mwdId=${scope.row.parentMwdId}`"
                                target="_blank" 
                                v-if="p_MwdInfoView"
                            >
                                {{ scope.row.parentMwdNumber }}
                            </router-link>
                            <span v-else>
                                {{ scope.row.parentMwdNumber }}
                            </span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收日期"
                    prop="receiveDate"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修人"
                    prop="repairUser"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修耗时"
                    prop="laborHours"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    min-width="120"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="返回原因"
                    prop="returnReason"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <!-- <el-table-column
                    label="本次入井时间"
                    prop="currentInWellHour"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总入井时间"
                    prop="totalInWellHrs"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="本次最高温度"
                    prop="currentMaxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总最高温度"
                    prop="maxBht"
                    align="center"
                    width="120"
                ></el-table-column> -->
                <el-table-column
                    label="完成日期"
                    prop="endDate"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后编辑人"
                    prop="lastModifiedByStr"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后修改时间"
                    prop="updateTime"
                    min-width="170"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    width="140"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="onDetail(scope.row.eaId)"
                        >
                            查看
                        </el-button>
                        <el-button
                            :disabled="scope.row.finish===1"
                            :title="scope.row.finish===1?'已完成的工单不能删除':''"
                            type="text"
                            @click="onDelete(scope.row.eaId)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>
<script>
import { apiDeleteEaWorkOrder, apiGetEaWorkOrderList } from "@/api/ea";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    data(){
        return {
            tableLoading: false,
            searchForm: {},
            tableData: [],
            currentPage: 1,
            pageSize: 100,
            total: 0,
            orderType: "",
            orderBy: "",
        }
    },
    computed: {
        p_AddEaWorkOrder(){
            this.$checkBtnPermission('sys:ea:create:all')
        },
        p_EaWorkOrderInfo(){
            return this.$checkBtnPermission('sys:ea:detail:all');
        },
        p_MwdInfoView(){
            return this.$checkBtnPermission('sys:mwd:info');
        }
    },
    mounted() {
        this.getWorkOrderList();
    },
    activated() {
        this.getWorkOrderList();
    },
    methods: {
        getWorkOrderList() {
            this.tableLoading = true;
            apiGetEaWorkOrderList({
                ...this.searchForm,
                orderType: this.orderType,
                orderBy: this.orderBy
            }, {
                current: this.currentPage,
                size: this.pageSize,
            }).then((res) => {
                const data = res.data.data || {};
                this.tableData = data.records || [];
                this.total = data.total;
            }).finally(()=>{
                this.tableLoading = false;
            });
        },
        onDelete(eaId){
            this.$confirm("确认删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    apiDeleteEaWorkOrder({eaId}).then(() => {
                        this.getWorkOrderList();
                    });
                })
                .catch(() => {});
        },
        onDetail(eaId) {
            this.$router.push(`/ea/detail?eaId=${eaId}`);
        },
        onAddWorkOrder() {
            this.$router.push("/ea/create");
        },
        onSearchWorkOrder() {
            const receiveDaterange = this.searchForm.receiveDaterange;

            if (receiveDaterange?.length) {
                this.searchForm.startTime = receiveDaterange[0];
                this.searchForm.endTime = receiveDaterange[1];
            } else {
                this.searchForm.startTime = undefined;
                this.searchForm.endTime = undefined;
            }
            const finishDaterange = this.searchForm.finishDaterange;

            if (finishDaterange?.length) {
                this.searchForm.finishStartTime = finishDaterange[0];
                this.searchForm.finishEndTime = finishDaterange[1];
            } else {
                this.searchForm.finishStartTime = undefined;
                this.searchForm.finishEndTime = undefined;
            }
            Object.keys(this.searchForm).forEach((key) => {
                let value = this.searchForm[key];
                if (value === "" || value === null) {
                    this.searchForm[key] = undefined;
                }
            });
            this.currentPage = 1;
            this.getWorkOrderList();
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getWorkOrderList();
        },
        onSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.getWorkOrderList();
        }
    }
}
</script>
<style lang="scss" scoped>
.total-info {
    transition: height 0.25s;
    overflow: hidden;
    margin-top: 14px;
    display: inline-block;
    border: 2px solid #ff4949;
    padding: 10px 8px 0;
    min-width: 200px;
    height: 105px;
    box-sizing: border-box;
    &.collapsed {
        border: none;
        height: 0;
    }
}
</style>
