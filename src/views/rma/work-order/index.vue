<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">工单查询</div>
            <el-row :gutter="10">
                <el-col :span="22">
                    <el-row :gutter="20">
                        <el-form :model="searchForm" label-width="70px">
                            <el-col :span="6">
                                <el-form-item label="RMA工单号" label-width="90px">
                                    <el-input
                                        v-model="searchForm.rmaNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="部件名称">
                                    <el-input
                                        v-model="searchForm.invName"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="序列号">
                                    <el-input
                                        v-model="searchForm.serialNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="井号">
                                    <el-input
                                        v-model="searchForm.wellNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修状态">
                                    <el-select
                                        style="width:calc(100%)"
                                        v-model="searchForm.finish"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    >
                                        <el-option
                                            label="完成"
                                            :value="1"
                                        ></el-option>
                                        <el-option
                                            label="未完成"
                                            :value="0"
                                        ></el-option>
                                        <el-option
                                            label="滞留"
                                            :value="-1"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修部门">
                                    <el-select
                                        style="width:calc(100%)"
                                        v-model="searchForm.departmentName"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    >
                                        <el-option v-for="item in dptList" :key="item" :label="item" :value="item"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-form>
                    </el-row>
                </el-col>
                <el-col :span="2">
                    <el-button
                        type="primary"
                        @click="onAddWorkOrder"
                        v-if="p_rmaAdd"
                        style="float: right"
                        >新增</el-button
                    >
                </el-col>
            </el-row>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                @sort-change="onSortChange"
                v-loading="tableLoading"
                :height="`calc(100vh - 280px)`"
                style="transition: height 0.25s;"
            >
                <el-table-column
                    label="RMA工单号"
                    min-width="200"
                    prop="rmaNumber"
                    align="center"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    label="部件名称"
                    prop="invName"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修状态"
                    prop="finish"
                    width="120px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            v-if="scope.row.finish == 1"
                            type="success"
                            effect="dark"
                        >
                            完成
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="danger"
                            v-if="scope.row.finish == 0"
                        >
                            未完成
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="warning"
                            v-if="scope.row.finish == -1"
                        >
                            滞留
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收日期"
                    prop="receiveDate"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修人"
                    prop="repairUser"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修部门"
                    prop="departmentName"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修耗时"
                    prop="laborHours"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    min-width="120"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="返回原因"
                    prop="returnReason"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <el-table-column
                    label="本次入井时间"
                    prop="currentInWellHour"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总入井时间"
                    prop="totalInWellHrs"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="本次最高温度"
                    prop="currentMaxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总最高温度"
                    prop="maxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="完成日期"
                    prop="endDate"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后编辑人"
                    prop="lastModifiedByStr"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后修改时间"
                    prop="updateTime"
                    min-width="170"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="操作"
                    :width="operatorWidth"
                    v-if="p_rmaInfo||p_rmaDelete"
                    fixed="right"
                    width="140"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_rmaInfo"
                            type="text"
                            @click="onDetail(scope.row.rmaId)"
                        >
                            查看
                        </el-button>
                        <el-button
                            type="text"
                            @click="onHandover(scope.row.rmaId)"
                            :title="handoverAttrs(scope.row, 'title')"
                            :disabled="handoverAttrs(scope.row, 'disabled')"
                        >
                            交接
                        </el-button>
                        <el-button
                            v-if="p_rmaDelete"
                            :disabled="scope.row.finish===1"
                            :title="scope.row.finish===1?'已完成的工单不能删除':''"
                            type="text"
                            @click="onDelete(scope.row.rmaId)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <EmailDialog email-business-type="RMA_HANDOVER" ref="emailDialogRef"></EmailDialog>
    </div>
</template>
<script>
import { apiDeleteRmaWorkOrder, apiGetRmaWorkOrderList, apiGetRmaWorkOrderDptNameList } from "@/api/rma";
import { apiCreateHandover } from "@/api/handover";
import EmailDialog from "@/components/EmailDialog/index.vue"
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    components: { EmailDialog },
    data(){
        return {
            tableLoading: false,
            searchForm: {},
            tableData: [],
            currentPage: 1,
            pageSize: 100,
            total: 0,
            orderType: "",
            orderBy: "",
            dptList: [],
        }
    },
    computed: {
        p_rmaInfo(){
            return this.$checkBtnPermission(`sys:rma:workorder:info`)
        },
        p_rmaDelete(){
            return this.$checkBtnPermission(`sys:rma:workorder:delete`)
        },
        p_rmaAdd(){
            return this.$checkBtnPermission(`sys:rma:workorder:add`)
        },
        operatorWidth(){
            return [this.p_rmaDelete,this.p_rmaInfo].filter(item=>item).length * 50 + 'px'
        }
    },
    mounted() {
        this.getWorkOrderList();
        this.getDptList();
    },
    activated() {
        this.getWorkOrderList();
        this.getDptList();
    },
    methods: {
        handoverAttrs(row, type){
            const isFinished = row.finish===1;
            const isHandovered = !!row.handoverStatus;
            if(!isFinished){
                return type==='title' ? '未完成的工单不能交接' : true
            }
            if(isHandovered){
                return type==='title' ? '已交接' : true
            }
            return type==='title' ? '' : false
        },
        getDptList() {
            apiGetRmaWorkOrderDptNameList({}).then(res=>{
                this.dptList = res.data.data || [];
            })
        },
        getWorkOrderList() {
            this.tableLoading = true;
            apiGetRmaWorkOrderList({
                ...this.searchForm,
                orderType: this.orderType,
                orderBy: this.orderBy
            }, {
                current: this.currentPage,
                size: this.pageSize,
            }).then((res) => {
                const data = res.data.data || {};
                this.tableData = data.records || [];
                this.total = data.total;
            }).finally(()=>{
                this.tableLoading = false;
            });
        },
        onDelete(rmaId){
            this.$confirm("确认删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    apiDeleteRmaWorkOrder({rmaId}).then(() => {
                        this.getWorkOrderList();
                    });
                })
                .catch(() => {});
        },
        onHandover(rmaId){
            this.$confirm("确认交接该工单？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    const form = new FormData();
                    form.append("idList", [rmaId].toString());
                    form.append("type", "RMA");
                    apiCreateHandover(form).then(()=>{
                        this.getWorkOrderList();
                        this.$refs.emailDialogRef.showDialog()
                    })
                })
                .catch(() => {});
        },
        onDetail(rmaId) {
            this.$router.push(`/rma/maintain?rmaId=${rmaId}`);
        },
        onAddWorkOrder() {
            this.$router.push("/rma/maintain");
        },
        onSearchWorkOrder() {
            const receiveDaterange = this.searchForm.receiveDaterange;

            if (receiveDaterange?.length) {
                this.searchForm.startTime = receiveDaterange[0];
                this.searchForm.endTime = receiveDaterange[1];
            } else {
                this.searchForm.startTime = undefined;
                this.searchForm.endTime = undefined;
            }
            const finishDaterange = this.searchForm.finishDaterange;

            if (finishDaterange?.length) {
                this.searchForm.finishStartTime = finishDaterange[0];
                this.searchForm.finishEndTime = finishDaterange[1];
            } else {
                this.searchForm.finishStartTime = undefined;
                this.searchForm.finishEndTime = undefined;
            }
            Object.keys(this.searchForm).forEach((key) => {
                let value = this.searchForm[key];
                if (value === "" || value === null) {
                    this.searchForm[key] = undefined;
                }
            });
            this.currentPage = 1;
            this.getWorkOrderList();
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getWorkOrderList();
        },
        onSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.getWorkOrderList();
        }
    }
}
</script>
<style lang="scss" scoped>
.total-info {
    transition: height 0.25s;
    overflow: hidden;
    margin-top: 14px;
    display: inline-block;
    border: 2px solid #ff4949;
    padding: 10px 8px 0;
    min-width: 200px;
    height: 105px;
    box-sizing: border-box;
    &.collapsed {
        border: none;
        height: 0;
    }
}
</style>
