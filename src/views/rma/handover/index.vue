<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">核心部件交接单</div>
            </div>
            
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                style="margin-top: 10px;"
            >
                <el-table-column
                    label="交接时间"
                    align="center"
                    prop="handoverDate"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="品名"
                    prop="invName"
                    align="center"
                    fixed="left"
                    width="140"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    prop="riskType"
                    label="风险类型"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="versionNumber"
                    label="固件版本号"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    label="交接人"
                    prop="handoverUserName"
                    min-width="120"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="接收人"
                    prop="receiveUserName"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receivedDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="RMA工单号"
                    prop="businessNumber"
                    align="center"
                    width="140"
                >
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            target="_blank" 
                            :to="`/rma/maintain?rmaId=${row.businessId}`" 
                            v-if="p_RmaInfoView"
                        >
                            {{ row.businessNumber }}
                        </router-link>
                        <span v-else>{{ row.businessNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收状态"
                    prop="received"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RECEIVE_STATUS" v-if="scope.row.handoverStatus === 'RECEIVED'" tagValue="RECEIVED" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else tagValue="SUBMITTED" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="100"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button type="text" @click="onSendEmail(scope.row)" v-if="scope.row.handoverStatus !== 'RECEIVED'">
                            邮件通知
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <EmailDialog email-business-type="RMA_HANDOVER" ref="emailDialogRef"></EmailDialog>
    </div>
</template>

<script>
import { apiGetHandoverList } from "@/api/handover"
import { apiReceiveRmaRepair } from "@/api/rma";
import EmailDialog from "@/components/EmailDialog/index.vue";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    name: 'rma-handover',
    components: { EmailDialog },
    data(){
        return {
            orderBy: "createTime",
            orderType: "desc",
            isLoading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {
                handoverType: "RMA"
            },
        };
    },
    computed: {
        p_RmaInfoView(){
            return this.$checkBtnPermission('sys:rma:workorder:info');
        }
    },
    async mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.getRepairList();
    },
    methods: {
        getRepairList() {
            apiGetHandoverList(
                {
                    ...this.searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },
        onReceive(row){
            this.$confirm("确认接收这个返修单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiReceiveRmaRepair({repairId: row.repairId}).then(()=>{
                    this.$message.success("操作成功！");
                    this.getRepairList();
                })
            })
        },
        async onDetail(row) {
            this.$router.push(`/rma/repair/detail?id=${row.repairId}`);
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getRepairList();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.getRepairList();
        },
        onSendEmail(){
            this.$refs.emailDialogRef.showDialog();
        },
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
