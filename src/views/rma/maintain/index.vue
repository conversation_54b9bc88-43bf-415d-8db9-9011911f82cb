<template>
    <div>
        <div class="tool-maintain-container" v-loading="loading">
            <div class="tool-card" style="position: relative">
                <!-- #region 创建工单 -->
                <template v-if="!rmaId">
                    <Create />
                </template>
                <!-- #endregion -->
                <template v-else>
                    <div class="update-info" style="position: absolute; right:0; color: grey; font-size: 14px;line-height:20px">
                        <div v-if="detailForm.lastModifiedByStr">
                            最近编辑人: {{ detailForm.lastModifiedByStr }}
                        </div>
                        <div v-if="detailForm.lastModifiedDate">
                            更新于: {{ detailForm.lastModifiedDate}}
                        </div>
                    </div>
                    <div class="operators" :style="`top:calc(50% - ${operatorList.length*15+7}px)`" v-if="showOperators">
                        <div
                            class="operator"
                            v-for="item in operatorList"
                            :key="item.type"
                            v-html="item.title"
                            @click.stop="onClickOperator(item.type)"
                        ></div>
                    </div>

                    <div
                        class="operator-cover"
                        @click.stop="showOperators = true"
                        v-if="!showOperators&&operatorList.length>1"
                    >
                        操作
                    </div>
                    <div class="tool-card-title">
                        RMA工单信息
                    </div>
                    <el-form :model="originForm" label-width="60px">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="工单号">
                                    <el-input
                                        disabled
                                        v-model="originForm.rmaNumber"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修部门" prop="departmentName" label-width="80px">
                                    <el-input
                                        disabled
                                        v-model="detailForm.departmentName"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <BaseInfo :originForm="originForm" :isEdit="isEdit" />
                    <Workshop :originForm="originForm" :isEdit="isEdit" />
                    <MaintainCheck :originForm="originForm" :isEdit="isEdit" />
                    <MaintainSummary :originForm="originForm" :isEdit="isEdit" />
                    <Exchange :originForm="originForm" :isEdit="isEdit" />
                    <LaborHours ref="laborHours" :originForm="originForm" :isEdit="isEdit" />
                    <FileManage :isEdit="isEdit" />
                    <div class="tool-card-title">完成工单</div>
                    <el-button type="primary" @click="onFinishWorkOrder" v-if="!isFinished">完成工单</el-button>
                    <span v-else>工单已完成</span>
                </template>
            </div>
        </div>
    </div>
</template>
<script>
import { apiFinishRmaWorkOrder, apiGetRmaWorkOrderInfo, apiUpdateRmaWorkOrder } from "@/api/rma"
import Create from "./components/create.vue";
import BaseInfo from "./components/base.vue";
import Workshop from "./components/workshop.vue";
import MaintainCheck from "./components/check.vue"
import MaintainSummary from "./components/summary.vue"
import Exchange from "./components/exchange.vue";
import LaborHours from './components/labor.vue'
import FileManage from './components/file.vue'
export default {
    name: "rma-maintain",
    components: {
        Create,
        BaseInfo,
        Workshop,
        MaintainCheck,
        MaintainSummary,
        Exchange,
        LaborHours,
        FileManage,
    },
    data(){
        return {
            showOperators: false,
            finish: 0,
            rmaId: null,
            originForm: {
                workOrderRmaDetail: {},
                rmaCheckData: {
                    replaceItemList: [],
                    repairSummary: {},
                    rmaDiagnosis: {},
                    repairCheckList: [],
                },
            },
            loading: false,
            isEdit: false,
            detailForm: {}
        }
    },
    computed: {
        isFinished() {
            return !!this.finish;
        },
        p_MwdUpdate() {
            return this.$checkBtnPermission(`sys:pittools:update`);
        },
        operatorList() {
            return [
                {
                    show: !this.isEdit,
                    type: "EDIT",
                    title: "编辑工单",
                },
                {
                    show: this.isEdit,
                    type: "SAVE",
                    title: "保存编辑",
                },
                {
                    show: this.isEdit,
                    type: "CANCEL",
                    title: "取消编辑",
                },
                {
                    show: true,
                    type: "BACK",
                    title: "返&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;回",
                },
            ].filter((item) => item.show);
        }
    },
    async mounted() {
        this.rmaId = Number(this.$route.query.rmaId);
        this.loading = true;
        try {
            if (this.rmaId) {
                await this.getWorkOrderInfo();
                this.$refs.laborHours.erpLabor = this.originForm.rmaNumber;
            }
        } finally {
            this.loading = false;
        }
    },
    methods: {
        onClickOperator(type){
            switch(type){
                case "EDIT":
                    this.isEdit = true;
                    break;
                case "SAVE":
                    this.saveOrder();
                    break;
                case "CANCEL":
                    this.getWorkOrderInfo();
                    this.isEdit = false;
                    break;
                case "COMPLETE":
                    this.onFinishWorkOrder();
                    break;
                case "BACK":
                    this.showOperators = false;
                    break;
                default:
                    return;
            }
        },
        onFinishWorkOrder() {
            this.$confirm("确认完成工单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiFinishRmaWorkOrder({
                    rmaId: this.rmaId,
                    finish: 1,
                }).then(()=>this.getWorkOrderInfo())
            });
        },
        async saveOrder(){
            this.isEdit = false;
            apiUpdateRmaWorkOrder(this.originForm).then(() => {
                this.getWorkOrderInfo();
                this.isEdit = false;
                this.$message.success("更新成功！");
            });
        },
        getWorkOrderInfo() {
            return apiGetRmaWorkOrderInfo({ rmaId: this.rmaId }).then((res) => {
                const data = res.data.data || {};
                this.originForm = data;
                this.finish = data.finish;
                this.detailForm = data.workOrderRmaDetail || {};
            });
        },
        onAddWorkOrderSuccess(rmaId) {
            this.rmaId = rmaId;
        },
        onFinishWorkOrderSuccess(finish) {
            this.finish = finish;
        },
        onGetFinish(finish) {
            this.finish = finish;
        }
    },
    
}
</script>
<style lang="scss" scoped>
.operators {
    position: fixed;
    right: 30px;
    top: calc(50% - 97px);
    display: flex;
    flex-direction: column;
    justify-content: center;

    padding: 6px 0;
    margin: 2px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .operator {
        line-height: 30px;
        padding: 0 10px;
        margin: 0;
        font-size: 14px;
        text-align: center;
        color: #606266;
        cursor: pointer;
        outline: none;
        &:hover {
            background-color: #ecf5ff;
            color: #66b1ff;
        }
    }
}
.operator-cover {
    position: fixed;
    right: 30px;
    top: calc(50% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    color: white;
    padding-left: 6px;
    font-size: 14px;
    line-height: 2;
    width: 26px;
    height: 80px;
    background: rgba(67, 86, 152, 0.8);
    cursor: pointer;
    user-select: none;
    .operator {
        font-size: 14px;
        line-height: 1.4;
        border: 1px solid grey;
        padding: 4px;
    }
}
.step-container {
    display: flex;
    justify-content: space-between;

    .step {
        width: 140px;
        height: 60px;
        line-height: 58px;
        text-align: center;
        font-size: 18px;
        border: 1px solid #ccc;
        font-weight: 500;
        border-radius: 16px;
        cursor: pointer;
        user-select: none;
        &.active {
            background: pink;
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
}
</style>
<style lang="scss" scoped>
.tool-maintain-container {
    padding: 20px 40px;
    padding-right: 100px;
    margin: 12px;
    background: white;
    overflow: hidden;
    .tool-maintain-title {
        font-size: 16px;
        font-weight: 600;
    }
    .tool-card-subtitle {
        padding-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        color: rgb(67, 86, 152);
        margin-left: -12px;
        &::before {
            display: inline-block;
            margin-right: 6px;
            content: "-";
        }
    }
    .is-process {
        color: rgb(67, 86, 152) !important;
        font-weight: normal !important;
        .el-step__icon {
            border-color: rgb(67, 86, 152) !important;
        }
    }
    .active-step {
        .is-process {
            color: rgb(67, 86, 152) !important;
            font-weight: bold !important;
            .el-step__icon {
                border-color: rgb(67, 86, 152) !important;
            }
        }
    }
}
.el-table__expanded-cell {
    background: rgb(235, 238, 245);
}
.el-table__expanded-cell:hover {
    background: rgb(235, 238, 245) !important;
}
.active-step .el-step__title {
    color: rgb(67, 86, 152) !important;
    font-weight: bold;
}
</style>
