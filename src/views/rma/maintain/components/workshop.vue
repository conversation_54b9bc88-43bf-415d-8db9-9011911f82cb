<template>
    <div>
        <div class="tool-card-title">车间检查信息</div>
        <div class="rma-item-container">
            <div class="item-title">
                功能检查
            </div>
            <el-row>
                <el-col :span="4">
                    <div class="sub-item-title">
                        常温功能检查
                    </div>
                    <el-select v-model="detailForm.normalTemperatureFunctionCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.normalTemperatureFunctionCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        高温测试
                    </div>
                    <el-select v-model="detailForm.highTemperatureTest" :disabled="!isEdit" :class="getCheckClass(detailForm.highTemperatureTest)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        振动测试
                    </div>
                    <el-select v-model="detailForm.vibrationTest" :disabled="!isEdit" :class="getCheckClass(detailForm.vibrationTest)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        旋转测试
                    </div>
                    <el-select v-model="detailForm.rotationTest" :disabled="!isEdit" :class="getCheckClass(detailForm.rotationTest)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        短传性能测试
                    </div>
                    <el-select v-model="detailForm.shortTransmissionPerformanceTest" :disabled="!isEdit" :class="getCheckClass(detailForm.shortTransmissionPerformanceTest)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        LOG数据存储测试
                    </div>
                    <el-select v-model="detailForm.logDataStorageTest" :disabled="!isEdit" :class="getCheckClass(detailForm.logDataStorageTest)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
            </el-row>
        </div>
        
        <div class="rma-item-container">
            <div class="item-title">
                机械检查
            </div>
            <el-row>
                <el-col :span="4">
                    <div class="sub-item-title">
                        绝缘检查
                    </div>
                    <el-select v-model="detailForm.insulationCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.insulationCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        本体检查
                    </div>
                    <el-select v-model="detailForm.bodyCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.bodyCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        盖板检查
                    </div>
                    <el-select v-model="detailForm.coverCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.coverCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
            </el-row>
        </div>
        <div class="rma-item-container">
            <div class="item-title">
                电子/线路检查
            </div>
            <el-row>
                <el-col :span="4">
                    <div class="sub-item-title">
                        本体线路ringout测量
                    </div>
                    <el-select v-model="detailForm.bodyLineRingOutMeasurement" :disabled="!isEdit" :class="getCheckClass(detailForm.bodyLineRingOutMeasurement)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        线路固封情况检查
                    </div>
                    <el-select v-model="detailForm.lineSealingConditionCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.lineSealingConditionCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        接头状态检查
                    </div>
                    <el-select v-model="detailForm.connectorStatusCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.connectorStatusCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        EC物理状态检查
                    </div>
                    <el-select v-model="detailForm.ecPhysicalStatusCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.ecPhysicalStatusCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        伽马探头物理状态检查
                    </div>
                    <el-select v-model="detailForm.gammaProbePhysicalStatusCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.gammaProbePhysicalStatusCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="4">
                    <div class="sub-item-title">
                        电池物理状态检查
                    </div>
                    <el-select v-model="detailForm.batteryPhysicalStatusCheck" :disabled="!isEdit" :class="getCheckClass(detailForm.batteryPhysicalStatusCheck)" class="check-select" size="mini">
                        <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                    </el-select>
                </el-col>
            </el-row>
        </div>
        <div class="rma-item-container">
            <div class="item-title">
                车间诊断初步结论
            </div>
            <el-input type="textarea" v-model="detailForm.workshopPreliminaryDiagnosisConclusion" :disabled="!isEdit"></el-input>
        </div>
    </div>
</template>
<script>
export default {
    name: "RmaWorkShop",
    props: {
        isEdit: {
            type: Boolean,
            default: false
        },
        originForm: {
            type: Object,
            default: () => ({ rmaCheckData: { rmaDiagnosis: {}} })
        }
    },
    data(){
        return {
            checkList: [
                {value: -1, label: 'FAIL', class: 'FAIL'},
                {value: 0, label: 'N/A', class: 'NA'},
                {value: 1, label: 'PASS', class: 'PASS'},
            ]
        }
    },
    computed:{
        detailForm() {
            return this.originForm.rmaCheckData.rmaDiagnosis || {};
        }
    },
    methods: {
        getCheckClass(value){
            return this.checkList.find(item=>item.value === value)?.label || ''
        }
    }
}
</script>
<style lang="scss" scoped>
.flex-row .el-row{
    display: flex;
    flex-wrap: wrap;
}
.rma-item-container{
    margin-bottom: 26px;
    .item-title{
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 16px;
        &::before {
            display: inline-block;
            width:6px;
            height: 20px;
            vertical-align: sub;
            background: rgba(67, 86, 152, 0.8);
            margin-right: 6px;
            content: "";
        }
    }
    .sub-item-title{
        font-size: 14px;
        // font-weight: bold;
        margin-bottom: 8px;
        margin-left: 16px;
        // &::before {
        //     display: inline-block;
        //     width: 10px;
        //     height: 10px;
        //     background: rgba(67, 86, 152, 0.8);
        //     margin-right: 6px;
        //     margin-left: -2px;
        //     border-radius: 999999%;
        //     content: "";
        // }
    }
    .check-select {
        width: 100px;
        margin-left: 16px;
        &.PASS :deep() .el-input__inner{
            background: #67c23a;
            color: white;
        }
        &.NA :deep() .el-input__inner{

        }
        &.FAIL :deep() .el-input__inner{
            background: #ff4949;
            color: white;
        }

    }
}
</style>
