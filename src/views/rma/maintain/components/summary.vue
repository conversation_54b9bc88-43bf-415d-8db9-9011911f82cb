<template>
    <div style="margin-bottom:20px" class="rma-summary">
        <div class="tool-card-title">维修总结</div>
        <el-table :data="[{}]" border>
            <el-table-column prop="desc" label="硬件" align="center">
                <el-table-column prop="inspectionFindingsHardware" label="检查发现" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.inspectionFindingsHardware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.inspectionFindingsHardware}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="rootCauseHardware" label="根本原因" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.rootCauseHardware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.rootCauseHardware}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="correctiveActionsHardware" label="解决措施" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.correctiveActionsHardware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.correctiveActionsHardware}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="desc" label="固件" align="center">
                <el-table-column prop="currentFirmwareVersion" label="当前固件版本号" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.currentFirmwareVersion" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.currentFirmwareVersion}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="upgradedFirmwareVersion" label="升级固件版本号" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.upgradedFirmwareVersion" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.upgradedFirmwareVersion}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="inspectionFindingsFirmware" label="检查发现" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.inspectionFindingsFirmware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.inspectionFindingsFirmware}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="rootCauseFirmware" label="根本原因" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.rootCauseFirmware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.rootCauseFirmware}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="correctiveActionsFirmware" label="解决措施" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="detailForm.correctiveActionsFirmware" v-if="isEdit"></el-input>
                        <span v-else>{{detailForm.correctiveActionsFirmware}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
        </el-table>
        <el-table :data="[{}]" :header-cell-style="{background:'#F5F7FA'}" style="margin-top:-1px"border>
            <el-table-column prop="summary" label="总结" align="center">
                <template slot-scope="scope">
                    <quill-editor
                        v-if="isEdit"
                        class="table-editor"
                        style="width: 100%"
                        :ref="`quillEditor`"
                        v-model="detailForm.summary"
                        :options="getEditorOption()"
                        @focus="onEditorFocus"
                    />
                    <div v-else v-html="detailForm.summary"></div>
                </template>
            </el-table-column>
        </el-table>
        <form
            method="post"
            enctype="multipart/form-data"
            id="uploadFormMulti"
        >
            <input
                style="display:none"
                id="rmaSummary"
                type="file"
                name="file"
                multiple
                accept="image/jpg,image/jpeg,image/png,image/gif"
                @change="uploadImg"
            />
        </form>
    </div>
</template>
<script>
import { apiUploadFile } from '@/api/file';

export default {
    name: "MaintainSummary",
    props: {
        originForm: {
            type: Object,
            default: () => ({ rmaCheckData: { repairSummary: {}} })
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            addRowItem: { desc: null, hours: null, date: null, notes: null },
        }
    },
    computed:{
        detailForm() {
            return this.originForm.rmaCheckData.repairSummary
        }
    },
    methods: {
        getEditorOption() {
            return {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: [["image"]],
                        handlers: {
                            image: () => {
                                const targetRef = this.$refs[`quillEditor`];
                                let quill = targetRef.quill;
                                const ghostUploaderDom = document.getElementById("rmaSummary");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click();                            
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    },
                },
            };
        },
        handlePateImage(event){
            const quill = this.$refs[`quillEditor`].quill;
            var items = (event.clipboardData || event.originalEvent.clipboardData)
                .items;
            for (let index in items) {
                var item = items[index];
                if (item.kind === "file") {
                    event.preventDefault();
                    var file = item.getAsFile();
                    const form = new FormData();
                    form.append("file", file);
                    apiUploadFile(form).then((res) => {
                        quill.insertEmbed(this.insertPosition, "image", res.data.data);
                        quill.setSelection(this.insertPosition + 1);
                    });
                }
            }
        },
        onEditorFocus() {
            const quill = this.$refs[`quillEditor`].quill;
            quill.root.addEventListener("paste", this.handlePateImage);
        },
        uploadImg() {
            let quill = this.$refs[`quillEditor`].quill;
            let files = document.getElementById("rmaSummary").files;
            Promise.all(Array.from(files).map(file=>{
                const form = new FormData();
                form.append("file", file);
                return apiUploadFile(form).then(res=>{
                    return res.data.data;
                })
            })).then((urls) => {
                urls.forEach((url) => {
                    quill.insertEmbed(this.insertPosition, "image", url);
                    this.insertPosition += 1;
                });
                quill.setSelection(this.insertPosition);
            });
        },
        deleteCell(scope) {
            let index = scope.$index;
            this.tableData.splice(index, 1);
        },
        addRow(scope) {
            if (scope) {
                let index = scope.$index;
                this.tableData.splice(index + 1, 0, {
                    ...this.addRowItem
                });
            } else {
                this.tableData.unshift({
                    ...this.addRowItem
                });
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.table-editor :deep(){
    .ql-container{
        min-height: 100px;
        max-height: 200px;
        overflow: auto;
    }
}
.rma-summary:deep() .cell{
    min-height: 20px;
}
</style>
