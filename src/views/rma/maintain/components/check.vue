<template>
    <ImageViewer :disabled="isEdit" style="margin-bottom:20px" class="rma-check">
        <div class="tool-card-title">维修检查</div>
        <div style="margin-bottom:10px" v-if="isEdit">
            <el-button @click="addRow()" type="primary" style="margin-right:10px">首行插入</el-button>
        </div>
        <el-table :data="tableData" border>
            <el-table-column prop="repairDescription" label="维修描述">
                <template slot-scope="scope">
                    <quill-editor
                        v-if="isEdit"
                        class="table-editor"
                        style="width: 100%"
                        :ref="`quillEditor-${scope.$index}`"
                        v-model="scope.row.repairDescription"
                        :options="getEditorOption(scope)"
                        @focus="onEditorFocus(scope.$index)"
                    />
                    <div v-else v-html="scope.row.repairDescription"></div>
                </template>
            </el-table-column>
            <el-table-column prop="repairLaborHour" label="维修用时" width="160px">
                <template slot-scope="scope">
                    <InputNumber v-model="scope.row.repairLaborHour" align="left" style="width: 100%" v-if="isEdit" />
                    <span v-else>{{ scope.row.repairLaborHour }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="date" label="日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isEdit"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.date"
                        type="date"
                    >
                    </el-date-picker>
                    <span v-else>{{scope.row.date}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="备注" width="300px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.note}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                </template>
            </el-table-column>
        </el-table>
        <form
            method="post"
            enctype="multipart/form-data"
            id="uploadFormMulti"
        >
            <input
                style="display:none"
                id="ghostUploader"
                type="file"
                name="file"
                multiple
                accept="image/jpg,image/jpeg,image/png,image/gif"
                @change="uploadImg"
            />
        </form>
    </ImageViewer>
</template>
<script>
import { apiUploadFile } from "@/api/file";
import ImageViewer from "@/components/ImageViewer/index.vue";
export default {
    name: "RmaMaintainCheck",
    components: { ImageViewer },
    props: {
        originForm: {
            type: Object,
            default: () => ({ rmaCheckData: { repairCheckList: [] } })
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            addRowItem: {
                repairDescription: null,
                repairLaborHour: null,
                date: null,
                note: null
            },
        }
    },
    computed: {
        tableData() {
            return this.originForm.rmaCheckData.repairCheckList
        }
    },
    methods: {
        getEditorOption({$index}) {
            return {
                placeholder: "",
                modules: {
                    toolbar: {
                        container: [["image"]],
                        handlers: {
                            image: () => {
                                const targetRef = this.$refs[`quillEditor-${$index}`];
                                let quill = targetRef.quill;
                                this.currentQuillRef = targetRef;
                                const ghostUploaderDom = document.getElementById("ghostUploader");
                                // fixbug: 无法重复上传同一文件
                                if(ghostUploaderDom.value){
                                    ghostUploaderDom.value = null;
                                }
                                // 获取光标所在位置
                                ghostUploaderDom.click();                            
                                this.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                    resizeImage: {
                        displayStyles: {
                            backgroundColor: "black",
                            border: "none",
                            color: "white",
                        },
                        modules: ["Resize", "DisplaySize"],
                    },
                },
            };
        },
        handlePateImage(event){
            const quill = this.$refs[`quillEditor-${this.currentEditorIndex}`].quill;
            var items = (event.clipboardData || event.originalEvent.clipboardData)
                .items;
            for (let index in items) {
                var item = items[index];
                if (item.kind === "file") {
                    event.preventDefault();
                    var file = item.getAsFile();
                    const form = new FormData();
                    form.append("file", file);
                    apiUploadFile(form).then((res) => {
                        quill.insertEmbed(this.insertPosition, "image", res.data.data);
                        quill.setSelection(this.insertPosition + 1);
                    });
                }
            }
        },
        onEditorFocus(index) {
            this.currentEditorIndex = index;
            const quill = this.$refs[`quillEditor-${index}`].quill;
            quill.root.addEventListener("paste", this.handlePateImage);
        },
        uploadImg() {
            let quill = this.currentQuillRef.quill;
            let file = document.getElementById("ghostUploader").files[0];
            const form = new FormData();
            form.append("file", file);
            apiUploadFile(form).then((res) => {
                quill.insertEmbed(this.insertPosition, "image", res.data.data);
                quill.setSelection(this.insertPosition + 1);
            });
        },
        deleteCell(scope) {
            let index = scope.$index;
            this.tableData.splice(index, 1);
        },
        addRow(scope) {
            if (scope) {
                let index = scope.$index;
                this.tableData.splice(index + 1, 0, {
                    ...this.addRowItem
                });
            } else {
                this.tableData.unshift({
                    ...this.addRowItem
                });
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.table-editor :deep(){
    .ql-container{
        min-height: 100px;
        max-height: 200px;
        overflow: auto;
    }
}
.hover-icon {
    display: none;
    position: absolute;
    right: 5px;
}
.hover-icon.add {
    bottom: 5px;
}
.hover-icon.delete {
    top: 5px;
}

.el-table__row:hover .hover-icon {
    display: block;
}
.el-table__row:hover .tail-input{
    width: calc(100% - 20px)!important;
}
</style>
