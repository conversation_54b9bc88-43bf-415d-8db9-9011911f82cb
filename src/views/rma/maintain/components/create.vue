<template>
    <div class="tool-card flex-row">
        <div class="tool-card-title">
            创建工单
            <el-button
                type="primary"
                style="float: right"
                @click="onAddWorkOrder"
                v-if="p_RmaWorkOrderAdd"
            >
                确认创建
            </el-button>
        </div>
        <el-form
            :model="form"
            :rules="topFormRules"
            label-width="90px"
            ref="topForm"
        >
            <el-row style="margin-left: -18px">
                <el-col :span="6">
                    <el-form-item label="工单号" prop="rmaNumber">
                        <el-input
                            clearable
                            style="width: 100%"
                            v-model="form.rmaNumber"
                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="井号">
                        <FuzzySelect type="WELL_NUMBER" v-model="form.wellNumber"></FuzzySelect>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业编号">
                        <el-input
                            clearable
                            style="width: 100%"
                            v-model="form.jobNumber"
                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="接收日期">
                        <el-date-picker
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="workOrderRmaDetail.receiveDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="tool-card-title">部件信息</div>
            <el-form
                :model="workOrderRmaDetail"
                label-width="90px"
                style="padding-top: 10px; margin-left: -18px"
                :rules="bottomFormRules"
                :validate-on-rule-change="false"
                ref="bottomForm"
            >
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item
                            prop="invName"
                            label="品名"
                            size="normal"
                        >
                            <FuzzySelect @change="onInvNameChange" type="MWD_INVNAME" v-model="workOrderRmaDetail.invName"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item
                            label="序列号"
                            size="normal"
                            prop="serialNumber"
                        >
                            <FuzzySelect :restParams="{invName: workOrderRmaDetail.invName}" v-model="workOrderRmaDetail.serialNumber" type="CORE_SN"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="固件版本号" prop="versionNumber" label-width="82px">
                            <el-input
                                v-model="workOrderRmaDetail.versionNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系人" size="normal">
                            <el-input
                                v-model="workOrderRmaDetail.contactUser"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系方式" size="normal">
                            <el-input
                                v-model="workOrderRmaDetail.contactNumber"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="6">
                        <el-form-item label="本次入井时间" label-width="110px">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderRmaDetail.currentInWellHour"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="总入井时间" label-width="85px">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderRmaDetail.totalInWellHrs"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="本次最高温度" label-width="110px">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderRmaDetail.currentMaxBht"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="总最高温度" label-width="110px">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderRmaDetail.maxBht"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="返回原因" size="normal">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                v-model="workOrderRmaDetail.returnReason"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="备注">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 6 }"
                                v-model="workOrderRmaDetail.notes"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-form>
    </div>
</template>
<script>
import { apiCheckSerialNumber, apiGetFuzzySerialNumberList } from "@/api/tool-mantain";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { apiAddRmaWorkOrder } from "@/api/rma";
import { RmaWorkOrderDataByCoreFormKey } from "@/utils/constant.rma";
export default {
    name: "RmaCreate",
    components:{ FuzzySelect },
    data() {
        return {
            initList: [],
            form: {
                rmaNumber: "",
                wellNumber: null,
                moDocNo: "",
                jobNumber: "",
                kitNumber: "",
                startDate: "",
                endDate: "",
            },
            workOrderRmaDetail: {
                serialNumber: null,
                invName: null,
                owner: null,
                contactUser: null,
                contactNumber: null,
                inWellHour: null,
                circulateHrs: null,
                failure: null,
                failureType: null,
                repairLevel: null,
                repairUser: null,
                checkedUser: null,
                approveUser: null,
                run: null,
                maxBht: null,
                laborHours: null,
                customerName: null,
                repairStartDate: null,
                repairEndDate: null,
                returnReason: null,
                rootReason: null,
                repairAction: null,
                notes: null,
            },
            topFormRules: {
                rmaNumber: [
                    {
                        required: true,
                        message: "请填写工单号！",
                        trigger: "blur",
                    },
                ],
            },
            
            bottomFormRules: {
                invName: [
                    {
                        required: true,
                        message: "请填写部件名称！",
                        trigger: "change",
                    },
                ],
                serialNumber: [
                    {
                        required: true,
                        message: "请填写部件序列号！",
                        trigger: "change",
                    },
                ],
            },
        }
    },
    computed: {
        p_RmaWorkOrderAdd(){
            return this.$checkBtnPermission('sys:rma:workorder:add')
        },
    },
    mounted(){
        const isFromCore = this.$route.query.core === 'true';
        if(isFromCore) {
            const formStr = sessionStorage.getItem(RmaWorkOrderDataByCoreFormKey);
            console.log(formStr)
            if(formStr){
                const form = JSON.parse(formStr);
                this.workOrderRmaDetail = form.workOrderRmaDetail;
                delete form.workOrderRmaDetail;
                this.form = {...form};
                this.form.wellNumber = form.wellNumber;
                this.form.rmaNumber = form.rmaNumber;
            }
        }
    },
    methods: {
        onInvNameChange(){
            this.workOrderRmaDetail.serialNumber = null;
        },
        async onAddWorkOrder() {
            const valid1 = await this.$refs.topForm
                .validate()
                .catch(() => {
                    return Promise.resolve(false);
                });
            const valid2 = await this.$refs.bottomForm
                .validate()
                .catch(() => {
                    return Promise.resolve(false);
                });
            if (!valid1 || !valid2) {
                return;
            }
            
            let valid = true;
            if (!valid) {
                this.$confirm("当前序列号无效，还要继续创建吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                }).then(() => {
                    this.onCreateOrder()
                })
            }else{
                this.onCreateOrder()
            }
        },
        onCreateOrder(){
            this.form.workOrderRmaDetail = this.workOrderRmaDetail;
            apiAddRmaWorkOrder(this.form).then((res) => {
                const rmaId = res.data.data.rmaId;
                this.$message.success("创建成功");
                this.$router.push(`/redirect/rma/maintain?rmaId=${rmaId}`)
            });
        },
        
        querySearchValidSN(query, cb){
            apiGetFuzzySerialNumberList({serialNumber:query}).then(res=>{
                const data = res.data.data || [];
                cb(data.map(item=>({value:item})))
            })
        }
    }
    
}
</script>

<style lang="scss" scoped>
.tool-card.flex-row .el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>