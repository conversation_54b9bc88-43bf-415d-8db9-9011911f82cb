<template>
    <div style="margin-bottom:20px">
        <div class="tool-card-title">维修器件清单</div>
        <div  style="margin-bottom:10px" v-if="isEdit">
            <el-button @click="addRow()" type="primary" style="margin-right:10px">首行插入</el-button>
            
        </div>
        
        <el-table :data="tableData" border>
            <el-table-column prop="invName" label="品名">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invName" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invName}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="invCode" label="品号">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invCode" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invCode}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="invStd" label="规格">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.invStd" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.invStd}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量">
                <template slot-scope="scope">
                    <InputNumber v-model="scope.row.quantity" align="left" v-if="isEdit"></InputNumber>
                    <span v-else>{{scope.row.quantity}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="unitName" label="单位">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.unitName" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.unitName}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width:100%" class="tail-input" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.note}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    name: "RmaExchange",
    props: {
        isEdit: {
            type: Boolean,
            default: false,
        },
        originForm: {
            type: Object,
            default: () => ({
                rmaCheckData: {
                    replaceItemList: [],
                },
            }),
        },
    },
    data() {
        return {
            addRowItem: {serialNumber:'',invName:'',invCode:'',invStd:'',quantity:'',unitName:'',note:''},

        }
    },
    computed: {
        tableData() {
            return this.originForm.rmaCheckData.replaceItemList
        }
    },
    methods: {
        deleteCell(scope) {
            let index = scope.$index;
            this.tableData.splice(index, 1);
        },
        addRow(scope) {
            if (scope) {
                let index = scope.$index;
                this.tableData.splice(index + 1, 0, {
                    ...this.addRowItem,
                });
            } else {
                this.tableData.unshift({
                    ...this.addRowItem,
                });
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
<style lang="scss">
.bom-dialog .el-dialog__body{
    padding-top: 6px
}
</style>
<style lang="scss">
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .code {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .code {
      color: #ddd;
    }
  }
}
</style>