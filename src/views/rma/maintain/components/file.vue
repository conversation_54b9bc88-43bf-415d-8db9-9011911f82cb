<template>
    <div style="margin-top: 20px">
        <div class="tool-card-title" style="position:relative;">
            <span>文件管理</span>
            <el-button 
                v-if="isEdit" 
                style="position:absolute; top:-5px;left:120px" 
                type="text" 
                @click="onClickUpload"
            >
                上传
            </el-button>
        </div> 
        <FileManage ref="fileManageRef" bussiness-type="RMA" :bussiness-id="rmaId" :mode="isEdit?'EDIT':'READ'" />
    </div>
</template>
<script>
import FileManage from "@/components/FileManage/index.vue"
export default {
    name: "RmaFile",
    components: { FileManage },
    data() {
        return {
            fileList: [],
            rmaId: ""
        }
    },
    props: {
        isEdit: {
            type: Boolean,
            default: false,
        },
    },
    mounted(){
        this.rmaId = this.$route.query.rmaId || "";
    },
    methods: {
        onClickUpload(){
            this.$refs.fileManageRef.onUpload();
        }
    }
}

</script>
<style lang="scss" scoped>
.file-list{
    
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 380px;
        margin-bottom: 10px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>