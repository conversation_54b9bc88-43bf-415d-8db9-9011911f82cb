<template>
    <div class="flex-row">
        <div class="tool-card-title">基本信息</div>
        <el-form :model="detailForm" ref="detailForm" label-width="70px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="维修品名" prop="serialNumber">
                        <el-input
                            disabled
                            v-model="detailForm.invName" 
                            style="width:100%"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修仪器" prop="serialNumber">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.serialNumber" 
                            style="width:100%"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="固件版本号" prop="versionNumber" label-width="82px">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.versionNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="升级固件版本号" prop="updatedVersionNumber" label-width="120px">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.versionNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修类型" prop="repairType">
                        <el-select
                            v-model="detailForm.repairType"
                            :disabled="!isEdit"
                            style="width: 100%;"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in repairTypeList"
                                :value="item.value"
                                :label="item.label"
                                :key="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                
                <el-col :span="6">
                    <el-form-item label="作业号">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="本次入井时间" label-width="100px">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.currentInWellHour"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总入井时间" label-width="90px">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.totalInWellHrs"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="本次最高温度" label-width="100px">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.currentMaxBht"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总最高温度" label-width="90px">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.maxBht"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人" label-width="82px">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="井号">
                        <el-input
                            :disabled="!isEdit"
                            v-model="originForm.wellNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="接收日期">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.receiveDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="开始维修日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.startDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="预计完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.estimatedEndDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="实际完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.endDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修人员">
                        <el-input
                            disabled
                            v-model="detailForm.repairUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修耗时">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            disabled
                            v-model="detailForm.laborHours"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修状态">
                        <el-select
                            disabled
                            v-model="originForm.finish"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option label="完成" :value="1"></el-option>
                            <el-option label="未完成" :value="0"></el-option>
                            <el-option label="滞留" :value="-1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="返回原因">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.returnReason"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="备注">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.notes"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script>
import { repairTypeList } from '@/utils/constant.rma';
export default {
    name: "rma-base",
    props: {
        isEdit: {
            type: Boolean,
            default: false,
        },
        originForm: {
            type: Object,
            default: () => ({ workOrderRmaDetail: {} }),
        },
    },
    computed: {
        detailForm() {
            return this.originForm.workOrderRmaDetail || {};
        }
    },
    data(){
        return {
            repairTypeList
        }
    }
}
</script>
<style lang="scss" scoped>
.flex-row .el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>
