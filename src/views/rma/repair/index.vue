<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">返修单</div>
            </div>
            
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
                style="margin-top: 10px;"
            >
                <el-table-column
                    label="RMA返修单号"
                    prop="repairCode"
                    fixed="left"
                    width="200"
                ></el-table-column>
                <el-table-column
                    label="品名"
                    prop="invName"
                    align="center"
                    fixed="left"
                    width="140"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="返修人"
                    prop="sendUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="返修时间"
                    prop="sendDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="接收状态"
                    prop="received"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-tag
                            v-if="scope.row.received == 1"
                            type="success"
                            effect="dark"
                        >
                            已接收
                        </el-tag>
                        <el-tag
                            effect="dark"
                            type="danger"
                            v-else
                        >
                            未接收
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收人"
                    prop="receiveUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receiveDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="MWD工单号"
                    prop="mwdNumber"
                    align="center"
                    width="140"
                >
                    <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                                v-if="p_MwdInfoView"
                            >
                                {{ row.mwdNumber }}
                            </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="本次入井时间"
                    prop="currentInWellHour"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总入井时间"
                    prop="totalInWellHrs"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="本次最高温度"
                    prop="currentMaxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="总最高温度"
                    prop="maxBht"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="返修原因"
                    prop="returnReason"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="备注"
                    prop="notes"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="100"
                    align="center"
                    fixed="right"
                    v-if="p_RmaRepairInfo || p_RmaRepairReceive"
                >
                    <template slot-scope="scope">
                        <el-button type="text" @click="onDetail(scope.row)" >
                            详情
                        </el-button>
                        <el-button type="text" @click="onReceive(scope.row)" :disabled="scope.row.received === 1">
                            接收
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <RepairDialog ref="RepairDialog" />
    </div>
</template>

<script>
import { apiGetRmaRepairList, apiReceiveRmaRepair } from "@/api/rma";
import RepairDialog from "./repairDialog.vue";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    name: 'rma-repair',
    components: { RepairDialog },
    data(){
        return {
            orderBy: "createTime",
            orderType: "desc",
            isLoading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {},
        };
    },
    computed: {
        p_RmaRepairInfo(){
            return this.$checkBtnPermission('sys:rma:repair:info')
        },
        p_RmaRepairReceive(){
            return this.$checkBtnPermission('sys:rma:repair:receive')
        },
        p_MwdInfoView(){
            return this.$checkBtnPermission('sys:mwd:info');
        }
    },
    async mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.getRepairList();
    },
    methods: {
        getRepairList() {
            apiGetRmaRepairList(
                {
                    ...this.searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },
        onReceive(row){
            this.$confirm("确认接收这个返修单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiReceiveRmaRepair({repairId: row.repairId}).then(()=>{
                    this.$message.success("操作成功！");
                    this.getRepairList();
                })
            })
        },
        async onDetail(row) {
            this.$router.push(`/rma/repair/detail?id=${row.repairId}`);
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.getRepairList();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.getRepairList();
        }
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
