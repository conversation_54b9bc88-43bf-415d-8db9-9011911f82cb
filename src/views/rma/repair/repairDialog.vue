<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="rma-repair-dialog"
        :close-on-click-modal="false"
        :title="dialogTitle"
        width="80%"
        top="6vh"
    >
        
        <el-form ref="detailForm" :model="detailForm" label-width="80px" style="margin-top: -20px">
            <div class="rma-item-container" style="margin-bottom: 10px">
                <div class="item-title">
                    基础信息
                </div>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="井号: ">
                            <FuzzySelect :disabled="!isEdit" placeholder="" type="WELL_NUMBER" v-model="detailForm.wellNumber"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业号: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.jobNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="趟次: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.run"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系人: " label-width="100px">
                            <el-input :disabled="!isEdit" v-model="detailForm.contactUser"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="部件品名: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.invName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="部件序列号: " label-width="100px">
                            <el-input :disabled="!isEdit" v-model="detailForm.serialNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="固件版本号: " label-width="100px">
                            <el-input :disabled="!isEdit" v-model="detailForm.versionNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系方式: " label-width="110px">
                            <el-input :disabled="!isEdit" v-model="detailForm.contactNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="母件品名: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.parentInvName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="母件序列号: " label-width="100px">
                            <el-input :disabled="!isEdit" v-model="detailForm.parentSerialNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="检修人: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.repairUser"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入井时间: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.inWellHour"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="循环时间: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.circulateHrs"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="最高温度: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.maxBht"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="循环温度: ">
                            <el-input :disabled="!isEdit" v-model="detailForm.circulateBht"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="现场返修原因: " label-width="110px">
                            <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.returnReason"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="备注: ">
                            <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.notes"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="rma-item-container">
                <div class="item-title">
                    功能检查
                </div>
                <el-row>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            常温功能检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.normalTemperatureFunctionCheck" :class="getCheckClass(detailForm.normalTemperatureFunctionCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            高温测试
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.highTemperatureTest" :class="getCheckClass(detailForm.highTemperatureTest)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            振动测试
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.vibrationTest" :class="getCheckClass(detailForm.vibrationTest)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            旋转测试
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.rotationTest" :class="getCheckClass(detailForm.rotationTest)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            短传性能测试
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.shortTransmissionPerformanceTest" :class="getCheckClass(detailForm.shortTransmissionPerformanceTest)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            LOG数据存储测试
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.logDataStorageTest" :class="getCheckClass(detailForm.logDataStorageTest)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                </el-row>
            </div>
            
            <div class="rma-item-container">
                <div class="item-title">
                    机械检查
                </div>
                <el-row>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            绝缘检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.insulationCheck" :class="getCheckClass(detailForm.insulationCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            本体检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.bodyCheck" :class="getCheckClass(detailForm.bodyCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            盖板检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.coverCheck" :class="getCheckClass(detailForm.coverCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                </el-row>
            </div>
            <div class="rma-item-container">
                <div class="item-title">
                    电子/线路检查
                </div>
                <el-row>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            本体线路ringout测量
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.bodyLineRingOutMeasurement" :class="getCheckClass(detailForm.bodyLineRingOutMeasurement)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            线路固封情况检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.lineSealingConditionCheck" :class="getCheckClass(detailForm.lineSealingConditionCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            接头状态检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.connectorStatusCheck" :class="getCheckClass(detailForm.connectorStatusCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            EC物理状态检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.ecPhysicalStatusCheck" :class="getCheckClass(detailForm.ecPhysicalStatusCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            伽马探头物理状态检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.gammaProbePhysicalStatusCheck" :class="getCheckClass(detailForm.gammaProbePhysicalStatusCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="4">
                        <div class="sub-item-title">
                            电池物理状态检查
                        </div>
                        <el-select :disabled="!isEdit" v-model="detailForm.batteryPhysicalStatusCheck" :class="getCheckClass(detailForm.batteryPhysicalStatusCheck)" class="check-select" size="mini">
                            <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                    </el-col>
                </el-row>
            </div>
            <div class="rma-item-container">
                <div class="item-title">
                    车间诊断初步结论
                </div>
                <el-form-item label-width="0">
                    <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.workshopPreliminaryDiagnosisConclusion"></el-input>
                </el-form-item>
            </div>
            <div class="rma-item-container" v-if="isRma">
                <div class="item-title">
                    附件
                </div>
                <FileManage v-if="detailDialogVisible" empty-text="暂无附件" :mode="isEdit?'EDIT':'READ'" ref="fileManageRef" bussinessType="RMA_REPAIR" :bussinessId="repairId" />
            </div>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <template v-if="isRma">
                <el-button @click="detailDialogVisible = false">
                    返 回
                </el-button>
                <el-button type="primary" @click="onReceive" :loading="isBtnLoading" v-if="p_RmaRepairReceive && detailForm.received !== 1">
                    <span>接 收</span> 
                </el-button>
                <el-button type="primary" @click="onCreateRmaWorkOrder" :loading="isBtnLoading" v-if="p_RmaWorkOrderAdd && detailForm.received === 1">
                    <span>创建RMA维修工单</span> 
                </el-button>
            </template>
            <template v-else-if="isAdd">
                <el-button @click="detailDialogVisible = false">
                    取 消
                </el-button>
                <el-button type="primary" @click="onConfirmRepairForm" :loading="isBtnLoading">
                    <span>确认创建并发送邮件</span> 
                </el-button>
            </template>
            <template v-else>
                <template v-if="!isEdit">
                    <el-button type="primary" v-if="detailForm.received!==1 && p_RmaRepairUpdate" @click="isEdit=true">
                        编辑
                    </el-button>
                    <el-button @click="detailDialogVisible = false">
                        返 回
                    </el-button>
                </template>
                <template v-else>
                    <el-button @click="onCancelEdit">
                        取消编辑
                    </el-button>
                    <el-button type="primary" @click="onConfirmRepairForm" :loading="isBtnLoading">
                        保存
                    </el-button>
                </template>
            </template>
        </span>
        <EmailDialog @onConfirm="onConfirmSend" email-business-type="RMA_REPAIR" ref="emailDialogRef"></EmailDialog>
    </el-dialog>
</template>
<script>
import { apiGetRmaRepairInfo, apiUpdateRmaRepair, apiAddRmaRepair, apiReceiveRmaRepair, apiCreateRmaWorkOrderByRepair } from "@/api/rma";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import FileManage from "@/components/FileManage/index.vue";
import EmailDialog from "@/components/EmailDialog/index.vue";
const defaultCheck = 0
export default {
    components: { FuzzySelect, FileManage, EmailDialog },
    data(){
        return {
            repairId: null,
            isBtnLoading: false,
            detailDialogVisible : false,
            detailForm: {},
            selectedRepairDetail: [],
            dialogType: 'ADD',
            isEdit: false,
            checkList: [
                {value: -1, label: 'FAIL', class: 'FAIL'},
                {value: 0, label: 'N/A', class: 'NA'},
                {value: 1, label: 'PASS', class: 'PASS'},
            ]
        }
    },
    computed: {
        isRma(){
            return this.dialogType==='RMA'
        },
        isAdd(){
            return this.dialogType==='ADD'
        },
        dialogTitle(){
            return this.isAdd ? '新增返修单' : '返修单详情'
        },
        p_RmaWorkOrderAdd(){
            return this.$checkBtnPermission('sys:rma:workorder:add')
        },
        p_RmaRepairUpdate(){
            return this.$checkBtnPermission('sys:mwd:core:repair:update')
        },
        p_RmaRepairReceive(){
            return this.$checkBtnPermission('sys:rma:repair:receive')
        }
    },
    methods: {
        getRepairInfo(){
            return apiGetRmaRepairInfo({repairId: this.repairId}).then(res=>{
                this.detailForm = res.data.data || {};
            })
        },
        onCancelEdit(){
            this.isEdit = false;
            this.getRepairInfo();
        },
        getCheckClass(value){
            return this.checkList.find(item=>item.value === value)?.label || ''
        },
        async showDialog(type, data) {
            this.dialogType = type;
            this.isEdit = false;
            if(type==="ADD"){
                this.repairId = null;
                this.detailForm = {
                    normalTemperatureFunctionCheck: defaultCheck,
                    highTemperatureTest: defaultCheck,
                    vibrationTest: defaultCheck,
                    rotationTest: defaultCheck,
                    shortTransmissionPerformanceTest: defaultCheck,
                    logDataStorageTest: defaultCheck,
                    insulationCheck: defaultCheck,
                    bodyCheck: defaultCheck,
                    coverCheck: defaultCheck,
                    bodyLineRingOutMeasurement: defaultCheck,
                    lineSealingConditionCheck: defaultCheck,
                    connectorStatusCheck: defaultCheck,
                    ecPhysicalStatusCheck: defaultCheck,
                    gammaProbePhysicalStatusCheck: defaultCheck,
                    batteryPhysicalStatusCheck: defaultCheck,
                    ...(data||{})
                };
                this.isEdit = true;
            }else{
                this.repairId = data.repairId;
                this.isEdit = false;
                await this.getRepairInfo();
            }
            this.detailDialogVisible = true;
        },
        onConfirmRepairForm() {
            if(this.isAdd){
                this.onOpenEmailDialog()
            }else{
                this.isBtnLoading = true;
                apiUpdateRmaRepair(this.detailForm).then(res=>{
                    this.$message.success('操作成功!');
                    this.getRepairInfo();
                    this.$emit('getRepairList');
                    this.isEdit = false;
                }).finally(()=>{
                    this.isBtnLoading = false;
                });
            }
        },
        onReceive(){
            this.$confirm("确认接收这个返修单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiReceiveRmaRepair({repairId: this.repairId}).then(()=>{
                    this.$message.success("操作成功！");
                    this.getRepairInfo();
                })
            })
        },
        onCreateRmaWorkOrder(){
            this.isBtnLoading = true;
            apiCreateRmaWorkOrderByRepair({repairId: this.repairId}).then(res=>{
                const rmaId = res?.data?.data?.rmaId;
                if(rmaId){
                    this.$message.success('操作成功！');
                    this.$router.push(`/rma/maintain?rmaId=${rmaId}`)
                }
            })
        },
        onConfirmSend(emaiForm){
            this.isBtnLoading = true;
            const form = { ...this.detailForm, emailTemplate: emaiForm }
            apiAddRmaRepair(form).then(res=>{
                this.$message.success('操作成功!');
                this.$emit('getRepairList');
                this.detailDialogVisible = false;
            }).finally(()=>{
                this.isBtnLoading = false;
            });
        },
        onOpenEmailDialog(){
            this.$refs.emailDialogRef.showDialog()
        }
    }
}
</script>

<style lang="scss" scoped>
.rma-repair-dialog .el-dialog__body{
    padding-top: 10px !important
}
.rma-item-container{
    margin-bottom: 26px;
    .item-title{
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 18px;
        &::before {
            display: inline-block;
            width:6px;
            height: 20px;
            vertical-align: sub;
            background: rgb(67, 86, 152);
            margin-right: 6px;
            content: "";
        }
    }
    .sub-item-title{
        font-size: 14px;
        // font-weight: bold;
        margin-left: 18px;
        margin-bottom: 8px;
        // &::before {
        //     display: inline-block;
        //     width: 10px;
        //     height: 10px;
        //     background: rgb(67, 86, 152);
        //     margin-right: 6px;
        //     margin-left: -2px;
        //     border-radius: 999999%;
        //     content: "";
        // }
    }
    .check-select {
        width: 100px;
        margin-left: 16px;
        &.PASS :deep() .el-input__inner{
            background: #67c23a;
            color: white;
        }
        &.NA :deep() .el-input__inner{

        }
        &.FAIL :deep() .el-input__inner{
            background: #ff4949;
            color: white;
        }

    }
}

</style>