<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isPageLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">核心部件返修单详情</div>
                <div v-if="!isPageLoading">
                    <el-button type="primary" @click="onReceive" key="9527" v-if="p_RmaRepairReceive&&!isReceived">接收</el-button>
                    <el-button type="primary" @click="onCreateRmaOrder" key="5173" v-if="p_RmaWorkOrderAdd&&isReceived&!hasOrderCreated">创建RMA工单</el-button>
                    <el-button type="primary" @click="onViewRmaOrder" key="8848" v-if="isReceived&&hasOrderCreated">查看RMA工单</el-button>
                </div>
            </div>
            <el-form ref="detailForm" :rules="formRules" :model="detailForm" label-width="80px" style="margin-top: 10px">
                <div class="rma-item-container" style="margin-bottom: 10px">
                    <div class="item-title">
                        基础信息
                    </div>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="RMA返修单号: " prop="repairCode" label-width="120px">
                                <el-input :disabled="!isAdd" v-model="detailForm.repairCode"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="井号: ">
                                <FuzzySelect :disabled="!isEdit" placeholder="" type="WELL_NUMBER" v-model="detailForm.wellNumber"></FuzzySelect>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业号: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.jobNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="现场联系人: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.contactUser"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="部件品名: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.invName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="部件序列号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.serialNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="固件版本号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.versionNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="现场联系方式: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.contactNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="母件品名: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.parentInvName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="母件序列号: " label-width="100px">
                                <el-input :disabled="!isEdit" v-model="detailForm.parentSerialNumber"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="检修人: ">
                                <el-input :disabled="!isEdit" v-model="detailForm.repairUser"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="总入井时间: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.totalInWellHrs"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="本次入井时间: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.currentInWellHour"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="本次最高温度: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.currentMaxBht"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="总最高温度: " label-width="110px">
                                <el-input :disabled="!isEdit" v-model="detailForm.maxBht"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="现场返修原因: " label-width="110px">
                                <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.returnReason"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="备注: ">
                                <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.notes"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        功能检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                常温功能检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.normalTemperatureFunctionCheck" :class="getCheckClass(detailForm.normalTemperatureFunctionCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                高温测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.highTemperatureTest" :class="getCheckClass(detailForm.highTemperatureTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                振动测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.vibrationTest" :class="getCheckClass(detailForm.vibrationTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                旋转测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.rotationTest" :class="getCheckClass(detailForm.rotationTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                短传性能测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.shortTransmissionPerformanceTest" :class="getCheckClass(detailForm.shortTransmissionPerformanceTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                LOG数据存储测试
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.logDataStorageTest" :class="getCheckClass(detailForm.logDataStorageTest)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                
                <div class="rma-item-container">
                    <div class="item-title">
                        机械检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                绝缘检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.insulationCheck" :class="getCheckClass(detailForm.insulationCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                本体检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.bodyCheck" :class="getCheckClass(detailForm.bodyCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                盖板检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.coverCheck" :class="getCheckClass(detailForm.coverCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        电子/线路检查
                    </div>
                    <el-row>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                本体线路ringout测量
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.bodyLineRingOutMeasurement" :class="getCheckClass(detailForm.bodyLineRingOutMeasurement)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                线路固封情况检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.lineSealingConditionCheck" :class="getCheckClass(detailForm.lineSealingConditionCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                接头状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.connectorStatusCheck" :class="getCheckClass(detailForm.connectorStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                EC物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.ecPhysicalStatusCheck" :class="getCheckClass(detailForm.ecPhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                伽马探头物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.gammaProbePhysicalStatusCheck" :class="getCheckClass(detailForm.gammaProbePhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <div class="sub-item-title">
                                电池物理状态检查
                            </div>
                            <el-select :disabled="!isEdit" v-model="detailForm.batteryPhysicalStatusCheck" :class="getCheckClass(detailForm.batteryPhysicalStatusCheck)" class="check-select" size="mini">
                                <el-option v-for="item in checkList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        车间诊断初步结论
                    </div>
                    <el-form-item label-width="0">
                        <el-input type="textarea" :disabled="!isEdit" v-model="detailForm.workshopPreliminaryDiagnosisConclusion"></el-input>
                    </el-form-item>
                </div>
                <div class="rma-item-container">
                    <div class="item-title">
                        附件
                    </div>
                    <div class="rma-item-container">
                        <FileManage empty-text="暂无附件" mode="READ" ref="fileManageRef" bussinessType="RMA_REPAIR" :bussinessId="repairId" />
                    </div>
                </div>
            </el-form>
        </div>
    </div>
</template>
<script>
import { apiGetRmaRepairInfo, apiReceiveRmaRepair, apiCreateRmaWorkOrderByRepair } from "@/api/rma";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import FileManage from "@/components/FileManage/index.vue";
export default {
    components: { FuzzySelect, FileManage },
    data(){
        return {
            repairId: null,
            isBtnLoading: false,
            detailForm: {},
            isEdit: false,
            checkList: [
                {value: -1, label: 'FAIL', class: 'FAIL'},
                {value: 0, label: 'N/A', class: 'NA'},
                {value: 1, label: 'PASS', class: 'PASS'},
            ],
            formRules: {
                repairCode: [{required: true, message: '请填写返修单号'}]
            },
            isPageLoading: false
        }
    },
    computed: {
        isAdd(){
            return !this.repairId
        },
        p_RmaRepairReceive(){
            return this.$checkBtnPermission('sys:rma:repair:receive')
        },
        p_RmaWorkOrderAdd(){
            return this.$checkBtnPermission('sys:rma:workorder:add')
        },
        isReceived(){
            return this.detailForm.received === 1
        },
        hasOrderCreated(){
            return !!this.detailForm.rmaId;
        },
    },
    mounted(){
        this.repairId = this.$route.query.id;
        if(this.repairId){
            this.getRepairInfo();
        }
    },
    methods: {
        getRepairInfo(){
            return new Promise((resolve)=>{
                this.isPageLoading = true;
                apiGetRmaRepairInfo({repairId: this.repairId, needExcel: false}).then(res=>{
                    this.detailForm = res.data.data || {};
                    resolve(this.detailForm);
                }).finally(()=>{
                    this.isPageLoading = false;
                })
            })
        },
        getCheckClass(value){
            return this.checkList.find(item=>item.value === value)?.label || ''
        },
        onReceive(){
            this.$confirm("确认接收这个返修单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiReceiveRmaRepair({repairId: this.repairId}).then(()=>{
                    this.$message.success("操作成功！");
                    this.getRepairInfo();
                })
            })
        },
        onViewRmaOrder(){
            this.$router.push(`/rma/maintain?rmaId=${this.detailForm.rmaId}`)
        },
        onCreateRmaOrder(){
            this.$confirm("确认创建RMA工单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiCreateRmaWorkOrderByRepair({repairId: this.repairId}).then(res=>{
                    const rmaId = res?.data?.data?.rmaId;
                    if(rmaId){
                        this.$message.success('操作成功！');
                        this.$router.push(`/rma/maintain?rmaId=${rmaId}`)
                    }
                })
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.rma-repair-dialog .el-dialog__body{
    padding-top: 10px !important
}
.rma-item-container{
    margin-bottom: 26px;
    .item-title{
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 18px;
        &::before {
            display: inline-block;
            width:6px;
            height: 20px;
            vertical-align: sub;
            background: rgb(67, 86, 152);
            margin-right: 6px;
            content: "";
        }
    }
    .sub-item-title{
        font-size: 14px;
        // font-weight: bold;
        margin-left: 18px;
        margin-bottom: 8px;
        // &::before {
        //     display: inline-block;
        //     width: 10px;
        //     height: 10px;
        //     background: rgb(67, 86, 152);
        //     margin-right: 6px;
        //     margin-left: -2px;
        //     border-radius: 999999%;
        //     content: "";
        // }
    }
    .check-select {
        width: 100px;
        margin-left: 16px;
        &.PASS :deep() .el-input__inner{
            background: #67c23a;
            color: white;
        }
        &.NA :deep() .el-input__inner{

        }
        &.FAIL :deep() .el-input__inner{
            background: #ff4949;
            color: white;
        }

    }
}

</style>