<template>
    <div style="width: 100%; height: 100%;">
        <div class="title">
            电池总成
        </div>
        <div id="chart1" style="width: 100%;height: calc(60%);"></div>
        
        <el-scrollbar wrap-class="home-scrollbar-wrapper" style="height: calc(40%);overflow-x: hidden;">
            <div
                style="font-size: 14px;display: flex;justify-content: space-around;margin-bottom: 6px;padding: 1px 4px;"
                v-for="(item,index) in series"
                :key="item.status"
                :style="{'background': colors[index]}"
            >
                <div class="label" style="width: 60px;">
                    {{ processStatus(item.name) }}
                </div>
                <div v-for="(count,idx) in item.data" :key="item.status + '' + idx" class="value" style="text-align: center; flex: 1;">
                    {{ count }}
                </div>
            </div>
        </el-scrollbar>
    </div>
</template>
<script>
import * as echarts from "echarts";
import { apiGetWarehouseDeviceStats } from "@/api/warehouse";
import getDict from "@/utils/getDict";
export default {
    data(){
        return {
            series: [],
            colors: []
        }
    },
    async mounted(){
        [this.statusList] = await getDict([20])
        this.initChart();
        window.addEventListener("resize", this.resize);
    },
    methods: {
        resize(){
            this.myChart && this.myChart.resize();
        },
        processStatus(status) {
            if (status.includes(" ") && status !== 'At Rig') {
                // 使用空格分割字符串
                const words = status.split(' ');
                const result = words.map(word => word.charAt(0)).join('');
                return result;
            } else {
                return status;
            }
        },
        getData(){
            return apiGetWarehouseDeviceStats({
                stockType: 20009,
                conditionFieldNameList: ["specification"],
                counterFieldName: "status",
                counterValueList: this.statusList.map(item=>item.id)
            }).then(res=>{
                return res?.data?.data || [];
            })
        },
        async initChart(){
            const data = await this.getData();
            const xData = data.map(item => item[0]);
            this.series = [];
            this.statusList.forEach((item, index)=>{
                const nums = data.map(d=>d[index+1]);
                if(nums.every(num=>num=='0')){
                    return;
                }
                this.series.push({
                    name: item.name,
                    type: 'bar',
                    stack: 'Ad',
                    data: data.map(d=>d[index+1]),
                    barWidth: 10
                })
            });
            this.myChart = echarts.init(document.getElementById('chart1'));
            let option = {
                title: {
                    show: false
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    left: 100,
                    top: 2
                },
                grid: {
                    bottom: 50,
                    right: 10,
                    left: 30,
                },
                xAxis: [
                    {
                        type: 'category',
                        data: xData,
                        axisLabel: {
                            interval: 0, // 强制显示所有标签
                            formatter: function (value) {
                                // 每 5 个字符换行
                                return value.replace(/(.{6})/g, '$1\n');
                            }
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        splitNumber: 4
                    }
                ],
                series: this.series
            };
            this.myChart.setOption(option);
            let renderSeries = this.myChart.getOption().series;
            this.colors = [];
            renderSeries.forEach((s, index) => {
                let color = s.itemStyle ? s.itemStyle.color : this.myChart.getModel().get('color')[index % this.myChart.getModel().get('color').length];
                this.colors.push(color);
            });
        }
    },
    beforeDestroy(){
        window.removeEventListener("resize", this.resize);
    }
}
</script>