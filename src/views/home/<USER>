<template>
    <div style="width: 100%; height: 100%;">
        <div class="title" style="color: #004F84;width: 100%;">
            {{ owner === 'Tartan' ? '达坦' : '客户' }}
            
            <span style="float: right;margin-right: 26px; color: #BEBEBF;font-size: 15px;user-select: none;">
                <span class="status" :class="{checked: status===19003}" @click="onClickStatus(19003)" >Ready</span>
                <span> | </span>
                <span class="status" :class="{checked: status===19001}" @click="onClickStatus(19001)">At Rig</span>
            </span>
        </div>
        <div class="assets-table-container" style="margin-top: 30px;height: calc(100%);">
            <div class="list-header" style="display: flex;">
                <span style="width: 110px;text-align: center;">仪器类型</span>
                <span style="flex: 1;text-align: center;">生产序列</span>
                <span style="flex: 1;text-align: center;">风险序列</span>
                <span style="flex: 1;text-align: center;">实验序列</span>
            </div>
            <el-scrollbar wrap-class="home-scrollbar-wrapper" class="list-content" style="height: calc(100% - 70px);">
                <div v-for="item in data" :key="item.deviceTypeStr" style="display: flex;padding: 6px 0;">
                    <span style="width: 110px;text-align: center;">{{ item.deviceTypeStr }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.produce }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.risk }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.test }}</span>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>
<script>
export default {
    name: "HOME_ASSETS2",
    props: {
        owner: {
            default: () => 'Tartan'
        },
        status: {
            default: () => 19003
        },
        data: {
            default: () => []
        }
    },
    methods: {
        onClickStatus(value) {
            this.$emit('statusChange', value)
        }
    }
}
</script>
<style lang="scss" scoped>
    .status{
        cursor: pointer;
        &.checked{
            color: #004F84;
        }
    }
</style>
