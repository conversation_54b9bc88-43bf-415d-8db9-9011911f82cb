<template>
    <div style="width: 100%; height: 100%;">
        <div class="title" style="width: 100%;">
            资产统计
            <span style="float: right;margin-right: 26px; color: #BEBEBF;font-size: 15px;user-select: none;">
                <span class="owner" :class="{checked: owner==='Tartan'}" @click="onClickOwner('Tartan')" >达坦</span>
                <span> | </span>
                <span class="owner" :class="{checked: owner==='Client'}" @click="onClickOwner('Client')">客户</span>
            </span>
        </div>
        <div class="assets-table-container" style="margin-top: 30px;height: calc(100%);">
            <div class="list-header">
                <span style="width: 110px;text-align: center;">仪器类型</span>
                <span style="flex: 1;text-align: center;">滞留</span>
                <span style="flex: 1;text-align: center;">上井中</span>
                <span style="flex: 1;text-align: center;">车间可用</span>
                <span style="flex: 1;text-align: center;">维修中</span>
                <span style="flex: 1;text-align: center;">利用率</span>
            </div>
            <el-scrollbar wrap-class="home-scrollbar-wrapper" class="list-content" style="height: calc(100% - 70px);">
                <div v-for="item in data" :key="item.deviceTypeStr" style="display: flex;padding: 4px 0;">
                    <span style="width: 110px;text-align: center;">{{ item.deviceTypeStr }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.pending }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.atRig }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.ready }}</span>
                    <span style="flex: 1;text-align: center;">{{ item.repair }}</span>
                    <span style="flex: 1;text-align: center;">
                        <div style="width: 100%;height: 16px;background-color: #e6ebf5;position: relative;display: inline-block;">
                            <div  style="height: 100%; width: 0; background-color: #A3C6FB;transition: width 0.5s;" :style="`width:${item.usageRate}`">
                            </div>
                            <span style="position: absolute;right: 4px;top:0px;color: #353535;font-size: 12px;">{{ item.usageRate }}</span>
                        </div>
                    </span>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>
<script>
export default {
    name: "HOME_ASSETS1",
    props: {
        owner: {
            default: () => 'Tartan'
        },
        data: {
            default: () => []
        },
    },
    methods: {
        onClickOwner(value) {
            this.$emit('ownerChange', value)
        }
    }
}
</script>
<style lang="scss" scoped>
    .owner{
        cursor: pointer;
        &.checked{
            color: #004F84;
        }
    }
</style>