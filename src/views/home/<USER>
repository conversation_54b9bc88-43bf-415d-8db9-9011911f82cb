<template>
    <div class="home-container" style="position: relative;">
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3);height: 40%;bottom: 10px; left: 10px;">
            <Battery />
        </div>
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3);height: 40%;bottom: 10px;left: 50%; transform: translateX(-50%);">
            <Kit />
        </div>
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3);height: 60px;top: 10px;right: 10px;">
            <Available @getData="getAvailableTable" :data="availableTable" />
        </div>
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3);height: calc(60% - 100px);top: 80px;right: 10px;">
            <Assets1 @ownerChange="onOwnerChange" :owner="owner" :data="assetData1" />
        </div>
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3);height: 40%;bottom: 10px;right: 10px;">
            <Assets2 @statusChange="onStatusChange" :owner="owner" :status="status" :data="assetData2" />
        </div>
        <div class="box-container" style="width: calc(calc(100% - 40px) / 3 * 2 + 10px);height: calc(60% - 30px);top: 10px;left: 10px;">
            <MapComponent />
        </div>
    </div>
</template>
<script>
import Battery from './Battery.vue';
import Kit from './Kit.vue';
import Assets1 from './Assets1.vue';
import Assets2 from './Assets2.vue';
import Available from './Available.vue';
import MapComponent from '@/components/MapComponent.vue';
import { apiGetAvailableDeviceCount, apiGetDeviceRiskStats, apiGetDeviceStats } from "@/api/home.js"
export default {
    name: "HOME",
    components: { Battery, Kit, Available, Assets1, Assets2, MapComponent },
    data(){
        return {
            owner: "Tartan",
            status: 19003,
            availableTable: [],
            assetData1: [],
            assetData2: []
        }
    },
    mounted(){
        this.getAvailableTable();
        this.getDeviceRiskStats();
        this.getAssetsStats();
    },
    methods: {
        onOwnerChange(value){
            this.owner = value;
            this.getAssetsStats();
            this.getDeviceRiskStats();
        },
        onStatusChange(value){
            this.status = value;
            this.getDeviceRiskStats();
        },
        getAvailableTable() {
            apiGetAvailableDeviceCount({}).then(res=>{
                this.availableTable = res?.data?.data || [];
            })
        },
        getDeviceRiskStats(){
            apiGetDeviceRiskStats({owner: this.owner, status: this.status}).then(res=>{
                this.assetData2 = res?.data?.data || [];
            })
        },
        getAssetsStats(){
            apiGetDeviceStats({owner: this.owner}).then(res=>{
                this.assetData1 = res?.data?.data || [];
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.home-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: url('./map.png');
  background-size: cover;
  background-position: bottom left;
  .box-container::v-deep {
    position: relative;
    border-radius: 4px;
    padding: 10px;position: absolute;
    background: #fff;
    .title{
        position: absolute;
        display: inline-block;
        top: 15px;
        left: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #333;
    }
    .assets-table-container{
        .list-header{
            display: flex;
            font-size: 15px;
            color: #A9A8AE;
            font-weight: bold;
            margin-bottom: 6px;
            padding: 10px 0;
            border-top: 1px solid #EBEEF5;
            border-bottom: 1px solid #EBEEF5;
            span{
                position: relative;
            }
            span:not(:last-child)::after {
                content: '';
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 1px;
                background-color: #EBEEF5;
            }
        }
        .list-content{
            font-size: 15px;
            font-weight: 400;
            color: #606266;
        }
    }
  }
}
</style>
<style lang="scss">
    .home-scrollbar-wrapper{
        overflow-x: hidden;
    }
    /* 地图组件特殊处理，移除内边距 */
    .box-container .map-component {
        margin: -10px;
        width: calc(100% + 20px);
        height: calc(100% + 20px);
    }
</style>