<template>
    <div style="width: 100%; height: 100%;">
        <div class="title">
            Kit箱
        </div>
        <div style="display: flex;justify-content: end;">
            <el-select size="small" multiple collapse-tags clearable placeholder="筛选条件" v-model="searchForm.conditionFieldNameList" @change="initData" style="width: 160px;">
                <el-option
                    v-for="item in conditionFieldNameList"
                    :disabled="item===searchForm.counterFieldName"
                    :key="item"
                    :label="getLabelByFieldName(item)"
                    :value="item"
                ></el-option>
            </el-select>
            <el-select size="small" placeholder="计数项" v-model="searchForm.counterFieldName" @change="initData" style="margin-left: 20px;width: 110px;">
                <el-option
                    v-for="item in conditionFieldNameList"
                    :disabled="searchForm.conditionFieldNameList.includes(item)"
                    :key="item"
                    :label="getLabelByFieldName(item)"
                    :value="item"
                ></el-option>
            </el-select>
        </div>
        <div id="chart2" style="width: 100%;height: calc(100% - 40px);"></div>
    </div>
</template>
<script>
import * as echarts from "echarts";
import { getLabelByFieldName as getLabelByFieldNameAndPanel } from "@/views/warehouse/stock/options.js";
import { apiGetWarehouseDeviceStats, apiGetWarehouseDeviceStatsCondition } from "@/api/warehouse";
import getDict from "@/utils/getDict";
export default {
    data(){
        return {
            // 三种形式: 原始供遍历 - 表单供提交 - 表格供展示
            conditionFieldNameList: ['invName', 'owner', 'location', 'riskType', 'specification', 'status'],
            counterFieldName: 'owner',
            counterValueList: [],

            searchForm: {
                conditionFieldNameList: ['status', 'specification'],
                counterFieldName: 'owner',
                counterValueList: []
            },

            tableConditionFieldNameList: [], 
            tableCounterFieldName: '',
            tableCounterValueList: [], 

            tableColumnList: [],
            originArray: [],
            riskTypeList: [
                { id: "RISK", name: "风险" },
                { id: "PRODUCE", name: "生产" },
                { id: "TEST", name: "试验" },
                { id: "SCRAP", name: "报废" },
            ],
            deviceStatusList: [],
        }
    },
    async mounted(){
        [this.deviceStatusList] = await getDict([20]);
        this.initData();
        window.addEventListener("resize", this.resize);
    },
    methods: {
        resize(){
            this.myChart && this.myChart.resize();
        },
        async initData(){
            await this.getCondition();
            this.renderData();
        },
        getLabelByFieldName(fieldName){
            return getLabelByFieldNameAndPanel(fieldName, 'kitbox')
        },
        getLabelByFieldValue(filedName, value){
            switch(filedName){
                case 'status': 
                    return this.deviceStatusList.find(item=>item.id==value)?.name || value;
                case 'riskType': 
                    return this.riskTypeList.find(item=>item.id==value)?.name || value;
                default: 
                    return value;
            }
        },
        getCondition(){
            return apiGetWarehouseDeviceStatsCondition({ stockType: 20001, counterFieldName: this.searchForm.counterFieldName }).then(res=>{
                const data = (res.data.data || []).map(item=>{
                    let label;
                    switch(this.searchForm.counterFieldName){
                        case 'status': 
                            label = this.deviceStatusList.find(v=>v.id==item)?.name || item;
                            break;
                        case 'riskType': 
                            label = this.riskTypeList.find(v=>v.id==item)?.name || item;
                            break;
                        default: 
                            label = item;
                    }
                    return {
                        value: item,
                        label: label
                    }
                });
                this.counterValueList = [...data];
                this.searchForm.counterValueList = data.map(item=>item.value);
            })
        },
        cutString(str, num) {
            let length = 0;
            let result = '';

            for (let char of str) {
                // 判断字符是否为中文（Unicode范围：\u4e00-\u9fa5）
                if (/[\u4e00-\u9fa5]/.test(char)) {
                    length += 2;
                } else if (char.trim() !== '') {
                    length += 1;
                } else {
                    length += 0;
                }
                if (length <= num) {
                    result += char;
                } else {
                    result += "...";
                    break;
                }
            }

            return result;
        },
        async renderData(){
            this.tableConditionFieldNameList = [...this.searchForm.conditionFieldNameList];
            this.tableCounterFieldName = this.searchForm.counterFieldName;
            this.tableCounterValueList = [...this.searchForm.counterValueList];
            this.tableColumnList = [...this.tableConditionFieldNameList, ...this.tableCounterValueList];
            const data = await apiGetWarehouseDeviceStats({
                stockType: 20001, 
                conditionFieldNameList: this.tableConditionFieldNameList,
                counterFieldName: this.tableCounterFieldName,
                counterValueList: this.tableCounterValueList,
            });
            // "null" -> ""
            this.originArray = (data.data?.data || []).map(row=>row.map((item,index)=>{
                item = this.getLabelByFieldValue(this.tableColumnList[index], item);
                return item==='null' ? '' : item
            }));
            // sort
            this.originArray.sort((a, b) => {
                for (let i = 0; i < a.length; i++) {
                    if (a[i] !== b[i]) {
                        return a[i].localeCompare(b[i]);
                    }
                }
                return 0;
            });
            const chartData = this.transform(this.originArray, this.tableConditionFieldNameList, this.tableCounterValueList);
            this.myChart = echarts.init(document.getElementById('chart2'));
            let option = {
                title: {
                    show: false
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    show: false
                },
                series: [
                    {
                        name: 'Kit箱',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            alignTo: 'edge',
                            formatter: (data)=>{
                                return `{name|${this.cutString(data.name, 25)}}\n{count|${data.value}}`
                            },
                            minMargin: 5,
                            edgeDistance: 10,
                            lineHeight: 15,
                            rich: {
                                count: {
                                    fontSize: 12,
                                    color: '#999',
                                    padding: [0, 5]
                                },
                                name:{
                                    formatter:(b)=>{
                                        this.cutString(b,5)
                                    }
                                }
                            }
                        },
                        labelLine: {
                            length: 15,
                            length2: 0,
                            maxSurfaceAngle: 80
                        },
                        labelLayout: (params) => {
                            const isLeft = params.labelRect.x < this.myChart.getWidth() / 2;
                            const points = params.labelLinePoints;
                            points[2][0] = isLeft
                                ? params.labelRect.x
                                : params.labelRect.x + params.labelRect.width;
                            return {
                                labelLinePoints: points
                            };
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontWeight: 'bold'
                            }
                        },
                        data: chartData
                    }
                ]
            };
            this.myChart.setOption(option);
        },
        transform(data, conditionList, counterList){
            const ret = [];
            data.forEach(row => {
                const conditions = row.slice(0, conditionList.length).join('-');
                const counters = row.slice(conditionList.length);
                const result = {};
                counters.forEach((value, index) => {
                    const counterName = this.getLabelByFieldValue(this.searchForm.counterFieldName, counterList[index]); // 获取计数项名称
                    result[`${conditions}-${counterName}`] = value;
                    if(value!=0){
                        ret.push({
                            name: `${conditions}-${counterName}`,
                            value
                        })
                    }
                });
            });
            return ret
        },
    },
    beforeDestroy(){
        window.removeEventListener("resize", this.resize);
    }
}
</script>