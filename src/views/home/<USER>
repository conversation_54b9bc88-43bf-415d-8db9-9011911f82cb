<template>
    <div style="width: 100%; height: 100%;position: relative;" class="available-container">
        <div style="display: flex;justify-content: space-around;">
            <div v-for="item in data" :key="item.id">
                <span style="font-size: 16px;color: #409EFF;font-weight: bold;">{{ item.deviceType }}</span>
                <span style="font-size: 14px;color: #BEBEBF;font-weight: bold;margin-left: 4px;">可用</span>
                <span style="font-size: 26px;color: #606266;font-weight: bold;margin-left: 4px;">{{ item.availableCount }}</span>
            </div>
        </div>
        <i class="el-icon-edit-outline edit-icon" @click="onEdit" v-if="p_AvailableEdit"></i>
        <el-dialog title="可用仪器" :visible.sync="isDialogVisible" width="400px">
            <el-form label-width="100px" :model="data">
                <el-form-item v-for="item in data" :key="item.deviceType" :label="item.deviceType">
                    <InputNumber style="width: 90%;" v-model="item.availableCount" placeholder="请输入可用数量" />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="onCancel">取 消</el-button>
                <el-button type="primary" @click="updateData">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { apiUpdateAvailableDeviceCount } from '@/api/home';

export default {
    name: "HOME_AVAILABLE",
    props: {
        data: {
            default: () => []
        }
    },
    data() {
        return {
            isDialogVisible: false,
        }
    },
    computed: {
        p_AvailableEdit(){
            return this.$checkBtnPermission('sys:home:available');
        }
    },
    methods: {
        updateData(){
            Promise.all(this.data.map(item=>apiUpdateAvailableDeviceCount(item))).then(()=>{
                this.isDialogVisible = false;
                this.$emit('getData')
                this.$message.success('更新成功');
            })
        },
        onEdit(){
            this.isDialogVisible = true;
        },
        onCancel(){
            this.isDialogVisible = false;
            this.$emit('getData')
        }
    }
}
</script>
<style lang="scss" scoped>
    .available-container{
        
        .edit-icon {
            display: none;
            position: absolute;
            right: 0px;
            cursor: pointer;
        }
        &:hover {
            .edit-icon {
                display: block;
            }
        }
    }
</style>