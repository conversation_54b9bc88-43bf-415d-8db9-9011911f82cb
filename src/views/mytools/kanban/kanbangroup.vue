<template>
    <div class="app-container">
        <div class="kanban-group-title">全部看板 <i v-if="false" title="看板设置" @click="onOpenDrawer" class="el-icon-setting" style="float:right;cursor:pointer"></i></div>
        <!-- kanban group
        <el-button @click="toKanban">kanban</el-button> -->
        <div class="kanban-group-container" v-loading="loading">
            <!-- TODO: kanban.color -->
            <div
                @click="onClickKanban(kanban.id)"
                v-for="kanban in kanbanList"
                :key="kanban.id"
                class="kanban"
                :style="`border-left:8px solid ${kanbanColorMap[kanban.color]}`"
            >
                <h3 class="kanban-name">{{ kanban.name }}</h3>
                <!-- <el-input size="small" class="kanban-name-input"></el-input> -->
                <el-popover placement="right" trigger="click">
                    <div class="kanban-group-popover">
                        <!-- <div
                            @click="onDeleteKanban(kanban.id)"
                            class="popover-content"
                        >
                            永久删除
                        </div> -->
                        <div class="popover-content">归档</div>
                        <!-- <el-popover trigger="click" placement="right">
                            <div
                                @click="onClickColorItem(kanban)"
                                slot="reference"
                                class="popover-content"
                                style="display:flex;justify-content:space-between"
                            >
                                <span>颜色</span
                                ><i class="el-icon-arrow-right"></i>
                            </div>
                            <div class="color-group-popover">
                                <div
                                    @click="onSelectBorderColor(colorCode)"
                                    v-for="(color,colorCode)  in kanbanColorMap"
                                    :key="colorCode"
                                    class="color-item"
                                    :style="
                                        `background-color:${color}`
                                    "
                                    :class="
                                        selectedKanbanColorCode === colorCode
                                            ? 'selected'
                                            : ''
                                    "
                                ></div>
                            </div>
                        </el-popover>

                        <div
                            @click="onRenameKanban(kanban.name, kanban.id)"
                            class="popover-content"
                        >
                            重命名
                        </div> -->
                        <div
                            @click="onEditKanban(kanban)"
                            class="popover-content"
                        >
                            编辑
                        </div>
                    </div>
                    <div
                        @click.stop=""
                        slot="reference"
                        class="kanban-more-icon"
                    >
                        <i class="el-icon-more"></i>
                    </div>
                </el-popover>

                <p class="kanban-create-time">创建于 {{ kanban.createTime }}</p>
                <p class="kanban-create-time" v-if="kanban.startDate&&kanban.endDate">{{kanban.startDate||'-'}} 至 {{kanban.endDate||'-'}}</p>
                <p class="kanban-create-time" v-else>-</p>
                <div class="kanban-creater">
                    <Avatar :title="kanban.createUserName" class="avatar" :username="kanban.createUserName">
                    </Avatar>
                    <i v-if="kanban.type==='PRIVATE_BOARD'" title="私密看板" class="el-icon-lock"></i>
                </div>
            </div>
            <!-- <div v-if="showTmpKanban" class="kanban">
                <h3 class="kanban-name">
                    <el-input
                        placeholder="请输入看板名称..."
                        ref="createInput"
                        @blur="onCreateKanban"
                        v-model="newKanbanName"
                    ></el-input>
                </h3>
            </div> -->
            <div
                v-if="!showTmpKanban"
                @click="onNewKanban"
                class="kanban-create"
            >
                <i class="el-icon-plus"></i>新建看板
            </div>
        </div>
        <el-dialog
            :title="dialogType==='create'?'新建看板':'编辑看板'"
            :visible.sync="showCreateKanbanDialog" :close-on-click-modal="false"
            class="create-kanban-dialog"
            width="540px"
        >
            <div class="create-kanban-dialog-container">
                
                <div class="item-name">
                    <span>名称：</span
                    ><el-input
                        v-model="selectedKanbanName"
                        style="width:230px"
                        placeholder="输入看板名称"
                    ></el-input>
                </div>
                <div class="item-color">
                    <span>主题色：</span>
                    <div class="color-container">
                        <div 
                            @click="onClickKanbanFormColor(colorCode)" 
                            v-for="(color,colorCode) in kanbanColorMap" 
                            :key="colorCode"
                            class="color-item" 
                            :style="`background-color:${color}`" 
                            :class="selectedKanbanColorCode === colorCode?'selected':''"
                        >
                        </div>
                    </div>
                </div>
                <div class="item-date">
                    <span>起止日期：</span>
                    <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="selectedKanbanStartEndDate"
                        class="date-picker"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </div>
                <div class="item-member" v-if="isSelectedKanbanPrivate">
                    <span>成员：</span
                    >
                    <div class="member-container">
                        <div
                            class="member-item dialog-member-list"
                            v-for="member in checkedMemberList"
                            :key="member.userId"
                        >
                            {{ member.name }}
                            <i
                                class="el-icon-close"
                                @click="onDeleteMember(member.userId)"
                            ></i>
                        </div>
                        <el-popover trigger="click" placement="right">
                            <div class="add-members">
                                <div class="add-members-header">添加成员</div>
                                <div class="add-members-content">
                                    <!-- <el-input placeholder="" class="add-members-input"></el-input> -->
                                    <div class="members-container">
                                        <div class="members-content">
                                            <div
                                                v-for="m in allMemberList"
                                                :key="m.userId"
                                                class="member-container"
                                            >
                                                <div
                                                    class="member-content"
                                                    @click="onToggleCheckMembers(m.userId, m.name)"
                                                >
                                                    <i
                                                        v-if="checkMember(m.userId)"
                                                        class="el-icon-check"
                                                    ></i>
                                                    <div class="member-name">
                                                        {{ m.name }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <el-popover popper-class="simple-popover" ref="popover" trigger="hover">
                                <div class="simple-div-popover">
                                    添加成员
                                </div>
                            </el-popover>
                            <i
                                v-popover:popover
                                slot="reference"
                                class="el-icon-plus memeber-list-icon"
                            ></i>
                        </el-popover>
                    </div>
                    

                </div>
                
                <div class="set-private"><el-checkbox v-model="isSelectedKanbanPrivate">设为私密</el-checkbox></div>
                <div class="operate-btn">
                    <span class="button plain" 
                        @click="onCancelKanbanDialog"
                        >取消</span
                    >
                    <span @click="onConfirmKanbanDialog" :class="selectedKanbanName?'':'disabled'" class="button red">{{dialogType==='create'?'新建':'确认'}}</span>
                </div>
            </div>
        </el-dialog>
        <el-drawer
            class="kanban-drawer"
            title="我是标题"
            :modal="false"
            :visible.sync="showDrawer">
            <span>我来啦!</span>
        </el-drawer>
    </div>
</template>

<script lang="ts">
import {IMember} from './interface' 
import { Component } from "vue-property-decorator";
import Avatar from "vue-avatar";
import {
    apiAddBoard,
    apiGetBoard,
    apiUpdateBoard,
    apiDeleteBoard,
} from "@/api/kanban";
import { apiGetListByMenuId } from "@/api/users"
import { UserModule } from '@/store/modules/user';
import { Input as ElInput } from "element-ui";
import Vue from "vue";
@Component({ components: { Avatar } })
export default class extends Vue {
    private showDrawer:boolean = false
    // form 
    private selectedKanbanId = -1
    private selectedKanbanName:string = ''
    private selectedKanbanColorCode = "color1";
    private selectedKanbanStartEndDate = ['',''];
    private isSelectedKanbanPrivate = false

    private dialogType = 'create'
    private showCreateKanbanDialog: boolean = false;
    private loading: boolean = false;
    private kanbanList: any[] = [];
    private newKanbanName: string = "";
    private showTmpKanban: boolean = false;
    private menuId: number = -1;
    private allMemberList:IMember[] = [];
    private checkedMemberList:IMember[] = []
    private kanbanColorMap = {
        color1: "rgb(246, 175, 5)",
        color2: "rgb(179, 138, 119)",
        color3: "rgb(240, 138, 93)",
        color4: "rgb(108, 175, 120)",
        color5: "rgb(58, 195, 174)",
        color6: "rgb(85, 133, 193)",
        color7: "rgb(245, 86, 84)",
        color8: "rgb(59, 89, 152)",
    }
    
    get userId() {
        return UserModule.userId;
    }
    created() {
        this.loading = true;
        let parentRouteId = this.$route.matched[1]?.parent?.meta?.id || -1;
        this.rootPath = this.$route.matched[1]?.parent?.path;
        
        if (parentRouteId === -1) {
            this.$message.error("未找到与该看板相匹配的菜单！");
            return;
        } else {
            this.menuId = parentRouteId;
        }
        console.log(this.$route,this.menuId)
        this.getKanban();
        apiGetListByMenuId({menuId: this.menuId}).then(res=>{
            let memberList = res.data.data
            this.allMemberList = memberList.map((item:any)=>{
                return {name:item.realName,userId:item.id}
            })
        })
        
    }
    onOpenDrawer(){
        this.showDrawer = true
    }
    onDeleteMember(userId:number){
        if(this.selectedKanbanCreatorId===userId)return
        let checkIdx = this.checkedMemberList.findIndex(
            (item) => item.userId === userId
        );
        this.checkedMemberList.splice(checkIdx,1)
    }
    onCancelKanbanDialog(){
        this.showCreateKanbanDialog = false
    }
    onConfirmKanbanDialog(){
        if(!this.selectedKanbanName)return
        let color = this.selectedKanbanColorCode;
        let name = this.selectedKanbanName;
        let menuId = this.menuId;
        let startDate = this.selectedKanbanStartEndDate[0]||''
        let endDate = this.selectedKanbanStartEndDate[1]||''
        let type = this.isSelectedKanbanPrivate?'PRIVATE_BOARD':'PUBLIC_BOARD'
        let memberIdList = this.checkedMemberList.map(item=>item.userId)
        if(this.dialogType==='create'){
            apiAddBoard({menuId,name,color,startDate,endDate,type,memberIdList}).then(res=>{
                this.getKanban()
                this.showCreateKanbanDialog = false
            })
        }else{
            let id = this.selectedKanbanId
            apiUpdateBoard({id,menuId,name,color,startDate,endDate,type,memberIdList}).then(res=>{
                this.getKanban()
                this.showCreateKanbanDialog = false
            })

        }
    }
    checkMember(userId: number) {
        return (
            this.checkedMemberList.findIndex(
                (item) => item.userId === userId
            ) !== -1
        );
    }
    onToggleCheckMembers(userId: number, name:string) {
        if(this.selectedKanbanCreatorId===userId)return
        let checkIdx = this.checkedMemberList.findIndex(
            (item) => item.userId === userId
        );
        if (checkIdx == -1) {
            this.checkedMemberList.push({name,userId})
        } else {
            this.checkedMemberList.splice(checkIdx,1)
        }
    }
    onClickKanbanFormColor(colorCode:string){
        this.selectedKanbanColorCode = colorCode
    }
    // onClickColorItem(kanban: any) {
    //     // this.selectedKanbanColorCode = kanban.color;
    //     // this.clickedKanbanId = kanban.id
    // }
    // onSelectBorderColor(colorCode) {
    //     this.selectedKanbanColorCode = colorCode;
    // }
    getKanban() {
        apiGetBoard({ id: this.menuId })
            .then((res) => {
                this.kanbanList = res.data.data;
                this.showTmpKanban = false;
                this.loading = false;
            })
            .catch((err) => {
                this.loading = false;
            });
    }
    onClickKanban(id: number) {
        this.$router.push(`${this.rootPath}/kanban/${id}`);
    }
    onNewKanban() {
        this.dialogType = 'create'
        this.showCreateKanbanDialog = true;
        this.checkedMemberList = []
        this.selectedKanbanColorCode = 'color1'
        this.selectedKanbanName = '';
        this.selectedKanbanStartEndDate = ['',''];
        this.isSelectedKanbanPrivate = false
        this.selectedKanbanCreatorId = this.userId
        this.checkedMemberList = this.allMemberList.map(item=>{return {...item}})
        // this.newKanbanName = "";
        // this.showTmpKanban = true;
        // this.$nextTick(() => {
        //     (this.$refs.createInput as ElInput).focus();
        // });
    }

    onEditKanban(kanban:any){
        this.selectedKanbanCreatorId = kanban.createUserId
        this.selectedKanbanId = kanban.id
        let boardMemberList = kanban.boardMemberList;
        this.checkedMemberList = boardMemberList.map((item:any)=>{return {name:item.realName,userId:item.userId}})
        this.selectedKanbanColorCode = kanban.color in this.kanbanColorMap ? kanban.color : 'color1' ;
        this.selectedKanbanName = kanban.name;
        this.selectedKanbanStartEndDate = [kanban.startDate, kanban.endDate]
        this.isSelectedKanbanPrivate = kanban.type === 'PRIVATE_BOARD'
        this.dialogType = 'edit'
        this.showCreateKanbanDialog = true;
    }
    // onRenameKanban(name: string, id: number) {
    //     this.$prompt("", "重命名", {
    //         confirmButtonText: "确定",
    //         cancelButtonText: "取消",
    //         inputPlaceholder: "请输入看板名称",
    //         confirmButtonClass: "custom-confirm-button",
    //         inputValue: name,
    //     })
    //         .then((value: any) => {
    //             let name = value?.value;
    //             apiUpdateBoard({
    //                 id: id,
    //                 name: name,
    //                 menuId: this.menuId,
    //             }).then(() => {
    //                 this.getKanban();
    //             });
    //         })
    //         .catch(() => {});
    // }
    // onCreateKanban() {
    //     if (this.newKanbanName) {
    //         apiAddBoard({ menuId: this.menuId, name: this.newKanbanName })
    //             .then(() => {
    //                 this.getKanban();
    //             })
    //             .catch(() => {
    //                 this.showTmpKanban = false;
    //             });
    //     } else {
    //         this.showTmpKanban = false;
    //     }
    // }
    // onDeleteKanban(id: number) {
    //     this.$confirm("确认删除？", "提示", {
    //         confirmButtonText: "确认",
    //         cancelButtonText: "取消",
    //         type: "warning",
    //     }).then(() => {
    //         apiDeleteBoard({ id: id }).then(() => {
    //             this.getKanban();
    //         });
    //     });
    // }
}
</script>
<style lang="scss">
.kanban-drawer{
    .el-drawer.ltr, .el-drawer.rtl {
    height: calc(100vh - 60px) !important;
    top: auto;
    bottom: 0;
    width: 400px !important;
    }
}
.kanban-name {
    .el-input__inner {
        padding: 0 5px !important;
        border-radius: 16px !important;
        border: 1px solid white !important;
    }
    .el-input__inner:focus {
        // outline: none;
        border: 1px solid #dcdfe6 !important;
        // border-color: #dcdfe6 !important ;
    }
}
</style>
<style lang="scss">
.item-date{
    .date-picker{
        width: 230px;
        .el-range__icon{
            display: none;
        }
        .el-range-separator{
            width: 30px !important;
        }
    }
}
.simple-popover {
    min-width: 0px !important;
    padding: 0 !important;
    .simple-div-popover {
        padding: 6px;
    }
    .popper-content {
        width: 110px;
        padding: 8px 14px;
        cursor: pointer;
        height: 40px;
        line-height: 22px;
    }
    .popper-content:hover {
        background-color: rgba(245, 86, 84, 0.09);
        color: rgb(245, 86, 84) !important;
    }
    .popper-content:first-child {
        margin-top: 0px;
    }
}
.add-members {
    padding: 16px 0px;
    width: 328px;
    .add-members-header {
        padding-bottom: 8px;
        text-align: center;
        border-bottom: 1px solid rgba(3, 14, 44, 0.04);
        color: rgba(3, 14, 44, 0.85);
        font-size: 16px;
        font-weight: 500;
        margin: 0px 24px 16px;
    }
    .add-members-content {
        height: 230px;
        overflow: auto;
        .members-container {
            padding-left: 24px;
            .members-content {
                overflow: auto;
                .member-container {
                    display: flex;
                    cursor: pointer;
                    -webkit-box-align: center;
                    align-items: center;
                    margin-bottom: 16px;
                    .member-content {
                        position: relative;
                        background-color: rgb(255, 78, 66);
                        width: 286px;
                        height: 40px;
                        border-radius: 10px;
                        transition: all 0.3s ease 0s;
                        color: rgb(255, 255, 255);
                        padding-left: 14px;
                        box-sizing: border-box;
                        line-height: 40px;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        i {
                            position: absolute;
                            right: 19px;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                        .member-name {
                            height: 100%;
                            max-width: 95%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                }
            }
        }
    }
    .add-members-content::-webkit-scrollbar {
        width: 5px;
        height: 8px;
        z-index: 999;
        position: fixed;
    }
    .add-members-content::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background: rgb(204, 204, 204);
        width: 6px;
    }
}
.create-kanban-dialog-container {
    padding: 0px 75px;
    .item-name {
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        -webkit-box-align: center;
        align-items: center;
        min-height: 40px;
        color: rgb(3, 14, 44);
        transform: translateX(12px);
        & > span {
            margin-right: 24px;
            text-align: right;
        }
    }
    .item-member{
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        min-height: 40px;
        color: rgb(3, 14, 44);
        margin-top: 16px;
        transform: translateX(10px);
        & > span {
            margin-right: 24px;
            text-align: right;
            transform: translateX(-16px);
            min-width: 60px;
        }
        .member-container {
            display: flex;
            margin-left: 8px;
            flex-flow: wrap;
            transform: translateX(-24px);
            .member-item {
                background-color: rgb(17, 175, 167);
                // min-width: 40px;
                border-radius: 4px;
                height: 20px;
                line-height: 20px;
                font-weight: 400;
                color: rgb(255, 255, 255);
                margin-right: 4px;
                max-width: 208px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                text-align: right;
                padding: 0px 8px;
                margin-bottom: 8px;
                position: relative;
                transition: all 0.5s ease 0s !important;
                i {
                    color: rgb(255, 255, 255);
                    // font-size: 10px;
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    display: none !important;
                    transition: all 0.5s ease 0s !important;
                }
                i:hover {
                    color: rgba(255, 255, 255, 0.65);
                }
            }
            .member-item.dialog-member-list:hover {
                padding-right: 24px;
                i {
                    display: block !important;
                }
            }
            .memeber-list-icon {
                //  margin-right: -5px;
                // font-size: 24px;
                // font-weight: 600;
                // color: rgba(3, 14, 44, 0.65);
                // span:first-child:focus {
                //     outline: none !important;
                // }
                // i {
                cursor: pointer;
                padding: 2px 0;
                border-radius: 50%;
                margin-left: 3px;
                // }
                &:hover {
                    background: rgba(3, 14, 44, 0.04);
                }
            }
        }
    }
    
    .item-color {
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        min-height: 40px;
        color: rgb(3, 14, 44);
        margin-top: 20px;
        transform: translateX(10px);
        .color-container {
            display: flex;
            margin-left: 8px;
            flex-flow: wrap;
            transform: translateX(-20px);
            .color-item {
                background-color: rgb(246, 175, 5);
                cursor: pointer;
                width: 32px;
                height: 32px;
                box-shadow: rgb(246 175 5 / 45%) 0px 2px 8px 0px;
                border-radius: 50%;
                margin-right: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 8px;
                &.selected::before{
                    content:'√'
                }
            }
        }

        & > span:first-child {
            text-align: right;
            display: block;
            margin-right: 0px;
            transform: translateX(-15px);
            padding-right: 24px;
            min-width: 84px;
        }
    }
    .item-date{
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        -webkit-box-align: center;
        align-items: center;
        min-height: 40px;
        color: rgb(3, 14, 44);
        margin: 8px 0;
        & > span{
            transform: translateX(-16px);
        }
        .date-picker{
            transform: translateX(8px);
        }
    }
    .operate-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        min-height: 40px;
        .button {
            padding-left: 2px;
            cursor: pointer;
            // background: white;
            text-align: center;
            font-size: 14px;
            width: 66px;
            height: 32px;
            line-height: 32px;
            border-radius: 14px;
            margin-left: 8px;
            &.red {
                color: white;
                background: rgb(245, 86, 84);
            }
            &.disabled {
                cursor: not-allowed;
                background: rgba(245, 86, 84, 0.5);
            }
        }
    }
    .set-private{
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        -webkit-box-align: center;
        align-items: center;
        min-height: 40px;
        color: rgb(3, 14, 44);
        transform: translateX(12px);
    }
}
.kanban-group-title {
    padding: 10px 8px;
}
.custom-confirm-button {
    background: red !important;
    border-color: red !important;
}
// .el-popover {
//     padding: 0 !important;
//     min-width: 0px !important;
//     border-radius: 8px;
//     .kanban-group-popover {
//         width: 100px;
//         .popover-content {
//             font-weight: 400;
//             cursor: pointer;
//             display: flex;
//             color: rgba(3, 14, 44, 0.85);
//             justify-content: flex-start;
//             padding-left: 16px;
//             padding-right: 16px;
//             align-items: center;
//             width: 100%;
//             min-height: 40px;
//             position: relative;
//             &:hover {
//                 background-color: rgba(255, 202, 25, 0.25);
//             }
//         }
//     }
//     .color-group-popover {
//         width: 135px;
//         display: flex;
//         flex-wrap: wrap;
//         padding: 5px 0px 0px 5px;
//         .color-item {
//             // margin: 5px 0 0 5px;
//             background-color: rgb(246, 175, 5);
//             cursor: pointer;
//             width: 24px;
//             height: 24px;
//             box-shadow: none;
//             border-radius: 50%;
//             margin-right: 8px;
//             display: flex;
//             justify-content: center;
//             align-items: center;
//             margin-bottom: 8px;
//             &.selected {
//                 background-color: rgb(246, 175, 5);
//                 cursor: pointer;
//                 box-shadow: rgb(246 175 5 / 45%) 0px 2px 8px 0px;
//             }
//             &.selected::before {
//                 content: "√";
//             }
//         }
//     }
// }
</style>
<style lang="scss" scoped>
.kanban-group-container {
    display: flex;
    flex-wrap: wrap;
    .kanban {
        margin: 0px 24px 16px 0px;
        cursor: pointer;
        user-select: none;
        position: relative;
        width: 224px;
        height: 132px;
        background: rgb(255, 255, 255);
        box-shadow: rgba(3, 14, 44, 0.09) 0px 2px 6px 0px;
        border-radius: 8px;
        box-sizing: border-box;
        border-left: 8px solid rgb(246, 175, 5);
        padding: 16px 24px;
        transition: transform 0.5s ease-out 0s, box-shadow 0.5s ease-out 0s;
        &:hover {
            transform: translateY(-5px);
            box-shadow: rgba(3, 14, 44, 0.15) 0px 2px 16px 0px;
        }
        .kanban-more-icon {
            position: absolute;
            top: 6px;
            right: 6px;
            // opacity: 0;
            display: flex;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            &:hover {
                background: rgba(3, 14, 44, 0.04);
            }
            i {
                font-weight: 200;
                transform: rotate(90deg);
            }
        }
        .kanban-name {
            font-size: 16px;
            color: rgba(3, 14, 44, 0.85);
            font-weight: 500;
            margin-bottom: 16px;
            max-width: 144px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 0;
        }
        .kanban-name-input {
            width: 100px;
            display: inline;
        }
        .kanban-create-time {
            color: rgba(3, 14, 44, 0.45);
            font-size: 12px;
            margin-bottom: 12px;
            margin-top: 0;
        }
        .kanban-creater {
            // width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 50%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            i{
                margin-top: 5px;
            }
            .avatar {
                width: 24px !important;
                height: 24px !important;
                line-height: 24px !important;
                margin-right: 2px;
                background-color: rgba(128, 128, 128, 0.3) !important;
                color: rgba(128, 128, 128, 1) !important;
                font-size: 10px !important;
            }
        }
    }
    .kanban-create {
        margin-bottom: 16px;
        width: 224px;
        height: 132px;
        border-radius: 8px;
        border: 1px dashed rgba(3, 14, 44, 0.25);
        color: rgba(3, 14, 44, 0.65);
        display: flex;
        -webkit-box-pack: center;
        justify-content: center;
        cursor: pointer;
        -webkit-box-align: center;
        align-items: center;
        &:hover{
            background-color: rgba(255,255,255,0.2);
            border-color:rgba(3, 14, 44, 0.5)
        }
        i {
            margin-right: 5px;
        }
    }
}
</style>
