<template>
    <!-- <div class="components-container board"> -->
    <!-- <div class=""> -->
    <div style="padding: 10px 20px;">

        <el-tabs v-model="activeTab" >
            <el-tab-pane label="任务" name="task">
                <draggable
                        @start="parentDragging = false"
                        @end="parentDragging = true"
                        :disabled="childDragging"
                        v-bind="dragOptions"
                        :list="boardList"
                        handle=".board-column-header"
                        @change="sortBoard"
                        class="components-container board"
                        id="componentsContainer"
                        ref="componentsContainer"
                >
                    <draggable-kanban
                            ref="kanban"
                            class="kanban"
                            id="kanban"
                            :parentDragging="parentDragging"
                            :headerText="board.name"
                            @listChanged="listChanged"
                            @detailChanged="detailChanged"
                            @listDeleted="listDeleted"
                            @dragChange="dragChange"
                            @getBoard="getBoard"
                            @childDrag="childDrag"
                            :allMemberList="allMemberList"
                            :boardId="boardId"
                            v-for="board in boardList"
                            :key="board.id"
                            :list="board.cardList"
                            :boardDetailId="board.id"
                    >
                    </draggable-kanban>
                    <div class="add-column">
                        <div v-if="isAdd" class="board-column-add">
                            <div class="board-add-item">
                                <el-input
                                        v-model="inputContent"
                                        :autosize="{ minRows: 3 }"
                                        ref="columnAddInput"
                                        placeholder="请输入..."
                                        style="width:100%"
                                        type="textarea"
                                ></el-input>
                            </div>
                            <div class="board-add-button">
                    <span @click="addColumnCancel" class="button plain"
                    >取消</span
                    >
                                <span
                                        @click="addColumnConfirm"
                                        :class="inputContent ? '' : 'disabled'"
                                        class="button red"
                                >添加</span
                                >
                            </div>
                        </div>
                        <div v-if="!isAdd" @click="addColumn" class="board-column-button">
                            <i class="el-icon-plus" /> 添加列表
                        </div>
                    </div>
                </draggable>
            </el-tab-pane>
            <el-tab-pane label="文件" name="file">
                <kanban-file :boardId="boardId"></kanban-file>
            </el-tab-pane>
        </el-tabs>

    </div>

    <!-- </div> -->

    <!-- </div> -->
</template>

<script lang="ts">
import Draggable from "vuedraggable";
import { Component, Vue } from "vue-property-decorator";
import DraggableKanban from "./kanbanitem.vue";
import KanbanFile from "./Components/KanbanFile.vue"
import { Input as ElInput } from "element-ui";
import { IMember } from "./interface";

import {
    apiGetBoardDetail,
    apiAddBoardDetail,
    apiSortBoardDetail,
    apiAddCard,
    apiSortCard,
    apiUpdateCard,
    apiGetAllMembers,
    apiGetBoardFileList,
    apiUploadBoardFile,
} from "@/api/kanban";
@Component({
    name: "DraggableKanbanDemo",
    components: {
        KanbanFile,
        DraggableKanban,
        Draggable,
    },
})
export default class extends Vue {
    private activeTab = "task";
    private childDragging: boolean = false;
    private group = "mission";
    private boardList: any[] = [];
    private boardFileList: any[] = [];
    private allMemberList: IMember[] = [];
    private boardId: number = -1;
    private menuId: number = -1;
    private parentDragging: boolean = true;
    private isAdd: boolean = false;
    private inputContent: string = "";
    get dragOptions() {
        return {
            animation: 200,
            group: "description",
            disabled: false,
            ghostClass: "ghost",
        };
    }
    created(): void {
        this.boardId = Number(this.$route.params.id);
    }

    mounted() {
        let parentRouteId = this.$route.matched[1]?.parent?.meta?.id || -1;
        if (parentRouteId === -1) {
            this.$message.error("未找到与菜单相对应的卡片列表！");
            return;
        } else {
            this.menuId = parentRouteId;
        }
        this.getBoard();
        this.getMembers();
        const main = document.querySelector(".board");
        let isMove = false;
        let initX = 0;
        let moveX = 0;
        let dis = 0;
        let initScrollLeft = 0;

        apiGetBoardFileList({boardId: this.boardId}).then(res=>{
            this.boardFileList = res.data.data
        })

        main?.addEventListener(
            "mousedown",
            function(e: any) {
                let target = e.target as HTMLElement;
                if (target.id === "kanban") {
                    initScrollLeft = main.scrollLeft;
                    isMove = true;
                    initX = e.pageX;
                } else {
                    isMove = false;
                }
            },
            true
        );
        main?.addEventListener("mousemove", function(e: any) {
            if (isMove) {
                moveX = e.pageX;
                dis = -moveX + initX;
                main.scrollLeft = dis + initScrollLeft;
            }
        });
        window?.addEventListener("mouseup", function() {
            isMove = false;
        });
    }
    getMembers() {
        apiGetAllMembers({ menuId: this.menuId }).then((res) => {
            this.allMemberList = [];
            let dataList = res.data.data || [];
            for (let member of dataList) {
                this.allMemberList.push({
                    name: member.realName,
                    userId: member.id,
                });
            }
        });
    }
    childDrag(bool: boolean) {
        this.childDragging = bool;
    }
    dragChange(event: any) {
        if(event.removed)return
        let currId = (event.moved || event.removed || event.added).element.id;
        let lastId: number;
        let allCardList: { boardId: number; cardId: number }[] = [];
        this.boardList.forEach((item) => {
            if (item.cardList) {
                for (let card of item.cardList) {
                    allCardList.push({ boardId: item.id, cardId: card.id });
                }
            }
        });
        let currIdx = allCardList.findIndex((item) => item.cardId === currId);
        let currBoardId = allCardList[currIdx].boardId;
        if (currIdx === allCardList.length - 1) {
            lastId = -1;
        } else {
            let nextItem = allCardList[currIdx + 1];
            if (nextItem.boardId === currBoardId) {
                lastId = nextItem.cardId;
            } else {
                lastId = -1;
            }
        }
        let boardDetailId = currBoardId;
        let form = new FormData();
        form.append("currId", String(currId));
        form.append("lastId", String(lastId));
        form.append("boardDetailId", String(boardDetailId));
        apiSortCard(form);
    }
    getBoard() {
        apiGetBoardDetail({ id: this.boardId }).then((res) => {
            this.boardList = res.data.data.map((item:any)=>{if(item.cardList){return item}else{item.cardList=[];return item}});
            this.$nextTick(() => {
                let kanbanLst: any = this.$refs.kanban || [];
                kanbanLst.forEach((item: any) => {
                    item.isModifyHeader = false;
                    item.showAdd = false;
                    item.inputContent = "";
                });
                this.isAdd = false;
                this.inputContent = "";
            });
            // (this.$refs.kanban)
        });
    }
    listChanged(data: { item: string; boardDetailId: number }) {
        let { item, boardDetailId } = data;
        apiAddCard({ boardDetailId, name: item }).then(() => {
            this.getBoard();
        });
    }
    detailChanged(data: {
        name: string;
        id: number;
        desc: string;
        status: number;
        members: string;
    }) {
        apiUpdateCard({ ...data }).then(() => {
            this.getBoard();
        });
        // apiAddCard({ boardDetailId: listId, name: content }).then((res) => {});
        // let { content, key, listId } = data;
        // for (let item of this[`list${listId}`]) {
        //     if (item.id === key) {
        //         item.name = content;
        //     }
        // }
    }
    listDeleted() {}
    sortBoard(event: any) {
        let currId = (event.moved || event.removed || event.added).element.id;
        let lastId: number;
        let boardId = this.boardId;

        let allBoardList: number[] = [];
        this.boardList.forEach((item) => {
            allBoardList.push(item.id);
        });
        let currIdx = allBoardList.indexOf(currId);
        // let currBoardId = allBoardList[currIdx];
        if (currIdx === allBoardList.length - 1) {
            lastId = -1;
        } else {
            lastId = allBoardList[currIdx + 1];
        }
        let form = new FormData();
        form.append("currId", String(currId));
        form.append("lastId", String(lastId));
        form.append("boardId", String(boardId));
        apiSortBoardDetail(form);
    }
    addColumn() {
        this.isAdd = true;
        this.$nextTick(() => {
            // scroll
            const componentsContainer = document.getElementById("componentsContainer") as Element
            componentsContainer.scrollLeft = componentsContainer.scrollWidth;
            // input focus
            (this.$refs.columnAddInput as ElInput).focus();
        });
    }
    addColumnConfirm() {
        if (!this.inputContent) {
            return;
        }
        apiAddBoardDetail({
            name: this.inputContent,
            boardId: this.boardId,
        }).then(() => {
            this.getBoard();
        });
    }
    addColumnCancel() {
        this.isAdd = false;
        this.inputContent = "";
    }
}
</script>

<style lang="scss">
.components-container.board {
    user-select: none;
    height: calc(100vh - 60px - 34px - 75px);
    padding: 0 !important;
    margin: 0 !important;
    width: 100%;
    overflow-x: auto;
}
.el-popover__reference-wrapper:focus {
    outline: none !important;
}
.el-popover__reference-wrapper {
    &:focus {
        outline: none !important;
    }
}
.simple-popover {
    min-width: 0px !important;
    padding: 0 !important;
    .simple-div-popover {
        padding: 6px;
    }
    .popper-content {
        width: 110px;
        padding: 8px 14px;
        cursor: pointer;
        height: 40px;
        line-height: 22px;
    }
    .popper-content:hover {
        background-color: rgba(245, 86, 84, 0.09);
        color: rgb(245, 86, 84) !important;
    }
    .popper-content:first-child {
        margin-top: 0px;
    }
}
</style>

<style lang="scss" scoped>
.add-column {
    min-width: 266px;
    padding: 10px;
    .board-column-button {
        color: rgb(245, 86, 84);
        // text-align: center;
        width: 100%;
        line-height: 40px;
        height: 40px;
        // background: rgb(255, 255, 255);
        // border-radius: 20px;
        // margin-bottom: 8px;
        cursor: pointer;
    }
    .board-column-add {
        height: auto;
        overflow: hidden;
        border: 1px solid transparent;
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        align-items: center;
        width: 100%;
        .board-add-item {
            width: 100%;
            height: fit-content;
            box-sizing: border-box;
            box-shadow: rgba(3, 14, 44, 0.09) 0px 2px 6px 0px;
            border-radius: 8px;
            margin-bottom: 8px;
            background-color: #fff;
            text-align: left;
            padding: 5px 10px;
            box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.2);
        }
        .board-add-button {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            .button {
                padding-left: 2px;
                cursor: pointer;
                // background: white;
                text-align: center;
                font-size: 14px;
                width: 66px;
                height: 32px;
                line-height: 32px;
                border-radius: 14px;
                margin-left: 8px;
                &.red {
                    color: white;
                    background: rgb(245, 86, 84);
                }
                &.disabled {
                    cursor: not-allowed;
                    background: rgba(245, 86, 84, 0.5);
                }
            }
        }
    }
}

.board {
    width: 100%;
    // background: #f0f0f0;
    display: flex;
    flex-direction: row;
}
.flip-list-move {
    transition: transform 0.5s;
}
</style>
