<template>
    <div style="min-height: 300px; background: white;">
        <div class="library-list-title">
            <div style="flex: 1 1 auto; margin-left: 20px; margin-right: 20px; border-bottom: 1px solid #e5e5e5;">
                <div style="display: flex">
                    <div style="width: 80%; font-size: 18px; color: #262626;">文件库</div>
                    <div style="width: 20%; display: flex; align-items: center;">
                        <input ref="excel-upload-input" class="excel-upload-input" type="file" @change="onSubmitUpload">
                        <el-button size="small" type="primary" @click="onUpload">上传</el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="library-list-title">
            <div style="flex: 1 1 auto; margin-left: 20px; margin-right: 20px; border-bottom: 1px solid #e5e5e5;">
                <div style="display: flex">
                    <div style="width: 50%;">文件名称</div>
                    <div style="width: 50%; display: flex; align-items: center;">
                        <div style="width: 150px;"><span>上传人</span></div>
                        <div style="width: 180px;"><span>上传时间</span></div>
                        <div style="width: 150px;">操作</div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="library-list-item" v-for="(item,fileKey) in boardFileList" :key="`file`+fileKey">
                <div style="flex: 1 1 auto; margin-left: 20px; margin-right: 20px; border-bottom: 1px solid #f7f7f7;">
                    <div style="display: flex">
                        <div style="width: 50%;">{{item.fileName}}</div>
                        <div style="width: 50%; display: flex; align-items: center;">
                            <div style="width: 150px;"><span>{{item.realName}}</span></div>
                            <div style="width: 180px;"><span>{{item.updateTime}}</span></div>
                            <div style="width: 150px;"><el-tag style="margin-right: 10px;" @click="onDownload(item.uuid)">下载</el-tag><el-tag type="danger" @click="onDelete(item.uuid)">删除</el-tag></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { Component, Prop, Vue } from 'vue-property-decorator'
    import {
        apiGetBoardFileList,
        apiUploadBoardFile,
        apiDeleteBoardFile,
    } from "@/api/kanban";
    import {AxiosResponse} from "axios";
    @Component({
        name: 'KanbanFile'
    })
    export default class extends Vue {
        @Prop({type: Number, default: 0}) private boardId: Number | undefined

        private boardFileList: any[] = [];

        mounted(): void {
            apiGetBoardFileList({boardId: this.boardId}).then(res=>{
                this.boardFileList = res.data.data
            })
        }

        onUpload() {
            let ref:any = this.$refs['excel-upload-input']
            ref.click()
        }

        onSubmitUpload(e:any) {
            const files = e.target.files
            const rawFile = files[0] // only use files[0]
            if (!rawFile) return

            let boardId = this.boardId
            let formData = new FormData()
            formData.append("boardId", boardId + "")
            formData.append("file", rawFile)
            apiUploadBoardFile(formData).then(res=>{
                this.boardFileList = res.data.data
                this.$message.success("上传成功!")
            }).catch(err=>{
                this.$message.error(err.msg)
            })
        }

        onDownload(uuid:string) {
            console.log(uuid)
            const downloadUrl = '/api/board/file/download?fileId=' + uuid
            window.open(downloadUrl)
        }

        onDelete(uuid:string) {
            this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                apiDeleteBoardFile({uuid: uuid}).then(res=>{
                    this.boardFileList = res.data.data
                })
            }).catch(() => {

            });
        }
    }
</script>

<style lang="scss" scoped>

    .excel-upload-input{
        display: none;
        z-index: -9999;
    }

    .library-list-title {
        display: flex;
        background: white;
        height: 56px;
        line-height: 56px;
        overflow: hidden;
        span {
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .library-list-item {
        display: flex;
        cursor: pointer;
        background: white;
        height: 56px;
        line-height: 56px;
        overflow: hidden;
        span {
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .library-list-item:hover {
        background-color: #f7f7f7;
    }
</style>
