<template>
    <div class="app-container">
        <div class="tools-container">
            <div
                @click="toPath"
                class="item"
                v-for="(item, key) in 1"
                :key="key"
                style="padding: 20px;"
            >
                <!--                <svg-icon-->
                <!--                    height="60px"-->
                <!--                    width="60px"-->
                <!--                    name="icon"-->
                <!--                    class="item-icon"-->
                <!--                ></svg-icon>-->
                <img
                    src="https://bizaladdin-image.baidu.com/0/pic/e51a30aa6b70086edecf46ea08795b26.jpg"
                    style="width: 60px; height: 60px;"
                />
                <span class="item-desc">腾讯文档</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
@Component({})
export default class extends Vue {
    private toPath() {
        window.open("https://docs.qq.com/desktop/?fromsrc=homepage", "_blank");
    }
}
</script>

<style lang="scss" scoped>
.tools-container {
    // width: 50%;
    // background: #cccccc;
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;
    padding-bottom: 10px;

    .item {
        cursor: pointer;
        margin: 10px 10px 0 0;
        // flex: 0 0 32%;
        width: 300px;
        /*border: 1px solid #cccccc;*/
        box-shadow: 0 3px 10px 0 rgba(31,35,41,.04);
        border-radius: 5px;
        height: 100px;
        display: flex;
        align-items: center;
        // justify-content: center;
        background: #ffffff;

        // background-clip: content-box;
        .item-icon {
            margin-left: 15px;
            color: #f7b263;
        }
        .item-desc {
            margin-left: 12px;
            font-size: 20px;
            display: flex;
        }
    }
    .item:hover {
        box-shadow: 0 10px 16px 0 rgba(31,35,41,.18);
    }
}
</style>
