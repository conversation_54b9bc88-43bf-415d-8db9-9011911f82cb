<template>
    <div class="app-container">
        <div class="ledge-container">
            <div class="ledge" v-for="(item, key) in 40" :key="key">
                <svg-icon
                    class="ledge-icon"
                    width="50px"
                    height="50px"
                    name="icon"
                ></svg-icon>
                <div class="ledge-title">新冠知识普及手册</div>
                <div class="ledge-subtitle">中国加油战疫必胜</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
@Component({})
export default class extends Vue {}
</script>
<style lang="scss" scoped>
.ledge-container {
    width: 100%;
    // background: #cccccc;
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;
    padding-bottom: 10px;

    .ledge {
        cursor: pointer;
        margin: 10px 10px 0 0;
        border: 1px solid #cccccc;
        height: 150px;
        width: 150px;
        // justify-content: center;
        background: #ffffff;
        padding: 14px;
        // background-clip: content-box;
        .ledge-icon {
            color: #f7b263;
            margin-bottom: 14px;
        }
        .ledge-title {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .ledge-subtitle {
            font-size: 12px;
        }
    }
    .ledge:hover {
        box-shadow: 0 0 3px #999;
    }
}
</style>
