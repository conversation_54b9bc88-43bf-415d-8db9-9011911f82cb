<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器列表</div>
            <el-form :model="searchForm" label-width="70px">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="仪器类型">
                            <el-select
                                clearable
                                style="width: 100%"
                                v-model="searchForm.deviceType"
                                placeholder=""
                                @change="getFailureInfoList(true)"
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="序列号" label-width="60px">
                            <el-input
                                style="width: calc(100%)"
                                clearable
                                v-model="searchForm.serialNumber"
                                @change="getFailureInfoList(true)"
                            >
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
                <el-table-column prop="invName" label="品名" width="100" fixed="left"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                    width="160"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    prop="wellNumber"
                    label="井号"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="departmentName"
                    label="提交维修报告部门"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="name"
                    label="创建维修报告人"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="jobNumber"
                    label="作业号"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="run"
                    label="趟次"
                    align="center"
                    width="60"
                ></el-table-column>
                <el-table-column
                    prop="receiveDate"
                    label="接收日期"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="endDate"
                    label="完成日期"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="failureReportDate"
                    label="失效报告"
                    align="center"
                    width="100"
                >
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            target="_blank" 
                            :to="`/failure/index?jobid=${row.jobId}&reportid=${row.reportId}`" 
                            v-if="p_JobView"
                        >
                            {{ row.failureReportDate }}
                        </router-link>
                        <span v-else>{{ row.failureReportDate }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="findings" label="车间检查发现" width="200">
                </el-table-column>
                <el-table-column prop="repairAction" label="维修措施" width="200">
                </el-table-column>
                <el-table-column
                    prop="mwdNumber"
                    label="维修工单号"
                    align="center"
                    width="160"
                    fixed="right"
                >
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            target="_blank" 
                            :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                            v-if="p_MwdInfoView"
                        >
                            {{ row.mwdNumber }}
                        </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    align="center"
                    width="120"
                    fixed="right"
                    v-if="p_MwdInfoView"
                >
                    <template slot-scope="{row}">
                        <el-button @click="onExportReport(row)" type="text">导出维修报告</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import getDict from "@/utils/getDict";
import { apiGetFailInfo, apiGetFailInfoV2 } from "@/api/failure";
import { apiGetMwdMaintainReport } from "@/api/tool-mantain";
@Component({ components: {  }})
export default class extends Vue {
    private searchForm: any = {};
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private deviceTypeList:any [] = [];
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    get p_JobView(){
        return this.$checkBtnPermission('sys:dailydetail:view');
    }
    private async mounted() {
        [this.deviceTypeList] = await getDict([17]);
        this.getFailureInfoList();
    }
    private getFailureInfoList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const invName = this.searchForm.invName || undefined;
        const serialNumber = this.searchForm.serialNumber || undefined;
        const deviceType = this.searchForm.deviceType || undefined;
        const wellNumber = undefined
        apiGetFailInfoV2({invName,serialNumber,deviceType, wellNumber}, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getFailureInfoList();
    }
    private onExportReport(row){
        const loading = this.$loading({
            lock: true,
            text: "处理中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
        });
        apiGetMwdMaintainReport({ mwdId: row.mwdId })
            .then((res) => {
                this.loadingExportExcelAll = false;
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `${row.deviceTypeStr}_${row.serialNumber}_${row.mwdNumber}_维修报告.docx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            })
            .finally(() => {
                loading.close();
            });
    }
}
</script>
<style lang="scss" scoped></style>
