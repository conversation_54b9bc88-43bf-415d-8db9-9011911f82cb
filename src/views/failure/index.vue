<template>
    <ImageViewer :disabled="isEditFailureReport" class="app-container">
        <div class="app-card failure-report" style="height: 100%; padding: 0">
            <div class="content-card" style="position: relative;">
                <div class="report-container" style="margin-top:0px;padding:10px;">
                    <div class="report" style="height: calc(100vh - 122px);">
                        <div class="content" v-loading="showLoading">
                            <div style="z-index: 999;height: 50px;display: flex;align-items: center;min-width: 700px;background: white;border-bottom: 1px dashed #eee;">
                                <span
                                    class="report-title"
                                    style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
                                >
                                    {{ $REPORT_TYPE_MAP.FAILURE }}
                                </span>
                                <el-select
                                    style="margin-left: 10px"
                                    v-model="selectedDateId"
                                    @change="onSelectDate"
                                >
                                    <el-option
                                        v-for="(item, key) in currentDateList"
                                        :key="`date${key}`"
                                        :label="item.date"
                                        :value="item.id"
                                    ></el-option>
                                </el-select>
                                <template>
                                    <template>
                                        <el-button v-if="!isEditFailureReport" type="primary" @click="onEditReport" style="margin-left: 10px">
                                            车间编辑
                                        </el-button>
                                        <el-button v-if="isEditFailureReport" style="margin-left: 10px" type="primary" @click="onSaveReport">
                                            保存
                                        </el-button>
                                        <el-button v-if="isEditFailureReport" style="" @click="onCancelEditReport">
                                            取消
                                        </el-button>
                                    </template>
                                </template>
                            </div>
                            <div v-if="showReport" class="mwd-container" style="margin-bottom: 20px">
                                <div class="content-container2">
                                    <div class="line">
                                        <div class="line" style="flex: 1; margin-bottom: 10px">
                                            <div class="line" style="flex: 400px 0 0; padding: 5px">
                                                <div class="info-title">工程师MWD Operator：</div>
                                                <el-input
                                                    class="mwd-input basic-info-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.operator"
                                                ></el-input>
                                            </div>
                                            <div class="line" style="flex: 300px 0 0; padding: 5px">
                                                <div class="info-title">Kit #：</div>
                                                <!-- TODO: kitId -> kit  -->
                                                <el-input
                                                    class="mwd-input basic-info-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.kit"
                                                ></el-input>
                                            </div>
                                            <div class="line" style="flex: 300px 0 0; padding: 5px">
                                                <div class="info-title">趟次：</div>
                                                <el-input
                                                    class="mwd-input basic-info-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.run"
                                                ></el-input>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-border">
                                        <div class="line">
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                客户Client Company
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.client"
                                                ></el-input>
                                            </div>
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                井队号Drilling Rig
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.drillingRig"
                                                ></el-input>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                井号Well Name
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.wellNumber"
                                                ></el-input>
                                            </div>
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                入井时间 Downhole Time
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.inHoleDate"
                                                ></el-input>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                位置Location
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.location"
                                                ></el-input>
                                            </div>
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                失效时间Failure Time
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-date-picker
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    format="yyyy-MM-dd HH:mm"
                                                    value-format="yyyy-MM-dd HH:mm"
                                                    v-model="report.failureDate"
                                                ></el-date-picker>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                区块Field
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-input
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.field"
                                                ></el-input>
                                            </div>
                                            <div class="title2 center" style="flex: 25% 0 0">
                                                报告日期Report Date
                                            </div>
                                            <div class="text left" style="flex: 25% 0 0">
                                                <el-date-picker
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    value-format="yyyy-MM-dd"
                                                    v-model="report.reportDate"
                                                ></el-date-picker>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <!-- TODO: 拆！ -->
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                MWD TOOLS
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                部件名称Components
                                            </div>
                                            <div class="title center" style="flex: 1 0 0">
                                                序列号Serial #
                                            </div>
                                            <div class="title center" style="flex: 1 0 0">数量</div>
                                            <div class="title center" style="flex: 1 0 0">
                                                循环Hours
                                            </div>
                                        </div>
                                        <div
                                            class="line hover-line"
                                            v-for="(item, key) in report.toolList"
                                            :key="`toolList` + item.stockId + key"
                                        >
                                            <div class="text center" style="flex: 1 0 0">
                                                <el-input
                                                    v-if="!isEdit"
                                                    class="mwd-input"
                                                    disabled
                                                    v-model="item.invName"
                                                ></el-input>
                                                <el-input
                                                    v-else
                                                    placeholder=""
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="item.invName"
                                                >
                                                </el-input>
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <el-input
                                                    placeholder=""
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="item.serialNumber"
                                                >
                                                </el-input>
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit || !!item.serialNumber"
                                                    v-model="item.quantity"
                                                ></InputNumber>
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="item.total"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1">
                                                失效环境Pre Failure Conditions
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                排量Flow Rate
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.flowRate"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                限流环Orfice
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.orfice"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                蘑菇头Poppet
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.poppet"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                脉宽PulseWth
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.pulsewth"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                井下时间Run Hours
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.runHours"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                测深Meas. Depth
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.measDepth"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                井斜Inc
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.inc"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                方位Azm
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.azm"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                磁偏角MagDec
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.magdec"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                温度Temp
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.temp"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 1 0 0">
                                                电池电压Battery Volts:
                                            </div>
                                            <div class="text center" style="flex: 1 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.batteryVolts"
                                                ></InputNumber>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                失效描述SYMPTOMS OF FAILURE
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div
                                                class="text left"
                                                style="
                                                    flex: 100% 0 0;
                                                    width: 100%;
                                                    min-height: 120px;
                                                    height: auto;
                                                "
                                            >
                                                <div
                                                    v-html="report.failureSymptoms"
                                                    class="rich-text-cls"
                                                ></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                {{ `采取措施及效果ACTIONS TAKEN & RESULTS` }}
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div
                                                class="text left"
                                                style="
                                                    flex: 100% 0 0;
                                                    width: 100%;
                                                    min-height: 120px;
                                                    height: auto;
                                                "
                                            >
                                                <div
                                                    v-html="report.actionTaken"
                                                    class="rich-text-cls"
                                                ></div>
                                            </div>
                                            <!-- <div
                                                class="text center"
                                                style="flex: 100% 0 0; height: 120px"
                                            >
                                                <el-input
                                                    :autosize="{ minRows: 6 }"
                                                    class="mwd-input"
                                                    type="textarea"
                                                    :disabled="!isEdit"
                                                    v-model="report.actionTaken"
                                                ></el-input>
                                            </div> -->
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                {{
                                                    `地面检查及结果SURFACE INSPECTION AND FINDINGS`
                                                }}
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div
                                                class="text left"
                                                style="
                                                    flex: 100% 0 0;
                                                    width: 100%;
                                                    min-height: 120px;
                                                    height: auto;
                                                "
                                            >
                                                <div
                                                    v-html="report.surfaceInspection"
                                                    class="rich-text-cls"
                                                ></div>
                                            </div>
                                            <!-- <div
                                                class="text center"
                                                style="flex: 100% 0 0; height: 120px"
                                            >
                                                <el-input
                                                    :autosize="{ minRows: 6 }"
                                                    class="mwd-input"
                                                    type="textarea"
                                                    :disabled="!isEdit"
                                                    v-model="report.surfaceInspection"
                                                ></el-input>
                                            </div> -->
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                {{ `车间调查结果Shop inspectionc` }}
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div v-if="isEditFailureReport" style="height:100%;" class="ql-container ql-snow">
                                                <quill-editor
                                                    class="editor ql-editor"
                                                    ref="shopInspectionc"
                                                    v-model="report.shopInspectionc"
                                                    :options="getEditorOption('shopInspectionc')"
                                                />
                                                <form
                                                    action
                                                    method="post"
                                                    enctype="multipart/form-data"
                                                    id="uploadFormMulti"
                                                >
                                                    <input
                                                        style="display:none"
                                                        id="shopInspectionc"
                                                        type="file"
                                                        name="file"
                                                        multiple
                                                        accept="image/jpg,image/jpeg,image/png,image/gif"
                                                        @change="uploadImg('shopInspectionc')"
                                                    />
                                                </form>
                                            </div>
                                            <div
                                                class="text left"
                                                v-else
                                                style="
                                                    flex: 100% 0 0;
                                                    width: 100%;
                                                    min-height: 120px;
                                                    height: auto;
                                                "
                                            >
                                                <div
                                                    v-html="report.shopInspectionc"
                                                    class="rich-text-cls"
                                                ></div>
                                            </div>
                                            <!-- <div
                                                class="text center"
                                                style="flex: 100% 0 0; height: 120px"
                                            >
                                                <el-input
                                                    ref="focusInput"
                                                    :autosize="{ minRows: 6 }"
                                                    class="mwd-input"
                                                    type="textarea"
                                                    :disabled="!isEditFailureReport"
                                                    v-model="report.shopInspectionc"
                                                ></el-input>
                                            </div> -->
                                        </div>
                                        <div class="file-list" v-if="fileList&&fileList.length">
                                            <div class="file-item" v-for="item in fileList" :key="item.massId">
                                                <span :title="item.massFileName" class="file-title">
                                                    {{ item.massFileName }}
                                                </span>
                                                <a :href="item.massFilePath">
                                                    <i class="el-icon-download oper-icon"></i>
                                                </a>
                                                <i v-if="isEditFailureReport" @click="onDeleteFailureShopFile(item.massId)" class="el-icon-delete oper-icon"></i>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="title center" style="flex: 100% 0 0">
                                                {{ `改进计划/措施` }}
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div v-if="isEditFailureReport" style="height:100%;" class="ql-container ql-snow">
                                                <quill-editor
                                                    class="editor ql-editor"
                                                    ref="improvementPlanMeasures"
                                                    v-model="report.improvementPlanMeasures"
                                                    :options="getEditorOption('improvementPlanMeasures')"
                                                />
                                                <form
                                                    action
                                                    method="post"
                                                    enctype="multipart/form-data"
                                                    id="uploadFormMulti2"
                                                >
                                                    <input
                                                        style="display:none"
                                                        id="improvementPlanMeasures"
                                                        type="file"
                                                        name="file"
                                                        multiple
                                                        accept="image/jpg,image/jpeg,image/png,image/gif"
                                                        @change="uploadImg('improvementPlanMeasures')"
                                                    />
                                                </form>
                                            </div>
                                            <div class="text left" v-else style="flex: 100% 0 0;width: 100%;min-height: 120px;height: auto;"
                                            >
                                                <div v-html="report.improvementPlanMeasures" class="rich-text-cls"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="box-border" style="border-top: none">
                                        <div class="line">
                                            <div class="line" style="flex: 20% 0 0">
                                                <div
                                                    class="title center"
                                                    style="flex: 1; height: auto"
                                                >
                                                    FAILED COMPONENT(S)2:
                                                </div>
                                            </div>
                                            <div class="line" style="flex: 1">
                                                <div class="line">
                                                    <div
                                                        @click="
                                                            isEditFailureReport &&
                                                                (component.failed =
                                                                    !component.failed)
                                                        "
                                                        v-for="component in report.componentList"
                                                        :key="component.componentId"
                                                        class="text center"
                                                        style="flex: 14.28% 0 0; cursor: pointer"
                                                        :class="{ failed: component.failed }"
                                                    >
                                                        {{ component.name }}
                                                    </div>
                                                    <div
                                                        class="text center"
                                                        style="flex: 14.28% 0 0"
                                                        v-for="i in 7 -
                                                        (report.componentList.length % 7 || 7)"
                                                        :key="i"
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line">
                                            <div class="title center" style="flex: 20% 0 0">
                                                井下时间TOTAL DOWNTIME:
                                            </div>
                                            <div class="text center" style="flex: 22.9% 0 0">
                                                <InputNumber
                                                    class="mwd-input"
                                                    :disabled="!isEdit"
                                                    v-model="report.totalDowntime"
                                                ></InputNumber>
                                            </div>
                                            <div class="title center" style="flex: 22.9% 0 0">
                                                发现失效原因PROBLEM RECTIFIED
                                            </div>
                                            <div class="text left" style="flex: 1 0 0">
                                                <el-select
                                                    v-model="report.findProblem"
                                                    :disabled="!isEdit"
                                                    class="mwd-input"
                                                    placeholder=""
                                                >
                                                    <el-option
                                                        v-for="item in findProblemList"
                                                        :key="item.id"
                                                        :label="item.name"
                                                        :value="item.id"
                                                    ></el-option>
                                                </el-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadFailureShopFile"
            >
            <div ref="ghostFileUploader" style="width:0;height:0"></div>
        </el-upload>
    </ImageViewer>
</template>

<script>
import ImageViewer from "@/components/ImageViewer/index.vue"
import {
  apiDeleteFailureShopFile,
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiGetFailureShopFile,
    apiUpdateFailedComponent,
    apiUpdateFailureShop,
    apiUploadFailureShopFile,
} from "@/api/mwd";
import dealReport from "../daily/Components/init";
import imageCompression from 'browser-image-compression';
import getDict from '@/utils/getDict';
const REPORT_TYPE = "FAILURE";
export default {
    name: "FAILURE",
    components: { ImageViewer },
    data() {
        return {
            report: {},
            showReport: false,
            selectedDateId: null,
            selectedDate: null,
            currentDateList: [],
            isEdit: false,
            findProblemList: [
                { id: 1, name: "是YES" },
                { id: 2, name: "否NO" },
                { id: 3, name: "其他OTHER" },
            ],
            showLoading: true,
            toolList: [],
            isEditFailureReport: false,
            jobId:174,
            fileList: [
            ],
            // { massId, operate: delete | add }
            failureShopFileOperatePool: [],
        };
    },
    mounted() {
        this.jobId = this.$route.query.jobid;
        this.reportId = this.$route.query.reportid;
        if(!this.jobId) {
            return;
        }
        if(this.reportId){
            this.selectedDateId = Number(this.reportId);
        }
        this.loadReport();
    },
    methods: {
        getEditorOption(editorUniqueId) {
            const self = this;
            return {
                modules: {
                    resizeImage: {
                        displayStyles: {
                        backgroundColor: "black",
                        border: "none",
                        color: "white",
                        },
                        modules: ["Resize"],
                    },
                    toolbar: {
                        container: [
                            ["bold", "italic", "underline", "strike"],
                            [{ header: 1 }, { header: 2 }],
                            [{ list: "ordered" }, { list: "bullet" }],
                            [{ script: "sub" }, { script: "super" }],
                            [{ size: ["small", false, "large", "huge"] }],
                            [{ color: [] }, { background: [] }],
                            [{ align: [] }],
                            ["clean"],
                            ["link", "image"],
                        ],
                        handlers: {
                            link(){
                                self.$refs.ghostFileUploader.click();
                            },
                            image() {
                                self.uploadType = "image";

                                let quill = self.$refs[editorUniqueId].quill;
                                // 获取光标所在位置
                                document.getElementById(editorUniqueId).click();
                                self.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                },
            }
        },
        async uploadImg(editorUniqueId) {
            const loading = this.$loading({
                lock: true,
                text: "图片处理中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            let quill = this.$refs[editorUniqueId].quill;
            const maxSizeMB = 0.5;
            let img = document.getElementById(editorUniqueId).files[0];
            const options = {
                maxSizeMB,
                maxWidthOrHeight: 1920,
                useWebWorker: true,
            }
            try {
                const compressedFile = img.size/1024/1024 > maxSizeMB ? await imageCompression(img, options) : img;
                var reader = new FileReader();
                reader.readAsDataURL(compressedFile);
                reader.onload = (ev) => {
                //文件读取成功完成时触发
                var dataURL = ev.target.result;
                // 插入图片，res为服务器返回的图片链接地址
                quill.insertEmbed(this.insertPosition, this.uploadType, dataURL);
                // 调整光标到最后
                quill.setSelection(this.insertPosition + 1);
            };
            } catch (error) {
                console.log(error);
            } finally {
                loading.close();
            }
        },
        
        onUploadFailureShopFile(file){
            const loading = this.$loading({
                lock: true,
                text: "文件上传中...",
                spinner: "el-icon-loading",
                background: "rgba(0, 0, 0, 0.7)",
            });
            const form = new FormData();
            form.append('jobId', String(this.jobId));
            form.append('date', this.selectedDate);
            form.append('file', file);
            apiUploadFailureShopFile(form).then(()=>{
                this.getFailureShopFileList();
            }).finally(()=>{
                loading.close();
            })
        },
        getFailureShopFileList() {
            apiGetFailureShopFile({
                jobId: this.jobId,
                date: this.selectedDate,
            },{
                current:1,
                size: 99999
            }).then(res=>{
                this.fileList = res.data.data.records || [];
            })
        },
        updateReport() {
            apiUpdateFailureShop({
                jobId: this.jobId,
                date: this.selectedDate, 
                content: this.report.shopInspectionc,
                partsList: this.report.componentList,
                improvementPlanMeasures: this.report.improvementPlanMeasures,
            }).then(()=>{
                this.$message({
                    message: '修改成功',
                    type:'success'
                })
                this.isEditFailureReport = false;
                this.loadReport()
            })
        },
        onDeleteFailureShopFile(massId) {
            this.$confirm("确认删除该附件?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(()=>{
                const form = new FormData();
                form.append("idList", [massId])
                apiDeleteFailureShopFile(form).then(()=>{
                    this.getFailureShopFileList();
                });
            })
            
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentDateList = baseInfo.data?.data || [];
            this.currentDateList.reverse();
            const idx = this.currentDateList.findIndex((item) => {
                return item.id === this.selectedDateId;
            });
            let reportId;
            if (idx != -1) {
                this.selectedDate = this.currentDateList[idx].date;
                this.getReportInfo(this.selectedDateId);
            } else {
                reportId = this.currentDateList[0]?.id;
                if (reportId) {
                    this.selectedDate = this.currentDateList[0].date;
                    this.selectedDateId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedDateId = null;
                    this.hideReport();
                }
            }
        },
        onSelectDate() {
            this.loadReport();
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            })
                .then(async (res) => {
                    this.report = dealReport(res.data.data, REPORT_TYPE);
                    this.getFailureShopFileList();
                    if(!this.report.componentList?.length){
                        const [failedComponentList] = await getDict([10]);
                        this.report.componentList = failedComponentList.map(item=>{
                            return {
                                componentId: item.id,
                                name: item.name,
                                failed: false
                            }
                        })
                    }
                    this.$nextTick(() => {
                        this.showReport = true;
                    });
                })
                .finally(() => {
                    this.showLoading = false;
                });
        },
        onEditReport() {
            this.isEditFailureReport = true;
            this.$nextTick(() => {
                console.log(this.$refs.shopInspectionc.quill)
                this.$refs.shopInspectionc.quill.focus();
            });
        },
        onCancelEditReport() {
            this.isEditFailureReport = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.img-container {
    position: relative;
    height: 100%;
    &:hover {
        img {
            filter: brightness(0.2);
        }
        .img-icon-container {
            opacity: 1;
        }
    }
    .img-icon-container {
        width: 100%;
        height: 20px;
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        opacity: 0;
        .img-icon {
            margin-left: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
    }
}
.failed {
    background-color: #f56c6c !important;
}
.problem {
    background-color: #f56c6c !important;
}

.info-title {
    //   flex: 150px 0 0;
    color: #3c4d73;
}
.basic-info-input {
    border-bottom: 1px solid #ccd3d9;
    //   max-width: 200px;
    flex: 50% 0 0;
}
</style>
<style lang="scss">
.add-popover {
    padding: 10px 15px;
    .add-popover-title {
        font-size: 20px;
        font-weight: 500;
        // margin-bottom: 20px;
    }
    .add-popover-form {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .form-name {
            width: 120px;
            text-align: right;
        }
    }
    .add-popover-btn {
        display: flex;
        justify-content: flex-end;
    }
    .el-calendar-table .el-calendar-day {
        height: 50px !important;
    }
}
</style>
<style lang="scss" scoped>
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>