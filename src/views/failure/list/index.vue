<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>作业列表</span>
            </div>
            <el-form :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="井号" label-width="50px">
                            <el-input @change="initData(true)" v-model="searchForm.wellNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="作业号" label-width="60px">
                            <el-input @change="initData(true)" v-model="searchForm.jobNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="是否有报告" label-width="100px">
                            <el-select @change="initData(true)" v-model="searchForm.filterType" clearable>
                                <el-option value="ONLY_FAILURE_REPORT" label="有失效报告"></el-option>
                                <el-option value="ONLY_MWD_REPORT" label="有维修报告"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 10px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column label="井号" prop="wellNumber" width="160px" fixed="left">
                </el-table-column>
                <el-table-column label="作业号" align="center" prop="jobNumber" fixed="left">
                </el-table-column>
                <el-table-column
                    prop="failureReportDepartmentName"
                    label="提交失效报告部门"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="failureReportUserName"
                    label="提交失效报告人"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="mwdDepartmentName"
                    label="提交维修报告部门"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column label="区块" align="center" prop="blocks">
                </el-table-column>
                <el-table-column label="服务类型" prop="jobType">
                    <template slot-scope="{row}">
                        <span>{{ getJobTypeStr(row.jobType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="总趟数" align="center" prop="totalRun">
                </el-table-column>
                <el-table-column label="失效趟数" align="center" prop="failureRun">
                </el-table-column>
                <el-table-column label="创建日期" align="center" prop="createTime">
                </el-table-column>
                <el-table-column label="开钻日期" align="center" prop="dateIn">
                </el-table-column>
                <el-table-column label="完钻日期" align="center" prop="dateOut">
                </el-table-column>
                <el-table-column label="维修状态" align="center" prop="updateStatus">
                    <template slot-scope="{row}">
                        <el-tag
                            v-if="row.updateStatus" :type="row.updateStatus === 'UPDATED' ? 'success' : 'danger'"
                        >
                            {{
                                row.updateStatus === 'UPDATED' ? "已处理" : "未处理"
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="160px" v-if="p_JobDelete||p_JobView">
                    <template slot-scope="scope">
                        <el-button type="text" @click="onView(scope)" v-if="p_JobView&&scope.row.hasFailureReport">
                            失效报告
                        </el-button>
                        <el-button type="text" @click="onMaintainView(scope)" v-if="p_JobView&&scope.row.hasMwdWorkOrder">
                            维修详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog :visible.sync="isMaintainDetailDialogVisible" width="80%" top="8vh" title="维修详情">
            <el-table
                :data="maintainTableData"
                stripe
                style="margin-top: 10px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column prop="invName" label="品名" width="100" fixed="left"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                    width="160"
                    fixed="left"
                ></el-table-column>
                <el-table-column
                    prop="departmentName"
                    label="提交维修报告部门"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="name"
                    label="创建维修报告人"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="wellNumber"
                    label="井号"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="jobNumber"
                    label="作业号"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="run"
                    label="趟次"
                    align="center"
                    width="50"
                ></el-table-column>
                <el-table-column
                    prop="receiveDate"
                    label="接收日期"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="endDate"
                    label="完成日期"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="failureReportDate"
                    label="失效报告"
                    align="center"
                    width="100"
                >
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            target="_blank" 
                            :to="`/failure/index?jobid=${row.jobId}&reportid=${row.reportId}`" 
                            v-if="p_JobView"
                        >
                            {{ row.failureReportDate }}
                        </router-link>
                        <span v-else>{{ row.failureReportDate }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="findings" label="车间检查发现" width="200">
                </el-table-column>
                <el-table-column prop="repairAction" label="维修措施" width="200">
                </el-table-column>
                <el-table-column
                    prop="mwdNumber"
                    label="维修工单号"
                    align="center"
                    width="160"
                    fixed="right"
                >
                    <template slot-scope="{row}">
                        <router-link 
                            tag="a" 
                            style="color: blue" 
                            target="_blank" 
                            :to="`/mwd-tool-maintain/maintain?mwdId=${row.mwdId}`" 
                            v-if="p_MwdInfoView"
                        >
                            {{ row.mwdNumber }}
                        </router-link>
                        <span v-else>{{ row.mwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    align="center"
                    width="120"
                    fixed="right"
                    v-if="p_MwdInfoView"
                >
                    <template slot-scope="{row}">
                        <el-button @click="onExportReport(row)" type="text">导出维修报告</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="maintainTotal"
                :page-size="10"
                @current-change="onCurrentMaintainChange"
                :current-page="currentMaintainPage"
            ></el-pagination>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetFailureReportList } from "@/api/mwd";
import { apiGetFailInfoV2 } from "@/api/failure";
import { Form as ElForm } from "element-ui/types/element-ui";
import getDict from "@/utils/getDict";
import { apiGetMwdMaintainReport } from "@/api/tool-mantain";

@Component({})
export default class extends Vue {
    private tableData: any[] = [];
    private total = 1;
    private currentPage = 1;
    private jobTypeList:any[] = []
    private searchForm:any = {
        wellNumber:"",
        jobNumber:""
    }
    private maintainTableData: any[] = [];
    private currentMaintainPage = 1;
    private maintainTotal = 1;
    private isMaintainDetailDialogVisible = false;
    get p_JobDelete(){
        return this.$checkBtnPermission('sys:job:delete');
    }
    get p_JobView(){
        return this.$checkBtnPermission('sys:dailydetail:view');
    }
    get p_MwdInfoView(){
        return this.$checkBtnPermission('sys:mwd:info');
    }
    async mounted() {
        this.searchForm.wellNumber = this.$route.query.wellNumber;
        [this.jobTypeList] = await getDict([9]);
        await this.initData();
    }
    private getJobTypeStr(key){
        return this.jobTypeList.find(item=>item.id==key)?.name||""
    }
    handleSelect(){
        this.initData()
    }
    async initData(bool=false) {
        if(bool){
            this.currentPage = 1;
        }
        const form = {
            wellNumber: this.searchForm.wellNumber,
            jobNumber: this.searchForm.jobNumber,
            filterType: this.searchForm.filterType || 'ALL_REPORT',
        }
        apiGetFailureReportList(form,{ current: this.currentPage, size: 10 }).then(res=>{
            this.tableData = res.data?.data?.records || [];
            this.total = res.data.data.total;
        })
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    private onAddWell() {
        this.operType = "ADD";
        this.form = {
            id: undefined,
            wellNumber: "",
            wellCategory: undefined,
            wellType: undefined,
            blocks: "",
            province: "",
            description: "",
        };
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }

    private onEdit(scope: any) {
        this.operType = "EDIT";
        this.form = Object.assign(this.form, scope.row);
        this.showDialog = true;
    }
    private onView(scope:any){
        const { jobType, jobId } = scope.row;
        const basePath = jobType === 8007 ? '/rss' : ''
        this.$router.push(`/failure${basePath}/index?jobid=${jobId}`)

    }
    private onCurrentMaintainChange(currentPage: number) {
        this.currentMaintainPage = currentPage;
        this.getMaintainDetail();
    }
    private async onMaintainView(scope:any){
        this.currentMaintainPage = 1;
        this.detailRow = scope.row;
        await this.getMaintainDetail();
        this.isMaintainDetailDialogVisible = true
    }
    private async getMaintainDetail(){
        await apiGetFailInfoV2(this.detailRow, { current: this.currentMaintainPage, size: 10 }).then(res=>{
            this.maintainTableData = res.data?.data?.records || [];
            this.maintainTotal = res.data.data.total;
        })
    }
    private onExportReport(row){
        const loading = this.$loading({
            lock: true,
            text: "处理中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
        });
        apiGetMwdMaintainReport({ mwdId: row.mwdId })
            .then((res) => {
                this.loadingExportExcelAll = false;
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `${row.deviceTypeStr}_${row.serialNumber}_${row.mwdNumber}_维修报告.docx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            })
            .finally(() => {
                loading.close();
            });
    }
}
</script>
