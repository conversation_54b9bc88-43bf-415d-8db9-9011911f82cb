<template>
    <div class="app-container">
        <div class="tools-container">
            <div
                @click="toPath1"
                class="item"
                style="padding: 20px;"
            >
                <img
                    src="@/assets/images/avatar/Jenkins.png"
                    style="width: 60px; height: 60px;"
                />
                <div class="item-desc">
                    <div class="item-desc-title">
                    Jenkins
                    </div>
                    <div class="item-desc-detail">
                        基于Java开发的持续集成工具
                    </div>
                </div>
            </div>
            <div
                @click="toPath2"
                class="item"
                style="padding: 20px;"
            >
                <img
                    src="@/assets/images/avatar/Wiki.png"
                    style="width: 60px; height: 60px;"
                />
                <div class="item-desc">
                    <div class="item-desc-title">
                        Wiki
                    </div>
                    <div class="item-desc-detail">
                        企业wiki构建系统
                    </div>
                </div>
            </div>
            <div
                @click="toPath3"
                class="item"
                style="padding: 20px;"
            >
                <img
                    src="@/assets/images/avatar/Jira.png"
                    style="width: 60px; height: 60px;"
                />
                <div class="item-desc">
                    <div class="item-desc-title">
                        Jira
                    </div>
                    <div class="item-desc-detail">
                        优秀的问题跟踪管理软件工具
                    </div>
                </div>
            </div>
            <div
                @click="toPath4"
                class="item"
                style="padding: 20px;"
            >
                <img
                    src="@/assets/images/avatar/Gitlab.png"
                    style="width: 60px; height: 60px;"
                />
                <div class="item-desc">
                    <div class="item-desc-title">
                        Gitlab
                    </div>
                    <div class="item-desc-detail">
                        分布式代码托管平台
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
@Component({})
export default class extends Vue {
    private toPath1() {
        window.open("http://************:8085/jenkins/", "_blank");
    }
    private toPath2() {
        window.open("http://************:8090/", "_blank");
    }
    private toPath3() {
        window.open("http://************:8080/", "_blank");
    }
    private toPath4() {
        window.open("http://************:9000/", "_blank");
    }
}
</script>

<style lang="scss" scoped>
.tools-container {
    // width: 50%;
    // background: #cccccc;
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;
    padding-bottom: 10px;

    .item {
        cursor: pointer;
        margin: 10px 10px 0 0;
        // flex: 0 0 32%;
        width: 300px;
        box-shadow: 0 3px 10px 0 rgba(31,35,41,.04);
        border-radius: 5px;
        height: 100px;
        display: flex;
        align-items: center;
        // justify-content: center;
        background: #ffffff;

        // background-clip: content-box;
        .item-icon {
            margin-left: 15px;
            color: #f7b263;
        }
        .item-desc {
            margin-left: 12px;
            font-size: 20px;

            .item-desc-detail {
                font-size: 12px;
                line-height: 18px;
                color: #8f959e;
            }
        }
    }
    .item:hover {
        box-shadow: 0 10px 16px 0 rgba(31,35,41,.18);
    }
}
</style>
