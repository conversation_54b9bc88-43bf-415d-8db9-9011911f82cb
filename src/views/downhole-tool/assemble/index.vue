<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井下工具组装</div>
            </div>
            <div>
                工具类型：{{ toolType }} 序列号：{{ serialNumber }}
                <template v-if="serialNumber">
                    <el-button
                        :type="
                            operateType === 'DISASSEMBLE' ? 'plain' : 'primary'
                        "
                        @click="onClickDisassemble"
                    >
                        拆卸
                    </el-button>
                    <el-button
                        :type="operateType === 'ASSEMBLE' ? 'plain' : 'primary'"
                        @click="onClickAssemble"
                        style="margin-left:10px"
                    >
                        组装
                    </el-button>
                    <el-button
                        :type="
                            operateType === 'ASSEMBLE_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="onClickAssembleHistory"
                    >
                        装配历史
                    </el-button>
                    <el-button
                        :type="
                            operateType === 'SERVICE_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="onClickServiceHistory"
                    >
                        服役历史
                    </el-button>
                    <el-button
                        :type="
                            operateType === 'MAINTAIN_HISTORY'
                                ? 'plain'
                                : 'primary'
                        "
                        @click="onClickMaintainHistory"
                    >
                        维修历史
                    </el-button>
                </template>
            </div>
            <assemble-module
                v-if="
                    toolType &&
                        (operateType === 'DISASSEMBLE' ||
                            operateType === 'ASSEMBLE')
                "
                :name="toolType"
                :operateType="operateType"
                :serialNumber="serialNumber"
            ></assemble-module>
            <tool-assemble-history
                v-if="operateType === 'ASSEMBLE_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-assemble-history>
            <tool-service-history
                v-if="operateType === 'SERVICE_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-service-history>
            <tool-maintain-history
                v-if="operateType === 'MAINTAIN_HISTORY'"
                :name="toolType"
                :serialNumber="serialNumber"
            ></tool-maintain-history>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AssembleModule from "./assemble.vue";
import ToolAssembleHistory from "../tool-assemble-history.vue";
import ToolServiceHistory from "../tool-service-history.vue";
import ToolMaintainHistory from "../tool-maintain-history.vue";
@Component({
    components: {
        AssembleModule,
        ToolAssembleHistory,
        ToolServiceHistory,
        ToolMaintainHistory,
    },
})
export default class extends Vue {
    private toolType = "7LZ172×7.0/5Ⅺ型螺杆钻具";
    private toolTypeList: any[] = [
        "7LZ172×7.0/5Ⅺ型螺杆钻具",
        "5LZ244×7.0/5Ⅷ型螺杆钻具",
        "7LZ172ZD×7.0/5Ⅺ型振荡螺杆钻具",
        "5LZ244×7.0/5Ⅷ型振荡螺杆钻具",
        "172Ⅱ型水力振荡器",
        "其他仪器",
    ];
    private operateType = "DISASSEMBLE"; // ASSEMBLE-组装 | DISASSEMBLE-拆卸 |
    private serialNumber = "test_1";
    private serialNumberList: any[] = ["test_1", "test_2"];
    private onClickAssemble() {
        this.operateType = "ASSEMBLE";
    }
    private onClickDisassemble() {
        this.operateType = "DISASSEMBLE";
    }
    private onClickAssembleHistory() {
        this.operateType = "ASSEMBLE_HISTORY";
    }
    private onClickServiceHistory() {
        this.operateType = "SERVICE_HISTORY";
    }
    private onClickMaintainHistory() {
        this.operateType = "MAINTAIN_HISTORY";
    }
}
</script>
<style lang="scss" scoped>
.module-container {
    margin-top: 20px;
    .module {
        width: 160px;
        height: 160px;
        margin-right: 20px;
        float: left;
        background: red;
        border-radius: 8px;
    }
}
</style>
