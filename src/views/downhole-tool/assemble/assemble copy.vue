<template>
    <div>
        <div style="margin:10px 0">
            {{ name }}——{{ serialNumber || "模板" }}
        </div>
        <div v-if="serialNumber" style="margin-bottom:10px;color:red">
            详情({{ operateType === "LIST" ? "不" : "" }}可编辑)
        </div>
        <el-table :data="template1" border>
            <el-table-column
                v-for="col in computedTemplateCol"
                :prop="col.value"
                :key="col.value"
                :label="col.label"
            >
            </el-table-column>
            <el-table-column
                label="操作"
                v-if="operateType !== 'TEMPLATE'"
                width="120"
            >
                <template slot-scope="scope">
                    <el-button
                        style="padding:0"
                        v-if="
                            operateType === 'DISASSEMBLE' &&
                                (scope.row.serialNumber ||
                                    scope.row.quantity > 0)
                        "
                        type="text"
                        @click="onDisassemble(scope)"
                    >
                        拆卸
                    </el-button>
                    <el-button
                        style="padding:0"
                        v-if="
                            operateType === 'ASSEMBLE' &&
                                !scope.row.serialNumber &&
                                scope.row.trackable
                        "
                        type="text"
                        @click="onSelectPart(scope)"
                    >
                        挑选零件
                    </el-button>
                    <el-button
                        style="padding:0"
                        v-if="
                            operateType === 'ASSEMBLE' &&
                                !scope.row.trackable &&
                                scope.row.quantity < 1
                        "
                        type="text"
                        @click="onAssembleUntrackblePart(scope)"
                    >
                        直接组装
                    </el-button>
                    <el-button
                        style="padding:0"
                        v-if="
                            operateType !== 'ASSEMBLE' && scope.row.serialNumber
                        "
                        type="text"
                        @click="onDetail(scope)"
                    >
                        零件详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <detail-dialog ref="DetailDialog"></detail-dialog>
        <part-list-dialog ref="PartListDialog"></part-list-dialog>
    </div>
</template>
<script lang="ts">
import { template1, templateCol } from "../template/template";
import { Component, Prop, Vue } from "vue-property-decorator";
import ScrewTemplate from "../template/screw.vue";
import DetailDialog from "../part-detail/index.vue";
import PartListDialog from "./partList.vue";
@Component({ components: { ScrewTemplate, DetailDialog, PartListDialog } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "DETAIL" }) private operateType!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private template1 = template1;
    private templateCol = templateCol;
    private toolType = "";
    private toolTypeList: any[] = ["螺杆", "水力振荡器", "其他"];
    private serialNumberList: any[] = ["test_1", "test_2"];
    get computedTemplateCol() {
        return this.templateCol.filter(
            (item) =>
                this.operateType !== "TEMPLATE" || item.value !== "serialNumber"
        );
    }
    private onDetail(scope: any) {
        (this.$refs.DetailDialog as any).showDialog();
    }
    private onAssemble(scope: any) {}
    private onDisassemble(scope: any) {}
    private onSelectPart() {
        (this.$refs.PartListDialog as any).showDialog();
    }
    private onAssembleUntrackblePart(scope: any) {}
}
</script>
