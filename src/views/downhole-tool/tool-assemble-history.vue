<template>
    <div>
        <div style="margin:10px 0">
            {{ name }}——{{ serialNumber }}——装配历史
        </div>
        <el-table :data="toolList" border>
            <el-table-column
                v-for="item in toolAssembleHistory"
                :key="item"
                :label="item"
            >
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="onClickDetail(scope)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { toolAssembleHistory } from "./field";
@Component({ components: {} })
export default class extends Vue {
    private toolAssembleHistory = toolAssembleHistory;
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private toolList: any[] = [{}];
    private onClickDetail(scope: any) {}
}
</script>
