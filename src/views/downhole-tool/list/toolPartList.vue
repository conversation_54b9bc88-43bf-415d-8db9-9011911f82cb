<template>
    <div>
        <div style="margin:10px 0">
            {{ name }}——{{ serialNumber || "模板" }}
        </div>
        <div v-if="serialNumber" style="margin-bottom:10px;color:red">
            详情(不可编辑)
        </div>
        <el-table :data="template1" border>
            <el-table-column
                v-for="col in computedTemplateCol"
                :prop="col.value"
                :key="col.value"
                :label="col.label"
            >
            </el-table-column>
            <el-table-column
                label="操作"
                v-if="operateType !== 'TEMPLATE'"
                width="120"
            >
                <template slot-scope="scope">
                    <el-button
                        style="padding:0"
                        v-if="scope.row.serialNumber"
                        type="text"
                        @click="onDetail(scope)"
                    >
                        零件详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <detail-dialog ref="DetailDialog"></detail-dialog>
    </div>
</template>
<script lang="ts">
import { template1, templateCol } from "../template/template";
import { Component, Prop, Vue } from "vue-property-decorator";
import DetailDialog from "../part-detail/index.vue";
@Component({ components: { DetailDialog } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "DETAIL" }) private operateType!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private template1 = template1;
    private templateCol = templateCol;
    get computedTemplateCol() {
        return this.templateCol.filter(
            (item) =>
                this.operateType !== "TEMPLATE" || item.value !== "serialNumber"
        );
    }
    private onDetail(scope: any) {
        (this.$refs.DetailDialog as any).showDialog();
    }
}
</script>
