<template>
    <div style="width:100%">
        <div class="title">7LZ172×7.0/5Ⅺ型螺杆钻具明细表</div>
        <el-table :data="screwTemplate" border="">
            <el-table-column prop="name" label="产品名称"> </el-table-column>
            <el-table-column prop="value" label="序列号">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.value }}
                    </span>
                    <el-input v-else v-model="scope.row.value"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEdit(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDelete(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSave(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancel(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({ name: "ScrewTemplate" })
export default class extends Vue {
    private preObject: any = {};
    private editObject = { editRow: false, editRowIndex: -1 };
    private screwTemplate = [
        {
            name: "name1",
            value: "value1",
        },
        {
            name: "name2",
            value: "value2",
        },
        {
            name: "name3",
            value: "value3",
        },
    ];
    private onEdit(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDelete(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.materialRequestDetailList.splice(scope.$index, 1);
    }

    private onSave(scope: any) {
        console.log(scope);
        // 必须有产品名和数量
        if (!scope.row.invName) {
            this.$message.error("请填写产品名称");
            return;
        }
        if (!scope.row.quantity || scope.row.quantity == 0) {
            this.$message.error("请填写正确的数量");
            return;
        }

        const invCodeItemList = this.screwTemplate.filter(
            (item: any) => item.invCode === scope.row.invCode
        );
        if (invCodeItemList.length > 1) {
            this.$message.error("有重复的存货编码");
            return;
        }
        scope.row.serialNumberList = [];
        scope.row.stockIdList.forEach((stockId: any) => {
            let tmp = this.serialNumberList.find(
                (item) => item.stockId == stockId
            );
            scope.row.serialNumberList.push(tmp.serialNumber);
        });
        this.editObject.editRowIndex = -1;
    }

    private onCancel(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.screwTemplate[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.screwTemplate.pop();
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss" scoped>
.module-container {
    margin-top: 20px;
    .module {
        width: 160px;
        height: 160px;
        margin-right: 20px;
        float: left;
        background: red;
        border-radius: 8px;
    }
}
</style>
