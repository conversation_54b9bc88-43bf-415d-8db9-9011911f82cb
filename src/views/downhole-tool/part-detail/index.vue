<template>
    <el-dialog
        custom-class="part-detail-dialog"
        title="零件详情"
        :visible.sync="isDialogVisible"
    >
        <part-detail></part-detail>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import PartDetail from "./partDetail.vue";
@Component({ components: { PartDetail } })
export default class extends Vue {
    @Prop({ default: "" }) private name!: String;
    private isDialogVisible = false;
    showDialog() {
        this.isDialogVisible = true;
    }
}
</script>
<style lang="scss">
.part-detail-dialog {
    .el-dialog__body {
        padding: 5px 20px;
    }
}
</style>
