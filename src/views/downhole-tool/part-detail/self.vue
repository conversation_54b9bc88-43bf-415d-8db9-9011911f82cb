<template>
    <div>
        <el-row>
            <el-col :span="8" v-for="item in partAttr" :key="item">
                {{ item }}：
            </el-col>
        </el-row>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { partAttr } from "../field";
@Component({ components: {} })
export default class extends Vue {
    private partAttr = partAttr;
    @Prop({ default: "" }) private name!: String;
    @Prop({ default: "" }) private serialNumber!: String;
    private toolList: any[] = [];
}
</script>
