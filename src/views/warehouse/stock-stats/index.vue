<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="loading">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                <span>库存统计</span>
                <!-- <el-button style="float:right" type="primary" @click="onExport">
                    导出Excel
                </el-button> -->
            </div>
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 20px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column label="库位类型" prop="stockType">
                    <template slot-scope="{row}">
                        {{getStockTypeStr(row.stockType)}}
                    </template>
                </el-table-column>

                <el-table-column label="车间可用" prop="ready">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/warehouse/stock?stock=${row.stockType}&status=19003`"
                            style="cursor: pointer; color: blue"
                        >
                            {{ row.ready }}
                        </router-link>
                    </template>
                </el-table-column>
                <el-table-column label="上井" prop="atRig">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/warehouse/stock?stock=${row.stockType}&status=19001`"
                            style="cursor: pointer; color: blue"
                        >
                            {{ row.atRig }}
                        </router-link>
                    </template>
                </el-table-column>

                <el-table-column label="维修" prop="repair">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/warehouse/stock?stock=${row.stockType}&status=19002`"
                            style="cursor: pointer; color: blue"
                        >
                            {{ row.repair }}
                        </router-link>
                    </template>
                </el-table-column>
                <el-table-column label="其它" prop="other">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/warehouse/stock?stock=${row.stockType}`"
                            style="cursor: pointer; color: blue"
                        >
                            {{ row.other }}
                        </router-link>
                    </template>
                </el-table-column>
                <el-table-column label="总计" prop="total">
                    <template slot-scope="{ row }">
                        <router-link
                            tag="a"
                            :to="`/warehouse/stock?stock=${row.stockType}`"
                            style="cursor: pointer; color: blue"
                        >
                            {{ row.total }}
                        </router-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { apiGetStockStats } from "@/api/warehouse";
import { tabsList } from "../stock/options.js"
export default {
    data() {
        return {
            loading: false,
            tableData: [],
        };
    },
    async mounted() {
        this.initData();
    },
    methods: {
        onExport(){
            this.loading = true;
            apiExportWarehouse({}).then(res=>{
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `仪器盘点.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                this.loading = false;
            })
        },
        getStockTypeStr(type){
            return tabsList.find(item=>item.stockType == type)?.label || ""
        },
        initData() {
            apiGetStockStats({}).then((res) => {
                this.tableData = res.data?.data || [];
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.el-dropdown-link {
    color: blue;
    cursor: pointer;
}
.switch-dropdown {
    .active {
        color: blue;
    }
    svg {
        margin-right: 6px;
    }
}
</style>
