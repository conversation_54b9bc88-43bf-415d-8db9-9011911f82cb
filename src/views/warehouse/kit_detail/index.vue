<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                MWD LOAD OUT LIST - 详情
                <span style="float: right;">
                    <el-button type="primary">导出评估单</el-button>
                    <el-button type="primary">保存并同步库存数据</el-button>
                    <el-button type="primary">保存</el-button>
                </span>
            </div>
            <el-form :model="searchForm" label-width="50px" style="margin-top: 20px;">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="KIT箱" label-width="50px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.kitNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="井号(或地点)" label-width="100px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.wellNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业号" label-width="100px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.jobNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入库日期" label-width="70px">
                            <el-date-picker
                                v-model="searchForm.startDate"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出库日期" label-width="70px">
                            <el-date-picker
                                v-model="searchForm.startDate"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.kitNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList />
        </div>
    </div>
</template>
<script>
import LoadOutList from '../components/LoadOutList.vue';
export default {
    name: 'WarehouseKitOut',
    components: { LoadOutList },
    data(){
        return {
            searchForm: {},
        }
    },
    methods: {
        initData(flag){},
    }
}
</script>
<style lang="scss">
</style>