<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                <span>{{ title }}</span>
                <span style="float: right;">
                    <el-popover placement="bottom" trigger="click" width="auto">
                        <div>
                            <div v-for="item in repairList" :key="item.repairId">
                                <router-link
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank"
                                    :to="`/mwd/repair/detail?id=${item.repairId}`"
                                >
                                    {{ item.repairCode }}
                                </router-link>
                            </div>
                        </div>
                        <el-button type="primary" slot="reference" v-show="repairList.length" style="margin-right: 10px;">返修单</el-button>
                    </el-popover>
                    <el-button @click="onClickExportLoadInfo" type="primary">导出清单</el-button>
                    <!-- <el-button @click="onClickExportEvaluation" type="primary" v-if="loadType==='OUT'">导出评估单</el-button> -->
                    <el-button @click="onSaveAndSync" type="primary">保存并同步库存数据</el-button>
                </span>
            </div>
            <el-form :model="loadOutForm" label-width="50px" style="margin-top: 20px;">
                <el-row :gutter="20">
                    <el-col :span="5">
                        <el-form-item label="旋导箱" label-width="60px">
                            <el-input disabled style="width: calc(100%)" clearable v-model="loadOutForm.serialNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="井号(或地点)" label-width="100px">
                            <!-- <el-input style="width: calc(100%)" clearable v-model="loadOutForm.location">
                            </el-input> -->
                            <FuzzyWellInput v-model="loadOutForm.location" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" v-if="loadType==='IN'">
                        <el-form-item label="作业号" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.jobNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" v-if="loadType==='IN'">
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.inCheckMember">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5" v-else>
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.outCheckMember">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="入库日期" label-width="70px" v-if="loadType==='IN'">
                            <el-date-picker
                                v-model="loadOutForm.dateIn"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="出库日期" label-width="70px" v-if="loadType==='OUT'">
                            <el-date-picker
                                v-model="loadOutForm.dateOut"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList :relatedRepairDetailList="relatedRepairDetailList" :loadType="loadType" :items="loadOutForm.items" />
            <SyncTable @update="onSave" :loadType="loadType" :lucidaStatus="lucidaStatus" :loadOutForm="loadOutForm" ref="syncTableRef" />
        </div>
    </div>
</template>
<script>
import { loadTypeMap } from '@/utils/constant.warehouse';
import LoadOutList from './components/LoadOutList.vue';
import SyncTable from './components/SyncTable.vue';
import { apiExportEvaluation, apiExportLucidaLoadOutInfo, apiGetLucidaLoadOutInfo, apiUpdateLucidaLoadOut, apiAddLucidaLoadOut } from '@/api/warehouse';
import FuzzyWellInput from "@/components/FuzzyWellInput/index.vue"
import { apiGetRepairInfo, apiGetRepairList } from '@/api/repair';
export default {
    name: 'WarehouseLucidaLoad',
    components: { LoadOutList, SyncTable, FuzzyWellInput },
    data(){
        return {
            loadType: "IN", // IN OUT SET
            lucidaStatus: null,
            loadOutForm: {
                serialNumber: null,
                inCheckMember: null,
                outCheckMember: null,
                dateOut: null,
                dateIn: null,
                location: null,
                wellNumber: null,
                items: [],
            },
            repairList: [],
            relatedRepairDetailList: []
        }
    },
    computed: {
        title(){
            switch(this.loadType){
                case 'IN':
                    return 'T-Well Link 675 LOAD OUT LIST - 入库'
                case 'OUT':
                    return 'T-Well Link 675 LOAD OUT LIST - 出库'
                case 'SET':
                    return 'T-Well Link 675 LOAD OUT LIST - 配置'
            }
        }
    },
    async mounted(){        
        let { id: loadOutId } = this.$route.query;
        if(loadOutId){
            this.loadOutId = loadOutId;
            await this.getLoadOutInfo();
            this.lucidaStatus = this.loadOutForm.status;
            this.loadType = loadTypeMap[this.loadOutForm.status].loadType;
            if(this.loadType==='SET'){
                // this.loadOutForm.items.forEach(item=>{
                //     item.units.forEach(unit=>{
                //         unit.kitNumber = this.loadOutForm.kitNumber;
                //     })
                // })
            }
            if(this.loadType==='IN'){
                this.getRepairList();
                this.loadOutForm.dateIn = new Date().Format("yyyy-MM-dd");
                this.loadOutForm.items.forEach(item=>{
                    item.mergedUnits.forEach(unit=>{
                        if(!unit.loadInAmount || unit.loadInAmount==="0"){
                            unit.loadInAmount = unit.actualAmount;
                        }
                        if(unit.deviceTypeList&&unit.serialNumberList){
                            unit.loadInAmount = unit.serialNumberList.length;
                        }
                    })
                })
            }
            if(this.loadType === 'OUT'){
                this.loadOutForm.dateOut = new Date().Format("yyyy-MM-dd");
                this.loadOutForm.items.forEach(item=>{
                    item.mergedUnits.forEach(unit=>{
                        // 有序列号的实发数量至少是1
                        if(unit.serialNumber&&!unit.actualAmount&&unit.actualAmount!=0){
                            unit.actualAmount = 1;
                        }
                    })
                })
            }
            // this.loadOutForm.location = this.loadOutForm.location || this.loadOutForm.wellNumber;
        }
    },
    methods: {
        getLoadOutInfo(){
            return apiGetLucidaLoadOutInfo({loadOutId: this.loadOutId}).then(res=>{
                this.loadOutForm = res.data.data;
                // NOTE: 编辑全部在mergeUnits中操作, 保存要拆行成units, 同步时使用的也是units
                this.mergeDetail();
            })
        },
        mergeDetail(){
            this.loadOutForm.items.forEach(item=>{
                const typeMap = new Map();
                item.units.forEach(unit => {
                    if (!typeMap.has(unit.unitName)) {
                        typeMap.set(unit.unitName, { initList: [], serialNumberList: [], requireAmount: 0, actualAmount: 0, loadInAmount: 0 });
                    }
                    if(unit.deviceTypeList){
                        if(unit.serialNumber){
                            typeMap.get(unit.unitName).initList.push({serialNumber: unit.serialNumber});
                            typeMap.get(unit.unitName).serialNumberList.push(unit.serialNumber);
                            typeMap.get(unit.unitName).actualAmount ++;
                        }
                    }else{
                        typeMap.get(unit.unitName).actualAmount += unit.actualAmount;
                    }
                    typeMap.get(unit.unitName).requireAmount += unit.requireAmount;
                    typeMap.get(unit.unitName).loadInAmount += unit.loadInAmount;
                });
                const result = [];
                const seenTypes = new Set();
                item.units.forEach(unit => {
                    if (!seenTypes.has(unit.unitName)) {
                        result.push({ ...unit, ...typeMap.get(unit.unitName) });
                        seenTypes.add(unit.unitName);
                    }
                });
                item.mergedUnits = result;
            })
        },
        splitDetail(){
            this.loadOutForm.items.forEach(item=>{
                const splitUnits = [];
                item.mergedUnits.forEach(unit=>{
                    // 要拆成的数量为需求数量requireAmount
                    const requireAmount = unit.requireAmount;
                    const row = Math.max(requireAmount, unit.serialNumberList?.length || 0); // 实发数量可能会比需求量大
                    let templateId = 1;
                    let units = [{...unit}];
                    if(unit.deviceTypeList)
                        units = Array.from({length: row}).map((_, index)=>{
                            const serialNumber = unit.serialNumberList[index] || null;
                            let loadInAmount = null;
                            if(this.loadType === 'IN'){
                                // 平摊给最前
                                if(unit.deviceTypeList){
                                    if(unit.loadInAmount > index){
                                        loadInAmount = 1;
                                    }else{
                                        loadInAmount = 0;
                                    }
                                }else{
                                    loadInAmount = unit.loadInAmount;
                                }
                            }
                            return {
                                ...unit,
                                templateId: templateId++,
                                serialNumber,
                                requireAmount: requireAmount > index ? 1 : 0, //保证需求数量不会被动改变
                                actualAmount: serialNumber ? 1 : 0,
                                loadInAmount
                            }
                        })
                    splitUnits.push(...units);
                })
                item.units = splitUnits;
            })
        },
        getRepairList(){
            const wellNumber = this.loadOutForm.location;
            if(wellNumber){
                apiGetRepairList({ wellNumber }, { current: 1, size: 999 }).then(async res=>{
                    const data = res.data?.data?.records || [];
                    this.repairList = data.filter(item=>item.wellNumber===wellNumber);
                    this.relatedRepairDetailList = (await Promise.all(data.map(item=>apiGetRepairInfo({repairId: item.repairId}).then(res=>res.data.data.repairDetailList)))).flat();                    
                })
            }
        },
        onSync(){

        },
        async onSaveAndSync(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            // 要先save获取每个deviceId
            try{
                await this.onSave();
                const deviceIdList = this.loadOutForm.items.reduce((acc, item)=>{
                    return [
                        ...acc,
                        ...item.units
                            .filter(unit=>unit.deviceId&&!this.relatedRepairDetailList.find(detail=>detail.serialNumber==unit.serialNumber))
                            .map(unit=>unit.deviceId)
                        ]
                }, []);
                const lucidaId = this.loadOutForm.deviceId;
                await this.$refs.syncTableRef.showDialog([lucidaId, ...deviceIdList]);
            }finally{
                loading.close();
            }
        },
        async onSave(newLucidaStatus){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            try{
                this.splitDetail();
                await apiUpdateLucidaLoadOut({...this.loadOutForm, status: newLucidaStatus || undefined}).then(async ()=>{
                    if(newLucidaStatus){
                        const needInit = loadTypeMap[this.lucidaStatus].needCreate;
                        if(needInit){
                            await apiAddLucidaLoadOut({serialNumber: this.loadOutForm.serialNumber})
                        }
                        this.$router.push("/warehouse/stock")
                    }else{
                        await this.getLoadOutInfo();
                    }
                });
            }finally{
                loading.close();
            }
        },
        onClickExportEvaluation(){
            this.$confirm("确认导出风险评估单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(()=>{
                this.onExportEvaluation();
            })
        },
        onExportEvaluation(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiExportEvaluation({loadOutId: this.loadOutForm.loadOutId}).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `风险评估单 - ${this.loadOutForm.serialNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        },
        onClickExportLoadInfo(){
            this.$confirm("确认导出当前Load Out清单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(()=>{
                this.onExportLoadInfo();
            })
        },
        onExportLoadInfo(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiExportLucidaLoadOutInfo({loadOutId: this.loadOutForm.loadOutId}).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `T-Well Link 675 LOAD OUT LIST - ${this.loadOutForm.serialNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        }
    }
}
</script>
<style lang="scss">
</style>