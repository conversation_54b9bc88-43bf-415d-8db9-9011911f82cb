<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                <span>{{ title }}</span>
                <span style="float: right;">
                    <el-button @click="onRevise" type="primary" v-if="loadOutForm.dateOut && !loadOutForm.dateIn" style="margin-right: 10px;">补发</el-button>
                    <el-popover placement="bottom" trigger="click" width="auto">
                        <div>
                            <div v-for="item in repairList" :key="item.repairId">
                                <router-link
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank"
                                    :to="`/mwd/repair/detail?id=${item.repairId}`"
                                >
                                    {{ item.repairCode }}
                                </router-link>
                            </div>
                        </div>
                        <el-button type="primary" slot="reference" v-show="repairList.length" style="margin-right: 10px;">返修单</el-button>
                    </el-popover>
                    <el-button @click="onClickExportLoadInfo" type="primary">导出清单</el-button>
                </span>
            </div>
            <el-form :model="loadOutForm" label-position="left" label-width="100px" class="less-margin-form">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="旋导箱">
                            <span>{{ loadOutForm.serialNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="井号(或地点)">
                            <span>{{ loadOutForm.location }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业号">
                            <span>{{ loadOutForm.jobNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入库日期">
                            <span>{{ loadOutForm.dateIn }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入库清点人员">
                            <span>{{ loadOutForm.inCheckMember }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出库日期">
                            <span>{{ loadOutForm.dateOut }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出库清点人员">
                            <span>{{ loadOutForm.outCheckMember }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList :relatedRepairDetailList="relatedRepairDetailList" :items="loadOutForm.items" />
        </div>
    </div>
</template>
<script>
import LoadOutList from './components/LoadOutList.vue';
import { apiGetLucidaLoadOutInfo, apiExportLucidaLoadOutInfo } from '@/api/warehouse';
import { apiGetRepairInfo, apiGetRepairList } from '@/api/repair';
export default {
    name: 'WarehouseLucidaLoadDetail',
    components: { LoadOutList },
    data(){
        return {
            loadType: "IN", // IN OUT SET
            loadOutForm: {
                serialNumber: null,
                inCheckMember: null,
                outCheckMember: null,
                dateOut: null,
                dateIn: null,
                location: null,
                wellNumber: null,
                items: [],
            },
            repairList: [],
            relatedRepairDetailList: [],
        }
    },
    computed: {
        title(){
            return "T-Well Link 675 LOAD OUT LIST"
        }
    },
    async mounted(){        
        let { id: loadOutId } = this.$route.query;
        if(loadOutId){
            this.loadOutId = loadOutId;
            await this.getLucidaLoadOutInfo();
            this.getRepairList();
        }
    },
    methods: {
        getLucidaLoadOutInfo(){
            return apiGetLucidaLoadOutInfo({loadOutId: this.loadOutId}).then(res=>{
                this.loadOutForm = res.data.data;
                this.mergeDetail();
            })
        },
        onClickExportLoadInfo(){
            this.$confirm("确认导出当前Load Out清单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(()=>{
                this.onExportLoadInfo();
            })
        },
        onRevise(){
            window.open(`/warehouse/twelllink/load/revise?id=${this.loadOutId}`, '_blank');
        },
        onExportLoadInfo(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiExportLucidaLoadOutInfo({loadOutId: this.loadOutForm.loadOutId}).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `T-Well Link 675 LOAD OUT LIST - ${this.loadOutForm.serialNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        },
        mergeDetail(){
            this.loadOutForm.items.forEach(item=>{
                const typeMap = new Map();
                item.units.forEach(unit => {
                    if (!typeMap.has(unit.unitName)) {
                        typeMap.set(unit.unitName, { initList: [], serialNumberList: [], requireAmount: 0, actualAmount: 0, loadInAmount: 0 });
                    }
                    if(unit.deviceTypeList){
                        if(unit.serialNumber){
                            typeMap.get(unit.unitName).initList.push({serialNumber: unit.serialNumber});
                            typeMap.get(unit.unitName).serialNumberList.push(unit.serialNumber);
                            typeMap.get(unit.unitName).actualAmount ++;
                        }
                    }else{
                        typeMap.get(unit.unitName).actualAmount += unit.actualAmount;
                    }
                    typeMap.get(unit.unitName).requireAmount += unit.requireAmount;
                    typeMap.get(unit.unitName).loadInAmount += unit.loadInAmount;
                });
                const result = [];
                const seenTypes = new Set();
                item.units.forEach(unit => {
                    if (!seenTypes.has(unit.unitName)) {
                        result.push({ ...unit, ...typeMap.get(unit.unitName) });
                        seenTypes.add(unit.unitName);
                    }
                });
                item.mergedUnits = result;
            })
        },
        getRepairList(){
            const wellNumber = this.loadOutForm.location;
            if(wellNumber){
                apiGetRepairList({ wellNumber }, { current: 1, size: 999 }).then(async res=>{
                    const data = res.data?.data?.records || [];
                    this.repairList = data.filter(item=>item.wellNumber===wellNumber);
                    this.relatedRepairDetailList = (await Promise.all(data.map(item=>apiGetRepairInfo({repairId: item.repairId}).then(res=>res.data.data.repairDetailList)))).flat();                    
                })
            }
        },
    }
}
</script>
<style lang="scss">
.less-margin-form{
    .el-form-item{
        margin-bottom: 6px;
    }
}
</style>