<template>
    <div class="load-out-list">
        <el-table :data="[]" class="no-empty-text-table" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="零件号" width="300px"></el-table-column>
            <el-table-column label="名称"></el-table-column>
            <el-table-column label="序列号" width="200px"></el-table-column>
            <el-table-column label="需求数量" align="center" width="100px"></el-table-column>
            <el-table-column :label="actualAmountLabel" align="center" width="100px"></el-table-column>
            <el-table-column label="备注" align="center" width="200px"></el-table-column>
        </el-table>
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <div v-for="item in items" :key="item.typeName">
                <div style="text-align: center; height: 30px; line-height: 30px; color: #1f2d3d; font-weight: bold; background-color: #f0f2f5;">
                    {{ item.typeName }}
                </div>
                <el-table :data="item.mergedUnits" :show-header="false">
                    <el-table-column prop="unitNumber" width="300px"></el-table-column>
                    <el-table-column prop="unitName"></el-table-column>
                    <el-table-column prop="serialNumber" width="200px">
                        <template slot-scope="scope">
                            <DisableFuzzySelect
                                @change="onSnChange(scope.row)"
                                :restLazy="true"
                                v-if="canRevise&&scope.row.deviceTypeList"
                                :restParams="{deviceTypeListStr: scope.row.deviceTypeList}"
                                v-model="scope.row.serialNumberList"
                                type="DEVICE_INFO"
                                :init-list="scope.row.initList"
                                multiple
                                :disabledList="disabledDeviceTypeSnMap[scope.row.deviceTypeList]"
                            >
                                <template slot-scope="{item}">
                                    <div>
                                        {{ item.serialNumber }}
                                        <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="item.status"></CustomTag>
                                        <CustomTag tagType="RISK_TYPE" :tagValue="item.riskType"></CustomTag>
                                    </div>
                                </template>
                            </DisableFuzzySelect>
                            <template v-else>
                                <span v-for="(item,key) in scope.row.serialNumberList" :key="item">
                                    <span>{{ item }}</span>
                                    <span v-if="key<scope.row.serialNumberList.length-1">,</span>
                                </span>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column prop="requireAmount" align="center" width="100px">
                        <template slot-scope="scope">
                            <span>{{ scope.row.requireAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="actualAmount"  align="center" width="100px">
                        <template slot-scope="scope">
                            <el-input v-if="canRevise&&!scope.row.deviceTypeList" class="center-input" size="small" v-model="scope.row.actualAmount" style="width: 100%;"></el-input>
                            <span v-else>{{ scope.row.actualAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="note" align="center" width="200px">
                        <template slot-scope="scope">
                            <el-input
                                v-if="canRevise"
                                size="small"
                                v-model="scope.row.note"
                                style="width: 100%;"
                                :placeholder="specialUnitName.includes(scope.row.unitName) ? '序列号请用逗号隔开' : ''"
                            ></el-input>
                            <span v-else>{{ scope.row.note }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-scrollbar>
    </div>
</template>
<script>
import DisableFuzzySelect from '@/components/DisableFuzzySelect';
const specialUnitName = ['螺杆', '震击器', '钻头']
export default {
    name: 'LoadOutList',
    components: { DisableFuzzySelect },
    props: {
        items: {
            type: Array,
            default: () => []
        },
        relatedRepairDetailList: {
            type: Array,
            default: () => []
        },
        disabledDeviceTypeSnMap: {
            type: Object,
            default: () => []
        },
        canRevise: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            specialUnitName
        }
    },
    computed:{
        actualAmountLabel(){
            return '实发数量'
        }
    },
    methods: {
        onSnChange(row){
            row.actualAmount = row.serialNumberList ? row.serialNumberList.length : null;
        },
        getRepairInfoBySerialNumber(serialNumber){
            return this.relatedRepairDetailList.find(item => item.serialNumber === serialNumber)
        }
    }
}
</script>
<style lang="scss">
.no-empty-text-table{
    .el-table__empty-block{
        display: none;
    }
}

.load-out-list{
    min-width: 1100px;
    .el-scrollbar {
        .el-scrollbar__wrap{
            overflow-x: auto;
            max-height: calc(100vh - 280px); // 最大高度
            
        }
    }
}
</style>
<style lang="scss" scoped>

::v-deep(.center-input .el-input__inner ){
    text-align: center;
}
</style>