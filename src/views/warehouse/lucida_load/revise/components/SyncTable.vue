<template>
    <el-dialog :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="invName" label="品名" width="100px"></el-table-column>
            <el-table-column align="center" prop="serialNumber" label="序列号" width="200px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.receivedSendDate"
                        style="width: calc(100%)"
                        type="date"
                        placeholder=""
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在BOX" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiBatchUpdateWarehouseDeviceList, apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import { CIRCULATE_TYPE } from '@/utils/constant.warehouse';
import getDict from '@/utils/getDict';
export default {
    name: "SyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: []
        }
    },
    props: {
        loadType: {
            type: String,
            default: 'OUT'
        },
        lucidaStatus: Number,
        loadOutForm: {
            type: Object,
            default: ()=>{}
        }
    },
    methods: {
        async showDialog(deviceIdList) {
            [this.deviceStatusList] = await getDict([20]);
            await apiGetWarehouseDeviceBatchInfo({deviceIdList}).then(res=>{
                this.tableData = (res.data.data || []);
                const targetLocation = this.loadOutForm.location;
                this.tableData.forEach((item)=>{
                    item.status = 19001;
                    item.receivedSendDate = new Date().Format("yyyy-MM-dd");
                    item.location = targetLocation;
                    item.kitNumber = this.loadOutForm.serialNumber;
                })
                this.isDialogVisible = true;
            })
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            this.syncData();
        },
        syncData(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiBatchUpdateWarehouseDeviceList({
                relatedBoxId: this.loadOutForm.deviceId,
                deviceList: this.tableData,
                circulateType: CIRCULATE_TYPE.WAREHOUSE,
                businessId: this.loadOutForm.loadOutId,
            }).then(() => {
                this.$emit("update");
                this.isDialogVisible = false;
            }).finally(()=>{
                loading.close();
            })
        }
    }
}
</script>
