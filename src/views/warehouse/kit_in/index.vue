<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                MWD LOAD OUT LIST - 入库
                <span style="float: right;">
                    <el-button @click="onSave" type="primary">保存并同步库存数据</el-button>
                </span>
            </div>
            <el-form :model="loadOutForm" label-width="50px" style="margin-top: 20px;">
                <el-row :gutter="20">
                    <el-col :span="5">
                        <el-form-item label="KIT箱" label-width="50px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.serialNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="井号(或地点)" label-width="100px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.location">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="作业号" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.jobNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.checkMember">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="入库日期" label-width="70px">
                            <el-date-picker
                                v-model="loadOutForm.dateIn"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList type="IN" :loadOutDetailList="loadOutForm.loadOutDetailList" />
            <SyncTable type="IN" ref="syncTableRef" :loadOutForm="loadOutForm" />
        </div>
    </div>
</template>
<script>
import LoadOutList from '../components/LoadOutList.vue';
import SyncTable from '../components/SyncTable.vue';
import { apiGetLoadOutInfo, apiKitIn } from '@/api/warehouse';
export default {
    name: 'WarehouseKitOut',
    components: { LoadOutList, SyncTable },
    data(){
        return {
            loadOutForm: {
                serialNumber: null,
                checkMember: null,
                dateOut: null,
                dateIn: null,
                location: null,
                wellNumber: null,
                loadOutDetailList: []
            },
        }
    },
    mounted(){
        let loadOutId = this.$route.query.id;
        if(loadOutId){
            apiGetLoadOutInfo({loadOutId}).then(res=>{
                this.loadOutForm = res.data.data
            })
        }
    },
    methods: {
        onUpdateLoadOutList(){
            apiKitIn(this.loadOutForm);
        },
        onSync(){

        },
        onSave(){
            apiKitIn(this.loadOutForm);
            this.$refs.syncTableRef.showDialog();
        }
    }
}
</script>
<style lang="scss">
</style>