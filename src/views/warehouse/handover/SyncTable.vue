<template>
    <el-dialog :visible.sync="isDialogVisible" title="仪器交接详情" width="80vw">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="serialNumber" label="序列号" width="200px">
                <template slot-scope="scope">
                    <span>{{ scope.row.serialNumber }}</span>
                    <div v-if="scope.row.isValid==='invalid'" style="color:red;font-size:10px;">无效序列号</div>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="invName" label="品名" width="120px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%" v-if="scope.row.isValid==='valid'">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.receivedSendDate"
                        v-if="scope.row.isValid==='valid'"
                        style="width: calc(100%)"
                        type="date"
                        placeholder=""
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在BOX" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDate" label="完工日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)&&scope.row.isValid==='valid'"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDate"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDays" label="完工时间" width="100px">
                <template slot-scope="scope">
                    <el-input v-if="isBattery(scope.row)&&scope.row.isValid==='valid'" v-model="scope.row.finishDays" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
        </el-table>

        <template #footer>
            <el-button @click="onCancel">取 消</el-button>
            <el-button type="primary" @click="onConfirm">确 定</el-button>
        </template>
    </el-dialog>
</template>
<script>
import getDict from '@/utils/getDict';
import { riskTypeList } from '@/utils/constant';
import { apiReceiveHandover } from '@/api/handover';
import { apiBatchUpdateWarehouseDeviceList, apiCheckSerialNumberList } from '@/api/warehouse';
import { CIRCULATE_TYPE } from '@/utils/constant.warehouse';
const READY_STATUS = 19003;
export default{
    name: "HandoverDialog",
    data(){
        return {
            isDialogVisible: false,
            tableData: [],
            deviceStatusList: [],
            riskTypeList
        }
    },
    methods: {
        isBattery(item){
            return item.deviceType == 16004
        },
        async getDictData(){
            [this.deviceStatusList] = await getDict([20]);
        },
        async showDialog(handoverList){
            this.handoverList = handoverList;
            await apiCheckSerialNumberList({serialNumberList:handoverList.map(item=>item.serialNumber)}).then(res=>{
                this.tableData = (res.data.data || []);
                this.tableData.forEach((item, index)=>{
                    item.status = READY_STATUS;
                    item.receivedSendDate = new Date().Format("yyyy-MM-dd");
                    item.handoverId = handoverList[index].id;
                })
                this.isDialogVisible = true;
            })
            await this.getDictData();
            this.isDialogVisible = true
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            const form = {
                idList: this.handoverList.map(item=>item.id),
                handoverStatus: "RECEIVED"
            };
            Promise.all([
                apiReceiveHandover(form),
                ...this.tableData.filter(item=>item.deviceId).map(item=>{
                    return apiBatchUpdateWarehouseDeviceList({
                        deviceList: [item],
                        circulateType: CIRCULATE_TYPE.HANDOVER,
                        businessId: item.handoverId,
                    })
                })
            ]).then(()=>{
                this.$message.success('操作成功');
                this.$emit("update");
                this.isDialogVisible = false
            })
        }
    }
}
</script>