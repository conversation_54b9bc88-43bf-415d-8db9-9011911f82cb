<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">旋导流转历史</div>
            <el-form :model="searchForm" label-width="50px">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="旋导箱" label-width="60px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出库日期" label-width="70px">
                            <el-date-picker
                                style="width: calc(100%)"
                                placement="bottom-start"
                                clearable
                                @change="initData(true)"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.stockInDaterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入库日期" label-width="70px">
                            <el-date-picker
                                style="width: calc(100%)"
                                placement="bottom-start"
                                clearable
                                @change="initData(true)"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.stockOutDaterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" >
                <el-table-column prop="serialNumber" label="旋导箱" align="center"></el-table-column>
                <el-table-column prop="location" label="井号(或地点)"></el-table-column>
                <el-table-column prop="jobNumber" label="作业号"></el-table-column>
                <el-table-column prop="dateOut" label="出库时间"></el-table-column>
                <el-table-column prop="outCheckMember" label="出库清点人员"></el-table-column>
                <el-table-column prop="dateIn" label="入库时间"></el-table-column>
                <el-table-column prop="inCheckMember" label="入库清点人员"></el-table-column>
                <el-table-column label="操作" width="100px" align="center">
                    <template slot-scope="scope">
                        <el-button @click="onDetail(scope.row)" type="text">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import { apiLucidaHistory } from "@/api/warehouse";
export default {
    name: 'WarehouseKitHistory',
    data() {
        return {
            searchForm: {
                serialNumber: null,
                stockInDaterange: null,
                stockOutDaterange: null,
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            detailForm: {},
        };
    },
    mounted() {
        this.searchForm.serialNumber = this.$route.query.sn;
        if(!this.searchForm.serialNumber){
            return
        }
        this.initData();
    },
    methods: {
        async onDetail(row){
            this.$router.push(`/warehouse/twelllink/load/detail?id=${row.loadOutId}`)
        },
        initData() {
            const serialNumber = this.searchForm.serialNumber || undefined;
            const dateInStart = this.searchForm.stockInDaterange ? this.searchForm.stockInDaterange[0] : undefined;
            const dateInEnd = this.searchForm.stockInDaterange ? this.searchForm.stockInDaterange[1] : undefined;
            const dateOutStart = this.searchForm.stockOutDaterange ? this.searchForm.stockOutDaterange[0] : undefined;
            const dateOutEnd = this.searchForm.stockOutDaterange ? this.searchForm.stockOutDaterange[1] : undefined;
            apiLucidaHistory({serialNumber, dateInStart, dateInEnd, dateOutStart, dateOutEnd}).then((res) => {
                this.tableData = res.data.data || [];
            });
        },
    }

}
</script>
<style lang="scss" scoped></style>
