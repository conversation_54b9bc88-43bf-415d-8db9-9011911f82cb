<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="调拨单详情"
        width="1000px"
    >
        <el-form ref="detailForm" style="margin-top: -20px;" class="thin-margin-form" :model="detailForm" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发出井号: " prop="fromWellNumber">
                        <span>{{ detailForm.fromWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出kit箱: " prop="fromKitNumber">
                        <span>{{ detailForm.fromKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出时间: " prop="sendDate">
                        <span>{{ detailForm.sendDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发往井号: " prop="toWellNumber">
                        <span>{{ detailForm.toWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发往kit箱: " prop="toKitNumber">
                        <span>{{ detailForm.toKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.detailList"
            :header-cell-style="commmonTableHeaderCellStyle"
            style="margin-bottom: 20px;"
        >
            <el-table-column label="物品名称" prop="itemName" width="120" align="center">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="120" align="center">
            </el-table-column>
            <el-table-column label="数量" prop="amount" width="60" align="center">
            </el-table-column>
            <el-table-column label="备注" prop="note" align="center">
            </el-table-column>
            <el-table-column label="现场接收人" prop="fieldReceiveBy" width="100" align="center">
            </el-table-column>
            <el-table-column label="接收时间" prop="fieldReceiveDate" width="100" align="center">
            </el-table-column>
        </el-table>
        <template #footer v-if="operType === 'RECEIVE'">
            <el-button @click="isDialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="isConfirmLoading" @click="onReceive">接收</el-button>
        </template>
        <SyncTable @update="onReceiveSuccess" ref="syncTableRef" />
    </el-dialog>
</template>
<script>
import { apiGetTransferInfo, apiReceiveTransfer } from "@/api/transfer";
import SyncTable from "./SyncTable.vue";
export default {
    name: "TransferDetailDialog",
    components: { SyncTable },
    data(){
        return {
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                detailList: [],
            },
            isConfirmLoading: false,
            operType: "DETAIL", // DETAIL RECEIVE
            isDialogVisible: false,
        }
    },
    methods: {
        async showDialog(operType, transferId) {
            this.operType = operType;
            this.transferId = transferId;
            await this.initData();
            this.isDialogVisible = true;
        },
        async initData(){
            await apiGetTransferInfo({ transferId: this.transferId }).then((res) => {
                const data = res.data.data || {};
                this.detailForm = data;
            });
        },
        onReceive(){
            const serialNumberList = this.detailForm.detailList.filter(item=>item.serialNumber).map(item=>item.serialNumber);
            if(serialNumberList.length){
                this.$refs.syncTableRef.showDialog(serialNumberList, this.detailForm)
            }else{
                apiReceiveTransfer({transferId: this.detailForm.transferId}).then(()=>{
                    this.$message.success("操作成功");
                    this.isDialogVisible = false;
                    this.$emit("initData");
                })
            }
        },
        onReceiveSuccess(){
            this.isDialogVisible = false;
            this.$emit("initData");
        },
        onSelectionChange(val){
            this.selectedRows = val;
        },
        isRowSelectable(row){
            return row.fieldReceiveStatus!=='RECEIVED'
        }
    }
    
}
</script>
