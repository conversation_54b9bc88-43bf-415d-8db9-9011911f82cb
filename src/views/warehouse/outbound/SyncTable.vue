<template>
    <el-dialog :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="invName" label="品名" width="100px"></el-table-column>
            <el-table-column align="center" prop="serialNumber" label="序列号" width="200px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiUpdateRepair } from '@/api/repair';
import { apiBatchUpdateWarehouseDeviceList, apiCheckOutboundOrder, apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import { CIRCULATE_TYPE } from '@/utils/constant.warehouse';
import getDict from '@/utils/getDict';
const REPAIR_STATUS = 19002;
export default {
    name: "OutboundSyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: []
        }
    },
    methods: {
        async showDialog(selectedRow) {
            this.selectedRow = selectedRow;
            [this.deviceStatusList] = await getDict([20]);
            await apiGetWarehouseDeviceBatchInfo({deviceIdList:selectedRow.map(item=>item.deviceId)}).then(res=>{
                this.tableData = (res.data.data || []);
                this.tableData.forEach(item=>{
                    item.status = REPAIR_STATUS;
                })
                this.isDialogVisible = true;
            })
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            const form = new FormData();
            form.append('idList', this.selectedRow.map(row=>row.id));
            Promise.all([
                apiCheckOutboundOrder(form),
                ...this.tableData.map((item,index) => {
                    return apiBatchUpdateWarehouseDeviceList({
                        deviceList: [item],
                        circulateType: CIRCULATE_TYPE.OUT_BOUND_ORDER,
                        businessId: this.selectedRow[index].id,
                    })
                })
            ]).then(()=>{
                this.$emit("success");
                this.$message.success('操作成功！');
                this.isDialogVisible = false;
            }).finally(()=>{
                loading.close();
            })
        },
    }
}
</script>
