<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">出库核对</div>
                <span>
                    <el-button @click="onBatchApprove" type="primary">批量核对</el-button>
                </span>
            </div>
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
                style="margin-top: 10px;"
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    :selectable="canApprove"
                    type="selection"
                    width="55"
                ></el-table-column>
                <el-table-column
                    label="领取单号"
                    prop="orderId"
                    width="200"
                ></el-table-column>
                <el-table-column
                    label="品名"
                    prop="invName"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="领取原因"
                    prop="reason"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="领取人"
                    prop="receiverName"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="领取时间"
                    prop="receiveDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="核对人"
                    prop="checkerName"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="核对时间"
                    prop="checkDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="100"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button type="text" @click="onApprove(scope.row)" v-if="canApprove(scope.row)">
                            核对
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <SyncTableDialog ref="syncTableDialogRef" @success="initData" />
    </div>
</template>

<script>
import { apiCheckOutboundOrder, apiGetOutboundList } from "@/api/warehouse";
import SyncTableDialog from "./SyncTable.vue"
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    name: "WarehouseOutbound",
    components: { SyncTableDialog },
    data() {
        return {
            orderBy: "createTime",
            orderType: "desc",
            isLoading: false,
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {
            },
        };
    },
    async mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.initData();
    },
    methods: {
        canApprove(row){
            return !row.checkerName;
        },
        onBatchApprove(){
            if(!(this.selectedRow && this.selectedRow.length)){
                this.$message.error('请先选择领料单！');
                return;
            }
            this.$refs.syncTableDialogRef.showDialog(this.selectedRow);
        },
        initData() {
            apiGetOutboundList(
                {
                    ...this.searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },
        async onApprove(row) {
            this.$refs.syncTableDialogRef.showDialog([row]);
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.initData();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.initData();
        },
        handleSelectionChange(val){
            this.selectedRow = val
        }
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
