<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">需求单</div>
            <el-form :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="返修单类型" label-width="90px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="井号" label-width="60px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="initData(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" >
                <el-table-column prop="invName" label="需求单号"></el-table-column>
                <el-table-column prop="invName" label="井号"></el-table-column>
                <el-table-column prop="invName" label="作业号"></el-table-column>
                <el-table-column prop="invName" label="需求日期"></el-table-column>
                <el-table-column prop="invName" label="目的地"></el-table-column>
                <el-table-column prop="invName" label="创建人"></el-table-column>
                <el-table-column prop="invName" label="创建时间"></el-table-column>
                <el-table-column prop="serialNumber" label="状态" align="center"></el-table-column>
                <el-table-column prop="invName" label="接收人"></el-table-column>
                <el-table-column prop="invName" label="接收时间"></el-table-column>
                <el-table-column label="操作" width="100px" align="center">
                    <template slot-scope="scope">
                        <el-button @click="onDetail(scope.row)" type="text">出库</el-button>
                        <el-button @click="onDetail(scope.row)" type="text">接收</el-button>
                        <el-button @click="onDetail(scope.row)" type="text">下载</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>
<script>
import { apiGetServiceHistoryDetailList } from "@/api/tool-track";
export default {
    name: 'WarehouseKitHistory',
    data() {
        return {
            searchForm: {},
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            detailForm: {},
        };
    },
    mounted() {
        this.initData();
    },
    methods: {
        async onDetail(row){
            
        },
        initData(flag = false) {
            if (flag) {
                this.currentPage = 1;
            }
            const invName = this.searchForm.invName || undefined;
            const serialNumber = this.searchForm.serialNumber || undefined;
            apiGetServiceHistoryDetailList({invName,serialNumber,toolType: "UNDER_WELL"}, {
                current: this.currentPage,
                size: this.pageSize,
            }).then((res) => {
                const data = res.data.data || {};
                this.tableData = data.records || [];
                this.total = data.total;
            });
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.initData();
        },
    }

}
</script>
<style lang="scss" scoped></style>
