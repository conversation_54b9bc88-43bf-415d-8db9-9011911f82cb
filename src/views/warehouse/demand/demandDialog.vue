<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="demand-dialog"
        :close-on-click-modal="false"
        title="需求单"
        width="1000px"
    >
        <el-form ref="detailForm" style="margin-top: -20px;" :model="detailForm" class="thin-margin-form" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="井号: " prop="wellNumber">
                        <span>{{ detailForm.wellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业号: " prop="jobNumber">
                        <span>{{ detailForm.jobNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <span>{{ detailForm.kitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="目的地: ">
                        <span>{{ detailForm.destination }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="需求日期: ">
                        <span>{{ detailForm.demandDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-tabs v-model="activeName">
            <el-tab-pane label="出库信息" name="stock">
                <el-table
                    :data="detailForm.actualInfoDetailList"
                    style="margin-bottom: 20px"
                    default-expand-all
                    :header-cell-style="commmonTableHeaderCellStyle"
                    v-if="activeName === 'stock'"
                >
                    <el-table-column label="需求物品名称" prop="itemName" width="120" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.itemName"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="仪器类型" prop="deviceType" align="center" width="180">
                        <template slot-scope="scope">
                            <el-select
                                v-model="scope.row.deviceType"
                                clearable
                                style="width:100%"
                                @change="scope.row.serialNumberList = []"
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="序列号" prop="requireAmount" width="220" align="center">
                        <template slot-scope="scope">
                            <FuzzySelect v-if="scope.row.deviceType" multiple collapse-tags :restParams="{deviceType: scope.row.deviceType}" v-model="scope.row.serialNumberList" type="DEVICE_SN"></FuzzySelect>
                        </template>
                    </el-table-column>
                    <el-table-column label="需求数量" prop="requireAmount" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="实发数量" prop="actualAmount" width="120" align="center">
                        <template slot-scope="scope">
                            <span v-if="scope.row.deviceType">
                                {{ scope.row.serialNumberList ? scope.row.serialNumberList.length : 0 }}
                            </span>
                            <InputNumber
                                v-else
                                size="small"
                                controls-position="right"
                                style="width: 100%;"
                                v-model="scope.row.actualAmount"
                            ></InputNumber>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" prop="note" align="center">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.note"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
                <el-button
                    icon="el-icon-plus"
                    style="float: right"
                    type="primary"
                    @click="onAddRow"
                >
                    添加元素
                </el-button>
            </el-tab-pane>
            <el-tab-pane label="原始需求" name="demand">
                <el-table
                    :data="detailForm.originInfoDetailList"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    style="margin-bottom: 20px;"
                    v-if="activeName === 'demand'"
                >
                    <el-table-column label="需求物品名称" prop="itemName" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="需求数量" prop="requireAmount" width="200" align="center">
                    </el-table-column>
                    <el-table-column label="备注" prop="note" align="center">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <span slot="footer" class="dialog-footer" v-if="activeName === 'stock'">
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onConfirm(true)" :loading="isConfirmLoading">确定并发送邮件</el-button>
            <el-button type="primary" @click="onConfirm(false)" :loading="isConfirmLoading">确 定</el-button>
        </span>
        <SyncTable @update="onSyncSuccess" ref="syncTableRef" />
        <EmailDialog email-business-type="DEMAND_ORDER_OUT" :emailBusinessId="detailForm.demandId" ref="emailDialogRef"></EmailDialog>
    </el-dialog>
</template>
<script>
import { apiGetDemandInfo, apiReceiveDemand } from "@/api/demand";
import { tabsList as stockTypeList } from "../stock/options";
import getDict from "@/utils/getDict";
import FuzzySelect from '@/components/FuzzySelect';
import SyncTable from "./SyncTable.vue";
import EmailDialog from "@/components/EmailDialog/index.vue";
export default {
    name: "DemandDialog",
    components: { FuzzySelect, SyncTable, EmailDialog },
    data(){
        return {
            stockTypeList,
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                demandId: null,
                originInfoDetailList: [],
                actualInfoDetailList: [],
            },
            isConfirmLoading: false,
            operType: "ADD",
            isDialogVisible: false,
            deviceTypeList: [],
            activeName: "stock",
            needEmail: false,
        }
    },
    methods: {
        onConfirm(email = false) {
            this.needEmail = email;
            this.detailForm.actualInfoDetailList.forEach(item=>{
                if(item.deviceType){
                    item.serialNumberListStr = item.serialNumberList.join(',');
                    item.actualAmount = item.serialNumberList.length;
                }
            });
            const serialNumberList = this.detailForm.actualInfoDetailList.reduce((acc, cur)=>{
                acc.push(...cur.serialNumberList);
                return acc;
            }, [])
            if(serialNumberList.length){
                this.$refs.syncTableRef.showDialog(serialNumberList, this.detailForm)
            }else{
                Promise.all([apiReceiveDemand({demandId: this.detailForm.demandId, actualInfoDetailList: this.detailForm.actualInfoDetailList})]).then(res=>{
                    this.$message.success("操作成功");
                    this.isDialogVisible = false;
                    this.$emit("initData");
                })
                email && this.$refs.emailDialogRef.showDialog();
            }
        },
        async showDialog(type, demandId) {
            [this.deviceTypeList] = await getDict([17]);
            await apiGetDemandInfo({ demandId: demandId }).then((res) => {
                const data = res.data.data || {};
                if(!data.actualInfoDetailList||!data.actualInfoDetailList.length){
                    data.actualInfoDetailList = [];
                    data.originInfoDetailList.forEach(item=>{
                        const copyItem = {...item};
                        delete copyItem.demandDetailId;
                        delete copyItem.isOriginNeeds;
                        data.actualInfoDetailList.push(copyItem)
                    })
                }
                data.actualInfoDetailList.forEach(item=>{
                    item.serialNumberList = [];
                    if(item.deviceType){
                        item.serialNumberList = item.serialNumberListStr?.split(',') || [];
                        item.actualAmount = item.serialNumberList.length;
                    }
                })
                this.detailForm = data;
            });
            this.isDialogVisible = true;
        },
        onSyncSuccess() {
            this.$emit("initData");
            if(this.needEmail){
                this.$refs.emailDialogRef.showDialog();
            }
            this.isDialogVisible = false;
        },
        onAddRow(){
            this.detailForm.actualInfoDetailList.push({
                itemName: null,
                serialNumber: null,
                requireAmount: null,
                actualAmount: null,
                serialNumberList: [],
                serialNumberListStr: null,
                note: null,
            });
        }
    }
    
}
</script>
<style lang="scss">
.demand-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
