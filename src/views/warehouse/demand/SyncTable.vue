<template>
    <el-dialog append-to-body :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="serialNumber" label="序列号" width="140px">
            </el-table-column>
            <el-table-column align="center" prop="invName" label="品名" width="120px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%" v-if="scope.row.isValid==='valid'">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.receivedSendDate"
                        v-if="scope.row.isValid==='valid'"
                        style="width: calc(100%)"
                        type="date"
                        placeholder=""
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在BOX" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDate" label="完工日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)&&scope.row.isValid==='valid'"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDate"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column v-if="tableData.some(item=>isBattery(item))" align="center" prop="finishDays" label="完工时间" width="100px">
                <template slot-scope="scope">
                    <el-input v-if="isBattery(scope.row)&&scope.row.isValid==='valid'" v-model="scope.row.finishDays" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%" v-if="scope.row.isValid==='valid'"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiReceiveDemand } from '@/api/demand';
import { apiBatchUpdateWarehouseDeviceList, apiCheckSerialNumberList } from '@/api/warehouse';
import { CIRCULATE_TYPE } from '@/utils/constant.warehouse';
import getDict from '@/utils/getDict';
const AT_RIG_STATUS = 19001;
export default {
    name: "DemandSyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: []
        }
    },
    methods: {
        isBattery(item){
            return item.deviceType == 16004
        },
        async showDialog(serialNumberList, detailForm) {
            this.detailForm = detailForm;
            [this.deviceStatusList] = await getDict([20]);
            await apiCheckSerialNumberList({serialNumberList}).then(res=>{
                this.tableData = (res.data.data || []).map(item=>{
                    item.status = AT_RIG_STATUS;
                    item.receivedSendDate = new Date().Format("yyyy-MM-dd");
                    item.location = detailForm.wellNumber;
                    item.kitNumber = detailForm.kitNumber;
                    return item;
                });
                this.isDialogVisible = true;
            })
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            Promise.all([apiReceiveDemand({demandId: this.detailForm.demandId, actualInfoDetailList: this.detailForm.actualInfoDetailList}), apiBatchUpdateWarehouseDeviceList({
                deviceList: this.tableData,
                circulateType: CIRCULATE_TYPE.DEMAND_ORDER,
                businessId: this.detailForm.demandId,
            })]).then(()=>{
                this.$emit("update");
                this.isDialogVisible = false;
            }).finally(()=>{
                loading.close();
            })
        },
    }
}
</script>
