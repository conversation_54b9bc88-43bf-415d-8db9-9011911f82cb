<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                <span>{{ title }}</span>
                <span style="float: right;">
                    <el-button @click="onClickExportLoadInfo" type="primary">导出清单</el-button>
                    <el-button @click="onSaveAndSync" type="primary">保存并同步库存数据</el-button>
                </span>
            </div>
            <el-form :model="loadOutForm" label-width="50px" style="margin-top: 20px;">
                <el-row :gutter="20">
                    <el-col :span="5">
                        <el-form-item label="KIT箱" label-width="50px">
                            <el-input disabled style="width: calc(100%)" clearable v-model="loadOutForm.serialNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="井号(或地点)" label-width="100px">
                            <el-input disabled v-model="loadOutForm.location"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input disabled style="width: calc(100%)" clearable v-model="loadOutForm.outCheckMember">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="出库日期" label-width="70px">
                            <el-date-picker
                                disabled
                                v-model="loadOutForm.dateOut"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList :canRevise="canRevise" :loadOutDetailList="loadOutForm.loadOutDetailList" :disabledDeviceTypeSnMap="disabledDeviceTypeSnMap" />
            <SyncTable @update="onSave" :loadOutForm="loadOutForm" ref="syncTableRef" />
        </div>
    </div>
</template>
<script>
import LoadOutList from './components/LoadOutList.vue';
import SyncTable from './components/SyncTable.vue';
import { apiExportEvaluation, apiExportLoadOutInfo, apiGetLoadOutInfo, apiUpdateLoadOut } from '@/api/warehouse';
export default {
    name: 'WarehouseKitLoadRevise',
    components: { LoadOutList, SyncTable },
    data(){
        return {
            canRevise: false,
            loadOutForm: {
                serialNumber: null,
                inCheckMember: null,
                outCheckMember: null,
                dateOut: null,
                dateIn: null,
                location: null,
                wellNumber: null,
                items: [],
            },
            disabledDeviceTypeSnMap: {},
        }
    },
    computed: {
        title(){
            return 'MWD LOAD OUT LIST - 补发'
        }
    },
    async mounted(){        
        let { id: loadOutId } = this.$route.query;
        if(loadOutId){
            this.loadOutId = loadOutId;
            await this.getLoadOutInfo();
        }
    },
    methods: {
        getLoadOutInfo(){
            return apiGetLoadOutInfo({loadOutId: this.loadOutId}).then(res=>{
                this.loadOutForm = res.data.data;
                if(!this.loadOutForm.dateOut || this.loadOutForm.dateIn){
                    this.canRevise = false;
                    this.$message.error("当前出入库单据不允许修改");
                }else{
                    this.canRevise = true;
                }
                this.disabledDeviceTypeSnMap = this.getDisabledDeviceTypeSnMap();
                this.mergeDetail();
            })
        },
        mergeDetail(){
            this.loadOutForm.loadOutDetailList.forEach(item=>{
                const typeMap = new Map();
                item.units.forEach(unit => {
                    if (!typeMap.has(unit.unitName)) {
                        typeMap.set(unit.unitName, { initList: [], serialNumberList: [], requireAmount: 0, actualAmount: 0, loadInAmount: 0 });
                    }
                    if(unit.deviceTypeList){
                        if(unit.serialNumber){
                            typeMap.get(unit.unitName).initList.push({serialNumber: unit.serialNumber});
                            typeMap.get(unit.unitName).serialNumberList.push(unit.serialNumber);
                            typeMap.get(unit.unitName).actualAmount ++;
                        }
                    }else{
                        typeMap.get(unit.unitName).actualAmount += unit.actualAmount;
                    }
                    typeMap.get(unit.unitName).requireAmount += unit.requireAmount;
                    typeMap.get(unit.unitName).loadInAmount += unit.loadInAmount;
                });
                const result = [];
                const seenTypes = new Set();
                item.units.forEach(unit => {
                    if (!seenTypes.has(unit.unitName)) {
                        result.push({ ...unit, ...typeMap.get(unit.unitName) });
                        seenTypes.add(unit.unitName);
                    }
                });
                item.mergedUnits = result;
            })
        },
        splitDetail(){
            this.loadOutForm.loadOutDetailList.forEach(item=>{
                const splitUnits = [];
                item.mergedUnits.forEach(unit=>{
                    // 要拆成的数量为需求数量requireAmount
                    const requireAmount = unit.requireAmount;
                    const row = Math.max(requireAmount, unit.serialNumberList?.length || 0); // 实发数量可能会比需求量大
                    let templateId = 1;
                    let units = [{...unit}];
                    if(unit.deviceTypeList)
                        units = Array.from({length: row}).map((_, index)=>{
                            const serialNumber = unit.serialNumberList[index] || null;
                            let loadInAmount = null;
                            return {
                                ...unit,
                                templateId: templateId++,
                                serialNumber,
                                requireAmount: requireAmount > index ? 1 : 0, //保证需求数量不会被动改变
                                actualAmount: serialNumber ? 1 : 0,
                                loadInAmount
                            }
                        })
                    splitUnits.push(...units);
                })
                item.units = splitUnits;
            })
        },
        async onSaveAndSync(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            // 要先save获取每个deviceId
            try{
                // save后禁用的sn列表会变，但不save又会获取不到deviceId，所以先暂存
                const currentDisableSNList = this.disabledDeviceTypeSnMap
                    ? Object.values(this.disabledDeviceTypeSnMap).flat()
                    : [];
                await this.onSave();
                // 这里过滤的序列号是save之前的，因为只做同步使用，所以就算disableSNList变了，也符合业务逻辑
                const deviceIdList = this.loadOutForm.loadOutDetailList.reduce((acc, item)=>{
                    return [
                        ...acc,
                        ...item.units
                            .filter(unit=>unit.deviceId && !currentDisableSNList.includes(unit.serialNumber))
                            .map(unit=>unit.deviceId)
                        ]
                }, []);
                if(deviceIdList.length){
                    await this.$refs.syncTableRef.showDialog([...deviceIdList]);
                }else{
                    this.$message.success("操作成功")
                }
            }finally{
                loading.close();
            }
        },
        async onSave(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            try{
                this.splitDetail();
                await apiUpdateLoadOut({...this.loadOutForm}).then(async ()=>{
                    // 补发仅更新，不做额外工单状态操作
                    await this.getLoadOutInfo();
                });
            }finally{
                loading.close();
            }
        },
        onClickExportEvaluation(){
            this.$confirm("确认导出风险评估单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(()=>{
                this.onExportEvaluation();
            })
        },
        onExportEvaluation(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiExportEvaluation({loadOutId: this.loadOutForm.loadOutId}).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `风险评估单 - ${this.loadOutForm.serialNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        },
        onClickExportLoadInfo(){
            this.$confirm("确认导出当前Load Out清单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(()=>{
                this.onExportLoadInfo();
            })
        },
        onExportLoadInfo(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiExportLoadOutInfo({loadOutId: this.loadOutForm.loadOutId}).then((res) => {
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `T-Well Link 675 LOAD OUT LIST - ${this.loadOutForm.serialNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        },
        getDisabledDeviceTypeSnMap(){
            const disabledDeviceTypeSnMap = {};
            this.loadOutForm.loadOutDetailList.forEach(item=>{
                item.units.forEach(unit=>{
                    if(unit.deviceTypeList && unit.serialNumber){
                        if(!disabledDeviceTypeSnMap[unit.deviceTypeList]){
                            disabledDeviceTypeSnMap[unit.deviceTypeList] = [];
                        }
                        disabledDeviceTypeSnMap[unit.deviceTypeList].push(unit.serialNumber);
                        
                    }
                })
            });
            return disabledDeviceTypeSnMap;
        },
    }
}
</script>
<style lang="scss">
</style>