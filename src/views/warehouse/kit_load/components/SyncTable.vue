<template>
    <el-dialog :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="invName" label="品名" width="100px"></el-table-column>
            <el-table-column align="center" prop="serialNumber" label="序列号" width="200px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%">
                        <el-option v-for="item in getFilterDeviceTypeList(scope.$index)" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.receivedSendDate"
                        style="width: calc(100%)"
                        type="date"
                        placeholder=""
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在BOX" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" v-if="scope.$index!==0" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column v-if="loadType==='IN'&&tableData.some(item=>isBattery(item))" align="center" prop="finishDate" label="完工日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDate"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column v-if="loadType==='IN'&&tableData.some(item=>isBattery(item))" align="center" prop="finishDays" label="完工时间" width="100px">
                <template slot-scope="scope">
                    <el-input v-if="isBattery(scope.row)" v-model="scope.row.finishDays" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiBatchUpdateWarehouseDeviceList, apiGetWarehouseDeviceBatchInfo } from '@/api/warehouse';
import { CIRCULATE_TYPE, loadTypeMap } from '@/utils/constant.warehouse';
import getDict from '@/utils/getDict';
export default {
    name: "SyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: []
        }
    },
    props: {
        loadType: {
            type: String,
            default: 'OUT'
        },
        kitStatus: Number,
        loadOutForm: {
            type: Object,
            default: ()=>{}
        }
    },
    methods: {
        getFilterDeviceTypeList(index){
            // index为0时是kit箱
            if(index){
                return this.deviceStatusList
            }else{
                return this.deviceStatusList.filter(item => {
                    return [this.kitStatus, loadTypeMap[this.kitStatus].targetStatus].includes(item.id);
                })
            }
        },
        isBattery(item){
            return item.deviceTypeList && item.deviceTypeList.includes("16004")
        },
        async showDialog(deviceIdList) {
            [this.deviceStatusList] = await getDict([20]);
            await apiGetWarehouseDeviceBatchInfo({deviceIdList}).then(res=>{
                this.tableData = (res.data.data || []);
                const kitNumber = this.tableData[0].serialNumber;
                const stockLocation = "Chengdu Shop";
                const targetLocation = this.loadOutForm.location;
                this.tableData.forEach((item, index)=>{
                    item.status = loadTypeMap[this.kitStatus].targetStatus;
                    item.receivedSendDate = new Date().Format("yyyy-MM-dd");
                    if(this.loadType === 'IN'){
                        if(index!==0){
                            item.kitNumber = null;
                        }
                        item.location = stockLocation;
                    } else {
                        if(index!==0){
                            item.kitNumber = kitNumber;
                        }
                        item.location = targetLocation;
                    }
                })
                this.isDialogVisible = true;
            })
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            // 用户修改了kit箱的状态
            if(this.tableData[0].status!=this.kitStatus){
                this.$confirm(`kit箱状态修改后将完成${loadTypeMap[this.kitStatus].loadTypeStr}操作，确认提交？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.syncData(this.tableData[0].status);
                })
            }else{
                this.syncData();
            }
        },
        syncData(newKitStatus){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            })
            apiBatchUpdateWarehouseDeviceList({
                deviceList: this.tableData,
                relatedBoxId: this.loadOutForm.deviceId,
                circulateType: CIRCULATE_TYPE.WAREHOUSE,
                businessId: this.loadOutForm.loadOutId,
            }).then(() => {
                this.$emit("update", newKitStatus);
                this.isDialogVisible = false;
            }).finally(()=>{
                loading.close();
            })
        }
    }
}
</script>
