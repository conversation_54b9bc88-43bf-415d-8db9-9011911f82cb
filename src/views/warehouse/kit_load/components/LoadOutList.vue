<template>
    <div class="load-out-list">
        <el-table :data="[]" class="no-empty-text-table" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="零件号" width="300px"></el-table-column>
            <el-table-column label="名称"></el-table-column>
            <el-table-column label="序列号" width="200px"></el-table-column>
            <el-table-column label="需求数量" align="center" width="100px"></el-table-column>
            <el-table-column :label="actualAmountLabel" align="center" width="100px"></el-table-column>
            <el-table-column label="入库数量" align="center" width="100px" v-if="loadType==='IN'"></el-table-column>
            <el-table-column label="备注" align="center" width="200px"></el-table-column>
        </el-table>
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <div v-for="item in loadOutDetailList" :key="item.typeName">
                <div style="text-align: center; height: 30px; line-height: 30px; color: #1f2d3d; font-weight: bold; background-color: #f0f2f5;">
                    {{ item.typeName }}
                </div>
                <el-table :data="item.mergedUnits" :show-header="false">
                    <el-table-column prop="unitNumber" width="300px"></el-table-column>
                    <el-table-column prop="unitName"></el-table-column>
                    <el-table-column prop="serialNumber" width="200px">
                        <template slot-scope="scope">
                            <FuzzySelect
                                @change="onSnChange(scope.row)"
                                :restLazy="true"
                                v-if="loadType!='IN'&&scope.row.deviceTypeList"
                                :restParams="{deviceTypeListStr: scope.row.deviceTypeList}"
                                v-model="scope.row.serialNumberList"
                                type="DEVICE_INFO"
                                :init-list="scope.row.initList"
                                multiple
                            >
                                <template slot-scope="{item}">
                                    <div>
                                        {{ item.serialNumber }}
                                        <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="item.status"></CustomTag>
                                        <CustomTag tagType="RISK_TYPE" :tagValue="item.riskType"></CustomTag>
                                    </div>
                                </template>
                            </FuzzySelect>
                            <span v-else>
                                <span v-for="(item,key) in scope.row.serialNumberList" :key="item">
                                    <router-link
                                        v-if="getRepairInfoBySerialNumber(item)"
                                        title="已返修"
                                        tag="a" 
                                        style="color: red" 
                                        target="_blank"
                                        :to="`/mwd/repair/detail?id=${getRepairInfoBySerialNumber(item).repairId}`"
                                    >
                                        {{ item }}
                                    </router-link>
                                    <span v-else>{{ item }}</span>
                                    <span v-if="key<scope.row.serialNumberList.length-1">,</span>
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="requireAmount" align="center" width="100px">
                        <template slot-scope="scope">
                            <el-input v-if="loadType==='SET'" size="small" v-model="scope.row.requireAmount" class="center-input" style="width: 100%;"></el-input>
                            <span v-else>{{ scope.row.requireAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="actualAmount"  align="center" width="100px">
                        <template slot-scope="scope">
                            <el-input v-if="loadType!=='IN'&&!scope.row.deviceTypeList" class="center-input" size="small" v-model="scope.row.actualAmount" style="width: 100%;"></el-input>
                            <span v-else>{{ scope.row.actualAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="loadInAmount" align="center" width="100px" v-if="loadType==='IN'">
                        <template slot-scope="scope">
                            <el-input size="small" v-model="scope.row.loadInAmount" style="width: 100%;"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="note" align="center" width="200px">
                        <template slot-scope="scope">
                            <el-input size="small" v-model="scope.row.note" style="width: 100%;"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-scrollbar>
    </div>
</template>
<script>
import FuzzySelect from '@/components/FuzzySelect';
export default {
    name: 'LoadOutList',
    components: { FuzzySelect },
    props: {
        loadOutDetailList: {
            type: Array,
            default: () => []
        },
        relatedRepairDetailList: {
            type: Array,
            default: () => []
        },
        loadType: {
            type: String,
            default: 'OUT'
        }
    },
    data(){
        return {}
    },
    computed:{
        actualAmountLabel(){
            return this.loadType==='SET' ? '配置数量' : '实发数量'
        }
    },
    methods: {
        onSnChange(row){
            row.actualAmount = row.serialNumberList ? row.serialNumberList.length : null;
        },
        getRepairInfoBySerialNumber(serialNumber){
            return this.relatedRepairDetailList.find(item => item.serialNumber === serialNumber)
        }
    }
}
</script>
<style lang="scss">
.no-empty-text-table{
    .el-table__empty-block{
        display: none;
    }
}

.load-out-list{
    min-width: 1100px;
    .el-scrollbar {
        .el-scrollbar__wrap{
            overflow-x: auto;
            max-height: calc(100vh - 280px); // 最大高度
            
        }
    }
}
</style>
<style lang="scss" scoped>

::v-deep(.center-input .el-input__inner ){
    text-align: center;
}
</style>