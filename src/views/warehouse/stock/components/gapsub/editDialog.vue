<template>
    <el-dialog :visible.sync="isDialogVisible" :title="isAdd ? '新增' : '编辑'">
        <el-form :model="form" label-width="60px">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="品名">
                        <el-input v-model="form.invName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="序列号">
                        <el-input v-model="form.serialNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="近钻类型" label-width="80px">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="接收/发出日期" label-width="110px">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="状态">
                        <el-input v-model="form.status" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="所在BOX" label-width="90px">
                        <el-input v-model="form.status" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="风险类型" label-width="80px">
                        <el-input v-model="form.status" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="资产所属" label-width="80px">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="地点">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注">
                        <el-input v-model="form.notes" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <el-button @click="isDialogVisible=false">
                取消
            </el-button>
            <el-button type="primary" @click="onSave">
                保存
            </el-button>
        </template>
    </el-dialog>
</template>
<script>
export default {
    name: 'WarehouseGapSubDialog',
    data() {
        return {
            isDialogVisible: false,
            dialogType: 'ADD',
            form: {
                invName: '',
                serialNumber: '',
                receiveDate: '',
                status: '',
                configQuantity: '',
                type: '',
                assetOwnership: '',
                location: '',
                notes: ''
            }
        }
    },
    computed: {
        isAdd(){
            return this.dialogType === 'ADD'
        }
    },
    methods: {
        showDialog(type){
            this.dialogType = type
            this.isDialogVisible = true
        },
        onSave(){
            this.isDialogVisible = false
        }
    }
}
</script>