<template>
    <el-dialog :visible.sync="isDialogVisible" :title="isAdd ? '新增' : '编辑'">
        <el-form :model="form" label-width="60px">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-form-item label="品名">
                        <el-input v-model="form.invName" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="序列号">
                        <el-input v-model="form.serialNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="接收/发出日期" label-width="110px">
                        <el-date-picker
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            v-model="form.receivedSendDate"
                            type="datetime"
                            default-time="08:00:00"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="状态">
                        <el-select v-model="form.status" style="width: 100%">
                            <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="仪器配置数量(串)" label-width="130px">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="类型">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="资产所属" label-width="80px">
                        <el-input v-model="form.owner" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="地点">
                        <el-input v-model="form.location" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注">
                        <el-input v-model="form.note" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <el-button @click="isDialogVisible=false">
                取消
            </el-button>
            <el-button type="primary" @click="onSave">
                保存
            </el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiAddWarehouseDeviceList, apiUpdateWarehouseDeviceList } from '@/api/warehouse';

export default {
    name: 'WarehouseStockKitboxDialog',
    props: {
        deviceStatusList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            isDialogVisible: false,
            dialogType: 'ADD',
            form: {
                invName: 'KIT箱',
                serialNumber: null,
                status: null,
                configQuantity: null,
                type: null,
                owner: null,
                location: null,
                note: null,
                receivedSendDate: null
            }
        }
    },
    computed: {
        isAdd(){
            return this.dialogType === 'ADD'
        }
    },
    methods: {
        showDialog(type, row){
            this.dialogType = type;
            if(type == 'ADD'){
                this.form = {
                    invName: 'KIT箱',
                    deviceType: 16010,
                    serialNumber: null,
                    status: null,
                    configQuantity: null,
                    type: null,
                    owner: null,
                    location: null,
                    note: null,
                    receivedSendDate: null
                }
            }else{
                this.form = { ...row };
            }
            this.isDialogVisible = true;
        },
        onSave(){
            this.isDialogVisible = false;
            if(this.isAdd){
                apiAddWarehouseDeviceList(this.form).then(()=>{
                    this.$message.success("操作成功");
                }).finally(()=>{
                    this.$emit("updateList");
                    this.isDialogVisible = false;
                })
            }else{
                apiUpdateWarehouseDeviceList(this.form).then(()=>{
                    this.$message.success("操作成功");
                }).finally(()=>{
                    this.$emit("updateList");
                    this.isDialogVisible = false;
                })
            }
        }
    }
}
</script>