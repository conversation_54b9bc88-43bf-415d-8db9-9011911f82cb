<template>
    <div>
        <el-button @click="onAdd" type="primary" style="float: right;margin-bottom: 8px;">新增</el-button>
        <el-table :data="tableData" v-loading="tableLoading" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="品名" prop="invName" width="120px"></el-table-column>
            <el-table-column label="序列号" prop="serialNumber" align="center" width="120px"></el-table-column>
            <el-table-column label="状态" prop="status" align="center">
                <template slot-scope="scope">
                    <span>{{ getDeviceStatus(scope.row.status) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="电池芯类型" prop="totalHour" align="center" width="150px"></el-table-column>
            <el-table-column label="接收/出发日期" prop="receivedSendDate" align="center" width="150px"></el-table-column>
            <el-table-column label="所在BOX" prop="totalHour" align="center" width="150px"></el-table-column>
            <el-table-column label="地点" prop="location" align="center" width="150px"></el-table-column>
            <el-table-column label="资产所属" prop="owner" align="center" width="150px"></el-table-column>
            <el-table-column label="完工日期" prop="finishDate" align="center" width="150px"></el-table-column>
            <el-table-column label="完工时间" prop="totalHour" align="center" width="150px"></el-table-column>
            <el-table-column label="电池状态" prop="totalHour" align="center" width="150px"></el-table-column>
            <el-table-column label="备注" prop="note" align="center" width="150px"></el-table-column>
            
            <el-table-column label="操作" prop="" align="center" width="100px" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" @click="onEdit(scope)">编辑</el-button>
                    <el-button type="text" @click="onHistory(scope)">历史</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :hide-on-single-page="true"
            style="margin-top: 20px"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            @current-change="onCurrentChange"
            :current-page="currentPage"
        ></el-pagination>
        <EditDialog ref="editDialogRef" />
    </div>
</template>
<script>
import { apiGetWarehouseDeviceList } from '@/api/warehouse';
import EditDialog from './editDialog.vue';

export default {
    name: 'WarehouseStockBattery',
    components: { EditDialog },
    props: {
        activePanelName: String,
        deviceType: Number,
        deviceStatusList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableData: [{}],
            tableLoading: false,
            total: 0,
            pageSize: 10,
            currentPage: 1,
        }
    },
    mounted(){
        // this.initData();
    },
    methods: {
        getDeviceStatus(statusId){
            // 获取状态名称
            return this.deviceStatusList.find(item=>item.id === statusId)?.name || '';
        },
        initData(bool=false) {
            if(bool){
                this.currentPage = 1;
            }
            this.tableLoading = true;
            return apiGetWarehouseDeviceList({deviceType: this.deviceType}, {current: this.currentPage, size: this.pageSize}).then(res=>{
                const data = res.data.data || {};
                this.total = data.total;
                this.tableData = data.records || [];
            }).finally(()=>{
                this.tableLoading = false;
            })
        },
        onCurrentChange(currentPage) {
            this.applyCurrentChange = currentPage;
            this.initData();
        },
        onAdd() {
            this.$refs.editDialogRef.showDialog('ADD')
        },
        onDetail(scope){
            this.$refs.applyDetailDialog.showDialog(scope.row)
        },
        onEdit(scope){},
        onHistory(scope){},
    }
}
</script>