<template>
    <div style="margin-top: 20px">
        <el-form inline :form="searchForm">
            <el-form-item label="筛选条件">
                <el-select multiple collapse-tags clearable v-model="searchForm.conditionFieldNameList">
                    <el-option
                        v-for="item in conditionFieldNameList"
                        :disabled="item===searchForm.counterFieldName"
                        :key="item"
                        :label="getLabelByFieldName(item)"
                        :value="item"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="计数项">
                <el-select v-model="searchForm.counterFieldName" @change="getCondition">
                    <el-option
                        v-for="item in conditionFieldNameList"
                        :disabled="searchForm.conditionFieldNameList.includes(item)"
                        :key="item"
                        :label="getLabelByFieldName(item)"
                        :value="item"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="计数条件">
                <el-select multiple collapse-tags clearable style="min-width: 250px;" v-model="searchForm.counterValueList">
                    <el-option
                        v-for="item in counterValueList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-button @click="renderData" type="primary">确认</el-button>
        </el-form>
        <el-table border :data="tableData" :span-method="arraySpanMethod" ref="tableDataRef" show-summary :summary-method="summaryMethod" :cell-style="cellStyle" :header-cell-style="cellStyle">
            <el-table-column v-for="(item,index) in tableColumnList" :key="item" :prop="item" :label="item">
                <template #header>
                    <div>
                        {{ getTableHeaderByProp(item, index) }}
                        <el-popover
                            placement="bottom"
                            width="auto"
                            trigger="click"
                        >
                            <el-checkbox-group v-model="checkForm[item]" @change="generateTableData">
                                <el-checkbox style="display: block;" v-for="checkItem in checkListMap[item]" :key="checkItem" :label="checkItem">{{ getLabelByFieldValue(item, checkItem) }}</el-checkbox>
                            </el-checkbox-group>
                            <i class="el-icon-arrow-down" style="cursor: pointer;" slot="reference" v-if="tableConditionFieldNameList.includes(item)"></i>
                        </el-popover>
                    </div>
                </template>
                <template slot-scope="scope">
                    {{ getLabelByFieldValue(item,scope.row[item]) }}
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { apiGetWarehouseDeviceStats, apiGetWarehouseDeviceStatsCondition } from '@/api/warehouse';
import { getLabelByFieldName as getLabelByFieldNameAndPanel, showPanelField } from "./options"
export default {
    props: {
        stockType: Number,
        activePanelName: String,
        deviceStatusList: {
            type: Array,
            default: () => []
        }
    },
    data(){
        return {
            // 表头筛选数据
            checkListMap: {},
            checkForm: {},
            // 三种形式: 原始供遍历 - 表单供提交 - 表格供展示
            // conditionFieldNameList: ['invName', 'owner', 'location', 'riskType', 'configType', 'specification', 'status'],
            counterFieldName: 'owner',
            counterValueList: [],

            searchForm: {
                conditionFieldNameList: ['status', 'specification'],
                counterFieldName: 'owner',
                counterValueList: []
            },

            tableConditionFieldNameList: [], 
            tableCounterFieldName: '',
            tableCounterValueList: [], 

            tableColumnList: [],
            tableData: [],
            originArray: [],
            riskTypeList: [
                { id: "RISK", name: "风险" },
                { id: "PRODUCE", name: "生产" },
                { id: "TEST", name: "试验" },
                { id: "SCRAP", name: "报废" },
            ],
        }
    },
    computed: {
        conditionFieldNameList(){
            return ['invName', 'owner', 'location', 'riskType', 'specification', 'status']
                // .filter(item=>showPanelField(this.activePanelName, item))
        },
    },
    mounted(){
        this.initData();
    },
    methods: {
        cellStyle({column}){
            if(column.property === '合计'){
                return {
                    backgroundColor: "#F5F7FA"
                }
            }
        },
        getLabelByFieldValue(filedName, value){
            switch(filedName){
                case 'status': 
                    return this.deviceStatusList.find(item=>item.id==value)?.name || value;
                case 'riskType': 
                    return this.riskTypeList.find(item=>item.id==value)?.name || value;
                default: 
                    return value;
            }
        },
        getTableHeaderByProp(prop, index){
            if(index >= this.tableConditionFieldNameList.length){
                // 计数条件
                return this.getLabelByFieldValue(this.tableCounterFieldName, prop);
            }else{
                // 筛选条件
                return this.getLabelByFieldName(prop)
            }

        },
        getCondition(){
            return apiGetWarehouseDeviceStatsCondition({ stockType: this.stockType, counterFieldName: this.searchForm.counterFieldName }).then(res=>{
                const data = (res.data.data || []).map(item=>{
                    let label;
                    switch(this.searchForm.counterFieldName){
                        case 'status': 
                            label = this.deviceStatusList.find(v=>v.id==item)?.name || item;
                            break;
                        case 'riskType': 
                            label = this.riskTypeList.find(v=>v.id==item)?.name || item;
                            break;
                        default: 
                            label = item;
                    }
                    return {
                        value: item,
                        label: label
                    }
                });
                this.counterValueList = [...data];
                this.searchForm.counterValueList = data.map(item=>item.value);
            })
        },
        arraySpanMethod({ _, __, rowIndex, columnIndex }) {
            // TODO: rules在这里不应该是完整的, 要么单独在getMergeStrategy中生成, 要么在这里命中后就返回相应的值, 但要考虑绑定问题
            if(rowIndex===this.tableData.length){
                if(columnIndex===0){
                    return [1, this.tableConditionFieldNameList.length]
                }
                if(columnIndex<=this.tableConditionFieldNameList.length){
                    return [0,0]
                }
                return [1,1]
            }
            const mergeRules = this.getMergeStrategy();
            const rules = {};
            mergeRules.forEach((item, index)=>{
                if(!rules[index]){
                    rules[index] = {};
                }
                let row = 0;
                item.forEach((item1) => {
                    rules[index][row] = [item1, 1]
                    row += item1;
                })
            })
            if(rules[columnIndex]){
                return rules[columnIndex][rowIndex] || [0, 0]
            }
            return [1, 1]
        },
        getMergeStrategy() {
            let last = [this.tableData.length];
            const res = [];
            
            for (let i = 0; i < this.tableConditionFieldNameList.length; i++) {
                const current = [];
                let lastIndex = 0;

                for (const lastSegment of last) {
                    if (lastSegment === 1) {
                        // 如果这一段只有一个, 直接push 1
                        current.push(1);
                    } else {
                        current.push(...this.processSegment(lastIndex, lastSegment, this.tableConditionFieldNameList[i]));
                    }
                    lastIndex += lastSegment;
                }

                last = current;
                res.push(current);
            }

            return res;
        },

        processSegment(startIndex, length, col) {
            const result = [];
            let count = 1;

            for (let k = startIndex; k < startIndex + length - 1; k++) {
                const curItem = this.tableData[k][col];
                const nextItem = this.tableData[k + 1][col];
                if (curItem === nextItem) {
                    // 如果下一个和当前一样, 则计数加一
                    count++;
                } else {
                    // 否则push数据并重置count为1
                    result.push(count);
                    count = 1;
                }
            }

            result.push(count); // 最后还要push数据
            return result;
        },
        getLabelByFieldName(fieldName){
            return getLabelByFieldNameAndPanel(fieldName, this.activePanelName)
        },
        async initData(){
            await this.getCondition();
            this.renderData();
        },
        async renderData(){
            // if(!this.searchForm.conditionFieldNameList?.length){
            //     this.$message.error("请选择筛选条件！");
            //     return;
            // }
            // if(!this.searchForm.counterFieldName){
            //     this.$message.error("请选择计数项！");
            //     return;
            // }
            // if(!this.searchForm.counterValueList?.length){
            //     this.$message.error("请选择计数条件！");
            //     return;
            // }
            this.tableConditionFieldNameList = [...this.searchForm.conditionFieldNameList];
            this.tableCounterFieldName = this.searchForm.counterFieldName;
            this.tableCounterValueList = [...this.searchForm.counterValueList];
            const data = await apiGetWarehouseDeviceStats({
                stockType: this.stockType, 
                conditionFieldNameList: this.tableConditionFieldNameList,
                counterFieldName: this.tableCounterFieldName,
                counterValueList: this.tableCounterValueList,
            });
            // "null" -> ""
            this.originArray = (data.data?.data || []).map(row=>row.map(item=>item==='null' ? '' : item));
            // sort
            this.originArray.sort((a, b) => {
                for (let i = 0; i < a.length; i++) {
                    if (a[i] !== b[i]) {
                        return a[i].localeCompare(b[i]);
                    }
                }
                return 0;
            });
            // checkListMap, init checkForm
            this.checkListMap = {};
            this.checkForm = {};
            this.tableConditionFieldNameList.forEach((item, idx)=>{
                const distinctColumn = Array.from(new Set(this.originArray.map(o=>o[idx])))
                this.checkListMap[item] = [...distinctColumn];
                this.$set(this.checkForm, item, [...distinctColumn]);
            })
            // (originArray + condition) -> tableData
            this.generateTableData();
        },
        generateTableData(){
            this.tableColumnList = [...this.tableConditionFieldNameList, ...this.tableCounterValueList, '合计'];
            this.tableData = [];
            this.originArray.forEach((o)=>{
                let ret = {};
                let flag = true;
                let total = 0;
                o.forEach((value, index)=>{
                    if(index>=this.tableConditionFieldNameList.length){
                        total = +value + total;
                    }
                    const fieldName = this.tableColumnList[index];
                    const hasConditionFieldName = !this.checkForm[fieldName] || this.checkForm[fieldName]?.includes(value)
                    if(hasConditionFieldName){
                        ret[this.tableColumnList[index]] = value;
                    }else{
                        flag = false;
                        return;
                    }
                })
                ret['合计'] = total;
                if(flag){
                    this.tableData.push(ret);
                }
            })
        },
        summaryMethod({columns, data}){
            return columns.map((col, colIdx)=>{
                if(colIdx === 0){
                    return '合计'
                }
                if(colIdx>=this.tableConditionFieldNameList.length){ 
                    return data.reduce((prev, cur)=>{
                        return prev + +cur[col.property||""]; // property为空串会被转为undefined
                    }, 0)
                }
                return null;
            })
        },
    }
}
</script>