/* 
    TODO: 
    近钻头：近钻类型

*/
export const fullFieldForm = {
    invName: null,
    deviceType: null,
    serialNumber: null,
    status: null,
    configQuantity: null,
    type: null,
    owner: null,
    location: null,
    specification: null,
    
    newOldStatus: null,
    riskType: null,
    kitNumber: null,
    configType: null,
    clientName: null,
    wellSuggest: null,
    finishDate: null,
    buckleType: null,
    materialNumber: null,
    note: null,
    receivedSendDate: null
}
export const allFieldList = [
    'invName', 'serialNumber', 'status', 'specification', 'riskType',
    'type', 'receivedSendDate', 'kitNumber', 'configQuantity', 'configType',
    'location', 'owner', 'loadInDays', 'finishDate', 'wellSuggest', 'note'
]
export const fieldNameLabelMap = {
    deviceType: {
        label: "仪器类型"
    },
    invName: {
        label: "品名" 
    },
    serialNumber: {
        label: "序列号" 
    },
    status: {
        label: '状态' 
    },
    specification: {
        label: {
            default: '规格',
            atbit: '近钻类型',
            kitbox: '配置类型'
        },

    }, 
    riskType: {
        label: '风险类型' 

    },
    type: {
        label: '类型' 

    }, 
    receivedSendDate: {
        label: '接收/发出时间' 

    },
    kitNumber: {
        label: '所在BOX' 

    },
    configQuantity: {
        label: '仪器配置数量(串)' 

    },
    location: {
        label: '地点' 

    },
    owner: {
        label: '资产所属' 

    },
    loadInDays: {
        label: '库存时间(天)' 

    },
    finishDate: {
        label: '完成日期' 

    },
    finishDays: {
        label: '完成天数' 

    },
    newOldStatus: {
        label: '新旧状态' 

    },
    // 已删除
    clientName: {
        label: '客户名称' 

    },
    wellSuggest: {
        label: '上井建议' 

    },
    configType: {
        label: "配置类型"
    },
    buckleType: {
        label: "公母扣型"
    },
    materialNumber: {
        label: "材料号"
    },
    hasSn: {
        label: "有无序列号"
    },
    note: {
        label: '备注'
    }
}
export const showPanelField = (componentName, fieldname) => {
    return componentOptionMap[componentName].fieldList.includes(fieldname);
}

export const showPanelFilter = (componentName, fieldname) => {
    return componentOptionMap[componentName].filterList.includes(fieldname);
}
export const getLabelByFieldName = (fieldName, panelName) => {
    const label = fieldNameLabelMap[fieldName]?.label;
    if(label){
        const type = typeof label;
        if(type==='string'){
            return label
        }
        if(type==='object'){
            return label[panelName] || label.default;
        }
    }
    return fieldName;
}
// TODO: 重构
export const tabsList = [
    { label: 'BOX', name: 'kitbox', stockType: 20001 },
    { label: '近钻头', name: 'atbit', stockType: 20002 },
    { label: '脉冲器', name: 'pulser', stockType: 20003 },
    // { label: '定向探管', name: 'dm', stockType: 20004 },
    // { label: '方位探管', name: 'azdm', stockType: 20005 },
    { label: '定向/方位探管', name: 'dm', stockType: 20004 },
    { label: '接收器', name: 'receiver', stockType: 20006 },
    { label: '自然伽马', name: 'ngamma', stockType: 20007 },
    { label: '方位伽马', name: 'azgamma', stockType: 20008 },
    { label: '电池总成', name: 'battery', stockType: 20009 },
    { label: '绝缘短节', name: 'gapsub', stockType: 20010 }, 
    { label: '定向接头', name: 'ubho', stockType: 20011 },
    { label: '恒泰JC', name: 'jc', stockType: 20012 },
    { label: '多点仪器', name: 'duodian', stockType: 20013 },
    { label: '底部总成/扶正器', name: 'be', stockType: 20015 },
    { label: '旋转导向', name: 'xzdx', stockType: 20017 },
    { label: '滤网短节', name: 'lwdj', stockType: 20024 },
    { label: 'TAPP', name: 'tapp', stockType: 20016 },
    { label: '长无磁', name: 'cwc', stockType: 20018 },
    { label: '无磁短节', name: 'wcdj', stockType: 20019 },
    { label: '司显', name: 'sx', stockType: 20020 },
    { label: '井深盒子', name: 'jshz', stockType: 20021 },
    { label: 'Gamma Key', name: 'gammakey', stockType: 20022 },
    { label: '电脑', name: 'pc', stockType: 20023 },
    // { label: '随钻测压', name: 'szcy', stockType: 20025 },
    // { label: '其他', name: 'others', stockType: 20014 },
    
]
export const componentOptionMap = {
    kitbox: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "specification", "receivedSendDate", "location", "configQuantity", "owner", "clientName", "note"],
        filterList: ["invName","serialNumber", "status", "specification", "location", "owner"]
    },
    atbit: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "specification", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "loadInDays", "note"],
        filterList: ["serialNumber", "status", "specification", "riskType", "location", "owner"]
    },
    pulser: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "loadInDays", "wellSuggest", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    dm: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "loadInDays", "wellSuggest", "note"],
        filterList: ["invName", "serialNumber", "status", "riskType", "location", "owner"]
    },
    // azdm: {
    //     fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "loadInDays", "wellSuggest", "note"],
    //     filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    // },
    receiver: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "loadInDays", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    ngamma: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "loadInDays", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    azgamma: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "loadInDays", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    battery: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "specification", 'finishDate', 'finishDays', "newOldStatus", "note"],
        filterList: ["serialNumber", "status", "newOldStatus", "specification", "location", "owner"]
    },
    gapsub: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "specification", "buckleType", "materialNumber", "note"],
        filterList: ["serialNumber", "status", "specification", "location", "owner"]
    },
    ubho: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "specification", "buckleType", "materialNumber", "note"],
        filterList: ["serialNumber", "status", "specification", "location", "owner"]
    },
    jc: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "specification", "note"],
        filterList: ["serialNumber", "status", "location", "owner"]
    },
    duodian: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "kitNumber", "note"],
        filterList: ["serialNumber", "status", "location", "owner"]
    },
    
    be: {
        fieldList: ['deviceType', 'invName', 'serialNumber', 'status', 'receivedSendDate', 'location', 'owner', 'clientName', 'specification', "loadInDays", 'note'],
        filterList: ["invName", "serialNumber", "status", "specification", "location", "owner"]
    },
    xzdx: {
        fieldList: ['deviceType', 'invName', 'serialNumber', 'status', 'receivedSendDate', 'location', 'kitNumber', 'owner', 'clientName', 'riskType', "note"],
        filterList: ["invName", "serialNumber", "status", "riskType", "location", "owner"]
    },
    lwdj: {
        fieldList: ['deviceType', 'invName', 'serialNumber', 'status', 'receivedSendDate', 'location', 'owner', 'clientName', 'kitNumber', 'note'],
        filterList: ["serialNumber", "status", "location", "owner"]
    },
    tapp: {
        fieldList: ['deviceType', 'invName', 'serialNumber', 'status', 'receivedSendDate', 'location', 'owner', 'clientName', 'riskType', 'kitNumber', 'note'],
        filterList: ["invName", "serialNumber", "status", "riskType", "location", "owner"]
    },
    cwc: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "buckleType", "materialNumber", "note"],
        filterList: ["invName", "serialNumber", "status", "riskType", "location", "owner"]
    },
    wcdj: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "buckleType", "materialNumber", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    sx: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    jshz: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    gammakey: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    },
    pc: {
        fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "kitNumber", "note"],
        filterList: ["serialNumber", "status", "riskType", "location", "owner", "note"]
    },
    // szcy: {
    //     fieldList: ["deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "kitNumber", "owner", "clientName", "riskType", "note"],
    //     filterList: ["serialNumber", "status", "riskType", "location", "owner"]
    // },
    others: {
        fieldList: ["hasSn", "deviceType", "invName", "serialNumber", "status", "receivedSendDate", "location", "owner", "clientName", "riskType", "note"],
    }
}

const getRGB = (rgbA, rgbB, vA, vB, variable) =>{
    return rgbA.map((a, index)=>{
        const b = rgbB[index];
        return parseInt((b-a)*(variable-vA)/(vB-vA) + a);
    })
}
export const finishDateCellRules = day => {
    day = +String(day);
    const green = [0, 204, 102];
    const yellow = [255, 235, 132];
    const red = [255, 0, 0];
    const white = [255, 255, 255];
    if(!isNaN(day)){
        if(day<=120){
            return {
                backgroundColor: `rgb(${green})`
            }
        }else if(day<=150){
            return {
                backgroundColor: `rgb(${getRGB(green, yellow, 120, 150, day)})`
            }
        }else if(day<=180){
            return {
                backgroundColor: `rgb(${getRGB(yellow, red, 150, 180, day)})`
            }
        }else{
            return {
                backgroundColor: `rgb(${red})`
            }
        }
    }
    return {
    }
}
export const loadInDaysCellRules = day => {
    day = +String(day);
    const green = [0, 204, 102];
    const yellow = [255, 235, 132];
    const red = [255, 0, 0];
    const white = [255, 255, 255];
    if(!isNaN(day)){
        if(day<=70){
            return {
                backgroundColor: `rgb(${green})`
            }
        }else if(day<=80){
            return {
                backgroundColor: `rgb(${getRGB(green, yellow, 70, 80, day)})`
            }
        }else if(day<=90){
            return {
                backgroundColor: `rgb(${getRGB(yellow, red, 80, 90, day)})`
            }
        }else{
            return {
                backgroundColor: `rgb(${red})`
            }
        }
    }
    return {
    }
}