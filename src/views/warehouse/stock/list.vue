<template>
    <div>
        <el-form inline :form="searchForm">
            <el-form-item v-if="showFilter('invName')" :label="getLabelByFieldName('invName')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="invNameSuggestList" v-model="searchForm.invName" style="width: 140px" />
            </el-form-item>
            <el-form-item v-if="showFilter('serialNumber')" :label="getLabelByFieldName('serialNumber')">
                <el-input @change="initData(true)" v-model="searchForm.serialNumber" style="width: 140px"></el-input>
            </el-form-item>
            <el-form-item v-if="showFilter('status')" :label="getLabelByFieldName('status')">
                <el-select @change="initData(true)" v-model="searchForm.status" style="width: 180px" clearable>
                    <el-option v-for="item in deviceStatusList" :label="item.name" :value="item.id" :key="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="showFilter('newOldStatus')" :label="getLabelByFieldName('newOldStatus')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="newOldStatusSuggestList" v-model="searchForm.newOldStatus" style="width: 140px" />
            </el-form-item>
            <el-form-item v-if="showFilter('specification')" :label="getLabelByFieldName('specification')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="specificationSuggestList" v-model="searchForm.specification" style="width: 140px" />
            </el-form-item>
            <el-form-item v-if="showFilter('riskType')" :label="getLabelByFieldName('riskType')">
                <el-select @change="initData(true)" v-model="searchForm.riskType" style="width: 120px" clearable>
                    <el-option v-for="item in riskTypeList" :label="item.label" :value="item.value" :key="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="showFilter('location')" :label="getLabelByFieldName('location')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="locationSuggestList" v-model="searchForm.location" style="width: 140px" />
            </el-form-item>
            <el-form-item v-if="showFilter('owner')" :label="getLabelByFieldName('owner')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="ownerSuggestList" v-model="searchForm.owner" style="width: 100px" />
            </el-form-item>
            <el-form-item v-if="showFilter('note')" :label="getLabelByFieldName('note')">
                <FuzzyInput clearable @change="initData(true)" :suggestList="noteSuggestList" v-model="searchForm.note" style="width: 140px" />
            </el-form-item>
            
            <el-form-item label="总计:">
                <span style="font-weight: bold;">{{ tableLoading ? "" : total }}</span>
            </el-form-item>
            <span style="float: right;margin-bottom: 8px;">
                
                <el-popover
                    placement="bottom"
                    width="1200"
                    trigger="manual"
                    v-model="showStatsTable"
                >
                    <StatsTable v-if="showStatsTable" ref="statsTableRef" :deviceStatusList="deviceStatusList" :activePanelName="activePanelName" :stockType="stockType" />
                    <el-button type="primary" @click="onToggleStats" slot="reference" style="margin-right: 10px;">统计</el-button>
                </el-popover>
                <el-button @click="onAdd" type="primary">新增</el-button>
            </span>
        </el-form>
        <el-table :cell-style="cellStyle" :data="tableData" @sort-change="handleSortChange" v-loading="tableLoading" :header-cell-style="commmonTableHeaderCellStyle" height="calc(100vh - 280px)">
            <el-table-column sortable="custom" :label="getLabelByFieldName('invName')" prop="invName" width="100px" v-if="showField('invName')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('deviceType')" prop="deviceType" align="center" width="100px" v-if="showField('deviceType')">
                <template slot-scope="scope">
                    <span>
                        {{ getDeviceTypeStr(scope.row.deviceType) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('serialNumber')" prop="serialNumber" align="center" min-width="100px" v-if="showField('serialNumber')">
                <template slot-scope="{row}">
                    <router-link 
                        tag="a" 
                        style="color: blue" 
                        target="_blank" 
                        :to="`/mwd-tool-maintain/device?type=${row.deviceType}&sn=${row.serialNumber}`" 
                        v-if="p_MwdDeviceInfoView"
                    >
                        {{ row.serialNumber }}
                    </router-link>
                    <span v-else>{{ row.serialNumber }}</span>
                </template>
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('status')" prop="status" align="center" width="200px" v-if="showField('status')">
                <template slot-scope="scope">
                    <CustomTag :key="scope.row.deviceId" tagType="STOCK_DEVICE_STATUS" :tagValue="scope.row.status"></CustomTag>
                </template>
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('newOldStatus')" prop="newOldStatus" align="center" width="120px" v-if="showField('newOldStatus')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('specification')" prop="specification" align="center" min-width="100px" v-if="showField('specification')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('type')" prop="type" align="center" width="150px" v-if="showField('type')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('riskType')" prop="riskType" align="center" width="120px" v-if="showField('riskType')">
                <template slot-scope="scope">
                    <CustomTag tagType="RISK_TYPE" :tagValue="scope.row.riskType" />
                </template>
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('receivedSendDate')" prop="receivedSendDate" align="center" width="150px" v-if="showField('receivedSendDate')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('kitNumber')" prop="kitNumber" align="center" width="150px" v-if="showField('kitNumber')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('configQuantity')" prop="configQuantity" align="center" width="160px" v-if="showField('configQuantity')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('configType')" prop="configType" align="center" width="250px" v-if="showField('configType')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('location')" prop="location" align="center" width="150px" v-if="showField('location')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('owner')" prop="owner" align="center" width="120px" v-if="showField('owner')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('wellSuggest')" prop="wellSuggest" align="center" width="150px" v-if="showField('wellSuggest')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('finishDate')" prop="finishDate" align="center" width="150px" v-if="showField('finishDate')"></el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('finishDays')" prop="finishDays" align="center" width="150px" v-if="showField('finishDays')">
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('loadInDays')" prop="loadInDays" align="center" width="130px" v-if="showField('loadInDays')">
            </el-table-column>
            <!-- <el-table-column sortable="custom" :label="getLabelByFieldName('hasSn')" prop="hasSn" align="center" width="120px" v-if="showField('hasSn')">
                <template slot-scope="scope">
                    <span>{{ scope.row.hasSn === 0 ? '无' : '有'}}</span>
                </template>
            </el-table-column> -->
            <el-table-column sortable="custom" :label="getLabelByFieldName('buckleType')" prop="buckleType" align="center" width="130px" v-if="showField('buckleType')">
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('materialNumber')" prop="materialNumber" align="center" width="130px" v-if="showField('materialNumber')">
            </el-table-column>
            <el-table-column sortable="custom" :label="getLabelByFieldName('note')" prop="note" align="center" width="150px" v-if="showField('note')"></el-table-column>
            <el-table-column label="操作" prop="" align="center" width="160px" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" @click="onEdit(scope)">编辑</el-button>
                    <el-button type="text" @click="onDetail(scope)" v-if="false">详情</el-button>
                    <template v-if="activePanelName==='kitbox'">
                        <el-button
                            type="text"
                            @click="onKitHistory(scope)"
                        >
                            流转历史
                        </el-button>
                        <el-button
                            type="text"
                            @click="onKitLoad(scope)"
                            v-if="scope.row.loadOutId&&loadTypeMap[scope.row.status]&&loadTypeMap[scope.row.status].loadType"
                        >
                            {{ loadTypeMap[scope.row.status].loadTypeStr }}
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            :hide-on-single-page="true"
            style="margin-top: 20px"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            @current-change="onCurrentChange"
            :current-page="currentPage"
        ></el-pagination>
        <EditDialog ref="editDialogRef" @updateList="initData()" :activePanelLabel="activePanelLabel" :activePanelName="activePanelName" :stockType="stockType" :deviceStatusList="deviceStatusList" />
    </div>
</template>
<script>
import EditDialog from './commonDialog.vue';
import { apiGetWarehouseDeviceList, apiGetWarehouseDeviceStatsCondition } from '@/api/warehouse';
import { showPanelField, showPanelFilter, getLabelByFieldName as getLabelByFieldNameAndPanel, finishDateCellRules, loadInDaysCellRules } from './options';
import StatsTable from './statsTable.vue';
import { riskTypeList } from '@/utils/constant';
import FuzzyInput from "@/components/FuzzyInput";
import { loadTypeMap } from '@/utils/constant.warehouse';
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
const KitDeviceType = 16010;
const LucidaDeviceType = 16035;
export default {
    name: 'WarehouseStockKitbox',
    components: { EditDialog, StatsTable, FuzzyInput },
    props: {
        activePanelName: String,
        activePanelLabel: String,
        stockType: Number,
        deviceStatusList: {
            type: Array,
            default: () => []
        },
        deviceTypeList: {
            type: Array,
            default: () => []
        },
        deviceType: Number | Array,
    },
    data() {
        return {
            riskTypeList,
            loadTypeMap,
            tableData: [],
            tableLoading: false,
            total: 0,
            pageSize: 100,
            currentPage: 1,
            showStatsTable: false,
            searchForm: {
                invName: null,
                serialNumber: null,
                status: null,
                specification: null,
                riskType: null,
                location: null,
                owner: null,
                newOldStatus: null,
                note: null,
            },
            ownerSuggestList: [],
            typeSuggestList: [],
            wellSuggestSuggestList: [],
            locationSuggestList: [],
            specificationSuggestList: [],
            filterDeviceTypeList: [],
            noteSuggestList: [],
            invNameSuggestList: [],
            newOldStatusSuggestList: []
        }
    },
    computed: {
        p_MwdDeviceInfoView(){
            return this.$checkBtnPermission('sys:mwd:devicelist:info'); 
        }
    },
    methods: {
        getDeviceTypeStr(deviceType){
            const target = this.deviceTypeList.find(item=>item.id === deviceType);
            return target ? target.name : '';
        },
        cellStyle(data){
            const { column, row } = data;
            if(column.property === 'finishDays'){
                return loadInDaysCellRules(row.finishDays)
            }
            if(column.property === 'loadInDays'){
                return loadInDaysCellRules(row.loadInDays)
            }
        },
        loadInDaysCellRules,
        finishDateCellRules,
        onToggleStats(){
            this.showStatsTable = !this.showStatsTable;
        },
        getLabelByFieldName(filedName){
            return getLabelByFieldNameAndPanel(filedName, this.activePanelName)
        },
        showField(fieldName){
            return showPanelField(this.activePanelName, fieldName);
        },
        showFilter(fieldName){
            return showPanelFilter(this.activePanelName, fieldName);
        },
        getDeviceStatus(statusId){
            // 获取状态名称
            return this.deviceStatusList.find(item=>item.id === statusId)?.name || '';
        },
        initData(bool=false, query = {}) {
            if(query.status){
                this.searchForm.status = Number(query.status);
            }
            if(bool){
                this.currentPage = 1;
            }
            this.tableLoading = true;
            this.getFilterOptions();
            return apiGetWarehouseDeviceList(
                    {
                        stockType: this.stockType,
                        invName: this.searchForm.invName || undefined,
                        serialNumber: this.searchForm.serialNumber || undefined,
                        status: this.searchForm.status || undefined,
                        specification: this.searchForm.specification || undefined,
                        riskType: this.searchForm.riskType || undefined,
                        location: this.searchForm.location || undefined,
                        owner: this.searchForm.owner || undefined,
                        newOldStatus: this.searchForm.newOldStatus || undefined,
                        note: this.searchForm.note || undefined,
                        orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                        orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined,
                    }, 
                    {current: this.currentPage, size: this.pageSize}
                ).then(res=>{
                    const data = res.data.data || {};
                    this.total = data.total;
                    this.tableData = data.records || [];
                }).finally(()=>{
                    this.tableLoading = false;
                })
        },
        async getFilterOptions(){
            if(this.showFilter('invName')){
                this.invNameSuggestList = await this.getSuggestList('invName');
            }
            if(this.showFilter('newOldStatus')){
                this.newOldStatusSuggestList = await this.getSuggestList('newOldStatus');
            }
            if(this.showFilter('owner')){
                this.ownerSuggestList = await this.getSuggestList('owner');
                // this.ownerSuggestList = [{value: 'Tartan'}, {value: 'Client'}]
            }
            if(this.showFilter('wellSuggest')){
                this.wellSuggestSuggestList = await this.getSuggestList('wellSuggest');
            }
            
            if(this.showFilter('location')){
                this.locationSuggestList = await this.getSuggestList('location');
            }
            if(this.showFilter('specification')){
                this.specificationSuggestList = await this.getSuggestList('specification');
            }
            if(this.showFilter('note')){
                this.noteSuggestList = await this.getSuggestList('note');
            }
        },
        getSuggestList(type){
            return new Promise(resolve=>{
                apiGetWarehouseDeviceStatsCondition({ stockType: this.stockType, counterFieldName: type }).then(res=>{
                    const data = (res.data.data || []).map(item=>({ value: item }));
                    resolve(data);
                })
            })
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.initData();
        },
        onStats(){

        },
        onAdd() {
            this.$refs.editDialogRef.showDialog('ADD')
        },
        onDetail(scope){
            this.$refs.applyDetailDialog.showDialog(scope.row)
        },
        onEdit(scope){
            this.$refs.editDialogRef.showDialog('EDIT', scope.row)
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.initData();
        },
        onKitLoad(scope){
            const { loadOutId, deviceType } = scope.row;
            switch (deviceType) {
                case KitDeviceType:
                    this.$router.push(`/warehouse/kit/load?id=${loadOutId}`)
                    break;
                case LucidaDeviceType:
                    this.$router.push(`/warehouse/twelllink/load?id=${loadOutId}`)
                    break;
            }
        },
        onKitHistory(scope){
            const { deviceType } = scope.row;
            switch (deviceType) {
                case KitDeviceType:
                    this.$router.push(`/warehouse/kit/history?sn=${scope.row.serialNumber}`)
                    break;
                case LucidaDeviceType:
                    this.$router.push(`/warehouse/twelllink/history?sn=${scope.row.serialNumber}`)
                    break;
            }
        }
    }
}
</script>