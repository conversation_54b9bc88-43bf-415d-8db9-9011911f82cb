<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>库存盘点</span>
                <el-button @click="onExport" type="primary" style="float: right">导出</el-button>
            </div>
            <el-tabs v-model="activePanelName" @tab-click="onTabClick">
                <el-tab-pane v-for="item in tabsList" :key="item.name" :label="item.label" :name="item.name">
                    <CommonList  v-if="activePanelName===item.name" :deviceTypeList="deviceTypeList" :deviceStatusList="deviceStatusList" :activePanelLabel="item.label" :activePanelName="activePanelName" :stockType="item.stockType" :ref="`${item.name}-list`" />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script>
import getDict from '@/utils/getDict';
import CommonList from "./list.vue";
import { apiExportWarehouseDevice } from '@/api/warehouse';
import { tabsList } from './options';
export default {
    name: 'WarehouseStock',
    components: { CommonList },
    data(){
        return {
            tabsList,
            activePanelName: 'kitbox',
            deviceStatusList: [],
            deviceTypeList: [],
        }
    },
    watch: {
    },
    async mounted(){
        await this.getDictData();
        const query = this.$route.query;
        if(query.stock){
            this.activePanelName = tabsList.find(tab=>tab.stockType==query.stock).name;
        }
        this.$nextTick(()=>{
            this.getPanelData(this.activePanelName, {status: query.status});
        })
    },
    methods: {
        async getDictData(){
            [this.deviceTypeList, this.deviceStatusList] = await getDict([17, 20]);
        },
        onTabClick(tab){
            this.$nextTick(()=>{
                this.getPanelData(tab.name);
            })
        },
        getPanelData(panel, query){
            const activeListRef = this.$refs[`${panel}-list`][0];
            // const activeStatsRef = this.$refs[`${panel}-stats`][0];
            activeListRef.initData?.(undefined, query);
            activeListRef.$children.forEach(component=>{
                component.doLayout?.();
            })
            // activeStatsRef.initData?.();
            // activeStatsRef.$children.forEach(component=>{
            //     component.doLayout?.();
            // })
        },
        onExport(){
            const loading = this.$loading({
                lock: true,
                text: '处理中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            apiExportWarehouseDevice({}).then((res)=>{
                if(!res.data){
                    this.$message.error("暂无数据")
                    return
                }
                const blob = new Blob([res.data], {
                    type: "application/word;charset=UTF-8",
                });
                const fileName = `库房盘点.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
                loading.close();
            })
        }
    }
}
</script>