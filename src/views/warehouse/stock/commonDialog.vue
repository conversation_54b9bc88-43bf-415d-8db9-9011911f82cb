<template>
    <el-dialog :visible.sync="isDialogVisible" :title="isAdd ? '新增' : '编辑'">
        <el-form :model="form" ref="formRef" label-width="auto" :rules="formRules">
            <el-row :gutter="10">
                <el-col :span="8" v-if="showField('hasSn')&&isAdd">
                    <el-form-item :label="getLabelByFieldName('hasSn', activePanelName)">
                        <el-radio-group v-model="form.hasSn" style="width: 100%" @change="onHasSnChange">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('invName')">
                    <el-form-item :label="getLabelByFieldName('invName', activePanelName)">
                        <el-input v-model="form.invName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('serialNumber')">
                    <el-form-item :label="getLabelByFieldName('serialNumber', activePanelName)" prop="serialNumber">
                        <el-input v-model="form.serialNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('deviceType')">
                    <el-form-item :label="getLabelByFieldName('deviceType', activePanelName)">
                        <el-select
                            style="width: 100%"
                            v-model="form.deviceType"
                        >
                            <el-option
                                v-for="item in filterDeviceTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('type')">
                    <el-form-item :label="getLabelByFieldName('type', activePanelName)">
                        <FuzzyInput :suggestList="typeSuggestList" v-model="form.type" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('specification')">
                    <el-form-item :label="getLabelByFieldName('specification', activePanelName)">
                        <FuzzyInput :suggestList="specificationSuggestList" v-model="form.specification" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('newOldStatus')">
                    <el-form-item :label="getLabelByFieldName('newOldStatus', activePanelName)">
                        <el-input v-model="form.newOldStatus" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('riskType')">
                    <el-form-item label="风险类型">
                        <el-select
                            clearable
                            v-model="form.riskType"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option
                                v-for="item in riskTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('receivedSendDate')">
                    <el-form-item :label="getLabelByFieldName('receivedSendDate', activePanelName)" label-width="110px">
                        <el-date-picker
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="form.receivedSendDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('kitNumber')">
                    <el-form-item :label="getLabelByFieldName('kitNumber', activePanelName)">
                        <FuzzyInput :suggestList="kitNumberSuggestList" v-model="form.kitNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('status')">
                    <el-form-item :label="getLabelByFieldName('status', activePanelName)">
                        <el-select v-model="form.status" style="width: 100%">
                            <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('configQuantity')">
                    <el-form-item :label="getLabelByFieldName('configQuantity', activePanelName)" label-width="130px">
                        <el-input v-model="form.configQuantity" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('configType')">
                    <el-form-item :label="getLabelByFieldName('configType', activePanelName)"">
                        <el-input v-model="form.configType" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('location')">
                    <el-form-item :label="getLabelByFieldName('location', activePanelName)">
                        <FuzzyInput :suggestList="locationSuggestList" v-model="form.location" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('owner')">
                    <el-form-item :label="getLabelByFieldName('owner', activePanelName)">
                        <FuzzyInput :suggestList="ownerNumberSuggestList" v-model="form.owner" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('clientName')">
                    <el-form-item :label="getLabelByFieldName('clientName', activePanelName)">
                        <el-input v-model="form.clientName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('wellSuggest')">
                    <el-form-item :label="getLabelByFieldName('wellSuggest', activePanelName)">
                        <FuzzyInput :suggestList="wellSuggestSuggestList" v-model="form.wellSuggest" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('finishDate')">
                    <el-form-item :label="getLabelByFieldName('finishDate', activePanelName)">
                        <el-date-picker
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="form.finishDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('buckleType')">
                    <el-form-item :label="getLabelByFieldName('buckleType', activePanelName)">
                        <el-input v-model="form.buckleType" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('materialNumber')">
                    <el-form-item :label="getLabelByFieldName('materialNumber', activePanelName)">
                        <el-input v-model="form.materialNumber" />
                    </el-form-item>
                </el-col>
                <el-col :span="8" v-if="showField('note')">
                    <el-form-item :label="getLabelByFieldName('note', activePanelName)">
                        <el-input v-model="form.note" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <el-button @click="isDialogVisible=false">
                取消
            </el-button>
            <el-button type="primary" @click="onSave">
                保存
            </el-button>
        </template>
    </el-dialog>
</template>
<script>
import { apiAddWarehouseDeviceList, apiGetWarehouseDeviceStatsCondition, apiUpdateWarehouseDeviceList } from '@/api/warehouse';
import { showPanelField, fullFieldForm, getLabelByFieldName } from "./options";
import FuzzyInput from "@/components/FuzzyInput"
import { riskTypeList } from '@/utils/constant';
import { apiGetDictRelationList } from '@/api/dict';
export default {
    name: 'WarehouseStockKitboxDialog',
    components: { FuzzyInput },
    props: {
        activePanelName: String,
        activePanelLabel: String,
        stockType: Number,
        deviceStatusList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            riskTypeList,
            isDialogVisible: false,
            dialogType: 'ADD',
            form: { ...fullFieldForm },
            kitNumberSuggestList: [],
            ownerNumberSuggestList: [],
            typeSuggestList: [],
            wellSuggestSuggestList: [],
            locationSuggestList: [],
            specificationSuggestList: [],
            filterDeviceTypeList: [],
            formRules: {
                serialNumber: [
                    {
                        validator: (_,__,cb) => {
                            if(this.form.hasSn === 1){
                                if(!this.form.serialNumber){
                                    cb(new Error('请输入序列号'));
                                }
                            }
                            cb();
                        }
                    }
                ]
            }
        }
    },
    computed: {
        isAdd(){
            return this.dialogType === 'ADD'
        }
    },
    methods: {
        getLabelByFieldName,
        showField(fieldName){
            return showPanelField(this.activePanelName, fieldName)
        },
        async showDialog(type, row){
            this.dialogType = type;
            this.filterDeviceTypeList = await this.getStockDeviceTypeList(this.stockType);
            if(this.showField('type')){
                this.typeSuggestList = await this.getSuggestList('type');
            }
            if(this.showField('kitNumber')){
                this.kitNumberSuggestList = await this.getSuggestList('kitNumber');
            }
            if(this.showField('owner')){
                // this.ownerNumberSuggestList = await this.getSuggestList('owner');
                this.ownerNumberSuggestList = [{value: 'Tartan'}, {value: 'Client'}]
            }
            if(this.showField('wellSuggest')){
                this.wellSuggestSuggestList = await this.getSuggestList('wellSuggest');
            }
            
            if(this.showField('location')){
                // this.locationSuggestList = await this.getSuggestList('location');
                this.locationSuggestList = [{value: 'Chengdu Shop'}]
            }
            if(this.showField('specification')){
                this.specificationSuggestList = await this.getSuggestList('specification');
            }
            if(type == 'ADD'){
                this.form = {
                    ...fullFieldForm,
                    hasSn: 1,
                    invName: this.activePanelLabel === '其他'||this.activePanelLabel==='底部总成/扶正器' ? null : this.activePanelLabel,
                    stockType: this.stockType,
                    deviceType: this.filterDeviceTypeList[0]?.id,
                }
            }else{
                this.form = { ...row };
            }
            this.isDialogVisible = true;
            this.$nextTick(()=>{
                this.$refs.formRef.clearValidate();
            })
        },
        getStockDeviceTypeList(stockType) {
            return new Promise(resolve=>{
                apiGetDictRelationList({parentId: stockType}).then(res=>{
                    resolve((res.data.data || []).map(item=>({
                        id: item.subId,
                        name: item.subName
                    })))
                })
            })

        },
        getSuggestList(type){
            return new Promise(resolve=>{
                apiGetWarehouseDeviceStatsCondition({ stockType: this.stockType, counterFieldName: type }).then(res=>{
                    const data = (res.data.data || []).map(item=>({ value: item }));
                    resolve(data);
                })
            })
        },
        onHasSnChange(){
            this.$refs.formRef.validate();
        },
        async onSave(){
            const valid = await this.$refs.formRef.validate();
            if(!valid){
                return
            }
            if(this.isAdd){
                apiAddWarehouseDeviceList(this.form).then(()=>{
                    this.$message.success("操作成功");
                    this.$emit("updateList");
                    this.isDialogVisible = false;
                })
            }else{
                apiUpdateWarehouseDeviceList(this.form).then(()=>{
                    this.$message.success("操作成功");
                    this.$emit("updateList");
                    this.isDialogVisible = false;
                })
            }
        }
    }
}
</script>