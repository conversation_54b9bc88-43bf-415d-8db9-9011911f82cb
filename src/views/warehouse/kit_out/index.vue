<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">
                MWD LOAD OUT LIST - 出库
                <span style="float: right;">
                    <el-button type="primary">导出评估单</el-button>
                    <el-button type="primary" @click="onSaveAndSync">保存并同步库存数据</el-button>
                    <el-button type="primary" @click="onSave">保存</el-button>
                </span>
            </div>
            <el-form :model="loadOutForm" label-width="50px" style="margin-top: 20px;">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="KIT箱" label-width="50px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.serialNumber">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="井号(或地点)" label-width="100px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.location">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="清点人员" label-width="70px">
                            <el-input style="width: calc(100%)" clearable v-model="loadOutForm.checkMember">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出库日期" label-width="70px">
                            <el-date-picker
                                v-model="loadOutForm.dateOut"
                                style="width: calc(100%)"
                                type="date"
                                placeholder=""
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <LoadOutList type="OUT" :loadOutDetailList="loadOutForm.loadOutDetailList" />
            <SyncTable ref="syncTableRef" type="OUT" :loadOutForm="loadOutForm" />
        </div>
    </div>
</template>
<script>
import { apiGetLoadOutInfo, apiGetLoadOutListTemplate, apiGetWarehouseDeviceInfo, apiKitOut, apiUpdateLoadOut } from '@/api/warehouse';
import LoadOutList from '../components/LoadOutList.vue';
import SyncTable from '../components/SyncTable.vue';
export default {
    name: 'WarehouseKitOut',
    components: { LoadOutList, SyncTable },
    data(){
        return {
            loadOutForm: {
                serialNumber:"",
                checkMember: "",
                dateOut:"",
                location:"", 
                loadOutDetailList: []
            },
        }
    },
    async mounted(){
        const loadOutId = this.$route.query.id;
        if(loadOutId){
            const loadOutInfo = await apiGetLoadOutInfo({loadOutId}).then(res => res.data?.data || {});
            this.loadOutForm = loadOutInfo;
        }
    },
    methods: {
        initData(flag){},
        onSaveAndSync(){
            this.$refs.syncTableRef.showDialog('OUT', [])
        },
        onSave(){
            if(this.loadOutForm.loadOutId){
                apiUpdateLoadOut(this.loadOutForm);
                this.$message.success('操作成功');
            }else{
                apiKitOut(this.loadOutForm).then(res=>{
                    this.$message.success('操作成功');
                    this.$router.push(`/redirect/warehouse/kit/out?id=${res.data.data.loadOutId}`)
                })
            }
        },
    }
}
</script>
<style lang="scss">
</style>