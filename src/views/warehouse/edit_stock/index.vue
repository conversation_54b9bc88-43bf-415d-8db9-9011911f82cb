<template>
    <div>
        <div style="height: calc(100vh - 126px);overflow-y: auto;">
            <van-cell-group>
                <template>
                    <VanSelect v-model="searchForm.stockType" :options="stockTypeList" @change="onStockTypeChange" value-key="stockType" label-key="label" input-align="right" name="库位类型" label="库位类型" placeholder="库位类型" />
                </template>
                <!-- <van-field v-model="searchForm.invName" input-align="right" name="品名" label="品名" placeholder="品名"></van-field> -->
                <template>
                    <VanSearch
                        v-model="searchForm.serialNumber"
                        @change="onSerialNumberChange"
                        type="SN_IN_STOCK"
                        :restParams="{ stockType: searchForm.stockType}"
                        input-align="right"
                        name="序列号"
                        label="序列号"
                        placeholder="序列号"
                    />
                </template>
            </van-cell-group>
            <van-cell-group style="margin-top: 10px;" v-if="deviceInfo.deviceId">
                <span>
                    <van-cell v-if="showField('invName')" :title="getLabelByFieldName('invName', activePanelName)" :value="deviceInfo.invName" />
                    <van-cell v-if="showField('serialNumber')" :title="getLabelByFieldName('serialNumber', activePanelName)" :value="deviceInfo.serialNumber" />
                    <van-cell v-if="showField('deviceType')" :title="getLabelByFieldName('deviceType', activePanelName)" :value="getDeviceTypeStr(deviceInfo.deviceType)" />
                    <template v-if="showField('type')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('type', activePanelName)" :value="deviceInfo.type" />
                        <van-field v-else v-model="deviceInfo.type" :label="getLabelByFieldName('type', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('specification')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('specification', activePanelName)" :value="deviceInfo.specification" />
                        <van-field v-else v-model="deviceInfo.specification" :label="getLabelByFieldName('specification', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('newOldStatus')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('newOldStatus', activePanelName)" :value="deviceInfo.newOldStatus" />
                        <van-field v-else v-model="deviceInfo.newOldStatus" :label="getLabelByFieldName('newOldStatus', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('riskType')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('riskType', activePanelName)" :value="deviceInfo.riskType">
                            <CustomTag tagType="RISK_TYPE" :tagValue="deviceInfo.riskType"></CustomTag>
                        </van-cell>
                        <VanSelect v-else v-model="deviceInfo.riskType" :options="riskTypeList" value-key="value" label-key="label" input-align="right" :label="getLabelByFieldName('riskType', activePanelName)" />
                    </template>

                    <template v-if="showField('receivedSendDate')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('receivedSendDate', activePanelName)" :value="deviceInfo.receivedSendDate" />
                        <VanDatePicker v-else v-model="deviceInfo.receivedSendDate" :label="getLabelByFieldName('receivedSendDate', activePanelName)" input-align="right" label-width="120"></VanDatePicker>
                    </template>

                    <template v-if="showField('kitNumber')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('kitNumber', activePanelName)" :value="deviceInfo.kitNumber" />
                        <VanSearch
                            v-else
                            v-model="deviceInfo.kitNumber"
                            type="SN_IN_STOCK"
                            :restParams="{ stockType: 20001 }"
                            input-align="right"
                            :label="getLabelByFieldName('kitNumber', activePanelName)"
                            label-width="120"
                            searchType="INPUT"
                        />
                    </template>

                    <template v-if="showField('status')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('status', activePanelName)" :value="deviceInfo.status">
                            <CustomTag tagType="STOCK_DEVICE_STATUS" :tagValue="deviceInfo.status"></CustomTag>
                        </van-cell>
                        <VanSelect v-else v-model="deviceInfo.status" :options="deviceStatusList" value-key="id" label-key="name" input-align="right" :label="getLabelByFieldName('status', activePanelName)" />
                    </template>

                    <template v-if="showField('configQuantity')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('configQuantity', activePanelName)" :value="deviceInfo.configQuantity" />
                        <van-field v-else v-model="deviceInfo.configQuantity" :label="getLabelByFieldName('configQuantity', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('configType')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('configType', activePanelName)" :value="deviceInfo.configType" />
                        <van-field v-else v-model="deviceInfo.configType" :label="getLabelByFieldName('configType', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('location')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('location', activePanelName)" :value="deviceInfo.location" />
                        <van-field v-else v-model="deviceInfo.location" :label="getLabelByFieldName('location', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('owner')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('owner', activePanelName)" :value="deviceInfo.owner" />
                        <van-field v-else v-model="deviceInfo.owner" :label="getLabelByFieldName('owner', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('clientName')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('clientName', activePanelName)" :value="deviceInfo.clientName" />
                        <van-field v-else v-model="deviceInfo.clientName" :label="getLabelByFieldName('clientName', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('wellSuggest')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('wellSuggest', activePanelName)" :value="deviceInfo.wellSuggest" />
                        <van-field v-else v-model="deviceInfo.wellSuggest" :label="getLabelByFieldName('wellSuggest', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('finishDate')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('finishDate', activePanelName)" :value="deviceInfo.finishDate" />
                        <VanDatePicker v-else v-model="deviceInfo.finishDate" :label="getLabelByFieldName('finishDate', activePanelName)" input-align="right" label-width="120"></VanDatePicker>
                    </template>

                    <template v-if="showField('buckleType')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('buckleType', activePanelName)" :value="deviceInfo.buckleType" />
                        <van-field v-else v-model="deviceInfo.buckleType" :label="getLabelByFieldName('buckleType', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('materialNumber')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('materialNumber', activePanelName)" :value="deviceInfo.materialNumber" />
                        <van-field v-else v-model="deviceInfo.materialNumber" :label="getLabelByFieldName('materialNumber', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>

                    <template v-if="showField('note')">
                        <van-cell v-if="!isEdit" :title="getLabelByFieldName('note', activePanelName)" :value="deviceInfo.note" />
                        <van-field v-else v-model="deviceInfo.note" type="textarea" rows="2" autosize :label="getLabelByFieldName('note', activePanelName)" input-align="right" label-width="120"></van-field>
                    </template>
                </span>
            </van-cell-group>
        </div>
        <div style="position: fixed; width: 100%; bottom: 0; padding: 10px;background: white;" v-if="deviceInfo.deviceId">
            <el-row :gutter="10" v-if="isEdit">
                <el-col :span="8">
                    <el-button style="width: 100%;" @click="onCancel">取消</el-button>
                </el-col>
                <el-col :span="16">
                    <el-button type="primary" style="width: 100%;" @click="onConfirm">确定</el-button>
                </el-col>
            </el-row>
            <el-row v-else>
                <el-col :span="24">
                    <el-button type="primary" @click="onEdit" style="width: 100%;">编辑</el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>
import VanSelect from "@/components/mobile/VanSelect";
import VanSearch from "@/components/mobile/VanSearch";
import VanDatePicker from "@/components/mobile/VanDatePicker";
import { showPanelField, getLabelByFieldName, tabsList } from "../stock/options";
import { apiGetWarehouseDeviceInfo, apiUpdateWarehouseDeviceList } from "@/api/warehouse";
import { riskTypeList } from "@/utils/constant";
import getDict from "@/utils/getDict";
export default {
    name: "AddRepairAddDevice",
    components: { VanSelect, VanSearch, VanDatePicker },
    data(){
        return {
            activePanelName: "kitbox",
            riskTypeList,
            deviceStatusList: [],
            searchForm: {
                stockType: 20002,
                serialNumber: null,
            },
            deviceInfo: {
                deviceId: null,
            },
            stockTypeList: tabsList,
            isEdit: false,
            deviceTypeList: []
        }
    },
    async mounted(){
        [this.deviceTypeList, this.deviceStatusList] = await getDict([17, 20]);
    },
    methods: {
        getLabelByFieldName,
        getDeviceTypeStr(deviceType) {
            return this.deviceTypeList.find((item) => item.id == deviceType)?.name || "";
        },
        showField(fieldName){
            const name = tabsList.find(item=>item.stockType == this.searchForm.stockType)?.name
            return showPanelField(name, fieldName)
        },
        getDeviceInfo(){
            return apiGetWarehouseDeviceInfo({ deviceId: this.deviceId }).then(res=>{
                this.deviceInfo = res.data.data || {}
            })
        },
        onConfirm(){
            apiUpdateWarehouseDeviceList(this.deviceInfo).then(async()=>{
                await this.getDeviceInfo();
                this.isEdit = false;
                this.$message.success("操作成功");
            })
            
        },
        onStockTypeChange(){
            this.isEdit = false;
            this.searchForm.serialNumber = null;
            this.deviceInfo = { deviceId: null}
        },
        onSerialNumberChange(item){
            this.deviceId = item.deviceId;
            this.isEdit = false;
            this.getDeviceInfo();
        },
        onEdit(){
            this.isEdit = true
        },
        async onCancel(){
            await this.getDeviceInfo();
            this.isEdit = false
        }
    }
}
</script>
<style lang="scss" scoped>
</style>