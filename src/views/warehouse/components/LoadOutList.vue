<template>
    <div>
        <el-table :data="[]" class="no-empty-text-table" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="零件号" width="300px"></el-table-column>
            <el-table-column label="名称"></el-table-column>
            <el-table-column label="序列号" width="200px"></el-table-column>
            <el-table-column label="需求数量" align="center" width="100px"></el-table-column>
            <el-table-column label="实发数量" align="center" width="100px"></el-table-column>
            <el-table-column label="入库数量" align="center" width="100px" v-if="loadType==='IN'"></el-table-column>
            <el-table-column label="备注" align="center" width="200px" v-if="loadType==='IN'"></el-table-column>
        </el-table>
        <div v-for="item in loadOutDetailList" :key="item.typeName">
            <div style="text-align: center; height: 30px; line-height: 30px; color: #1f2d3d; font-weight: bold; background-color: #f0f2f5;">
                {{ item.typeName }}
            </div>
            <el-table :data="item.units" :show-header="false">
                <el-table-column prop="unitNumber" label="零件号" width="300px"></el-table-column>
                <el-table-column prop="unitName" label="名称"></el-table-column>
                <el-table-column prop="serialNumber" label="序列号" width="200px">
                    <template slot-scope="scope">
                        <FuzzySelect v-if="loadType!='IN'&&scope.row.deviceTypeList" :restParams="{deviceTypeListStr: scope.row.deviceTypeList}" v-model="scope.row.serialNumber" type="DEVICE_SN"></FuzzySelect>
                        <span v-else>{{ scope.row.serialNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="requireAmount" label="需求数量" align="center" width="100px"></el-table-column>
                <el-table-column prop="actualAmount" label="实发数量" align="center" width="100px">
                    <template slot-scope="scope">
                        <el-input v-if="loadType==='OUT'" size="small" v-model="scope.row.actualAmount" style="width: 100%;"></el-input>
                        <span v-else>{{ scope.row.actualAmount }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="deliverNumber" label="入库数量" align="center" width="100px" v-if="loadType==='IN'">
                    <template slot-scope="scope">
                        <el-input size="small" v-model="scope.row.deliverNumber" style="width: 100%;"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="note" label="备注" align="center" width="200px" v-if="loadType==='IN'">
                    <template slot-scope="scope">
                        <el-input size="small" v-model="scope.row.note" style="width: 100%;"></el-input>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import FuzzySelect from '@/components/FuzzySelect';
export default {
    name: 'LoadOutList',
    components: { FuzzySelect },
    props: {
        loadOutDetailList: {
            type: Array,
            default: () => []
        },
        loadType: {
            type: String,
            default: 'OUT'
        }
    },
    data(){
        return {}
    },
    methods: {
    }
}
</script>
<style lang="scss">
.no-empty-text-table{
    .el-table__empty-block{
        display: none;
    }
}
</style>