<template>
    <el-dialog :visible.sync="isDialogVisible" width="80%" title="同步仪器库存信息">
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column prop="invName" label="品名" width="100px"></el-table-column>
            <el-table-column align="center" prop="serialNumber" label="序列号" width="200px"></el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="200px">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.status" style="width: 100%">
                        <el-option v-for="item in deviceStatusList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="receivedSendDate" label="接收/发出日期" width="120px"></el-table-column>
            <el-table-column align="center" prop="kitNumber" label="所在BOX" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.kitNumber" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="location" label="地点" width="160px">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.location" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column v-if="loadType==='IN'&&tableData.some(item=>isBattery(item))" align="center" prop="finishDate" label="完工日期" width="160px">
                <template slot-scope="scope">
                    <el-date-picker
                        v-if="isBattery(scope.row)"
                        placement="bottom-start"
                        clearable
                        style="width: 100%"
                        value-format="yyyy-MM-dd"
                        v-model="scope.row.finishDate"
                        type="date"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column v-if="loadType==='IN'&&tableData.some(item=>isBattery(item))" align="center" prop="finishDays" label="完工时间" width="100px">
                <template slot-scope="scope">
                    <el-input v-if="isBattery(scope.row)" v-model="scope.row.finishDays" style="width: 100%"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width: 100%"></el-input>
                </template>
            </el-table-column>
        </el-table>
        <template #footer>
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onConfirm">保存</el-button>
        </template>
    </el-dialog>
</template>
<script>
import getDict from '@/utils/getDict';
export default {
    name: "SyncTable",
    data(){
        return {
            isDialogVisible: false,
            deviceStatusList: [],
            tableData: [{}]
        }
    },
    props: {
        loadType: {
            type: String,
            default: 'OUT'
        }
    },
    methods: {
        isBattery(item){
            return item.deviceTypeList && item.deviceTypeList.includes("16004")
        },
        async showDialog(deviceIdList) {
            [this.deviceStatusList] = await getDict([20]);
            // TODO: 根据deviceIdList获取数据
            this.isDialogVisible = true;
        },
        onCancel(){
            this.isDialogVisible = false;
        },
        onConfirm(){
            // 入库 且 kit箱状态从At Rig变为Ready
            if(this.type==='IN'){
                if(this.tableData[0].status===19003){
                    this.$confirm('kit箱状态修改为Ready后将完成入库，确认修改？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.syncData();
                        this.createLoadOut();
                    })
                }else{
                    this.syncData();
                }
            }
            // 配置 且 kit箱状态从Ready变为Repair
            if(this.type==='SET'){
                if(this.tableData[0].status===19002){
                    this.$confirm('kit箱状态修改为Repair后将完成配置，确认修改？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.syncData();
                        this.createLoadOut();
                    })
                }else{
                    this.syncData();
                }
            }
            // 出库 且 kit箱状态从Repair变为At Rig
            if(this.type==='OUT'){
                if(this.tableData[0].status===19001){
                    this.$confirm('kit箱状态修改为At Rig后将完成出库，确认修改？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.syncData();
                        this.createLoadOut();
                    })
                }else{
                    this.syncData();
                }
            }
        },
        createLoadOut(){},
        syncData(){}
    }
}
</script>
