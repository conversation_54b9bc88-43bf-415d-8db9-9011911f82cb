<template>
    <div>
        <el-form :model="searchForm" inline>
            <el-form-item label="时间" label-width="40px">
                <el-date-picker
                    style="width: 260px"
                    placement="bottom-start"
                    clearable
                    @change="onDaterangeChange"
                    value-format="yyyy-MM-dd"
                    v-model="searchForm.daterange"
                    class="fixed-separator-date-picker"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
                <DaterangeShortcut class="form-radio-group" @change="onDaterangeChange" v-model="searchForm.daterange" style="margin-left:10px" />
            </el-form-item>
            <span style="float: right;margin-top:10px;font-size: 18px;">
                <span style="font-weight: 700;">总计：</span>{{totalLaborDays}}人天({{totalLaborHours}}工时)
            </span>
        </el-form>
        <el-table :data="tableData" style="margin-top:-10px" :header-cell-style="commmonTableHeaderCellStyle" v-loading="tableLoading">
            <el-table-column
                prop="laborDate"
                label="日期"
            ></el-table-column>
            <el-table-column
                prop="week"
                label="星期"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="userCount"
                label="上报人数/总人数"
                align="center"
            >
                <template slot-scope="{row}">
                    <span style="cursor: pointer;color: #004e7e" @click="onClickUserCount(row)">{{ row.userCount }} / {{ row.userTotal }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="totalLaborHours"
                label="总工时(h)"
                align="center"
            ></el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onDetail(scope)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog :visible.sync="showInfoDialog" width="300px" :show-close="false" custom-class="submit-info-dialog">
            <div style="text-align:right;display: flex;">
                <div style="min-width: 48%;">
                    <div style="text-align:center;margin-bottom: 10px;">未填写</div>
                    <el-scrollbar wrap-class="scrollbar-wrapper">
                        <div style="margin-bottom: 4px;padding: 0 16px;" v-for="item in infoList.filter(item=>item.filled===-1)" :key="item.submitMemberId">{{ item.submitUserName }}  ❌</div>
                    </el-scrollbar>
                </div>
                <div style="border-right:1px solid #ccc;margin-top:30px;"></div>
                <div style="min-width: 48%;">
                    <div style="text-align:center;margin-bottom: 10px;">已填写</div>
                    <el-scrollbar wrap-class="scrollbar-wrapper">
                        <div style="margin-bottom: 4px;padding: 0 16px;" v-for="item in infoList.filter(item=>item.filled===1)" :key="item.submitMemberId">{{ item.submitUserName }}  ✅</div>
                    </el-scrollbar>
                </div>
                
            </div>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DaterangeShortcut, { getThisWeekDaterange } from "@/components/DaterangeShortcut/index.vue"
import momentjs from "moment";
import { apiGetSubmitInfoByDay, apiGetTotalStats } from "@/api/workhours";
@Component({
    components:{ DaterangeShortcut }
})
export default class extends Vue {
    private searchForm: any = {
        daterange: getThisWeekDaterange()
    };
    private totalLaborDays = 0;
    private totalLaborHours = 0;
    private tableData: any[] = [];
    private tableLoading = false;
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private showInfoDialog = false;
    private infoList: any[] = [];
    private mounted() {
        this.initData();
    }
    private activated(){
        this.initData();
    }
    private onDaterangeChange(){
        if(!this.searchForm.daterange){
            this.$message.error("请选择一个时间区间！")
            return;
        }
        const [startDate, endDate] = this.searchForm.daterange;
        // 如果时间跨度超过3个月, 直接return, moment取的floor
        if(momentjs(endDate).diff(startDate, 'months') > 2){
            this.$message.error("查询时间跨度不能超过3个月！")
            return;
        }
        this.initData();
    }
    private initData() {
        const form = {
            startDate: this.searchForm.daterange?.[0] || undefined,
            endDate: this.searchForm.daterange?.[1] || undefined,
        }
        this.tableLoading = true;
        apiGetTotalStats(form).then((res) => {
            const { laborHourByDaysList, totalLaborDays, totalLaborHours } = res.data.data || {};
            this.tableData = laborHourByDaysList || [];
            this.totalLaborDays = totalLaborDays;
            this.totalLaborHours = totalLaborHours;
        }).finally(() => {
            this.tableLoading = false;
        });
    }
    private onDetail(scope: any) {
        const date = scope.row.laborDate
        this.$router.push(`/workhours/info?date=${date}`)
    }
    private async onClickUserCount(row){
        const loading = this.$loading({
            lock: true,
            text: '正在获取数据...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        try {
            const list = await this.getSubmitInfo(row);
            this.infoList = list;
            this.showInfoDialog = true;
        }finally {
            loading.close();
        }
    }
    private getSubmitInfo(row){
        return apiGetSubmitInfoByDay({laborDate: row.laborDate}).then(res=>{
            return res.data.data || [];
        })
    }
}
</script>
<style lang="scss">
.submit-info-dialog{
    .el-dialog__header{
        padding: 0
    }
    .el-dialog__body{
        padding: 10px 10px;
    }
    
    .el-scrollbar {
        overflow-x: hidden;
        .el-scrollbar__wrap{
            max-height: 60vh; // 最大高度
            overflow-x: hidden !important;
        }
    }
}
</style>

<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>