<!-- NOTE: 这个页面是keepalive的 -->
<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>工时统计</span>
            </div>
            <el-tabs v-model="activePanelName" @tab-click="onTabClick">
                <el-tab-pane label="总体统计" name="total">
                    <Total ref="total" />
                </el-tab-pane>
                <el-tab-pane label="项目统计" name="project">
                    <Project ref="project" />
                </el-tab-pane>
                <el-tab-pane label="人员工时统计" name="hours">
                    <Hours ref="hours" />
                </el-tab-pane>
                <el-tab-pane label="工时审批" name="approve">
                    <Approve ref="approve" />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import Total from "./total/_index.vue";
import Project from "./project/_index.vue";
import Hours from "./hours/_index.vue";
import Approve from "./approve/_index.vue";
const tabList = ['total', 'project', 'hours', 'approve']
@Component({
    components: {
        Total,
        Project,
        Hours,
        Approve
    }
})
export default class extends Vue {
    private activePanelName = "total";
    private mounted() {
        const tab = this.$route.query.tab as string|undefined;
        if(tab && tabList.includes(tab) ){
            this.activePanelName = tab;
        }
    }
    private onTabClick({paneName}) {
        this.activePanelName = paneName;
        // fixbug: 解决切换tab导致table的闪烁问题, 但不能使用v-if, 因为会触发mounted影响缓存
        this.$nextTick(()=>{
            const paneRef = this.$refs[paneName];
            (paneRef as any)?.initData?.();
            (paneRef as any)?.$children?.forEach((component: any)=>{
                component.doLayout?.();
            })
        })
    }
}
</script>
<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>