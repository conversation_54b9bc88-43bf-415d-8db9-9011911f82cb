<template>
    <div>
        <el-form :model="searchForm" inline>
            <el-form-item label="员工姓名" label-width="70px">
                <FuzzySelect multiple type="USER_FOR_PM" collapse-tags @change="initData(true)" v-model="searchForm.submitMemberIdList" style="width: 200px;" />
            </el-form-item>
            <el-form-item label="办公区域" label-width="70px">
                <el-select @change="initData(true)" v-model="searchForm.sector" style="width:140px" clearable>
                    <el-option v-for="item in sectorList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="工时过滤" label-width="70px">
                <el-select @change="initData" v-model="searchForm.costHourCondition" style="width:140px" clearable>
                    <el-option v-for="item in conditionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="时间" label-width="40px">
                <el-date-picker
                    style="width: 260px"
                    placement="bottom-start"
                    clearable
                    @change="onDaterangeChange"
                    value-format="yyyy-MM-dd"
                    v-model="searchForm.daterange"
                    class="fixed-separator-date-picker"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
                <DaterangeShortcut class="form-radio-group" @change="onDaterangeChange" v-model="searchForm.daterange" style="margin-left:10px" />
            </el-form-item>
            <el-button @click="onExport" type="primary" style="float: right;margin-bottom:10px">导出</el-button>
        </el-form>
        <el-table @sort-change="onSortChange" :data="tableData" style="margin-top: -10px" :header-cell-style="commmonTableHeaderCellStyle" v-loading="tableLoading">
            <el-table-column
                prop="submitUserName"
                label="员工姓名"
                width="120px"
                sortable="custom"
            ></el-table-column>
            <el-table-column
                prop="departmentName"
                label="所属部门"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column
                prop="projectName"
                label="项目名称"
                align="center"
                sortable="custom"
            ></el-table-column>
            <el-table-column
                prop="projectNumber"
                label="项目编号"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column
                prop="projectManagerName"
                label="项目经理"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column
                prop="totalCostHour"
                label="总工时(h)"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column
                prop="costHour"
                label="花费工时(h)"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column
                prop="percentHour"
                label="工时占比(%)"
                align="center"
                width="120px"
            ></el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onDetail(scope)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        
        <el-pagination
            :hide-on-single-page="true"
            style="margin-top: 20px"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            @current-change="onCurrentChange"
            :current-page="currentPage"
        ></el-pagination>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DaterangeShortcut, { getThisMonthDaterange } from "@/components/DaterangeShortcut/index.vue"
import FuzzySelect from "@/components/FuzzySelect/index.vue"
import momentjs from "moment";
import { apiGetHoursStats } from "@/api/workhours";
import { sectorList } from "@/utils/constant.workhours"
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({
    components:{ DaterangeShortcut, FuzzySelect }
})
export default class extends Vue {
    private conditionList = [
        {value: 'gt', label: '有工时'},
        {value: 'eq', label: '无工时'},
    ]
    private searchForm: any = {
        daterange: getThisMonthDaterange(),
    };
    private tableData: any[] = [];
    private tableLoading = false;
    private currentPage = 1;
    private pageSize = 9999;
    private total = 0;
    private mounted() {
        this.initData();
    }
    private activated(){
        this.initData();
    }
    private sectorList = sectorList;
    private onDaterangeChange(){
        if(!this.searchForm.daterange){
            this.$message.error("请选择一个时间区间！")
            return;
        }
        const [startDate, endDate] = this.searchForm.daterange;
        // 如果时间跨度超过3个月, 直接return, moment取的floor
        if(momentjs(endDate).diff(startDate, 'months') > 2){
            this.$message.error("查询时间跨度不能超过3个月！")
            return;
        }
        this.initData(true);
    }
    private initData(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const form = {
            submitMemberIdList: this.searchForm.submitMemberIdList || undefined,
            sector: this.searchForm.sector || undefined,
            startDate: this.searchForm.daterange?.[0] || undefined,
            endDate: this.searchForm.daterange?.[1] || undefined,
            costHourCondition: this.searchForm.costHourCondition || undefined,
            orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
            orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
        }
        this.tableLoading = true;
        apiGetHoursStats(form, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        }).finally(() => {
            this.tableLoading = false;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    
    private onSortChange({ prop, order }: any) {
        const propMap = {
            projectName: 'project',
            submitUserName: 'submitMember'
        }
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = propMap[prop];
        this.initData();
    }
    private onDetail(scope: any) {
        const { submitMemberId, submitUserName, projectName } = scope.row;
        this.$router.push(`/workhours/info?userid=${submitMemberId}&username=${submitUserName}&pname=${projectName}`)
    }
    private formatJson(filterVal: any, jsonData: any) {
        return jsonData.map((v: any) =>
            filterVal.map((j: any) => {
                return v[j];
            })
        );
    }
    private onExport() {
        let header = [
            "员工姓名",
            "所属部门",
            "项目名称",
            "项目编号",
            "项目经理",
            "总工时(h)",
            "花费工时(h)",
            "工时占比(%)",
        ];
        let filterVal = [
            "submitUserName",
            "departmentName",
            "projectName",
            "projectNumber",
            "projectManagerName",
            "totalCostHour",
            "costHour",
            "percentHour",
        ];
        const data: any[] = this.formatJson(filterVal, this.tableData);
        let filename = `人员工时统计`;
        import("@/utils/xlsx-style").then((excel) => {
            excel.exportJson2Excel({ header, data, filename, styleCb: { alignment: { horizontal: 'center' } } });
        });
    }
}
</script>
<style lang="scss" scoped>
.four-year-warning {
    color: red;
    font-weight: bold;
}
</style>

<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>