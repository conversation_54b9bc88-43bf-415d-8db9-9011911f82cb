<template>
    <div>
        <el-button @click="onExport" type="primary" style="float: right;margin-bottom:12px">导出</el-button>
        <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" v-loading="tableLoading">
            <el-table-column
                prop="projectName"
                label="项目名称"
            ></el-table-column>
            <el-table-column
                prop="projectManagerName"
                label="项目经理"
                align="center"
                width="200px"
            ></el-table-column>
            <el-table-column
                prop="projectNumber"
                label="项目编号"
                align="center"
                width="200px"
            ></el-table-column>
            <el-table-column
                prop="projectStatus"
                label="项目状态"
                align="center"
                width="200px"
            >
                <template slot-scope="scope">
                    <CustomTag tagType="PROJECT_STATUS" :tagValue="scope.row.projectStatus" />
                </template>
            </el-table-column>
            <el-table-column
                prop="totalLaborHours"
                label="工时总计(h)"
                align="center"
                width="200px"
            ></el-table-column>
            <el-table-column
                prop="totalLaborHoursWeek"
                label="本周总计(h)"
                align="center"
                width="200px"
            ></el-table-column>
            <el-table-column
                prop="totalLaborHoursMonth"
                label="本月总计(h)"
                align="center"
                width="200px"
            ></el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onDetail(scope)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiGetProjectStats } from "@/api/workhours";
@Component({})
export default class extends Vue {
    private tableData: any[] = [];
    private tableLoading = false;
    private mounted() {
        this.initData();
    }
    private activated(){
        this.initData();
    }
    private initData() {
        const form = {};
        this.tableLoading = true;
        apiGetProjectStats(form).then((res) => {
            this.tableData = res.data.data || [];
        }).finally(() => {
            this.tableLoading = false;
        });
    }
    private onDetail(scope: any) {
        const pname = scope.row.projectName
        this.$router.push(`/workhours/info?pname=${pname}`)
    }
    
    private formatJson(filterVal: any, jsonData: any) {
        return jsonData.map((v: any) =>
            filterVal.map((j: any) => {
                if(j==='projectStatus'){
                    return v[j] == 0 ? '进行中' : '已完结'
                }
                return v[j];
            })
        );
    }
    private onExport() {
        let header = [
            "项目名称",
            "项目经理",
            "项目编号",
            "项目状态",
            "工时总计(h)",
            "本周总计(h)",
            "本月总计(h)",
        ];
        let filterVal = [
            "projectName",
            "projectManagerName",
            "projectNumber",
            "projectStatus",
            "totalLaborHours",
            "totalLaborHoursWeek",
            "totalLaborHoursMonth",
        ];
        const data: any[] = this.formatJson(filterVal, this.tableData);
        let filename = `项目工时统计`;
        import("@/utils/xlsx-style").then((excel) => {
            excel.exportJson2Excel({ header, data, filename, styleCb: { alignment: { horizontal: 'center' } } });
        });
    }
}
</script>
<style lang="scss" scoped>
.four-year-warning {
    color: red;
    font-weight: bold;
}
</style>

<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>