<template>
    <div>
        <el-form :model="searchForm" inline>
            <el-form-item label="员工姓名" label-width="70px">
                <FuzzySelect type="USER_FOR_PM" @change="initData" multiple v-model="searchForm.memberIdList" style="width: 200px;" />
            </el-form-item>
            <el-form-item label="时间" label-width="40px">
                <el-date-picker
                    style="width: 260px"
                    placement="bottom-start"
                    clearable
                    @change="initData"
                    value-format="yyyy-MM-dd"
                    v-model="searchForm.daterange"
                    class="fixed-separator-date-picker"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
            </el-form-item>
            <span style="float: right;margin-bottom:10px">
                <el-button @click="onBatchOperate" type="primary" v-if="!isBatch">批量审批</el-button>
                <template v-else>
                    <el-button @click="onBatchReject" type="danger">批量驳回</el-button>
                    <el-button @click="onBatchApprove" type="primary">批量通过</el-button>
                    <el-button @click="onBatchCancel">取消</el-button>
                </template>
            </span>
        </el-form>
        <el-table
            ref="tableData"
            :data="tableData"
            @selection-change="onSelectionChange"
            style="margin-top: -10px"
            :header-cell-style="commmonTableHeaderCellStyle"
            v-loading="tableLoading"
        >
            <el-table-column
                prop="laborDate"
                label="日期"
            ></el-table-column>
            <el-table-column
                prop="week"
                label="星期"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="submitUserName"
                label="员工姓名"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="departmentName"
                label="所属部门"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="totalHour"
                label="工作时长(h)"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="submitDate"
                label="提交时间"
                align="center"
                width="150"
            ></el-table-column>
            <el-table-column label="操作" width="130" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="onApprove(scope)">
                        通过
                    </el-button>
                    <el-button type="text" @click="onReject(scope)">
                        驳回
                    </el-button>
                    <el-button type="text" @click="onDetail(scope)">
                        详情
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column
                v-if="isBatch"
                type="selection"
                width="55"
            ></el-table-column>
        </el-table>
        <DetailDialog ref="detailDialog" />
    </div>
</template>
<script lang="ts">
import { Component} from "vue-property-decorator";
import Vue from "vue";
import FuzzySelect from "@/components/FuzzySelect/index.vue"
import DetailDialog from "./DetailDialog.vue"
import { ElTable } from "element-ui/types/table";
import { apiGetHoursApproveList, apiHoursApprove } from "@/api/workhours";
import momentjs from 'moment';
@Component({
    components:{ FuzzySelect, DetailDialog }
})
export default class extends Vue {
    private searchForm: any = {
        memberIdList: []
    };
    private tableData: any[] = [];
    private isBatch = false;
    private tableLoading = false;
    private selectedList:any[] = []
    private mounted() {
        this.initData();
    }
    private initData() {
        this.tableLoading = true;
        const { memberIdList, daterange } = this.searchForm;
        const form = {
            memberIdList: memberIdList?.length ? memberIdList : undefined,
            startDate: daterange ? momentjs(daterange[0]).startOf('day').format("YYYY-MM-DD HH:mm:ss") : undefined,
            endDate: daterange ? momentjs(daterange[1]).endOf('day').format("YYYY-MM-DD HH:mm:ss") : undefined,
        }
        apiGetHoursApproveList(form).then((res) => {
            this.tableData = res.data.data || [];
            (this.$refs.tableData as ElTable).clearSelection();
        }).finally(() => {
            this.tableLoading = false;
        });
    }
    
    private onSelectionChange(val){
        this.selectedList = val.map(val=>val.id);
    }
    private onApprove(scope: any) {
        this.$confirm("确定通过该申请？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiHoursApprove({ reviewIdList: [scope.row.id], reviewResult: "APPROVED" }).then(() => {
                this.$message.success("操作成功");
                this.initData();
            });
        })
    }
    private onReject(scope: any) {
        this.$confirm("确定驳回该申请？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiHoursApprove({ reviewIdList: [scope.row.id], reviewResult: "REJECTED" }).then(() => {
                this.$message.success("操作成功");
                this.initData();
            });
        })
    }
    private onDetail(scope: any) {
        console.log(scope);
        (this.$refs.detailDialog as any).showDialog(scope.row)
    }
    private onExport() {
        console.log("导出");
    }
    private onBatchOperate() {
        this.isBatch = true;
    }
    private onBatchApprove() {
        if(!this.selectedList?.length){
            this.$message.error("请先选择要操作的申请");
            return;
        }
        this.$confirm("确定批量通过申请？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiHoursApprove({ reviewIdList: this.selectedList, reviewResult: "APPROVED" }).then(() => {
                this.$message.success("操作成功");
                this.initData();
            });
        });
    }
    private onBatchReject() {
        if(!this.selectedList?.length){
            this.$message.error("请先选择要操作的申请");
            return;
        }
        this.$confirm("确定批量驳回申请？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiHoursApprove({ reviewIdList: this.selectedList, reviewResult: "REJECTED" }).then(() => {
                this.$message.success("操作成功");
                this.initData();
            });
        });
    }
    private onBatchCancel() {
        this.isBatch = false;
        (this.$refs.tableData as ElTable).clearSelection();
    }
}
</script>
<style lang="scss" scoped>
.four-year-warning {
    color: red;
    font-weight: bold;
}
</style>

<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>