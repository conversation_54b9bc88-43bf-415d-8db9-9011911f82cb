<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="申请详情"
        width="80%"
    >
        <el-form :model="detailForm" label-width="50px" style="margin-top:-10px">
            <el-row>
                <el-col :span="4">
                    <el-form-item label="日期: " prop="toolType">
                        <span>{{ detailForm.laborDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="星期: " prop="wellNumber">
                        <span>{{ detailForm.week }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="申请人: " prop="toolType" label-width="80px">
                        <span>{{ detailForm.submitUserName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="申请时间: " prop="wellNumber" label-width="80px">
                        <span>{{ detailForm.submitDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div style="margin:10px 0;font-size: 15px;font-weight: 700;">申请详情</div>
        <el-table :data="detailForm.laborHourInfos" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="项目" prop="projectName" width="300px" align="center"></el-table-column>
            <el-table-column label="时间区间" prop="timerange" width="300px" align="center">
                <template slot-scope="scope">
                    {{ getTimerange(scope.row) }}
                </template>
            </el-table-column>
            <el-table-column label="工作时长(h)" prop="laborHours" width="100px" align="center">
            </el-table-column>
            <el-table-column label="工作内容" prop="workSummary" align="center">
            </el-table-column>
        </el-table>
        <div style="margin:20px 0 10px;font-size: 15px;font-weight: 700;">审核记录</div>
        <el-table :data="detailForm.reviewDetailList" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="审核人" prop="reviewUser" align="center"></el-table-column>
            <el-table-column label="审核结果" prop="reviewStatus" align="center">
                <template slot-scope="scope">
                    <CustomTag tagType="HOURS_APPROVE_STATUS" :tagValue="transferApproveStatus(scope.row.reviewStatus)" />
                </template>
            </el-table-column>
            <el-table-column label="审核时间" prop="reviewDate" align="center"></el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { getTimerange } from "@/utils/constant.workhours";
@Component({})
export default class extends Vue {
    private isDialogVisible = false;
    private getTimerange = getTimerange;
    private detailForm: any = {
        laborHourInfos: [],
        reviewDetailList: []
    };
    private async showDialog(row) {
        this.detailForm = row
        this.isDialogVisible = true;
    }
    private transferApproveStatus(status) {
        if(status==='ONGOING'){
            return 'PENDING'
        }
        return status;
    }
}
</script>
<style lang="scss">
</style>
