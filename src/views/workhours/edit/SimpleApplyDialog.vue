<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="申请调整工时"
        width="80%"
        custom-class="bfc-body-dialog"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            label-width="50px"
            style="margin-top:-10px"
        >
            <el-row>
                <el-col :span="4">
                    <el-form-item label="日期: " prop="toolType">
                        <span>{{ detailForm.laborDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="星期: " prop="wellNumber">
                        <span>{{ detailForm.week }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="detailForm.infoList" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="项目" prop="projectId" width="300px" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.projectName }}
                    </span>
                    <el-select v-else v-model="scope.row.projectId" style="width: 100%;"> 
                        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="时间区间" prop="timerange" width="300px" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.timerange[0] }} - {{ scope.row.timerange[1] }}
                    </span>
                    <el-time-picker
                        v-else
                        is-range
                        :value-format='timeFormat'
                        :format="timeFormat"
                        style="width: 100%;"
                        v-model="scope.row.timerange"
                        @change="onTimerangeChange(scope)"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        placeholder="选择时间范围"
                        :clearable="false"
                    >
                    </el-time-picker>
                </template>
            </el-table-column>
            <el-table-column label="工作时长(h)" prop="hours" width="100px" align="center">
                <template slot-scope="scope">
                    <span>
                        {{ getHoursByTimerange(scope.row.timerange) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="工作内容" prop="workSummary" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.workSummary }}
                    </span>
                    <el-input
                        v-else
                        size="small"
                        v-model="scope.row.workSummary"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="margin-top:20px;float: right;display: block;"
            type="primary"
            @click="onAddRow"
        >
            添加行
        </el-button>
        <span slot="footer">
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onConfirm" :loading="isConfirmLoading">提 交</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Form as ElForm } from "element-ui/types/element-ui";
import momentjs from "moment"
import { apiAddWorkHour } from "@/api/workhours"
import { getHoursByTimerange, noonStart, noonEnd, dayStart, reviseTimerange, timeFormat } from "@/utils/constant.workhours"
@Component({})
export default class extends Vue {
    @Prop({ default: () => [] }) projectList!: any[];
    private timeFormat = timeFormat;
    private isConfirmLoading = false;
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private isDialogVisible = false;
    private detailForm: any = {};
    private getInitInfoItem():any {
        return {
            projectName: null,
            projectId: null,
            timerange: null,
            hours: null,
            workSummary: null,
        };
    }
    private getHoursByTimerange = getHoursByTimerange;
    private onConfirm() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        if (this.detailForm.infoList.length == 0) {
            this.$message.error("请添加工时条目");
            return;
        }
        this.isConfirmLoading = true;
        this.detailForm.infoList = this.detailForm.infoList.map(item=>{
            item.startTime = momentjs(item.timerange[0], 'HH:mm').format('YYYY-MM-DD HH:mm:ss');
            item.endTime = momentjs(item.timerange[1], 'HH:mm').format('YYYY-MM-DD HH:mm:ss');
            return item;
        })
        apiAddWorkHour(this.detailForm).then(() => {
            this.isDialogVisible = false;
            this.$emit("onAddHoursSuccess");
            this.$message.success("操作成功");
        }).finally(() =>{
            this.isConfirmLoading = false;
        });
    }
    private handleOriginData(originData){
        const originInfoList = originData.infoList || [];
        

    }
    private async showDialog() {
        this.editObject.editRowIndex = -1;
        // this.detailForm = this.handleOriginData(originData);
        this.isDialogVisible = true;
    }
    private onTimerangeChange(scope){
        scope.row.timerange = reviseTimerange(scope.row.timerange)
    }
    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        const infoList = this.detailForm.infoList;
        const detailItem = this.getInitInfoItem();
        if(!infoList.length){
            detailItem.timerange = [dayStart, momentjs().format(timeFormat)];
        }else{
            const lastEndMoment = infoList[infoList.length-1].timerange[1];
            let startTime = lastEndMoment;
            if(lastEndMoment===noonStart){
                startTime = noonEnd
            }
            detailItem.timerange = [startTime, momentjs().format(timeFormat)];
        }
        detailItem.hours = this.getHoursByTimerange(detailItem.timerange);
        infoList.push(detailItem);
        this.editObject.editRow = true;
        this.editObject.editRowIndex = infoList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.infoList.splice(scope.$index, 1);
    }

    private async onSaveRow(scope: any) {
        if(!scope.row.projectId){
            this.$message.error("请选择所属项目");
            return;
        }
        if(!scope.row.timerange){
            this.$message.error("请选择工作时间");
            return;
        }
        if(scope.row.hours<=0){
            this.$message.error("无效工时！");
            return;
        }
        scope.row.projectName = this.projectList.find(item=>{
            return item.id == scope.row.projectId
        })?.projectName;
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.$set(this.detailForm.infoList, scope.$index, {
                ...this.preObject,
            });
        } else {
            this.detailForm.infoList.pop();
        }
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
.bfc-body-dialog{
    .el-dialog__body{
        overflow: hidden;
    }
}
</style>
