<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        :title="`编辑工时信息`"
        width="600px"
    >
        <el-form v-if="isDialogVisible" ref="detailForm" :rules="formRules" :model="detailForm" label-width="90px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="日期: " prop="laborDate">
                        <el-date-picker
                            placement="bottom-start"
                            style="width: 100%"
                            v-model="detailForm.laborDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            disabled
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="星期: " prop="week">
                        <el-input disabled v-model="detailForm.week"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="时间区间: " prop="timerange">
                        <el-time-select
                            style="width: 220px;"
                            el-time-select
                            placeholder="起始时间"
                            v-model="detailForm.originStartTime"
                            :picker-options="{
                                start:'00:00',
                                step: '00:30',
                                end: '23:30',
                                maxTime: detailForm.originEndTime
                            }">
                        </el-time-select>
                        -
                        <el-time-select
                            style="width: 220px;"
                            placeholder="结束时间"
                            v-model="detailForm.originEndTime"
                            :picker-options="{
                                start:'00:00',
                                step: '00:30',
                                end: '23:30',
                                minTime: detailForm.originStartTime
                            }">
                        </el-time-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="项目: " prop="projectId">
                        <el-select v-model="detailForm.projectId" style="width: 100%;"> 
                            <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="工作内容: " prop="workSummary">
                        <el-input type="textarea" v-model="detailForm.workSummary"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" @click="onConfirm">
                确 认
            </el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { ElForm } from "element-ui/types/form";
import { apiUpdateWorkHour } from "@/api/workhours";
import { reviseTimerange, timeFormat } from "@/utils/constant.workhours"
import momentjs from "moment"
@Component({})
export default class extends Vue {
    @Prop({ default: () => [] }) projectList!: any[];
    private timeFormat = timeFormat;
    private isDialogVisible = false;
    private detailForm: any = {};
    private getInitForm(){
        return {
            laborDate: '',
            week: '',
            projectId: '',
            originStartTime: '',
            originEndTime: '',
            workSummary: ''
        }
    }
    get formRules() {
        return {
            timerange: [{required:true, message: "请填写工作时间", trigger:'blur'}],
            projectId: [{required:true, message: "请选择项目", trigger:'blur'}],
        }
    }
    private async showDialog(row) {
        this.detailForm = Object.assign(this.getInitForm(), row);
        this.detailForm.originStartTime = momentjs(row.startTime).format(timeFormat);
        this.detailForm.originEndTime = momentjs(row.endTime).format(timeFormat);
        this.isDialogVisible = true;
        this.$nextTick(()=>{
            (this.$refs.detailForm as ElForm).clearValidate();
        })
    }
    private async onConfirm(){
        const valid = await (this.$refs.detailForm as any).validate()
        if(!valid){
            return 
        }
        this.detailForm.startTime = momentjs(this.detailForm.originStartTime, timeFormat).format('YYYY-MM-DD HH:mm:ss');
        this.detailForm.endTime = momentjs(this.detailForm.originEndTime, timeFormat).format('YYYY-MM-DD HH:mm:ss');
        apiUpdateWorkHour(this.detailForm).then(()=>{
            this.$message.success("操作成功");
            this.isDialogVisible = false;
            this.$emit("onEditHoursSuccess");
        })
    }
}
</script>
<style lang="scss" scoped>
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            float: right;
            margin-top: 6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>
