<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="申请调整工时"
        width="80%"
        custom-class="bfc-body-dialog"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            label-width="50px"
            style="margin-top:-10px"
        >
            <el-row :gutter="20">
                <el-col :span="4">
                    <el-form-item label="日期: ">
                        <el-date-picker
                            placement="bottom-start"
                            style="width: 100%"
                            v-model="detailForm.laborDate"
                            type="date"
                            @change="onDateChange"
                            :picker-options="pickerOptions"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            :clearable="false"
                            :disabled="shouldDateDisabled"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="星期: ">
                        <span>{{ detailForm.week }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="detailForm.infoList" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="项目" prop="projectId" width="300px" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.projectName }}
                    </span>
                    <el-select v-else v-model="scope.row.projectId" style="width: 100%;"> 
                        <el-option v-for="item in filterProjectList" :disabled="item.disableApply" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="时间区间" prop="timerange" width="300px" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.timerange ? scope.row.timerange.join(' - ') : '' }}
                    </span>
                    <RangeTimeSelect v-else v-model="scope.row.timerange" />
                </template>
            </el-table-column>
            <el-table-column label="工作时长(h)" prop="hours" width="100px" align="center">
                <template slot-scope="scope">
                    <span>
                        {{ getHoursByTimerange(scope.row.timerange) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="工作内容" prop="workSummary" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.workSummary }}
                    </span>
                    <el-input v-else v-model="scope.row.workSummary"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="margin-top:20px;float: right;display: block;"
            type="primary"
            @click="onAddRow"
        >
            添加行
        </el-button>
        <span slot="footer">
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onConfirm" :loading="isConfirmLoading">提 交</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import momentjs from "moment"
import { apiAddHoursApply, apiGetHoursApplyList } from "@/api/workhours"
import { getHoursByTimerange, getWeek } from "@/utils/constant.workhours"
const isBeforeToday = (date)=>{
    return momentjs(date).isBefore(momentjs().subtract(1, 'day'))
}
import RangeTimeSelect from "@/components/RangeTimeSelect/index.vue"
@Component({components: {RangeTimeSelect}})
export default class extends Vue {
    @Prop({ default: () => [] }) projectList!: any[];
    private filterProjectList:any[] = [];
    private isConfirmLoading = false;
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private isDialogVisible = false;
    private detailForm: any = {
        laborDate: null,
        week: null,
        infoList: [],
    };
    private pickerOptions = {
        disabledDate(date){
            const last = momentjs().subtract(1, 'months').date(25);
            const current = momentjs().date(25);
            // 起于上个月26号, 止于这个月25号
            return last.isAfter(momentjs(date)) || current.isBefore(momentjs(date))
        }
    }
    get shouldDateDisabled(){
        return !!this.detailForm.infoList?.length
    }
    get shouldAddRowDisabled(){
        return !this.detailForm.laborDate
    }
    private getInitInfoItem():any {
        return {
            projectName: null,
            projectId: null,
            timerange: null,
            hours: 0,
            workSummary: null,
        };
    }
    private async onDateChange(){
        const laborDate = this.detailForm.laborDate;
        if(!isBeforeToday(laborDate)){
            this.$message.error('请选择今天之前的日期');
            return;
        }
        this.detailForm.week = laborDate ? getWeek(laborDate) : null;
        const applyList = await apiGetHoursApplyList({laborDate}).then(res=>{
            return res.data?.data || [];
        })
        this.filterProjectList = this.projectList.map(item=>{
            const findItem = applyList.find(al=>al.projectId === item.id);
            // 在申请列表, 且审批状态为ONGOING时禁用
            const disableApply = findItem && findItem.reviewStatus === "ONGOING";
            return { ...item, disableApply }
        });
        if(!this.filterProjectList.length){
            this.$message.error('当天无可供申请的项目')
        }
    }
    private getHoursByTimerange = getHoursByTimerange;
    private onConfirm() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存正在编辑的行");
            return;
        }
        if (this.detailForm.infoList.length == 0) {
            this.$message.error("请添加工时条目");
            return;
        }
        const laborDate = this.detailForm.laborDate;
        if(!isBeforeToday(laborDate)){
            this.$message.error('请选择今天之前的日期');
            return;
        }
        this.isConfirmLoading = true;
        const form = this.detailForm.infoList.reduce((acc, cur) => {
            const existing = acc.find(item => item.projectId === cur.projectId);
            if(cur.hours){
                cur.startTime = momentjs(cur.timerange[0], 'HH:mm').format('YYYY-MM-DD HH:mm:ss');
                cur.endTime = momentjs(cur.timerange[1], 'HH:mm').format('YYYY-MM-DD HH:mm:ss');
            }
            if (existing) {
                cur.hours && existing.laborHourInfos.push(cur);
            } else {
                const initList = cur.hours ? [cur] : [];
                acc.push({ projectId: cur.projectId, laborDate, laborHourInfos: initList });
            }
            return acc;
        }, []);
        apiAddHoursApply(form).then(() => {
            this.isDialogVisible = false;
            this.$emit("onAddApplySuccess");
            this.$message.success("操作成功");
        }).finally(() =>{
            this.isConfirmLoading = false;
        });
    }
    private async showDialog() {
        console.log(new Date())
        this.editObject.editRowIndex = -1;
        this.detailForm = {
            laborDate: null,
            week: null,
            infoList: [],
        }
        this.isDialogVisible = true;
    }
    private onAddRow() {
        if(this.shouldAddRowDisabled){
            this.$message.error("请先选择日期");
            return;
        }
        if(!this.filterProjectList.length){
            this.$message.error('当天无可供申请的项目');
            return;
        }
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存正在编辑的行");
            return;
        }
        this.preObject = {};
        const infoList = this.detailForm.infoList;
        const detailItem = this.getInitInfoItem();
        infoList.push(detailItem);
        this.editObject.editRow = true;
        this.editObject.editRowIndex = infoList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存正在编辑的行");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存正在编辑的行");
            return;
        }
        this.detailForm.infoList.splice(scope.$index, 1);
    }

    private async onSaveRow(scope: any) {
        if(!scope.row.projectId){
            this.$message.error("请选择所属项目");
            return;
        }
        // TODO: 没必要在这里计算工时
        // 申请调整工时时, 工时可以为空, 无需校验
        if(scope.row.timerange){
            scope.row.hours = getHoursByTimerange(scope.row.timerange)
        }
        
        if(scope.row.hours&&!scope.row.workSummary){
            this.$message.error("请填写工作内容！");
            return;
        }
        scope.row.projectName = this.projectList.find(item=>{
            return item.id == scope.row.projectId
        })?.projectName;
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.$set(this.detailForm.infoList, scope.$index, {
                ...this.preObject,
            });
        } else {
            this.detailForm.infoList.pop();
        }
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
.bfc-body-dialog{
    .el-dialog__body{
        overflow: hidden;
    }
}
</style>
