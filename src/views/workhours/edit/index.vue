<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>工时填报</span>
            </div>
            <el-form :model="searchForm">
                <el-row :gutter="20">
                    <el-col :span="20">
                        <el-row :gutter="20">
                            <el-col :span="6" v-if="activePanelName==='detail'">
                                <el-form-item label="项目" label-width="40px">
                                    <el-select multiple @change="initData(true)" v-model="searchForm.projectIdList" style="width:100%" clearable>
                                        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="18">
                                <el-form-item label="时间" label-width="40px">
                                    <el-date-picker
                                        style="width: 260px"
                                        placement="bottom-start"
                                        clearable
                                        @change="onDaterangeChange"
                                        value-format="yyyy-MM-dd"
                                        v-model="searchForm.daterange"
                                        class="fixed-separator-date-picker"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                    >
                                    </el-date-picker>
                                    <DaterangeShortcut class="form-radio-group" @change="onDaterangeChange" v-model="searchForm.daterange" style="margin-left:10px" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                    
                    
                    <el-col :span="4">
                        <el-button @click="onAddWorkHourShortcut" type="primary" v-if="userId==2" style="float: right">快捷方式</el-button>
                        <el-button @click="onAddWorkHour" type="primary" style="float: right">填写工时</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-tabs v-model="activePanelName">
                <el-tab-pane label="详细模式" name="detail">
                    <div style="font-size: 16px;">
                        <span style="font-weight: 700;">总计：</span>{{totalLaborDays}}人天({{totalLaborHours}}工时)
                    </div>
                    <el-table style="margin-top: 6px" v-if="activePanelName==='detail'" :data="detailTableData" v-loading="detailTableLoading" :header-cell-style="commmonTableHeaderCellStyle">
                        <el-table-column label="日期" prop="laborDate" width="100px"></el-table-column>
                        <el-table-column label="星期" prop="week" align="center" width="100px"></el-table-column>
                        <el-table-column label="项目名称" prop="projectName" align="center" width="200px"></el-table-column>
                        <el-table-column label="时间区间" prop="timerange" align="center" width="200px">
                            <template slot-scope="scope">
                                <span>{{ getTimerange(scope.row) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="工作时长(h)" prop="laborHours" align="center" width="100px"></el-table-column>
                        <el-table-column label="工作内容" prop="workSummary" align="center"></el-table-column>
                        <el-table-column label="提交时间" prop="submitTime" align="center" width="150px"></el-table-column>
                        <el-table-column label="操作" prop="" align="center" width="100px">
                            <template slot-scope="scope">
                                <el-button type="text" @click="onEditDetailRow(scope)" :title="disableEditDetail(scope)?'只能操作今天的数据':''" :disabled="disableEditDetail(scope)">编辑</el-button>
                                <el-button type="text" @click="onDeleteDetailRow(scope)" :title="disableEditDetail(scope)?'只能操作今天的数据':''" :disabled="disableEditDetail(scope)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :hide-on-single-page="true"
                        style="margin-top: 20px"
                        background
                        layout="prev, pager, next"
                        :total="detailTotal"
                        :page-size="detailPageSize"
                        @current-change="onDetailCurrentChange"
                        :current-page="detailCurrentPage"
                    ></el-pagination>
                </el-tab-pane>
                <el-tab-pane label="精简模式" name="simple">
                    <el-table v-if="activePanelName==='simple'" :data="simpleTableData" v-loading="simpleTableLoading" :header-cell-style="commmonTableHeaderCellStyle">
                        <el-table-column label="日期" prop="laborDate"></el-table-column>
                        <el-table-column label="星期" prop="week" align="center"></el-table-column>
                        <el-table-column label="工作时长(h)" prop="totalLaborHours" align="center"></el-table-column>
                        <el-table-column label="操作" prop="" align="center" width="80px">
                            <template slot-scope="scope">
                                <el-button type="text" @click="onClickSimpleDetail(scope)">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :hide-on-single-page="true"
                        style="margin-top: 20px"
                        background
                        layout="prev, pager, next"
                        :total="simpleTotal"
                        :page-size="simplePageSize"
                        @current-change="onSimpleCurrentChange"
                        :current-page="simpleCurrentPage"
                    ></el-pagination>
                </el-tab-pane>
                <el-tab-pane label="我的申请" name="apply">
                    <el-button @click="onApply" type="primary" style="float: right;margin-bottom: 8px;" :disabled="!canApply" :title="canApply?'':'每月26,27,28号才能申请'">申请调整工时</el-button>
                    <el-table v-if="activePanelName==='apply'" :data="applyTableData" v-loading="applyTableLoading" :header-cell-style="commmonTableHeaderCellStyle">
                        <el-table-column label="日期" prop="laborDate" width="120px"></el-table-column>
                        <el-table-column label="星期" prop="week" align="center" width="120px"></el-table-column>
                        <el-table-column label="项目名称" prop="projectName" align="center"></el-table-column>
                        <el-table-column label="总工作时长(h)" prop="totalHour" align="center" width="150px"></el-table-column>
                        <el-table-column label="提交申请时间" prop="submitDate" align="center" width="150px"></el-table-column>
                        <el-table-column label="审核状态" prop="totalLaborHours" align="center" width="150px">
                            <template slot-scope="scope">
                                <CustomTag tagType="HOURS_APPROVE_STATUS" :tagValue="transferApproveStatus(scope.row)" />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" prop="" align="center" width="160px">
                            <template slot-scope="scope">
                                <el-button type="text" @click="onClickApplyDetail(scope)">申请详情</el-button>
                                <el-button type="text" @click="onClickApplyWithdraw(scope)" v-if="canWithdraw(scope.row)">撤销申请</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        :hide-on-single-page="true"
                        style="margin-top: 20px"
                        background
                        layout="prev, pager, next"
                        :total="applyTotal"
                        :page-size="applyPageSize"
                        @current-change="onApplyCurrentChange"
                        :current-page="applyCurrentPage"
                    ></el-pagination>
                </el-tab-pane>
            </el-tabs>
        </div>
        <AddDialog ref="addDialog" :projectList="projectList" @onAddHoursSuccess="initData" />
        <DetailEditDialog ref="detailEditDialog" :projectList="projectList" @onEditHoursSuccess="initData" />
        <SimpleDetailDialog ref="simpleDetailDialog" />
        <ApplyDetailDialog ref="applyDetailDialog" />
        <ApplyDialog ref="applyDialog" @onAddApplySuccess="initApplyData" :projectList="projectList" />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AddDialog from "./AddDialog.vue"
import DetailEditDialog from "./DetailEditDialog.vue";
import SimpleDetailDialog from "./SimpleDetailDialog.vue";
import ApplyDetailDialog from "./ApplyDetailDialog.vue";
import ApplyDialog from "./ApplyDialog.vue";
import DaterangeShortcut, { getThisWeekDaterange } from "@/components/DaterangeShortcut/index.vue"
import { apiGetProjectWriteableList } from "@/api/project";
import { apiGetWorkHourList, apiDeleteWorkHour, apiGetSimpleList, apiGetHoursApplyList, apiWithdrawHoursApply, apiGetTotalWorkHours } from "@/api/workhours";
import momentjs from "moment";
import { getTimerange } from "@/utils/constant.workhours";
import { UserModule } from "@/store/modules/user";
const canApplyDates = [26, 27, 28];
const tabList = ['detail','simple','apply']
@Component({
    components: {
        AddDialog,
        DetailEditDialog,
        SimpleDetailDialog,
        DaterangeShortcut,
        ApplyDetailDialog,
        ApplyDialog,
    }
})
export default class extends Vue {
    private searchForm:any = {
        daterange: getThisWeekDaterange(),
        projectIdList: null
    }
    private activePanelName = "detail";
    private projectList:any[] = [];
    private canApply = canApplyDates.includes(new Date().getDate());
    private totalLaborDays: number | null = null;
    private totalLaborHours: number | null = null;
    // private canApply = true;
    private getTimerange= getTimerange;
    get userId(){
        return UserModule.userId;
    }
    async mounted() {
        const tab = this.$route.query.tab as string|undefined;
        if(tab && tabList.includes(tab) ){
            this.activePanelName = tab;
        }
        this.getProjectList();
        this.initData();
    }
    private transferApproveStatus({reviewStatus, isLocked}){
        if(!reviewStatus){
            return null
        }
        if(reviewStatus === 'ONGOING'){
            if(!isLocked){
                return "PENDING"
            }
        }
        return reviewStatus;
    }
    private initData(bool=false){
        this.initDetailData(bool);
        this.initSimpleData(bool);
        this.initApplyData(bool);
    }
    private getProjectList() {
        apiGetProjectWriteableList({}, {current:1, size:999}).then((res)=>{
            this.projectList = res.data?.data?.records || [];
        })
    }
    private onDaterangeChange(){
        if(!this.searchForm.daterange){
            this.$message.error("请选择一个时间区间！")
            return;
        }
        // const [startDate, endDate] = this.searchForm.daterange;
        // 如果时间跨度超过3个月, 直接return, moment取的floor
        // if(momentjs(endDate).diff(startDate, 'months') > 2){
        //     this.$message.error("查询时间跨度不能超过3个月！")
        //     return;
        // }
        this.initData(true);
    }
    private onAddWorkHour() {
        (this.$refs.addDialog as any).showDialog();
    }
    private onAddWorkHourShortcut() {
        (this.$refs.addDialog as any).showDialog(true);
    }
    // #region --- 详细模式
    private detailTableData: any[] = []
    private detailTableLoading = false;
    private detailCurrentPage = 1;
    private detailTotal = 1;
    private detailPageSize = 10;
    private initDetailData(bool=false) {
        if(bool){
            this.detailCurrentPage = 1;
        }
        const projectIdList = this.searchForm.projectIdList?.length ? this.searchForm.projectIdList : undefined;
        const form = {
            startDate: this.searchForm.daterange?.[0],
            endDate: this.searchForm.daterange?.[1],
            projectIdList
        }
        this.detailTableLoading = true;
        return apiGetWorkHourList(form,{ current: this.detailCurrentPage, size: this.detailPageSize }).then(res=>{
            this.detailTableData = res.data?.data?.records || [];
            this.detailTotal = res.data.data.total;
        }).finally(()=>{
            apiGetTotalWorkHours(form).then(res=>{
                this.totalLaborDays = res.data?.data?.totalLaborDays || 0;
                this.totalLaborHours = res.data?.data?.totalLaborHours || 0;
            })
            this.detailTableLoading = false;
        })
    }
    private onDetailCurrentChange(currentPage: number) {
        this.detailCurrentPage = currentPage;
        this.initData();
    }
    private onEditDetailRow(scope){
        if(this.disableEditDetail(scope)){
            return
        }
        (this.$refs.detailEditDialog as any).showDialog(scope.row);
    }
    private onDeleteDetailRow(scope){
        if(this.disableEditDetail(scope)){
            return
        }
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiDeleteWorkHour({ id: scope.row.id }).then(() => {
                this.$message.success("操作成功");
                this.initDetailData();
            });
        })
    }
    // TODO: 这个前端不稳
    private disableEditDetail(scope){
        return momentjs().format('YYYY-MM-DD') !== scope.row.laborDate
    }
    // #endregion
    // #region --- 精简模式
    private simpleTableData: any[] = []
    private simpleTableLoading = false;
    private simpleCurrentPage = 1;
    private simpleTotal = 1;
    private simplePageSize = 10;
    private initSimpleData(bool=false) {
        if(bool){
            this.simpleCurrentPage = 1;
        }
        const form = {
            startDate: this.searchForm.daterange?.[0],
            endDate: this.searchForm.daterange?.[1],
        }
        this.simpleTableLoading = true;
        return apiGetSimpleList(form).then(res=>{
            this.simpleTableData = res.data?.data || [];
        }).finally(()=>{
            this.simpleTableLoading = false;
        })
    }
    private onSimpleCurrentChange(currentPage: number) {
        this.simpleCurrentChange = currentPage;
        this.initData();
    }
    private onClickSimpleDetail(scope){
        (this.$refs.simpleDetailDialog as any).showDialog(scope.row)
    }
    // #endregion
    // #region --- 我的申请
    private applyTableData: any[] = [];
    private applyTableLoading = false;
    private applyCurrentPage = 1;
    private applyTotal = 1;
    private applyPageSize = 10;
    private initApplyData(bool=false) {
        if(bool){
            this.applyCurrentPage = 1;
        }
        const form = {
            startDate: this.searchForm.daterange?.[0],
            endDate: this.searchForm.daterange?.[1],
        }
        this.applyTableLoading = true;
        return apiGetHoursApplyList(form).then(res=>{
            this.applyTableData = res.data?.data || [];
        }).finally(()=>{
            this.applyTableLoading = false;
        })
    }
    private onApplyCurrentChange(currentPage: number) {
        this.applyCurrentChange = currentPage;
        this.initData();
    }
    private onApply() {
        if(!this.canApply){
            return;
        }
        (this.$refs.applyDialog as any).showDialog()
    }
    private onClickApplyDetail(scope){
        (this.$refs.applyDetailDialog as any).showDialog(scope.row)
    }
    private canWithdraw(row){
        // isLocked
        return row.reviewStatus === 'ONGOING' && !row.isLocked;
    }
    private onClickApplyWithdraw(scope){
        this.$confirm("确定撤销申请？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiWithdrawHoursApply({ reviewIdList: [scope.row.id] }).then(() => {
                this.$message.success("操作成功");
                this.initApplyData();
            });
        })
    }
    // #endregion
}
</script>
<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>