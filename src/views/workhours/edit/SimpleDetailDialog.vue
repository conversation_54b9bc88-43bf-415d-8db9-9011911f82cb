<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="工时详情"
        width="80%"
    >
        <el-form :model="detailForm" label-width="50px" style="margin-top:-10px">
            <el-row>
                <el-col :span="4">
                    <el-form-item label="日期: " prop="toolType">
                        <span>{{ detailForm.laborDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label="星期: " prop="wellNumber">
                        <span>{{ detailForm.week }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="detailTableData" v-loading="detailTableLoading" :header-cell-style="commmonTableHeaderCellStyle">
            <el-table-column label="项目" prop="projectName" width="300px" align="center"></el-table-column>
            <el-table-column label="时间区间" prop="timerange" align="center" width="200px">
                <template slot-scope="scope">
                    <span>{{ getTimerange(scope) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="工作时长(h)" prop="laborHours" width="100px" align="center">
            </el-table-column>
            <el-table-column label="工作内容" prop="workSummary" align="center">
            </el-table-column>
        </el-table>
        <el-pagination
            :hide-on-single-page="true"
            style="margin-top: 20px"
            background
            layout="prev, pager, next"
            :total="detailTotal"
            :page-size="detailPageSize"
            @current-change="onDetailCurrentChange"
            :current-page="detailCurrentPage"
        ></el-pagination>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetWorkHourList } from "@/api/workhours";
import { Component, Vue } from "vue-property-decorator";
import momentjs from "moment"
import { timeFormat } from "@/utils/constant.workhours";
@Component({})
export default class extends Vue {
    private isDialogVisible = false;
    private detailForm: any = {};
    private detailTableData: any[] = []
    private detailTableLoading = false;
    private detailCurrentPage = 1;
    private detailTotal = 1;
    private detailPageSize = 10;
    private getTimerange(scope){
        const { startTime, endTime } = scope.row;
        return `${momentjs(startTime).format(timeFormat)} - ${momentjs(endTime).format(timeFormat)}`;
    }
    private async initDetailData(bool=false) {
        if(bool){
            this.detailCurrentPage = 1;
        }
        const form = {
            startDate: this.detailForm.laborDate,
            endDate: this.detailForm.laborDate,
        }
        this.detailTableLoading = true;
        return apiGetWorkHourList(form,{ current: this.detailCurrentPage, size: this.detailPageSize }).then(res=>{
            this.detailTableData = res.data?.data?.records || [];
            this.detailTotal = res.data.data.total;
        }).finally(()=>{
            this.detailTableLoading = false;
        })
    }
    private onDetailCurrentChange(currentPage: number) {
        this.detailCurrentPage = currentPage;
        this.initData();
    }
    private async showDialog(row) {
        this.detailForm = { ...row };
        await this.initDetailData();
        this.isDialogVisible = true;

    }
}
</script>
<style lang="scss">
</style>
