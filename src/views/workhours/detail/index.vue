<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">工时明细</div>
            <el-form :model="searchForm" label-width="70px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="项目名称">
                            <FuzzySelect type="PROJECT_NAME" @change="onProjectNameChange" v-model="searchForm.projectName"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="项目编号">
                            <FuzzySelect type="PROJECT_NUMBER" @change="onProjectNumberChange" v-model="searchForm.projectNumber"></FuzzySelect>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="员工名称">
                            <FuzzySelect type="USER_FOR_PM" :initList="initList" @change="initData(true)" v-model="searchForm.submitMemberId" clearable></FuzzySelect>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="时间区间" label-width="70px">
                            <el-date-picker
                                style="width: 260px"
                                placement="bottom-start"
                                clearable
                                @change="onDaterangeChange"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.daterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                            <DaterangeShortcut class="form-radio-group" @change="onDaterangeChange" v-model="searchForm.daterange" style="margin-left:10px" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div style="font-size: 16px;">
                <span style="font-weight: 700;">总计：</span>{{totalLaborDays}}人天({{totalLaborHours}}工时)
            </div>
            <el-table style="margin-top: 12px;" @sort-change="onSortChange" :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" v-loading="tableLoading">
                <el-table-column
                    prop="laborDate"
                    label="日期"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="week"
                    label="星期"
                    align="center"
                    width="80"
                ></el-table-column>
                <el-table-column
                    prop="projectName"
                    label="项目名称"
                    align="center"
                    width="200"
                ></el-table-column>
                <el-table-column
                    prop="projectNumber"
                    label="项目编号"
                    align="center"
                    width="200"
                ></el-table-column>
                <el-table-column
                    prop="submitUserName"
                    label="员工名称"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="departmentName"
                    label="部门"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="timerange"
                    label="时间区间"
                    align="center"
                    width="160"
                >
                    <template slot-scope="scope">
                        <span>{{ getTimerange(scope.row) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="laborHours"
                    label="工作时长(h)"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    prop="workSummary"
                    label="工作内容"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="submitTime"
                    label="提交时间"
                    align="center"
                    width="150"
                ></el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DaterangeShortcut from "@/components/DaterangeShortcut/index.vue"
import FuzzySelect from "@/components/FuzzySelect/index.vue"
import { apiGetWorkHourDetailList } from "@/api/workhours";
import { getTimerange } from "@/utils/constant.workhours";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({
    components: {
        DaterangeShortcut,
        FuzzySelect
    },
})
export default class extends Vue {
    private getTimerange = getTimerange;
    private totalLaborDays = null;
    private totalLaborHours = null;
    private initList: any[] = [];
    private searchForm: any = {
        projectName: null,
        projectNumber: null,
        submitMemberId: null,
        daterange: null,
    };
    private tableData: any[] = [];
    private tableLoading = false;
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private mounted() {
        const { userid: memberId, username: name, date, pname: projectName } = this.$route.query;
        if(memberId){
            this.searchForm.submitMemberId = memberId;
            this.initList = [{memberId, name}]
        }
        if(date){
            this.searchForm.daterange = [date, date]
        }
        if(projectName){
            this.searchForm.projectName = projectName
        }
        this.initData();
    }
    private onDaterangeChange(){
        this.initData(true);
    }
    private initData(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const form = {
            projectName: this.searchForm.projectName || undefined,
            projectNumber: this.searchForm.projectNumber || undefined,
            submitMemberId: this.searchForm.submitMemberId || undefined,
            startDate: this.searchForm.daterange?.[0] || undefined,
            endDate: this.searchForm.daterange?.[1] || undefined,
        }
        this.tableLoading = true;
        apiGetWorkHourDetailList(form, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const { page, totalLaborDays, totalLaborHours } = res.data.data || {};
            this.tableData = page.records || [];
            this.total = page.total;
            this.totalLaborDays = totalLaborDays;
            this.totalLaborHours = totalLaborHours;
        }).finally(() => {
            this.tableLoading = false;
        });
    }
    private onProjectNameChange(item){
        this.searchForm.projectNumber = item.projectNumber;
        this.initData(true);
    }
    private onProjectNumberChange(item){
        this.searchForm.projectName = item.projectName;
        this.initData(true);
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    
    private onSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.initData();
    }
}
</script>
<style lang="scss" scoped>
.form-radio-group.el-radio-group {
    vertical-align: bottom;
    & :deep() .el-radio-button--medium .el-radio-button__inner {
        padding: 10px 10px !important;
    }
}
</style>