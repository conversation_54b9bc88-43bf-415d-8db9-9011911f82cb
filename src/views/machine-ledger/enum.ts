export const workOrderTypeMap = {
    PRODUCE: '生产',
    REPAIR: '维修',
}
export const projectTypeMap = {
    'INHOLE_COMPLETION_TOOLS': '井下/完井工具',
    'MWD': 'MWD',
    'OUT_PROCESS': '对外加工',
    'RD': '研发件'
}


export const statisticsTypeList = {
    DEVICE : '设备',
    WORK_HOURS : '工时'
}

export const deviceList=[
    {deviceName:'手工',deviceCode:"DB-001"},{deviceName:'打标机',deviceCode:"DB-002"},{deviceName:'攻丝机',deviceCode:"Z-003"},
    {deviceName:'外协',deviceCode:"WX"},{deviceName:'检验',deviceCode:"JY"},{deviceName:'入库',deviceCode:"RK"},
    {deviceName:'台钻',deviceCode:"Z-002"},{deviceName:'摇臂钻',deviceCode:"Z-001"},{deviceName:'半自动锯床',deviceCode:"J-001"},
    {deviceName:'普车',deviceCode:"PC-001"},{deviceName:'普车',deviceCode:"PC-002"},{deviceName:'普车',deviceCode:"PC-003"},{deviceName:'普铣',deviceCode:"PX-001"},
    {deviceName:'线切割',deviceCode:"Q-001"},{deviceName:'纽威数控车床',deviceCode:"C-001"},{deviceName:'纽威数控车床',deviceCode:"C-002"},
    {deviceName:'纽威数控车床',deviceCode:"C-003"},{deviceName:'纽威数控车床',deviceCode:"C-004"},{deviceName:'纽威数控车床',deviceCode:"C-005"},
    {deviceName:'纽威数控车床',deviceCode:"C-006"},{deviceName:'纽威数控车床',deviceCode:"C-007"},{deviceName:'纽威数控车床',deviceCode:"C-008"},
    {deviceName:'新诺加工中心',deviceCode:"X-001"},{deviceName:'新诺加工中心',deviceCode:"X-002"},{deviceName:'新诺加工中心',deviceCode:"X-003"}
]


















