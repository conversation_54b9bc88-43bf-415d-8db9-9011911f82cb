<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">机加总览</div>
            <!-- <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="6">
                        <el-form-item label="加工时间">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="统计维度">
                            <el-select style="width:100%" v-model="form.range">
                                <el-option v-for="(item, key) in stasticsRange"
                                           :key="key"
                                           :label="item"
                                           :value="key">
                                
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="工单类型">
                            <el-select style="width:100%" v-model="form.type">
                                <el-option
                                    v-for="(item, key) in workOrderTypeMap"
                                    :key="key"
                                    :label="item"
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="项目类型">
                            <el-select style="width:100%" v-model="form.type">
                                <el-option
                                    v-for="(item, key) in projectTypeMap"
                                    :key="key"
                                    :label="item"
                                    
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label-width="0">
                            <el-button type="primary" @click="onSearch">
                                查询
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row> -->
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
// import { apiDeleteProcessOperate, apiGetProcessOperateList } from "@/api/ledger";
// import { projectTypeMap, stasticsRange, workOrderTypeMap } from "@/views/machine-ledger/enum";

@Component({})
export default class extends Vue {
   
    private mounted() {
       // this.getProcessOperateList();
    }
    
    // private getProcessOperateList() {
    //     apiGetProcessOperateList(
    //         {},
    //         { size: 10, current: this.currentPage },
    //     ).then((res) => {
    //         const data = res.data.data;
    //         this.total = data.total;
    //         this.tableData = data.records;
    //     });
    // }
    
    // private onCurrentChange(currentPage: number) {
    //     this.currentPage = currentPage;
    //     this.getProcessOperateList();
    // }
    
    // private onDetail(scope: any) {
    //     (this.$refs.detailDialog as any).showDialog(scope);
    // }
    
    // private onDelete(scope: any) {
    //     this.$confirm("确认删除？", "提示", {
    //         confirmButtonText: "确定",
    //         cancelButtonText: "取消",
    //     })
    //         .then(() => {
    //             apiDeleteProcessOperate({ id: scope.row.id }).then(() => {
    //                 this.getProcessOperateList();
    //             });
    //         })
    //         .catch(() => {
    //         });
    // }
    
    // private onSearch() {
    // }
}
</script>
<style lang="scss">

</style>
