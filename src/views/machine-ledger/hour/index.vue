<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">机加工时</div>
            <div class="simple-line"></div>
            <div></div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="6">
                        <el-form-item label="时间周期">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="人员: ">
                            <el-cascader
                                clearable
                                :show-all-levels="false"
                                style="width:90%"
                                v-model="operateId"
                                :options="userTreeList"
                                :props="{
                                    value: 'id',
                                    label: 'name',
                                    emitPath: false,
                                }"
                            ></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label-width="0">
                            <el-button type="primary" @click="onSearch">
                                查询
                            </el-button>
                            <!--                            <el-button type="primary" @click="onAdd">-->
                            <!--                                新增-->
                            <!--                            </el-button>-->
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <el-table :data="tableData" :row-class-name="tableRowClassName">
                <el-table-column prop="operateName" label="人员">
                </el-table-column>
                <el-table-column label="加工时间" width="300">
                    <template slot-scope="scope">
                        {{
                            scope.row.startTime
                                ? (scope.row.startTime || "") +
                                  " 至 " +
                                  (scope.row.endTime || "")
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="deviceNumber"
                    label="设备编号"
                ></el-table-column>
                <el-table-column
                    prop="deviceName"
                    label="设备名称"
                ></el-table-column>
                <el-table-column
                    prop="partNumber"
                    label="零件号"
                ></el-table-column>
                <el-table-column
                    prop="processFlowCode"
                    label="工序编号"
                ></el-table-column>
                <el-table-column
                    prop="processFlowName"
                    label="工序名称"
                ></el-table-column>
                <el-table-column
                    prop="totalHours"
                    label="总耗时(h)"
                ></el-table-column>
            </el-table>
        </div>
        <detail-dialog
            @updateList="getProcessOperateList"
            ref="detailDialog"
        ></detail-dialog>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DetailDialog from "./dialog.vue";
import {
    apiDeleteProcessOperate,
    apiGetDeviceList,
    apiGetProcessOperateList,
    apiGetProcessOperateStatistics,
} from "@/api/ledger";

import { projectTypeMap, statisticsTypeList, workOrderTypeMap } from "../enum";
import { ConstantModule } from "@/store/modules/constant";
@Component({ components: { DetailDialog } })
export default class extends Vue {
    private projectTypeMap = projectTypeMap;
    private workOrderTypeMap = workOrderTypeMap;
    private statisticsTypeList = statisticsTypeList;
    private deviceList: any[] = [];
    private form: any = { operateId: "" };
    private confirmDate = ["", ""];
    private tableData: any[] = [];
    private userTreeList: any[] = [];
    private operateId = "";
    async mounted() {
        // this.apiGetProcessOperateStatistics();
        this.getDetailList();
        this.userTreeList = await ConstantModule.getUserTreeList();
        apiGetDeviceList({}).then((res) => {
            this.deviceList = res.data.data;
        });
    }

    getType() {
        this.apiGetProcessOperateStatistics();
    }
    private tableRowClassName({ row }: any) {
        if (row.processFlowName == "总计") {
            return "total-row";
        }
    }
    private getSummaries(param: any) {
        // 上面自定义 内容
        const { columns, data } = param;
        const sums: any = [];
        columns.forEach((column: any, index: any) => {
            if (index === 0) {
                sums[index] = "总计";
                return;
            }
            if (index <= 6) {
                sums[index] = "";
                return;
            }
            const values = data.map((item: { [x: string]: any }) =>
                Number(item[column.property])
            );
            console.log(values);

            if (!values.every((value: number) => isNaN(value))) {
                sums[index] = values.reduce((prev: any, curr: any) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    } else {
                        return prev;
                    }
                }, 0);
                sums[index] += " 小时";
            } else {
                sums[index] = ""; //当不是number数字是返回的内容
            }
        });
        console.log(sums);
        return sums;
    }

    private apiGetProcessOperateStatistics() {
        apiGetProcessOperateStatistics({
            statisticsType: "WORK_HOURS",
            operateId: this.operateId || undefined,
        }).then((res) => {
            this.tableData = res.data.data;
        });
    }

    private onDetail(scope: any) {
        (this.$refs.detailDialog as any).showDialog(scope);
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteProcessOperate({ id: scope.row.id }).then(() => {
                    this.getProcessOperateList();
                });
            })
            .catch((err) => {
                console.log(err);
            });
    }

    private getProcessOperateList() {
        apiGetProcessOperateList({}, { size: 10, current: 1 }).then((res) => {
            const data = res.data.data;
            this.tableData = data.records;
        });
    }

    private getDetailList() {
        let detail: any = {};
        detail.operateId = this.operateId || undefined;
        if (!this.confirmDate) this.confirmDate = ["", ""];
        if (this.confirmDate[0] != "") detail.startTime = this.confirmDate[0];
        if (this.confirmDate[1] != "") detail.endTime = this.confirmDate[1];
        detail.statisticsType = "WORK_HOURS";
        apiGetProcessOperateStatistics(detail)
            .then((res) => {
                let { code, message, data } = res.data;
                const tableData = this.mutateData(res.data.data || []);
                this.tableData = tableData;
            })
            .catch((err) => {
                console.log(err);
            });
    }
    private mutateData(data: any[]) {
        if (!data.length) {
            return [];
        }
        let ret: any[] = [];
        let tmpTotalSum = 0;
        let lastItem = data[0].operateName;
        data.forEach((item) => {
            if (lastItem == item.operateName) {
                ret.push({ ...item });
            } else {
                ret.push({
                    processFlowName: "总计",
                    totalHours: tmpTotalSum.toFixed(2),
                });
                ret.push({ ...item });
                lastItem = item.operateName;
                tmpTotalSum = 0;
            }
            tmpTotalSum += Number(item.totalHours || 0);
        });
        ret.push({
            processFlowName: "总计",
            totalHours: tmpTotalSum.toFixed(2),
        });
        return ret;
    }
    private onSearch() {
        this.getDetailList();
    }

    private onAdd() {
        (this.$refs.detailDialog as any).showDialog(null);
    }
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.el-table .total-row {
    background: #f0f9eb;
}
</style>
