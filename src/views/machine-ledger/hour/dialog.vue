<template>
    <el-dialog width="80%" title="" :visible.sync="isDialogVisible">
        <el-form label-width="100px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="工序编号">
                        <el-input
                            style="width:100%"
                            disabled
                            v-model="form.processFlowCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="关联工艺">
                        <el-select v-model="form.processFlowId" placeholder="请选择" style="width:100%;" @change="onChangeCraft">
                            <el-option
                                v-for="item in crafts"
                                :key="item.processFlowCode"
                                :label="item.processFlowCode+'|'+item.partNumber+'|'+item.processFlowName"
                                :value="item.processFlowId">
                                <span style="float: left">{{ item.processFlowCode }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.partNumber+"|"+ item.processFlowName}}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="班次">
                        <el-select style="width:100%" v-model="form.workType">
                            <el-option value="DAY" label="白"> </el-option>
                            <el-option value="NIGHT" label="夜"> </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="加工时间">
                        <el-date-picker
                            clearable
                            style="width:100%"
                            value-format="yyyy-MM-dd HH:mm"
                            v-model="confirmDate"
                            class="product-date-picker"
                            type="datetimerange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="设备编号">
                        <!-- <el-input
                            style="width:100%"
                            v-model="form.deviceName"
                        ></el-input> -->
                        <el-select style="width:100%" v-model="form.deviceName">
                            <el-option
                                v-for="item in deviceList"
                                :key="item.deviceCode"
                                :label="item.deviceName"
                                :value="item.deviceCode"
                            >
                                <span style="float: left">{{ item.deviceCode }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.deviceName}}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="零件号">
                        <el-input
                            style="width:100%" disabled="true"
                            v-model="form.partNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <!-- 工艺名称 -->
                    <el-form-item label="配件名称">
                        <el-input
                            style="width:100%" disabled="true"
                            v-model="form.processFlowName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <!-- 工序 -->
                    <el-form-item label="加工内容">
                        <el-select style="width:100%" v-model="form.flowId">
                            <el-option
                                v-for="item in flowList"
                                :key="item.flowId"
                                :label="item.flowName"
                                :value="item.flowId"
                            >
                                <span style="float: left">{{ item.flowName }}</span>
                                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.deviceName}}</span>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="工单类型">
                        <el-select
                            style="width:100%"
                            v-model="form.workOrderType"
                        >
                            <el-option
                                v-for="(item, key) in workOrderTypeMap"
                                :key="key"
                                :label="item"
                                :value="key"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="项目类型">
                        <el-select
                            style="width:100%"
                            v-model="form.projectType"
                        >
                            <el-option
                                v-for="(item, key) in projectTypeMap"
                                :key="key"
                                :label="item"
                                :value="key"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="首次调试">
                        <el-radio-group v-model="form.firstTest">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总件数">
                        <!-- <el-input
                            disabled
                            style="width:100%"
                            v-model="form.total"
                        ></el-input> -->
                        {{form.qualifiedQuantity+form.repairQuantity}}
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="合格数量">
                        <el-input
                            style="width:100%"
                            v-model="form.qualifiedQuantity"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="返修数量">
                        <el-input
                            style="width:100%"
                            v-model="form.repairQuantity"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="报废数量">
                        <el-input
                            style="width:100%"
                            v-model="form.abandonQuantity"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="操作人">
                        <!-- <el-input style="width:100%" v-model="form.operateId"></el-input> -->
                        <el-cascader  @change="onOperateChange(scope,'operateId')"  :show-all-levels="false" style="width:90%"
                                      v-model="form.operateId" :options="userTreeList"
                                      :props="{value: 'id',label: 'name',emitPath: false,}"></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="总耗时">
                        <el-input
                            style="width:100%"
                            v-model="form.totalHours"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="效率">
                        <!-- (工序标准工时*总件数+准备工时)/总耗时 -->
                        {{0}}
                        <!-- <el-select style="width:100%" v-model="form.efficiency">
                            <el-option value="白" label="白"> </el-option>
                            <el-option value="夜" label="夜"> </el-option>
                        </el-select> -->
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="要求交货日期">
                        <el-date-picker
                            style="width:100%"
                            v-model="form.planEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="报废原因">
                        <el-input
                            style="width:100%"
                            v-model="form.abandonReason"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="质检员">
                        <!-- <el-input
                            style="width:100%"
                            v-model="form.checkUserId"
                        ></el-input> -->
                        <el-cascader  @change="onOperateChange(scope,'checkUserId')"  :show-all-levels="false" style="width:90%"
                                      v-model="form.checkUserId" :options="userTreeList"
                                      :props="{value: 'id',label: 'name',emitPath: false,}"></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="备注">
                        <el-input
                            style="width:100%"
                            v-model="form.comment"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer">
            <el-button type="primary" @click="onConfirm">确认</el-button>
            <el-button @click="onCancel">取消</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { projectTypeMap, workOrderTypeMap,deviceList } from "../enum";
import {
    apiUpdateProcessOperate,
    apiGetProcessCraftList,
    apiGetProccessCraftInfo,
} from "@/api/ledger";
import { apiGetProcessFlowList } from "@/api/process";
import { ConstantModule } from "@/store/modules/constant";
@Component({ components: {} })
export default class extends Vue {
    private projectTypeMap = projectTypeMap;
    private workOrderTypeMap = workOrderTypeMap;
    private deviceList = deviceList;
    private flowList: any[] = [];
    private form: any = {};
    private crafts:any=[];
    private userTreeList: any[] = [];
    private isDialogVisible = false;
    private async showDialog(scope: any) {
        this.serviceGetCraftList();
        this.userTreeList = await ConstantModule.getUserTreeList();
        
        //edit
        if(scope!=null){
            this.form = { ...scope.row };
            this.getFlowList(scope.row.processFlowId);
            this.confirmDate = [this.form.startTime, this.form.endTime];
            this.isDialogVisible = true;
            return;
        }
        //add
        this.initFormProperty();
        this.isDialogVisible=true;
    }
    private initFormProperty(){
    
    }
    
    private getFlowList(processFlowId: number) {
        apiGetProcessFlowList({ processFlowId }).then((res) => {
            this.flowList = res.data.data || [];
        });
    }
    private confirmDate: any = ["", ""];
    private onConfirm() {
        [this.form.startTime, this.form.endTime] = this.confirmDate;
        apiUpdateProcessOperate(this.form).then((res) => {
            this.isDialogVisible = false;
            this.$emit("updateList");
        });
    }
    // 人员分配业务
    private onOperateChange(scope: any,key:any) {
        this.form[key] = this.findNameById(scope.row[key]);
    }
    private findNameById(id: string) {
        let flag = false;
        let ret = "";
        function recursiveGetName(children: any[]) {
            for (let i = 0, len = children.length; i < len; i++) {
                if (flag) {
                    break;
                }
                if (children[i].id == id) {
                    flag = true;
                    ret = children[i].name;
                }
                if (children[i].children && !flag) {
                    recursiveGetName(children[i].children);
                }
            }
        }
        recursiveGetName(this.userTreeList);
        return ret;
    }
    private serviceGetCraftList(){
        apiGetProcessCraftList({},{current:1,size:100}).then((res) => {
            // this.isDialogVisible = false;
            // this.$emit("updateList");
            this.crafts=res.data.data.records||[];
            //console.log(this.crafts)
        });
    }
    private serviceGetCraftInfo(){
        this.flowList=[];
        this.form.processFlowCode='';
        this.form.flowId='';
        apiGetProccessCraftInfo({processFlowId:this.form.processFlowId}).then(res=>{
            this.flowList=res.data.data.flowDetailList;
            this.form.processFlowCode=res.data.data.processFlowCode;
        }).catch(err=>{})
    }
    private onChangeCraft(){
        //获取某个工艺下的工序
        this.serviceGetCraftInfo();
        
    }
    private onCancel() {
        this.isDialogVisible = false;
    }
}
</script>
