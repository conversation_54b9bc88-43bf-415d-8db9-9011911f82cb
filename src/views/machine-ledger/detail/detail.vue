<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">机加明细</div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <div class="simple-line"></div>
                    <el-col :span="6">
                        <el-form-item label="设备编号: ">
                            <el-select style="width:100%" v-model="form.deviceNumber" clearable>
                                <el-option
                                    v-for="item in deviceList"
                                    :key="item.deviceNumber"
                                    :label="item.deviceNumber"
                                    :value="item.deviceNumber"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="工单类型">
                            <el-select style="width:100%" v-model="form.workOrderType" clearable>
                                <el-option
                                    v-for="(item, key) in workOrderTypeMap"
                                    :key="key"
                                    :label="item"
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="项目类型" >
                            <el-select style="width:100%" clearable v-model="form.projectType">
                                <el-option
                                    v-for="(item, key) in projectTypeMap"
                                    :key="key"
                                    :label="item"
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="加工时间">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label-width="0">
                            <el-button type="primary" @click="onSearch">
                                查询
                            </el-button>
                            <!-- <el-button type="primary" @click="onAdd">
                                新增
                            </el-button> -->
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <div class="simple-line"></div>
            <el-table :data="tableData">
                <el-table-column label="加工时间" width="300">
                    <template slot-scope="scope">
                        {{
                            scope.row.startTime
                                ? (scope.row.startTime || "") +
                                  " 至 " +
                                  (scope.row.endTime || "")
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="deviceNumber" label="设备编号">
                </el-table-column>
                <el-table-column prop="deviceName" label="设备名称">
                </el-table-column>
                <el-table-column prop="processFlowCode" label="工序编号">
                </el-table-column>
                <el-table-column prop="partNumber" label="零件号">
                </el-table-column>
                <el-table-column prop="processFlowName" label="工序名称">
                </el-table-column>
                <el-table-column prop="projectType" label="项目类型">
                    <template slot-scope="scope">
                        {{ projectTypeMap[scope.row.projectType] }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            style="margin-right:10px"
                            @click="onDetail(scope)"
                        >
                            详情
                        </el-button>
                        <el-button
                            type="text"
                            style="margin-right:10px"
                            @click="onDelete(scope)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <detail-dialog
            @updateList="getProcessOperateList"
            ref="detailDialog"
        ></detail-dialog>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DetailDialog from "./dialog.vue";
import {
    apiGetProcessOperateList,
    apiUpdateProcessOperate,
    apiGetMwdWorkOderInfo,
    apiAddProcessOperate,
    apiDeleteProcessOperate, apiGetDeviceList,
} from "@/api/ledger";

import { projectTypeMap, workOrderTypeMap } from "../enum";
@Component({ components: { DetailDialog } })
export default class extends Vue {
    private projectTypeMap = projectTypeMap;
    private workOrderTypeMap = workOrderTypeMap;
    private form: any = { workOrderType: "", projectType: "", deviceNumber: "" };
    private confirmDate = ["", ""];
    private tableData: any[] = [];
    private flowList: any[] = [];
    private deviceList: any[] = [];
    private total = 1;
    private currentPage = 1;
    private mounted() {
        this.getProcessOperateList();
        apiGetDeviceList({}).then((res) => {
            this.deviceList = res.data.data;
        });
    }
    private getProcessOperateList() {
        apiGetProcessOperateList(
            {},
            { size: 10, current: this.currentPage }
        ).then((res) => {
            const data = res.data.data;
            this.total = data.total;
            this.tableData = data.records;
        });
    }
    
    private getSearchedList(){
        let detail: any = {};
        let params:any = { size: 10, current: this.currentPage };
        console.log(this.form.deviceNumber)
        if (this.form.projectType != "")
            detail.projectType = this.form.projectType;
        if (this.form.workOrderType != "")
            detail.workOrderType = this.form.workOrderType;
        if (this.form.deviceNumber != "")
            detail.deviceNumber = this.form.deviceNumber;
        if (!this.confirmDate)
            this.confirmDate = ["", ""];
        if (this.confirmDate[0] != "")
            detail.startTime = this.confirmDate[0];
        if (this.confirmDate[1] != "")
            detail.endTime = this.confirmDate[1];
        apiGetProcessOperateList( detail,params).then((res) => {
            let { code, message, data } = res.data;
            this.tableData = res.data.data.records;
            this.total = data.total;
            this.currentPage = data.current;
        }).catch((err) => {
            console.log(err);
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getProcessOperateList();
    }
    private onDetail(scope: any) {
        (this.$refs.detailDialog as any).showDialog(scope);
    }
    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteProcessOperate({ id: scope.row.id }).then(() => {
                    this.getProcessOperateList();
                });
            })
            .catch((err) => {
                console.log(err)
            });
    }
    private onAdd() {
        //add by lce
          (this.$refs.detailDialog as any).showDialog(null);
    }
    private onSearch() {
        this.currentPage = 1;
        this.getSearchedList();
    }
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
</style>
