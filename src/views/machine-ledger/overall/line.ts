export const option = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            crossStyle: {
                color: '#999'
            }
        }
    },
    toolbox: {
        feature: {
           saveAsImage: { show: true }
        }
    },
    legend: {
        data: ['总工时(h)', '总产量(件)']
    },
    xAxis:
        {
            type: 'category',
            data:[],
            axisPointer: {
                type: 'shadow'
            }
        },
    yAxis: [
        {
            type: 'value',
            name: '总工时',
            min: 0,
            axisLabel: {
                formatter: '{value} h'
            }
        },
        {
            type: 'value',
            name: '总产量',
            min: 0,
            axisLabel: {
                formatter: '{value} 件'
            }
        }
    ],
    series: [
        {
            name: '总工时(h)',
            type: 'bar',
            data: []
        },
        {
            name: '总产量(件)',
            type: 'line',
            yAxisIndex: 1,
            data: []
        }
    ]
};