<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">生产总览</div>
            <el-row :gutter="20" style="margin-bottom: 20px">
                <el-form :model="form" label-width="80px">
                    <el-col :span="7">
                        <span class="demonstration">时间周期：</span>
                        <el-date-picker
                            v-model="month"
                            type="month"
                            value-format="yyyy-MM"
                            placeholder="选择月">
                        </el-date-picker>
                        <el-button type="primary" style="margin-left: 10px" @click="onSearch">
                            查询
                        </el-button>
                    </el-col>
                </el-form>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12" style="margin-bottom:20px">
                    <el-card style="height:500px">
                        <div
                            id="line-chart"
                            style="width:100%;height:500px"
                        ></div>
                    </el-card>
                </el-col>
                <el-col :span="12" style="margin-bottom:20px">
                    <el-card style="height:500px">
                        <div
                            id="pie-chart"
                            style="width:100%;height:500px"
                        ></div>
                    </el-card>
                </el-col>
                <el-col :span="12" style="margin-bottom:20px">
                    <el-card style="height:500px">
                        <div
                            id="tree-chart"
                            style="width:100%;height:500px"
                        ></div>
                    </el-card>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import * as echarts from "echarts";
import { option as lineOption } from "./line";
import { option as pieOption } from "./pie";
import { option as treeOption } from "./tree";
import { apiGetProcessOverAll } from "@/api/ledger";
import { projectTypeMap } from "@/views/machine-ledger/enum";

@Component({ components: {} })
export default class extends Vue {
    
    private month = new Date().toISOString().slice(0, 7);
    
    private async mounted() {
        this.drawGraphical({ month: this.month });
    }
    
    private onSearch() {
        this.drawGraphical({ month: this.month });
    }
    
    
    private async drawGraphical(data: any) {
        let lineChartEl = document.getElementById("line-chart") as HTMLElement;
        let pieChartEl = document.getElementById("pie-chart") as HTMLElement;
        let treeChartEl = document.getElementById("tree-chart") as HTMLElement;
        let lineChart = echarts.init(lineChartEl);
        let pieChart = echarts.init(pieChartEl);
        let treeChart = echarts.init(treeChartEl);
        lineChart.clear()
        pieChart.clear()
        treeChart.clear()

        await apiGetProcessOverAll(data).then((res) => {
            //设备工时柱状图
            const data = res.data.data;
            lineOption.xAxis.data = [];
            let deviceArr: any = [];
            let quantityArr: any = [];
            let hoursArr: any = [];
            data.statisticsHoursAndQuantityVos.forEach((item: any) => {
                deviceArr.push(item.deviceNumber);
                quantityArr.push(item.totalQuantity);
                hoursArr.push(item.totalHours);
            });
            lineOption.xAxis.data = deviceArr;
            lineOption.series[0].data = hoursArr;
            lineOption.series[1].data = quantityArr;

            //饼状图
            let pieArr: { value: any; name: string }[] = [];
            let legendArr: string[] = [];
            data.statisticsProjectTypeVos.forEach((item: any) => {
                let object: { value: any; name: string } = {
                    value: item.totalQuantity,
                    name: projectTypeMap[item.projectType]
                };
                pieArr.push(object);
                legendArr.push(projectTypeMap[item.projectType]);
            });
            (pieOption.series[0] as any).data = pieArr;
            (pieOption.legend[0] as any).data = legendArr;
            
            //合格率
            (pieOption.series[1].data as any[]).push(data.qualifiedRateVo);

            let qualified: { name: string; value: any } = { name: '', value: null };
            let unqualified: { name: string; value: any } = { name: '', value: null };
            let qualifiedArr: any = [];
            qualified.name = "合格";
            qualified.value = data.qualifiedRateVo.quantity;
            unqualified.name = "不合格";
            unqualified.value = data.qualifiedRateVo.abandonQuantity;
            qualifiedArr.push(qualified);
            qualifiedArr.push(unqualified);
            pieOption.series[1].data = qualifiedArr;
            
            // 树形图
            treeOption.series[0].data[0].children = data.statisticsUserVos;
        });
        
        lineChart.setOption(lineOption, true, true);
        pieChart.setOption(pieOption);
        treeChart.setOption(treeOption);
        this.resizeFun = () => {
            lineChart.resize({ width: "auto" });
            pieChart.resize({ width: "auto" });
            treeChart.resize({ width: "auto" });
        };
        this.sidebarEl = document.getElementsByClassName(
            "sidebar-container",
        )[0];
        this.sidebarEl.addEventListener("transitionend", this.resizeFun);
        window.addEventListener("resize", this.resizeFun);
    }
    
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
</style>
