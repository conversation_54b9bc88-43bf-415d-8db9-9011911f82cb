<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">机加汇总</div>
            <div class="simple-line"></div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="6">
                        <el-form-item fixed label="加工时间: ">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                                >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-form-item label="工单类型: ">
                            <el-select
                                style="width:100%"
                                v-model="form.type"
                                multiple
                            >
                                <el-option
                                    v-for="(item, key) in workOrderTypeMap"
                                    :key="key"
                                    :label="item"
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="项目类型: ">
                            <el-select
                                style="width:100%"
                                v-model="form.projectType"
                                multiple
                            >
                                <el-option
                                    v-for="(item, key) in projectTypeMap"
                                    :key="key"
                                    :label="item"
                                    :value="key"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="设备编号: ">
                            <el-select
                                style="width:100%"
                                v-model="form.deviceNumber"
                                multiple
                            >
                                <el-option
                                    v-for="item in deviceList"
                                    :key="item.deviceNumber"
                                    :label="item.deviceNumber"
                                    :value="item.deviceNumber"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="3">
                        <el-form-item label-width="0">
                            <el-button type="primary" @click="onSearch">
                                查询
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <el-table :data="tableData" :row-class-name="tableRowClassName">
                <el-table-column label="加工时间" width="300" fixed>
                    <template slot-scope="scope">
                        {{
                            scope.row.startTime
                                ? (scope.row.startTime || "") +
                                  " 至 " +
                                  (scope.row.endTime || "")
                                : ""
                        }}
                    </template>
                </el-table-column>

                <el-table-column
                    prop="deviceNumber"
                    label="设备编号"
                ></el-table-column>
                <el-table-column
                    prop="deviceName"
                    label="设备名称"
                ></el-table-column>
                <el-table-column
                    prop="partNumber"
                    label="零件号"
                ></el-table-column>
                <el-table-column
                    prop="processFlowName"
                    label="工序名称"
                ></el-table-column>
                <el-table-column prop="operateName" label="操作人">
                    <template slot-scope="scope">
                        {{
                            scope.row.operateName === "总计"
                                ? "总计"
                                : scope.row.operateName
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="totalQuantity"
                    label="总数量"
                ></el-table-column>
                <el-table-column
                    prop="abandonQuantity"
                    label="报废量"
                ></el-table-column>
                <el-table-column
                    prop="flowHours"
                    label="工序工时(min)"
                ></el-table-column>
                <el-table-column
                    prop="standardHours"
                    label="标准工时(min)"
                ></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiGetDeviceList, apiGetProcessOperateStatistics } from "@/api/ledger";

import {
    projectTypeMap,
    statisticsTypeList,
    workOrderTypeMap,
} from "@/views/machine-ledger/enum";

@Component({})
export default class extends Vue {
    private projectTypeMap = projectTypeMap;
    private workOrderTypeMap = workOrderTypeMap;
    private statisticsTypeList = statisticsTypeList;
    private deviceList: any[] = [];
    private form: any = { type: [], projectType: [], deviceNumber: [] };
    private confirmDate: any = "";
    private tableData: any[] = [];
    private flowList: any[] = [];
    private total = 1;
    private currentPage = 1;

    mounted() {
        this.getDetailList();
        apiGetDeviceList({}).then((res) => {
            this.deviceList = res.data.data;
        });
    }

    getType() {
        this.apiGetProcessOperateStatistics();
    }

    private tableRowClassName({ row }: any) {
        if (row.operateName == "总计") {
            return "total-row";
        }
    }

    private apiGetProcessOperateStatistics() {
        apiGetProcessOperateStatistics({ statisticsType: "DEVICE" }).then(
            (res) => {
                this.tableData = res.data.data;
            }
        );
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.apiGetProcessOperateStatistics();
    }

    private onSearch() {
        this.getDetailList();
    }

    private getDetailList() {
        let detail: any = {};
        if (this.form.projectType.length != 0)
            detail.projectType = this.form.projectType;
        if (this.form.type.length != 0) detail.workOrderType = this.form.type;
        if (this.form.deviceNumber.length != 0)
            detail.deviceNumber = this.form.deviceNumber;
        if (!this.confirmDate) this.confirmDate = ["", ""];
        if (this.confirmDate[0] != "") detail.startTime = this.confirmDate[0];
        if (this.confirmDate[1] != "") detail.endTime = this.confirmDate[1];
        detail.statisticsType = "DEVICE";
        apiGetProcessOperateStatistics(detail)
            .then((res) => {
                let { code, message, data } = res.data;
                const tableData = this.mutateData(res.data.data || []);
                this.tableData = tableData;
            })
            .catch((err) => {
                console.log(err);
            });
    }
    private mutateData(data: any[]) {
        if (!data.length) {
            return [];
        }
        let ret: any[] = [];
        let tmpTotalSum = 0;
        let tmpAbandonSum = 0;
        let tmpFlowSum = 0;
        let tmpStandardSum = 0;
        let lastItem = data[0].deviceNumber;
        data.forEach((item) => {
            if (lastItem == item.deviceNumber) {
                ret.push({ ...item });
            } else {
                ret.push({
                    operateName: "总计",
                    totalQuantity: tmpTotalSum,
                    abandonQuantity: tmpAbandonSum,
                    flowHours: tmpFlowSum.toFixed(2),
                    standardHours: tmpStandardSum.toFixed(2),
                });
                ret.push({ ...item });
                lastItem = item.deviceNumber;
                tmpTotalSum = 0;
                tmpAbandonSum = 0;
                tmpStandardSum = 0;
                tmpFlowSum = 0;
            }
            tmpTotalSum += Number(item.totalQuantity || 0);
            tmpAbandonSum += Number(item.abandonQuantity || 0);
            tmpStandardSum += Number(item.standardHours || 0);
            tmpFlowSum += Number(item.flowHours || 0);
        });
        ret.push({
            operateName: "总计",
            totalQuantity: tmpTotalSum,
            abandonQuantity: tmpAbandonSum,
            flowHours: tmpFlowSum.toFixed(2),
            standardHours: tmpStandardSum.toFixed(2),
        });
        return ret;
    }
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
.el-table .total-row {
    background: #f0f9eb;
}
</style>
