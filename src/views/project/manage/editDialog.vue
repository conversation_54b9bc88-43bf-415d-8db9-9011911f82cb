<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        :title="`${isAdd?'新增':'编辑'}项目信息`"
        width="600px"
    >
        <el-form v-if="isDialogVisible" ref="detailForm" :rules="formRules" :model="detailForm" label-width="90px">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="项目名称: " prop="projectName">
                        <el-input v-model="detailForm.projectName"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="项目编号: " prop="projectNumber">
                        <el-input v-model="detailForm.projectNumber"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="项目经理: " prop="projectManagerId">
                        <FuzzySelect type="USER_ALL" :initList="initManagerList" v-model="detailForm.projectManagerId" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="产品总监: " prop="projectDirectorId">
                        <FuzzySelect type="USER_ALL" :initList="initDirectorList" v-model="detailForm.projectDirectorId" />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="开始日期: " prop="startDate">
                        <el-date-picker
                            placement="bottom-start"
                            style="width: 100%"
                            v-model="detailForm.startDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="12" v-if="!isAdd">
                    <el-form-item label="结束日期: " prop="endDate">
                        <el-date-picker
                            placement="bottom-start"
                            style="width: 100%"
                            v-model="detailForm.endDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col> -->
                <el-col :span="24">
                    <el-form-item label="参项人员: " prop="projectMemberList">
                        <FuzzySelect type="USER_ALL" :initList="initRelationList" multiple v-model="detailForm.projectMemberList" />
                    </el-form-item>
                </el-col>
                <el-col :span="24" v-if="!isAdd">
                    <el-form-item label="项目文件: " prop="maxBht">
                        <div class="file-list" v-if="fileList&&fileList.length">
                            <div class="file-item" v-for="item in fileList" :key="item.massId">
                                <span :title="item.massFileName" class="file-title">
                                    {{ item.massFileName }}
                                </span>
                                <i @click="onDeleteFile(item.massId)" class="el-icon-delete oper-icon"></i>
                            </div>
                        </div>
                        <el-upload
                            action="#"
                            :show-file-list="false"
                            :before-upload="onUploadFile"
                        >
                            <el-button type="text" :loading="isFileUploading" >上传</el-button>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">
                取 消
            </el-button>
            <el-button type="primary" :loading="btnLoading" @click="onConfirm">
                确 认
            </el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ElForm } from "element-ui/types/form";
import FuzzySelect from "@/components/FuzzySelect/index.vue";
import { apiAddProject, apiGetProjectInfo, apiUpdateProject } from "@/api/project";
import { apiDeleteMassFile, apiGetMassFileList, apiUploadMassFile } from "@/api/file";
import dayjs from "dayjs";
@Component({
    components: { FuzzySelect }
})
export default class extends Vue {
    private initDirectorList:any [] = [];
    private initManagerList: any[] = [];
    private initRelationList: any[] = [];
    private isDialogVisible = false;
    private dialogType = "ADD"
    private detailForm: any = {};
    private isFileUploading = false;
    private fileList: any[] = [];
    private btnLoading = false;
    get formRules() {
        return {
            projectName : [{required:true, message: "请填写项目名称", trigger:'blur'}],
            projectNumber: [{required:true, message: "请填写项目编号", trigger:'blur'}],
            projectManagerId : [{required:true, message: "请填写项目经理", trigger:'blur'}],
            projectDirectorId: [{required:true, message: "请填写产品总监", trigger:'blur'}],
        }
    }
    get isAdd(){
        return this.dialogType === "ADD";
    }
    private getInitForm(): any {
        return {
            projectName: null,
            projectNumber: null,
            projectManagerId: null,
            projectDirectorId: null,
            startDate: dayjs().format('YYYY-MM-DD'),
            projectMemberRelation: [],
            projectMemberList: []
        };
    }
    private async showDialog(type, row) {
        const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        try{
            this.dialogType = type;
            this.initManagerList = [];
            this.initRelationList = [];
            this.initDirectorList = [];
            if(type === 'ADD'){
                this.detailForm = this.getInitForm();
            }else{
                const infoData = await apiGetProjectInfo({ projectId: row.id });
                const info = infoData?.data?.data || {}
                this.detailForm = { ...info };
                this.detailForm.projectMemberList = (info.projectMemberRelation || []).map(item=>item.memberId)
                if(info.projectManagerId){
                    this.initManagerList = [{memberId: info.projectManagerId, name: info.projectManagerName}];
                }
                if(info.projectMemberRelation){
                    this.initRelationList = info.projectMemberRelation.map(item=>({
                        memberId: item.memberId,
                        name: item.name
                    }))
                }
                if(info.projectDirectorId){
                    this.initDirectorList = [{memberId: info.projectDirectorId, name: info.projectDirectorName}];
                }
                await this.getFileList();
            }
            this.isDialogVisible = true;
            this.$nextTick(()=>{
                (this.$refs.detailForm as ElForm).clearValidate();
            })
        }finally{
            loading.close();
        }
    }
    private async onConfirm(){
        const valid = await (this.$refs.detailForm as any).validate()
        if(!valid){
            return 
        }
        this.btnLoading = true;
        if(this.isAdd) {
            apiAddProject(this.detailForm).then(()=>{
                this.$message.success("操作成功");
                this.isDialogVisible = false;
                this.$emit("getProjectList", true);
            }).finally(()=>{
                this.btnLoading = false;
            })
        }else{
            apiUpdateProject(this.detailForm).then(()=>{
                this.$message.success("操作成功");
                this.isDialogVisible = false;
                this.$emit("getProjectList");
            }).finally(()=>{
                this.btnLoading = false;
            })
        }
    }
    private onUploadFile(file){
        this.isFileUploading  = true
        const form = new FormData();
        form.append('id', String(this.detailForm.id));
        form.append('file', file);
        form.append('fileType', 'PROJECT_FILE');
        apiUploadMassFile(form).then(()=>{
            this.getFileList();
        }).finally(()=>{
            this.isFileUploading = false
        })
    }
    private async getFileList(){
        const fileData = await apiGetMassFileList({id: this.detailForm.id, massFileType:"PROJECT_FILE"}, {current: 1, size: 99999});
        this.fileList = fileData?.data?.data?.records || [];
    }
    private onDeleteFile(massId){
        this.$confirm("确认删除该附件?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(()=>{
            const form = new FormData();
            form.append('type', 'MASSID');
            form.append("idList", [massId].toString())
            apiDeleteMassFile(form).then(()=>{
                this.getFileList();
            });
        })
        
    }
}
</script>
<style lang="scss" scoped>
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            float: right;
            margin-top: 6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>
