<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">项目列表 <el-button @click="onAdd" v-if="p_ProjectAdd" type="primary" style="float: right">新增项目</el-button></div> 
            <el-table @sort-change="onSortChange" :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" style="margin-top: 20px" v-loading="tableLoading">
                <el-table-column
                    prop="projectName"
                    label="项目名称"
                ></el-table-column>
                <el-table-column
                    prop="projectNumber"
                    label="项目编号"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="projectManagerName"
                    label="项目经理"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="projectDirectorName"
                    label="产品总监"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="totalLaborHours"
                    label="工时总计(h)"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="startDate"
                    label="开始日期"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="endDate"
                    label="结束日期"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="projectStatus"
                    label="状态"
                    align="center"
                    width="150"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="PROJECT_STATUS" :tagValue="scope.row.projectStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="160">
                    <template slot-scope="scope">
                        <el-button type="text" v-if="p_ProjectDetail" @click="onDetail(scope)">
                            详情
                        </el-button>
                        <el-button type="text" v-if="p_ProjectUpdate" :disabled="scope.row.projectStatus==1" @click="onEdit(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" v-if="p_ProjectFinish" :disabled="scope.row.projectStatus==1" @click="onFinish(scope)">
                            结项
                        </el-button>
                        <!-- <el-button type="text" v-if="p_ProjectDelete" :disabled="scope.row.projectStatus==1" @click="onDelete(scope)">
                            删除
                        </el-button> -->
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
            <EditDialog @getProjectList="getProjectList" ref="editDialog" />
            <DetailDialog ref="detailDialog" />
            <el-dialog :visible.sync="isFinishDialogVisible" width="300px" top="30vh" title="选择结项时间">
                <el-date-picker
                    v-model="finishProjectEndDate"
                    type="date"
                    placeholder="结项时间"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    style="width: 100%;"
                    :clearable="false"
                ></el-date-picker>
                <template #footer>
                    <el-button @click="isFinishDialogVisible = false">
                        取 消
                    </el-button>
                    <el-button type="primary" @click="onConfirmFinish">
                        确认结项
                    </el-button>
                </template>
            </el-dialog>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import EditDialog from "./editDialog.vue";
import DetailDialog from "./detailDialog.vue";
import { apiGetProjectList, apiFinishProject, apiDeleteProject } from "@/api/project";
import momentjs from "moment";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({
    components: {
        EditDialog,
        DetailDialog
    },
})
export default class extends Vue {
    private isFinishDialogVisible = false;
    private finishProjectId:any = null;
    private finishProjectEndDate = "";
    private tableData: any[] = [];
    private tableLoading = false;
    private currentPage = 1;
    private pageSize = 100;
    private total = 0;
    get p_ProjectDetail(){
        return this.$checkBtnPermission('sys:project:manage:detail')
    }
    get p_ProjectAdd(){
        return this.$checkBtnPermission('sys:project:manage:add')
    }
    get p_ProjectUpdate(){
        return this.$checkBtnPermission('sys:project:manage:edit')
    }
    get p_ProjectFinish(){
        return this.$checkBtnPermission('sys:project:manage:finish')
    }
    get p_ProjectDelete(){
        return this.$checkBtnPermission('sys:project:manage:delete')
    }
    private mounted() {
        this.getProjectList();
    }
    private getProjectList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        this.tableLoading = true;
        apiGetProjectList({}, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        }).finally(() => {
            this.tableLoading = false;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getProjectList();
    }
    
    private onSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.getProjectList();
    }
    private onAdd() {
        (this.$refs.editDialog as any).showDialog('ADD');
    }
    private onEdit(scope) {
        (this.$refs.editDialog as any).showDialog('EDIT', scope.row);
    }
    private onDetail(scope) {
        (this.$refs.detailDialog as any).showDialog(scope.row);
    }
    private onFinish(scope) {
        this.finishProjectId = scope.row.id;
        this.finishProjectEndDate = momentjs().format("YYYY-MM-DD");
        
        // this.$confirm('确认结项？', '确认', {
        //     confirmButtonText: '确认',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        // }).then(() => {
        //     apiFinishProject({id: scope.row.id}).then(() => {
        //         this.$message.success('操作成功');
        //         this.getProjectList();
        //     })
        // })
    }
    private onConfirmFinish() {
        if(!this.finishProjectEndDate){
            return;
        }
        apiFinishProject({id: this.finishProjectId, endDate: this.finishProjectEndDate}).then(() => {
            this.$message.success('操作成功');
            this.isFinishDialogVisible = false;
            this.getProjectList();
        })
    }
    private onDelete(scope){
        this.$confirm('确认删除？', '确认', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            apiDeleteProject({id: scope.row.id}).then(() => {
                this.$message.success('操作成功');
                this.getProjectList();
            })
        })
    }
}
</script>
<style lang="scss" scoped>
.four-year-warning {
    color: red;
    font-weight: bold;
}
</style>
