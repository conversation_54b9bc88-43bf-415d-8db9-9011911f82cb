<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :title="`项目详情`"
        width="600px"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="80px">
            <el-row :gutter="20" class="flex-row">
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            项目名称:
                        </div>
                        <div class="item-content">
                            {{  detailForm.projectName  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            项目编号:
                        </div>
                        <div class="item-content">
                            {{  detailForm.projectNumber  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            开始日期:
                        </div>
                        <div class="item-content">
                            {{  detailForm.startDate  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            结束日期:
                        </div>
                        <div class="item-content">
                            {{  detailForm.endDate  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            项目经理:
                        </div>
                        <div class="item-content">
                            {{  detailForm.projectManagerName  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            产品总监:
                        </div>
                        <div class="item-content">
                            {{  detailForm.projectDirectorName  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            参项人员:
                        </div>
                        <div class="item-content">
                            {{ detailForm.projectMemberRelation ? detailForm.projectMemberRelation.map(item=>item.name).join(', ') : "" }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            项目状态:
                        </div>
                        <div class="item-content">
                            <CustomTag tagType="PROJECT_STATUS" :tagValue="detailForm.projectStatus" />
                        </div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="item-container">
                        <div class="item-title">
                            耗时(h):
                        </div>
                        <div class="item-content">
                            {{  detailForm.totalLaborHours  }}
                        </div>
                    </div>
                </el-col>
                <el-col :span="24">
                    <div class="item-container">
                        <div class="item-title">
                            项目文件:
                        </div>
                        <div class="item-content">
                            <div class="file-list" v-if="fileList&&fileList.length">
                                <div class="file-item" v-for="item in fileList" :key="item.massId">
                                    <span :title="item.massFileName" class="file-title">
                                        {{ item.massFileName }}
                                    </span>
                                    <i @click="onPreviewFile(item.massId)" class="el-icon-view oper-icon"></i>
                                    <a :href="item.massFilePath">
                                        <i class="el-icon-download oper-icon"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetProjectInfo } from "@/api/project";
import { apiGetMassFileList, apiPreviewMassFile } from "@/api/file";
@Component({})
export default class extends Vue {
    private isDialogVisible = false;
    private fileList:any [] = [];
    private detailForm:any = {};
    private async showDialog(row) {
        const loading = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        })
        try {
            const data = await apiGetProjectInfo({ projectId: row.id })
            const fileData = await apiGetMassFileList({id: row.id, massFileType:"PROJECT_FILE"}, {current: 1, size: 99999});
            this.fileList = fileData?.data?.data?.records || [];
            const info = data?.data?.data || {}
            this.detailForm = { ...info };
            this.isDialogVisible = true;
        } finally {
            loading.close();
        }
    }
    private onPreviewFile(massId){
        apiPreviewMassFile({massId}).then(res=>{
            const previewPath = res.data.data?.previewPath;
            if(previewPath){
                window.open(previewPath);
            }
        })
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
.item-container{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    .item-title{
        flex: 80px 0 0;
        text-align: right;
        font-size: 14px;
        color: #1f2d3d;
        padding: 0 12px 0 0;
        box-sizing: border-box;
        font-weight: 700;
    }
    .item-content{
        flex: 1 0 0;
    }
}
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
            cursor: pointer;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
.flex-row.el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>
