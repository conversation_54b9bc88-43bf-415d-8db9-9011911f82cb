<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井下工具管理</div>
            </div>
            <el-select @change="onChangeWellNumber" v-model="wellNumber" clearable placeholder="请选择">
                <el-option v-for="item in wellList" :key="item.wellNumber" :label="item.wellNumber"
                           :value="item.wellNumber"></el-option>
            </el-select>
            
            <el-table :data="tableData" stripe style="margin-top:10px;">
                <el-table-column label="井号" prop="wellNumber"></el-table-column>
                <el-table-column label="库存ID" prop="stockId"></el-table-column>
                <el-table-column label="存货编码" prop="invCode"></el-table-column>
                <el-table-column label="存货名称" prop="invName"></el-table-column>
                <el-table-column label="零件编号" prop="partNumber"></el-table-column>
                <el-table-column label="序列号" prop="serialNumber"></el-table-column>
            </el-table>
        </div>
    </div>
</template>


<script lang="ts">

import { Component, Vue } from "vue-property-decorator";
import { apiGetDownholeToolList } from "@/api/underwell";
import { ConstantModule } from "@/store/modules/constant";

@Component({})
export default class extends Vue {
    private wellNumber: string = "";
    
    wellList:any[] = [];
    tableData = [];
    
    mounted() {
        this.getWellList();
        this.getUnderWellToolList();
    }
    
    /**
     * 获取井列表
     * */
    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }
    
    /**
     * 井下工具列表
     */
    getUnderWellToolList() {
        apiGetDownholeToolList({ wellNumber: this.wellNumber }).then((response) => {
            this.tableData = response.data.data;
        });
    }
    
    private onChangeWellNumber(){
        this.getUnderWellToolList();
    }
}
</script>
