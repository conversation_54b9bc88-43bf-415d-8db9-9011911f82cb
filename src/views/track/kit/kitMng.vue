<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">Kit箱管理</div>
            </div>
            <el-form :model="searchForm">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label="流向：">
                            <el-select
                                v-model="searchForm.kitFlow"
                                clearable
                                placeholder="请选择"
                                style="width:180px"
                            >
                                <el-option
                                    v-for="item in kitFlowMap"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                                <el-option label="闲置" :value="-1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="状态：">
                            <el-select
                                v-model="searchForm.status"
                                clearable
                                placeholder="请选择"
                                style="width:180px"
                            >
                                <el-option
                                    label="准备就绪"
                                    :value="7004"
                                ></el-option>
                                <el-option
                                    label="上井服务"
                                    :value="7002"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="9">
                        <el-button
                            icon="el-icon-search"
                            style="margin-left:30px"
                            type="primary"
                            @click="onSearch"
                        >
                            查 询
                        </el-button>
                        <el-button
                            icon="el-icon-plus"
                            style="margin-left:10px"
                            type="primary"
                            @click="onAddNewBox"
                        >
                            新增Kit箱
                        </el-button>
                    </el-col>
                </el-row>
            </el-form>
            <div class="simple-line" style="margin-top:0"></div>
            <el-table
                :data="kitBoxList"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    label="Kit箱"
                    prop="kitNumber"
                ></el-table-column>
                <el-table-column
                    label="kit箱流向"
                    prop="kitFlow"
                ></el-table-column>
                <el-table-column label="Status" prop="status">
                    <template slot-scope="scope">
                        {{ displayStatus(scope.row.status) }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                ></el-table-column>
                <el-table-column label="创建日期" prop="createTime">
                    <template slot-scope="scope">{{
                        new Date(scope.row.createTime).Format("yyyy-MM-dd")
                    }}</template>
                </el-table-column>
                <el-table-column label="操作" width="160">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            style="padding:6px 6px"
                            size="medium"
                            circle
                            icon="el-icon-document"
                            @click="onEdit(scope)"
                        >
                        </el-button>
                        <el-button
                            :disabled="scope.row.status == 7002"
                            type="danger"
                            circle
                            style="padding:6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                            @click="onDelete(scope)"
                        >
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            :close-on-click-modal="false"
            width="80%"
            title="kit箱"
            :visible.sync="showAssembleDialog"
        >
            <kit-assemble
                :kitBoxId="kitBoxId"
                @hideDialog="hideDialog"
                v-if="showAssembleDialog"
                :unableEdit="unableEdit"
            ></kit-assemble>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiDeleteKitBox, apiGetKitBoxList } from "@/api/kitBox";
import KitAssemble from "./kitAssembleDialog.vue";
import getDict from "@/utils/getDict";

interface IKitMng {
    kitBoxId?: number;
    kitNumber?: number;
    kitFlow?: number;
    status?: string;
    wellNumber?: string;
    assetOwner?: string;
    notes?: string;
    createTime?: string;
    updateTime?: string;
}

@Component({ components: { KitAssemble } })
export default class extends Vue {
    private searchForm: any = {};
    private kitFlowMap: any[] = [];
    private kitStatus = 7002;
    private kitFlowId?: number = 10;
    private showAssembleDialog = false;
    private kitBoxId?: number = -1;
    private kitBoxList: any[] = [];
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};

    get unableEdit() {
        return this.kitStatus == 7002;
    }

    mounted() {
        this.getKitBoxList();
        this.getKitFlowMap();
    }
    private async getKitFlowMap() {
        [this.kitFlowMap] = await getDict(7);
    }
    displayStatus(status: any) {
        let str = "";
        switch (status) {
            case 7002:
                str = "上井服务";
                break;
            case 7004:
                str = "准备就绪";
                break;
            default:
                str = "组装中";
        }
        return str;
    }
    onSearch() {
        this.getKitBoxList();
    }
    private getKitBoxList() {
        apiGetKitBoxList(this.searchForm).then((res) => {
            this.kitBoxList = res.data.data;
            for (let key = 0; key < this.kitBoxList.length; key++) {
                let item = this.kitBoxList[key];
                if (item.kitFlow == 6001) {
                    item.kitFlow = "发送现场";
                } else if (item.kitFlow == -1) {
                    item.kitFlow = "闲置";
                } else {
                    item.kitFlow = "租借";
                }
            }
        });
    }

    private onAddNewBox() {
        this.kitStatus = 7004;
        this.kitBoxId = -1;
        this.showAssembleDialog = true;
    }

    private onEdit(scope: any) {
        this.kitBoxId = scope.row.kitBoxId;
        this.kitStatus = scope.row.status;
        this.$nextTick(() => {
            this.showAssembleDialog = true;
        });
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteKitBox({ kitBoxId: scope.row.kitBoxId }).then(() => {
                    this.getKitBoxList();
                });
            })
            .catch((err) => {
                console.log(err);
            });
    }

    private hideDialog() {
        this.getKitBoxList();
        this.showAssembleDialog = false;
    }
}
</script>
