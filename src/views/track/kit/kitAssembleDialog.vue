<template>
    <div class="" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="custom-form">
                <span class="custom-form-head" style="width: 90px;"
                >kit箱编号：</span
                >
                <el-input
                    v-model="kitBoxNumber"
                    :disabled="kitBoxId > 0||unableEdit"
                    placeholder="填写Kit箱编号"
                    style="width:200px"
                ></el-input>
            </div>
            <div v-if="kitBoxId === -1" class="custom-form">
                <span class="custom-form-head" style="width: 90px;"
                >kit箱模板：</span
                >
                <el-select
                    :disabled="unableEdit"
                    v-model="kitTemplateId"
                    @change="onKitTemplateChange"
                >
                    <el-option
                        v-for="(item, key) in templateTypeList"
                        :key="`kitTemplateId` + key"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </div>
            <div class="custom-form">
                <span class="custom-form-head" style="width: 90px;">
                    kit箱流向：</span>
                <el-radio-group :disabled="unableEdit" v-model="kitFlowId" @change="onChangeFlowType">
                    <el-radio
                        v-for="(item, key) in flowTypeList"
                        :key="key"
                        :label="item.id"
                    >{{ item.name }}
                    </el-radio>
                </el-radio-group>
            </div>
            <div class="custom-form" v-if="showWellId">
                <span class="custom-form-head" style="width: 90px;"
                >井号：</span>
                <el-select :disabled="unableEdit" v-model="wellId" placeholder="选择井" clearable>
                    <el-option
                        v-for="(item, key) in wellList"
                        :key="`wellId` + key"
                        :label="item.wellNumber"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </div>
            <div class="custom-form">
                <span class="custom-form-head" style="width: 90px;"
                >描述信息：</span
                >
                <el-input :disabled="unableEdit" v-model="notes" style="width:200px"></el-input>
            </div>
            <div class="app-card-title">组装元素列表</div>
            <el-table
                :data="kitBoxItemList"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column label="编号" width="80">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column width="120">
                    <template slot="header">存货编码</template>
                    <template slot-scope="scope">
                        <span>{{ scope.row.invCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header"
                    >名称<span style="color:red;">*</span></template
                    >
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">{{
                                scope.row.invName
                            }}</span>
                        <el-autocomplete
                            v-else
                            v-model="scope.row.invName"
                            :fetch-suggestions="querySearchInvName"
                            placeholder="填写名称"
                            value-key="invName"
                            @select="onSelectInvName"
                        >
                            <template slot-scope="{ item }">
                                <div style="line-height:normal">
                                    {{ item.invName }}
                                </div>
                                <span style="color:#b4b4b4;font-size:12px;">
                                    {{ item.invCode || "无" }}
                                </span>
                            </template>
                        </el-autocomplete>   
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header"
                    >零件号<span style="color:red;"></span></template
                    >
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">{{
                                scope.row.partNumber
                            }}</span>
                        <el-select v-else @focus="onFocusPartNumber(scope)"  v-model="scope.row.partNumber">
                            <el-option
                                v-for="(item, key) in new Set(options.map(v=>v.partNumber).filter(v=>v))" 
                                :key="key" 
                                :label="item || '无'" 
                                :value="item">
                            </el-option>
                        </el-select>
                        <!-- <el-autocomplete
                            v-else
                            v-model="scope.row.partNumber"
                            :fetch-suggestions="querySearch"
                            :trigger-on-focus="false"
                            class="inline-input"
                            placeholder="填写零件编号"
                            value-key="partNumber"
                            @select="onSelectPartNumber"
                        ></el-autocomplete> -->
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header">序列号列表</template>
                    <template slot-scope="scope">
                        <div v-if="editObject.editRowIndex !== scope.$index">
                            <span>{{
                                    scope.row.serialNumberList.join(',')
                                }}</span>
                        </div>
                        <el-select
                            v-else
                            v-model="scope.row.stockIdList"
                            collapse-tags
                            multiple
                            placeholder="序列表"
                            style="width:100%"
                            @focus="onFocusSerialNumber(scope)">
                            <el-option
                                v-for="item in options"
                                :key="item.stockId"
                                :disabled="item.status!=7004"
                                :label="item.serialNumber || '无'"
                                :value="item.stockId">
                                <span>{{ item.serialNumber || '无' }}</span>
                                <span>{{ item.status==7004 ? '' : '(正在使用)' }}</span>
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <!-- <el-table-column width="80">
                    <template slot="header">需求数量</template>
                      <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.quantityRequired }}
                        </span>
                        <el-input
                            v-else
                            v-model="scope.row.quantityRequired"
                        ></el-input>
                    </template>
                </el-table-column> -->
                <el-table-column width="80">
                    <template slot="header">实际数量</template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.actualQuantity }}
                        </span>
                        <el-input
                            v-else
                            v-model="scope.row.actualQuantity"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header">类型</template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.subType }}
                        </span>
                        <el-input v-else v-model="scope.row.subType"></el-input>
                    </template>
                </el-table-column>
                <el-table-column v-if="!unableEdit" label="操作">
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            <el-button type="text" @click="onEdit(scope)">
                                编辑
                            </el-button>
                            <el-button type="text" @click="onDelete(scope)">
                                删除
                            </el-button>
                        </span>
                        <span v-else>
                            <el-button type="text" @click="onSave(scope)">
                                保存
                            </el-button>
                            <el-button type="text" @click="onCancel(scope)">
                                取消
                            </el-button>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-button
                v-if="!unableEdit"
                :disabled="this.kitBoxId === -1"
                style="float:right;margin-top:20px;margin-right: 20px;"
                type="warning"
                @click="onConfirmStatus"
                round
            >确认上井
            </el-button>
            <el-button
                v-if="!unableEdit"
                :disabled="status === 1"
                style="float:right;margin-top:20px;margin-right: 20px;"
                type="primary"
                @click="onSaveBox"
            >保存Kit箱
            </el-button>
            <el-button
                v-if="!unableEdit"
                :disabled="status === 1"
                style="float:right;margin-top:20px;margin-right: 20px;"
                type="primary"
                @click="onAdd"
            >添加元素
            </el-button>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiGetDictList } from "@/api/dict";
import { apiConfirmStatus, apiGetKitBoxInfo, apiGetKitBoxList, apiKitBoxPackage } from "@/api/kitBox";
import { apiGetKitTemplateList } from "@/api/kit";
import { apiGetDeviceFuzzyList, apiGetInvNameFuzzyList } from "@/api/warehouse";
import { IBusinessType } from "@/utils/constant";
import { ConstantModule } from "@/store/modules/constant";

interface IStructure {
    label: string;
    prop: string;
    type: string;
    data?: { label: string; value: string }[];
}

interface IKitBoxData {
    invCode?: number;
    invName?: string;
    partNumber?: string;
    quantityRequired?: number;
    actualQuantity?: number;
    subType?: string;
    itemSpecificList?: [{ stockId?: number; serialNumber?: string }];
    serialNumberList?: string[];
    stockIdList?: number[];
}

@Component({})
export default class extends Vue {
    private notes = "";
    private editObject = { editRow: false, editRowIndex: -1 };
    private kitFlowId?: number = 6001;
    // private kitBoxId?: number = -1;
    private flowTypeList: IBusinessType[] = [];
    private kitBoxList: any[] = [];
    private kitBoxNumber: string = "";
    private wellList: any[] = [];
    private kitTemplateId?: string = "";
    private templateTypeList: IBusinessType[] = [];
    private templateList: any[] = [];
    private wellNumber = "";
    private status: number = 0;
    private wellId: number | null = null;
    private kitBoxItemList: IKitBoxData[] = [];
    private preObject: any = {};
    private Structure: IStructure[] = [];
    private clickIndex = [-1, -1];
    // private options: any[] = [];
    private allOptions: any[] = [];
    private optionsOne: any[] = [];
    private serialNumberList = [];
    private showWellId = true;
    @Prop({ default: -1 }) private kitBoxId!: -1;
    @Prop({ default: true }) private unableEdit!: true;
    
    created(): void {
        // this.kitBoxId = Number(this.$route.query.id);
    }
    get options(){
        return this.allOptions.filter(item=>item.status==7004)
    }
    async mounted() {
        apiGetDictList({ businessType: 7 }).then((res) => {
            this.flowTypeList = res.data.data;
            
            // this.kitFlowId = this.flowTypeList[0].id;
        });
        this.wellList = await ConstantModule.getWellList()
        
        apiGetDictList({ businessType: 6 }).then((res) => {
            this.templateTypeList = res.data.data;
            // this.kitTemplateId = this.templateTypeList[0].id;
        });
        if (this.kitBoxId == -1) return;
        this.$nextTick(() => {
            apiGetKitBoxInfo({ kitBoxId: this.kitBoxId }).then((res) => {
                const kitBoxInfo = res.data.data.kitBoxInfo;
                this.kitFlowId = kitBoxInfo.kitFlow;
                this.showWellId = this.kitFlowId == 6001 ? true : false;
                this.kitBoxNumber = kitBoxInfo.kitNumber;
                this.wellId = kitBoxInfo.wellId;
                this.notes = kitBoxInfo.notes;
                this.kitBoxItemList = res.data.data.kitBoxItemList.map(
                    (item: any) => {
                        item.stockIdList = item.itemSpecificList.map(
                            (t: any) => t.stockId,
                        );
                        item.serialNumberList = item.itemSpecificList.map(
                            (t: any) => t.serialNumber,
                        );
                        return item;
                    },
                );
            });
        });
    }
    
    onChangeFlowType() {
        //发现场this.kitFlowId=10
        this.showWellId = this.kitFlowId == 6001 ? true : false;
    }
    
    onKitTemplateChange() {
        apiGetKitTemplateList({ templateType: this.kitTemplateId }).then(
            (res) => {
                const templateList = res.data.data[0].kitTemplateItemList;
                this.kitBoxItemList = templateList.map((item: any) => {
                    let tmp = this.getKitBoxItem();
                    tmp.invCode = item.invCode;
                    tmp.invName = item.invName;
                    tmp.partNumber = item.partNumber;
                    tmp.stockIdList = [];
                    tmp.serialNumberList = [];
                    return tmp;
                });
            },
        );
    }
    
    getKitBoxItem(): any {
        return {
            actualQuantity: undefined,
            invCode: "",
            invName: "",
            itemSpecificList: [],
            partNumber: "",
            quantityRequired: undefined,
            subType: "",
        };
    }
    
    onSelectPartNumber(item: any) {
        this.kitBoxItemList[this.editObject.editRowIndex].invCode =
            item.invCode;
        this.kitBoxItemList[this.editObject.editRowIndex].invName =
            item.invName;
        this.kitBoxItemList[this.editObject.editRowIndex].serialNumberList = [];
        this.kitBoxItemList[this.editObject.editRowIndex].stockIdList = [];
    }
    
    onSelectInvName(item: any) {
        this.kitBoxItemList[this.editObject.editRowIndex].partNumber =
            item.partNumber;
        this.kitBoxItemList[this.editObject.editRowIndex].invCode =
            item.invCode;
        this.kitBoxItemList[this.editObject.editRowIndex].stockIdList =
            [];
        this.kitBoxItemList[this.editObject.editRowIndex].serialNumberList =
            [];
        this.kitBoxItemList[this.editObject.editRowIndex].actualQuantity =
            0;
    }
    
    onFocusPartNumber(scope: any) {
        if (scope.row.invCode) {
            apiGetDeviceFuzzyList({
                invCode: scope.row.invCode,
                status: 7004,
                size: 30,
            }).then((res) => {
                let list = res.data.data.currentStockList || [];
                this.allOptions = list;
                // this.kitTemplateItemList[index].invName = list[0] ? list[0].invName : ''
            });
        }
    }
    
    onFocusSerialNumber(scope: any) {
        if (scope.row.invCode) {
            apiGetDeviceFuzzyList({
                invCode: scope.row.invCode,
                partNumber: scope.row.partNumber || '',
                status: 7004,
                size: 30,
            }).then((res) => {
                let list = res.data.data.currentStockList || [];
                this.allOptions = list;
                // this.kitTemplateItemList[index].invName = list[0] ? list[0].invName : ''
            });
        }
    }
    
    onSaveBox() {
        if (this.kitBoxItemList.length == 0) {
            this.$message.error("请添加清单");
            return;
        }
        let params = {
            kitBoxInfo: {
                kitNumber: this.kitBoxNumber,
                kitFlow: this.kitFlowId,
                wellId: this.wellId,
                assetOwner: this.assetOwner,
                status: this.status,
                notes: this.notes,
            },
            kitBoxItemList: this.kitBoxItemList,
        };
        apiKitBoxPackage(params)
            .then((res) => {
                let { code, message, data } = res.data;
                if (code == 200) {
                    this.$message.success("保存成功");
                    this.$emit("hideDialog");
                }
            })
            .catch((err) => {
            });
    }
    
    createFilter(queryString: string) {
        return (kitBox: any) => {
            return (
                kitBox.kitNumber
                    .toLowerCase()
                    .indexOf(queryString.toLowerCase()) === 0
            );
        };
    }
    
    private getKitBoxList() {
        apiGetKitBoxList({}).then((res) => {
            this.kitBoxList = res.data.data;
        });
    }
    
    private onSave(scope: any) {
        if (!scope.row.invCode) {
            this.$message.error("没有存货编码的仪器无法组装进Kit箱！");
            return;
        }
        if (scope.row.invName === "" || scope.row.invName == undefined) {
            this.$message.error("请填写名称");
            return;
        }
        let err = false;
        for (let i = 0; i < this.kitBoxItemList.length; i++) {
            if (i == scope.$index) continue;
            if (this.kitBoxItemList[i].invCode == scope.row.invCode  && scope.row.invCode!="" && scope.row.invCode!=null) {
                err = true;
                
                break;
            }
        }
        if (err) {
            this.$message.error("有重复项");
            return;
        }
        let stockIdList = scope.row.stockIdList || [];
        let serialNumberList = stockIdList.map((v:any)=>{
            let targetItem = this.allOptions.find(item=>item.stockId==v);
            return targetItem.serialNumber
        });
        // if(!stockIdList.length){
        //     let len = Number(scope.row.actualQuantity || 0);
        //     if(len&&!serialNumberList.length){
        //         this.options.forEach((v, k)=>{
        //             if(k < len&&v.serialNumber&&v.serialNumber!='无'){
        //                 scope.row.serialNumberList.push(v.serialNumber)
        //             }
        //         })
        //     }else{
        //         this.$message.error('请选择序列号或填写实发数量！')
        //         return;
        //     }
        // }
        if(!scope.row.actualQuantity){
            this.$message.error('请填写实发数量！')
            return;
        }
        this.$set(scope.row, "serialNumberList", serialNumberList);
        // 有序列号才给stockId
        let filterStockIdList:any[] = stockIdList.filter((item:any)=>{
           let targetItem =  this.allOptions.find((op: any) => op.stockId == item);
           let serialNumber = targetItem.serialNumber;
           return serialNumber&&serialNumber!='无'
        });
        this.$set(scope.row, "stockIdList", filterStockIdList);
        this.editObject.editRowIndex = -1;
    }
    
    private querySearchKitBoxNumber(queryString: string, callback: any) {
        var kitBoxList = this.kitBoxList;
        var results = queryString
            ? kitBoxList.filter(this.createFilter(queryString))
            : kitBoxList;
        // 调用 callback 返回建议列表的数据
        callback(results);
    }
    
    private querySearch(queryString: string, callback: any) {
        apiGetDeviceFuzzyList({
            partNumber: queryString,
            status: 7004,
            size: 30,
        }).then((res) => {
            this.allOptions = res.data.data.currentStockList;
            callback(this.allOptions);
        });
    }
    
    private querySearchInvName(queryString: string, callback: any) {
        apiGetInvNameFuzzyList({ invName: queryString, size: 30 }).then(
            (res) => {
                this.optionsOne = res.data.data||[];
                callback(this.optionsOne);
            },
        );
    }
    
    private onConfirmStatus() {
        apiConfirmStatus({ kitBoxId: this.kitBoxId, status: 7002 }).then(() => {
            this.getKitBoxList();
            this.status = 1;
            this.$message.success("确认上井");
            this.$emit("hideDialog");
        }).catch((err) => {
            console.log(err);
        });
    }
    
    private onInitAssemble() {
        apiGetKitBoxInfo({ kitBoxId: this.kitBoxId })
            .then((res) => {
                let { data, code, message } = res.data;
                // if (data.length > 0)
                this.kitBoxItemList = data.kitBoxItemList.map((item: any) => {
                    item.stockIdList = item.itemSpecificList.map(
                        (t: any) => t.stockId,
                    );
                    item.serialNumberList = item.itemSpecificList.map(
                        (t: any) => t.serialNumber,
                    );
                    return item;
                });
            })
            .catch((err) => {
                console.log(err);
            });
        apiGetKitBoxList({}).then((res) => {
            this.kitBoxList = res.data.data;
            this.kitBoxNumber = this.kitBoxList[0].kitNumber;
            this.kitBoxId = this.kitBoxList[0].kitBoxId;
        });
    }
    
    private onAdd() {
        
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.kitBoxItemList.push({
            invCode: undefined,
            invName: "",
            partNumber: "",
            quantityRequired: undefined,
            actualQuantity: undefined,
            serialNumberList:[],
            stockIdList:[],
            subType: "",
        });
        this.editObject.editRow = true;
        this.editObject.editRowIndex = this.kitBoxItemList.length - 1;
    }
    
    private onEdit(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        if(scope.row.invCode){
            apiGetDeviceFuzzyList({
                invCode: scope.row.invCode,
                size: 30,
            }).then((res) => {
                let list = res.data.data.currentStockList || [];
                this.allOptions = list;
            });
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }
    
    private onDelete(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.kitBoxItemList.splice(scope.$index, 1);
    }
    
    private onCancel(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.kitBoxItemList[scope.$index] = { ...this.preObject };
        } else {
            this.kitBoxItemList.pop();
        }
        
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss" scoped>
.editable {
    .add-button {
        width: 100%;
        margin-bottom: 20px;
        height: 30px !important;
        font-size: 16px;
    }
    
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
        
        &.add {
            bottom: 5px;
        }
        
        &.delete {
            top: 5px;
        }
        
        &:hover {
        }
    }
    
    .el-table__row:hover .hover-icon {
        display: block;
    }
}
</style>
