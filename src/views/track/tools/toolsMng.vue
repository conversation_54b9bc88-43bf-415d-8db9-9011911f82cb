<template>
    <div class="app-container">
        <div class="tools-container">
            <div class="top">
                <el-form ref="searchForm" label-width="100px">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item label="工具类：" prop="invClassCode">
                                <el-select v-model="searchForm.invClassCode" clearable placeholder="请选择"
                                           style="width:180px">
                                    <el-option v-for="item in deviceTypes" :key="item.invClassCode"
                                               :label="item.invClassName" :value="item.invClassCode"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="零件号：" prop="partNumber">
                                <el-input
                                    v-model.trim="searchForm.partNumber"
                                    clearable
                                    placeholder="请输入"
                                    style="width:180px"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="序列号：" prop="serialNumber">
                                <el-input
                                    v-model.trim="searchForm.serialNumber"
                                    clearable
                                    placeholder="请输入"
                                    style="width:180px"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-button style="margin-left:30px"  type="primary"  @click="onSearch">
                                查 询
                            </el-button>
                             <el-button style="margin-left:30px"  type="primary"  @click="onGetNewData">
                               同步ERP库存
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                         <el-col :span="5">
                            <el-form-item label="状态：" prop="status">
                                <el-select
                                    v-model="searchForm.status"
                                    clearable
                                    placeholder="请选择"
                                    style="width:180px"
                                >
                                    <el-option
                                        v-for="item in deviceStatusList"
                                        :key="item.name"
                                        :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="工具名称：" prop="invName">
                                <el-input
                                    v-model.trim="searchForm.invName"
                                    clearable
                                    placeholder="请输入"
                                    style="width:180px"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item label="Kit箱：" prop="kitNumber">
                                <el-input
                                    v-model.trim="searchForm.kitNumber"
                                    clearable
                                    placeholder="请输入"
                                    style="width:180px"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="simple-line" style="margin-top:0"></div>
            <el-table :data="filterTableData" :header-cell-style="commmonTableHeaderCellStyle" stripe style="margin-top:16px">
                <el-table-column label="存货编码" prop="invCode"></el-table-column>
                <el-table-column label="存货名称" prop="invName"></el-table-column>
                <el-table-column label="规格x型号" prop="invStd"></el-table-column>
                <el-table-column label="设备序列号" prop="serialNumber"></el-table-column>
                <el-table-column label="所属分类" prop="invClassName"></el-table-column>
                <el-table-column label="零件号" prop="partNumber"></el-table-column>
                <el-table-column label="状态" prop="status">
                    <template slot-scope="scope">
                        {{ getStatusName(scope.row.status) }}
                    </template>
                </el-table-column>
                <el-table-column label="kit箱编号" prop="kitNumber"></el-table-column>
                <!-- <el-table-column prop="field9" label="从属"></el-table-column> -->
                <el-table-column label="资产所属" prop="assetOwner"></el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            icon="el-icon-edit"
                            round
                            size="medium" style="padding:6px 6px" type="success"
                            @click="onEditInventory(scope.row)"
                        ></el-button
                        >
                        <el-button
                            icon="el-icon-info"
                            round
                            size="medium" style="padding:6px 6px" type="primary"
                            @click="onInventoryHistory(scope.row)"
                        >
                        </el-button>
                        <!-- <el-button
                            type="danger"
                            style="padding:6px 6px" round
                            size="medium"
                            icon="el-icon-delete"
                            ></el-button> -->
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog
            :title="dialogTitle" :close-on-click-modal="false"
            :visible.sync="showDialog"
            width="1000px"
        >
            <el-form
                ref="editForm"
                :rules="formRules"
                label-width="120px"
            >
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="存货编码：" prop="invCode">
                            <el-input v-model="editForm.invCode" disabled style="width:280px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="存货名称：" prop="invName">
                            <el-input v-model="editForm.invName" disabled style="width:280px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号：" prop="invStd">
                            <el-input v-model="editForm.invStd" disabled style="width:280px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备序列号：" prop="serialNumber">
                            <el-input v-model="editForm.serialNumber" disabled style="width:280px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="存货大类：" prop="invClassName">
                            <el-input v-model="editForm.invClassName" disabled style="width:280px"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态：" prop="status">
                            <el-select v-model="editForm.status" placeholder="请选择" style="width:180px">
                                <el-option v-for="item in deviceStatusList" :key="item.name" :label="item.name"
                                           :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="kit箱编号：" prop="kitNumber">
                            <el-input
                                v-model="editForm.kitNumber"
                                style="width:280px"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item prop="fromInventory" label="从属：">
                            <el-input
                                style="width:280px"
                                v-model="editForm.fromInventory"
                            ></el-input>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                        <el-form-item label="资产所属：" prop="assetOwner">
                            <el-input
                                v-model="editForm.assetOwner"
                                style="width:280px"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="onFormCancel">取消</el-button>
                <el-button type="primary" @click="onFormConfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script lang="ts">
    import { Component } from "vue-property-decorator";
    import Vue from "vue";
    import { apiGetDictList } from "@/api/dict";
    import { apiGetDeviceList, apiGetDeviceTypes, apiUpdateDevice } from "@/api/warehouse";
    import {apiSyncData} from '@/api/erp';
    @Component({})
    export default class extends Vue {
        private total = 1;
        private currentPage = 1;
        pager = {
            total: 0, size: 20, current: 1,
        };
        private filterTableData = [];
        private editForm = {
            stockId: -1, invCode: "", invName: "", invStd: "", serialNumber: "", invClassCode: "", invClassName: "",
            kitNumber: "", assetOwner: "",
        };
        private formRules = {};
        private dialogType = "ADD";
        private showDialog: boolean = false;
        private searchForm = {
            invName: "",
            partNumber: "",
            serialNumber: "",
            invClassCode: "",
            status: "",
            kitNumber: "",
        };
        private deviceTypes = [];
        private deviceStatusList = [];
        private loading: boolean = false;
        
        get dialogTitle(): string {
            return this.dialogType === "ADD" ? "新增" : "编辑";
        }
        
        created() {
            this.getDeviceTypes();
            this.getDeviceStatusList();
            this.getDeviceList();
        }
        
        getDeviceTypes() {
            apiGetDeviceTypes({}).then(res => {
                let { data, message, code } = res.data;
                this.deviceTypes = data;
            }).catch(err => {
            });
        }
        
        getDeviceStatusList() {
            apiGetDictList({ businessType: 8 }).then((res) => {
                let { data, message, code } = res.data;
                this.deviceStatusList = data;
            });
        }
        
        onSearch() {
            this.currentPage = 1;
            this.getDeviceList();
        }
        
        deviceTypeFormatter(val: any) {
            if (val == "KITBOXES") {
                return "Kit Boxes";
            } else if (val == "ATBITSUBS") {
                return "ATBITSUBS";
            } else if (val == "PULSER") {
                return "Pulser";
            } else if (val == "DM") {
                return "DM";
            } else if (val == "RECEIVER") {
                return "Receiver";
            } else if (val == "GAMMANAT") {
                return "Gamma (Natural)";
            } else if (val == "GAMMAAZI") {
                return "Gamma (Azi)";
            } else if (val == "BATTERY") {
                return "Battery Module";
            } else if (val == "GAPSUB") {
                return "Gap Sub";
            } else if (val == "NMDP") {
                return "NMDP";
            } else if (val == "RFD") {
                return "RFD";
            } else if (val == "TRANSDUCER") {
                return "Transducer Assemblies";
            } else if (val == "PONYSUB") {
                return "PONY SUB";
            } else if (val == "FLOWSIMU") {
                return "Flow Simulator";
            } else if (val == "DEPTRACK") {
                return "Depth Tracking";
            }
            return "";
        }
        
        typeTag(tag: any) {
            return tag == "READY"
                ? ""
                : tag == "ATRIG"
                    ? "warning"
                    : tag == "REPAIR"
                        ? "success"
                        : "info";
        }
        
        colorTag(tag: any) {
            return tag == "READY"
                ? "#009d00"
                : tag == "ATRIG"
                    ? "#1867e5"
                    : tag == "REPAIR"
                        ? "#e58505"
                        : "#606266";
        }
        
        nameTag(tag: any) {
            // console.log('tag: ', tag);
            return tag == "READY"
                ? "待命"
                : tag == "ATRIG"
                    ? "上井中"
                    : tag == "REPAIR"
                        ? "维护"
                        : "废弃";
        }
        
        
        onFormCancel() {
            this.showDialog = false;
        }
        
        onFormConfirm() {
            
            let postData = {
                stockId: this.editForm.stockId,
                status: this.editForm.status,
                kitNumber: this.editForm.kitNumber,
                assetOwner: this.editForm.assetOwner,
            };
            apiUpdateDevice(postData).then((res) => {
                let { message, code, data } = res.data;
                if (code == 200) {
                    this.getDeviceList();
                    this.showDialog = false;
                } else {
                    this.showDialog = false;
                }
            }).catch((err) => {
            });
            
        }
        
        getStatusName(status: any) {
            let str = "";
            for (let i = 0; i < this.deviceStatusList.length; i++) {
                if (status == this.deviceStatusList[i].id) {
                    str = this.deviceStatusList[i].name;
                    break;
                }
            }
            return str;
        }
        
        onEditInventory(row: any) {
            this.dialogType = "EDIT";
            this.showDialog = true;
            Object.assign(this.editForm, row);
            // this.editForm = row;
            
        }
        
        onInventoryHistory(row: any) {
            this.$router.push({ path: "/track/toolshistory", query: { invCode: row.invCode } });
        }
        onGetNewData(){
            let form=new FormData();
            form.append("task", "ERP_CURRENTSTOCK_SYNC_TASK");
            apiSyncData(form).then(res=>{
                let {data,message,code}=res;
                if(code==200){
                    this.$message.success("同步成功");
                    this.getDeviceList();
                }
            }).catch(err=>{})
        }
        getDeviceList() {
            let params:any = { current: this.currentPage, size: 10 };
            if (this.searchForm.invName != "")
                params.invName = this.searchForm.invName;
            if (this.searchForm.partNumber != "")
                params.partNumber = this.searchForm.partNumber;
            if (this.searchForm.serialNumber != "")
                params.serialNumber = this.searchForm.serialNumber;
            if (this.searchForm.invClassCode != "")
                params.invClassCode = this.searchForm.invClassCode;
            if (this.searchForm.status != "")
                params.status = this.searchForm.status;
            if (this.searchForm.kitNumber != "")
                params.kitNumber = this.searchForm.kitNumber;
            
            
            apiGetDeviceList(params).then(res => {
                let { code, message, data } = res.data;
                this.filterTableData = data.currentStockList;
                this.total = data.total;
                this.currentPage = data.current;
            }).catch(err => {
            });
        }
        private onCurrentChange(currentPage: number) {
            this.currentPage = currentPage;
            this.getDeviceList();
        }
    }
</script>
<style>
    .warehouse-table {
    }
</style>
<style scoped>
    .tools-container {
        padding: 20px 30px;
        background: #ffffff;
        height: 100%;
    }

    .el-tag--dark.is-hit {
        border-color: #ffffff;
    }
</style>
