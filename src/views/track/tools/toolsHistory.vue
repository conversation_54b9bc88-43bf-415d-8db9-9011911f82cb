<template>
    <div class="app-container">
        <div class="tools-container">
            <div class="top">
                <el-form ref="searchForm" label-width="100px">
                    <el-row>
                        <el-col :span="5">
                            <el-form-item prop="invCode" label="库存编码：">
                                <el-input v-model.trim="searchForm.invCode" clearable placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item prop="invName" label="工具名称：">
                                <el-input v-model.trim="searchForm.invName" clearable placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item prop="partNum" label="零件号：">
                                <el-input v-model.trim="searchForm.partNumber" clearable placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="5">
                            <el-form-item prop="workOrder" label="业务单号：">
                                <el-input v-model.trim="searchForm.workOrder" clearable placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-button @click="onSearch" style="margin-left:30px" type="primary"> 查 询</el-button>
                            <!-- <el-button  @click="onAddNew"  style="margin-left:30px" type="primary"> 新 增</el-button> -->
                        </el-col>
                    </el-row>
                </el-form>
            
            </div>
            <div class="simple-line" style="margin-top:0"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :data="tableData">
                <el-table-column prop="date" label="日期"></el-table-column>
                <el-table-column prop="invName" label="工具名称"></el-table-column>
                <el-table-column prop="invCode" label="存货编码"></el-table-column>
                <el-table-column prop="partNumber" label="零件号"></el-table-column>
                <el-table-column prop="serialNumber" label="序列号"></el-table-column>
                <el-table-column prop="movingTypeFromStr" label="流动类型From"></el-table-column>
                <el-table-column prop="movingTypeToStr" label="流动类型To"></el-table-column>
                <el-table-column prop="fromLocation" label="FromLocation"></el-table-column>
                <el-table-column prop="toLocation" label="ToLocation"></el-table-column>
                <el-table-column prop="workOrder" label="业务单号"></el-table-column>
                <el-table-column prop="note" label="其他"></el-table-column>

            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog :title="dialogTitle" :close-on-click-modal="false" width="1000px" :visible.sync="showDialog">
            <el-form ref="editForm" :rules="editFormRules" label-width="120px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="invCode" label="存货编码：">
                            <el-input
                                style="width:280px" disabled
                                v-model="editForm.invCode" required
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="invName" label="存货名称：">
                            <el-input
                                style="width:280px" disabled
                                v-model="editForm.invName"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                
                    <el-col :span="12">
                        <el-form-item
                            prop="fromLocation"
                            label="FromLocation："
                        >
                            <el-input
                                style="width:280px"
                                v-model="editForm.fromLocation"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="toLocation" label="ToLocation：">
                            <el-input
                                style="width:280px"
                                :min="0"
                                v-model="editForm.toLocation"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="12">
                        <el-form-item prop="workOrder" label="业务单号：">
                            <el-input
                                style="width:280px"
                                v-model="editForm.workOrder"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="note" label="备注：">
                            <el-input
                                style="width:280px"
                                v-model="editForm.note"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="onFormCancel">取消</el-button>
                <el-button type="primary" @click="onFormConfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { Form as ElForm } from "element-ui";
import { apiGetDictList } from "@/api/dict";
import { apiGetDeviceInfo } from "@/api/warehouse";
import {
    apiAddDeviceHistory,
    apiGetDeviceHistoryList,
    apiGetDeviceHistoryListBy,
    apiUpdateDeviceHistory,
} from "@/api/tools";

@Component({})
export default class extends Vue {
    private total = 1;
    private currentPage = 1;
    pager = {
        total: 0, size: 20, current: 1,
    };
    
    searchForm = {
      invCode:'',  invName: "", partNumber: "", serialNumber: "", workOrder: "", movingTypeFrom: "", movingTypeTo: "",
    };
    private tableData = [];
    private dialogType = "ADD";
    private showDialog: boolean = false;
    private editForm = {
        id: -1,
        deviceId: -1, fromLocation: "", toLocation: "",
        invName: "", workOrder: "", note: "", invCode: "",
    };
    private movingTypeList = [];
    private editFormRules = {
        invCode: [
            { required: true, message: "不能为空", trigger: "blur" },
        ],
    };
    private loading: boolean = false;
    
    created() {
        this.searchForm.invCode = this.$route.query.invCode;
        //this.getDeviceHistory();
        this.getDeviceHistoryBy();
        this.getDictList();
    }
    
    // mounted() {
    //     this.$nextTick(() => {
    //         this.getDeviceHistory();
    //         this.getDictList();
    //     });
    // }
    
    getDictList() {
        apiGetDictList({ businessType: 7 }).then((res) => {
            this.movingTypeList = res.data.data;
        });
    }
    
    getDeviceHistory() {
        let params: any = { current: this.currentPage, size: 10 };
        apiGetDeviceHistoryList(params).then((res) => {
            let { code, message, data } = res.data;
            this.tableData = data.records;
            this.total = data.total;
        }).catch(err => {
            console.log(err);
        });
    }
    
    getDeviceHistoryBy() {
        let params: any = { current: this.currentPage, size: 10 };
        let detail: any ={};
        if(this.searchForm.invCode!=""){
            detail.invCode = this.searchForm.invCode;
        }
        if (this.searchForm.invName != "")
            detail.invName = this.searchForm.invName;
        if (this.searchForm.partNumber != "")
            detail.partNumber = this.searchForm.partNumber;
        if (this.searchForm.serialNumber != "")
            detail.serialNumber = this.searchForm.serialNumber;
        if (this.searchForm.workOrder != "")
            detail.workOrder = this.searchForm.workOrder;
        if (this.searchForm.movingTypeFrom != "")
            detail.movingTypeFrom = this.searchForm.movingTypeFrom;
        if (this.searchForm.movingTypeTo != "")
            detail.movingTypeTo = this.searchForm.movingTypeTo;
        apiGetDeviceHistoryListBy(params,detail).then((res) => {
            let { code, message, data } = res.data;
            this.tableData = data.records;
            this.total = data.total;
        }).catch(err => {
            console.log(err);
        });
    }
    
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getDeviceHistory();
    }
    
    onSearch() {
        this.currentPage = 1;
        this.getDeviceHistoryBy();
    }
    
    onAddNew() {
    }
    
    showSubTableDataHandler(scope: any) {
        let id = scope.row.id;
        this.showSubDialog = true;
        apiGetDeviceInfo({ deviceId: id })
            .then((res) => {
                this.currSubData = res.data.data;
                this.boxDeviceIdList = [];
                let list = res.data.data.boxDeviceList;
                list.forEach((item: any) => {
                    this.boxDeviceIdList.push(item.id);
                });
            })
            .catch((err) => {
            });
    }
    
    onEditHistory(row: any) {
        this.dialogType = "EDIT";
        this.showDialog = true;
        Object.assign(this.editForm, row);
        this.editForm.movingTypeId = parseInt(this.editForm.movingTypeId);
        // this.$nextTick(() => {
        //     (this.$refs.form as ElForm).clearValidate();
        // });
    }
    
    get dialogTitle(): string {
        return this.dialogType === "ADD" ? "新增" : "编辑";
    }
    
    onAddDataSub() {
        this.showAddSubData = true;
    }
    
    deviceTypeFormatter(val: any) {
        if (val == "KITBOXES") {
            return "Kit Boxes";
        } else if (val == "ATBITSUBS") {
            return "ATBITSUBS";
        } else if (val == "PULSER") {
            return "Pulser";
        } else if (val == "DM") {
            return "DM";
        } else if (val == "RECEIVER") {
            return "Receiver";
        } else if (val == "GAMMANAT") {
            return "Gamma (Natural)";
        } else if (val == "GAMMAAZI") {
            return "Gamma (Azi)";
        } else if (val == "BATTERY") {
            return "Battery Module";
        } else if (val == "GAPSUB") {
            return "Gap Sub";
        } else if (val == "NMDP") {
            return "NMDP";
        } else if (val == "RFD") {
            return "RFD";
        } else if (val == "TRANSDUCER") {
            return "Transducer Assemblies";
        } else if (val == "PONYSUB") {
            return "PONY SUB";
        } else if (val == "FLOWSIMU") {
            return "Flow Simulator";
        } else if (val == "DEPTRACK") {
            return "Depth Tracking";
        }
        return "";
    }
    
    typeTag(tag: any) {
        return tag == "READY"
            ? ""
            : tag == "ATRIG"
                ? "warning"
                : tag == "REPAIR"
                    ? "success"
                    : "info";
    }
    
    colorTag(tag: any) {
        return tag == "READY"
            ? "#009d00"
            : tag == "ATRIG"
                ? "#1867e5"
                : tag == "REPAIR"
                    ? "#e58505"
                    : "#606266";
    }
    
    nameTag(tag: any) {
        // console.log('tag: ', tag);
        return tag == "READY"
            ? "待命"
            : tag == "ATRIG"
                ? "上井中"
                : tag == "REPAIR"
                    ? "维护"
                    : "废弃";
    }
    
    onAddData() {
        this.dialogType = "ADD";
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.editForm as ElForm).clearValidate();
        });
    }
    
    onFormCancel() {
        this.showDialog = false;
    }
    
    onFormConfirm() {
        let postData = Object.assign({}, this.editForm);
        // this.$refs["editForm"].validate((valid) => {
        //     alert(valid)
        if (this.dialogType === "ADD") {
            apiAddDeviceHistory(postData).then((res) => {
                this.showDialog = false;
                this.getDeviceHistory();
            }).catch((err) => {
            });
            return;
        }
        apiUpdateDeviceHistory(postData).then((res) => {
            this.showDialog = false;
            this.getDeviceHistory();
        }).catch((err) => {
        });
        
        // });
    }
    
    onDeleteSub(scope: any) {
        let id = scope.row.id;
        let checkIdx = this.boxDeviceIdList.findIndex(
            (item: any) => item === id,
        );
        if (checkIdx >= 0) {
            this.boxDeviceIdList.splice(checkIdx, 1);
        }
        this.addSubDataLoading = true;
        let postData = {
            id: this.currSubData.id,
            deviceType: this.currSubData.deviceType,
            boxDeviceIdList: this.boxDeviceIdList,
        };
        console.log("postData: ", postData);
        apiUpdateDeviceHistory(postData)
            .then((res) => {
                apiGetDeviceInfo({ deviceId: this.currSubData.id })
                    .then((res) => {
                        this.currSubData = res.data.data;
                        this.boxDeviceIdList = [];
                        let list = res.data.data.boxDeviceList;
                        list.forEach((item: any) => {
                            this.boxDeviceIdList.push(item.id);
                        });
                    }).catch((err) => {
                    console.log(err);
                });
                this.addSubDataLoading = false;
            })
            .catch((err) => {
                this.addSubDataLoading = false;
            });
    }
    
    getDeviceList() {
        this.loading = true;
        apiGetDeviceHistoryList({ current: this.pager.current, size: this.pager.size }).then((res) => {
            this.tableData = res.data.data;
            this.tableData.forEach((item: any) => {
                // console.log("item: ",item)
                let type: any = item.deviceType;
                if (type !== "KITBOXES") {
                    this.boxDeviceList.push(item);
                }
            });
            console.log("this.boxDeviceList: ", this.boxDeviceList);
            this.filterTableData = res.data.data;
            this.loading = false;
        })
            .catch((err) => {
                this.loading = false;
            });
    }
}
</script>
<style>
.warehouse-table {
}
</style>
<style scoped>
.tools-container {
    padding: 20px 30px;
    background: #ffffff;
    height: 100%;
}

.el-tag--dark.is-hit {
    border-color: #ffffff;
}
</style>
