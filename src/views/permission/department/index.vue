<template>
    <div class="app-container">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">部门管理</div>
            </div>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :data="departmentList"
            >
                <el-table-column prop="id" label="ID"></el-table-column>
                <el-table-column prop="name" label="部门名称"></el-table-column>
                <el-table-column
                    prop="numOfEmployees"
                    label="部门人数"
                ></el-table-column>
                <el-table-column
                    prop="leader"
                    label="部门负责人"
                ></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetDeptList } from "@/api/department";
@Component({
    name: "PermissionDepartment",
})
export default class extends Vue {
    private departmentList = [];
    created() {
        this.getDeptList();
    }
    private getDeptList() {
        return apiGetDeptList({}).then((res) => {
            this.departmentList = res.data.data || [];
        });
    }
}
</script>
