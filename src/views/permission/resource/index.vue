<template>
    <div class="app-container">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">资源列表</div>
                <el-button
                    v-if="p_AddResource"
                    @click="onAddResource"
                    type="primary"
                    icon="el-icon-plus"
                >
                    添加资源
                </el-button>
            </div>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="resourceList"
                row-key="id"
                stripe
                highlight-current-row
            >
                <el-table-column
                    prop="resourceName"
                    label="资源名称"
                ></el-table-column>
                <el-table-column
                    prop="categoryName"
                    label="资源类型"
                ></el-table-column>
                <el-table-column prop="url" label="资源路径"></el-table-column>
                <el-table-column
                    prop="description"
                    label="资源描述"
                ></el-table-column>
                <el-table-column
                    v-if="p_EditResource || p_DeleteResource"
                    label="操作"
                    width="180"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_EditResource"
                            @click="onEdit(scope)"
                            type="success"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-edit"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-if="p_DeleteResource"
                            @click="onDelete(scope)"
                            type="danger"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            :title="resourceDialogTitle"
            :close-on-click-modal="false"
            :visible.sync="isResourceDialogVisible"
            width="600px"
        >
            <el-form
                :model="resourceForm"
                ref="resourceForm"
                :rules="resourceRules"
                label-width="100px"
                :inline="false"
                size="normal"
            >
                <el-form-item prop="name" label="资源名称：">
                    <el-input style="width:90%" v-model="resourceForm.name"></el-input>
                </el-form-item>
                <el-form-item prop="url" label="资源路径：">
                    <el-input style="width:90%" v-model="resourceForm.url"></el-input>
                </el-form-item>
                <el-form-item prop="categortyId" label="资源类型：">
                    <template>
                        <el-select
                            v-model="resourceForm.categoryId"
                            placeholder="请选择"
                            style="width:90%"
                        >
                            <el-option
                                v-for="item in categoryList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </el-form-item>
                <el-form-item prop="description" label="资源描述：">
                    <el-input style="width:90%" v-model="resourceForm.description"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="onResourceCancel">取消</el-button>
                <el-button type="primary" @click="onResourceConfirm">
                    确认
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import {
    getResourceList,
    addResource,
    updateResource,
    deleteResource,
    getCategoryList,
} from "@/api/resource";
import { Form as ElForm } from "element-ui";
interface IResourceForm {
    id?: number;
    categoryId?: number;
    name?: string;
    url?: string;
    description?: string;
}
@Component({
    name: "PagePermission",
})
export default class extends Vue {
    private resourceList: any[] = [];
    private categoryList: any[] = [];
    private isResourceDialogVisible: boolean = false;
    private resourceDialogTitle: string = "";
    private resourceForm: IResourceForm = {
        id: undefined,
        categoryId: undefined,
        name: undefined,
        url: undefined,
        description: undefined,
    };

    private resourceRules: any = {};
    // 权限
    get p_AddResource() {
        return this.$checkBtnPermission("sys:resource:add");
    }
    get p_EditResource() {
        return this.$checkBtnPermission("sys:resource:edit");
    }
    get p_DeleteResource() {
        return this.$checkBtnPermission("sys:resource:delete");
    }
    created() {
        this.getResourceList();
    }
    private getCategoryList() {
        return getCategoryList({}).then((res) => {
            this.categoryList = res.data.data || [];
        });
    }
    private getResourceList() {
        return getResourceList({}).then((res) => {
            this.resourceList = res.data.data || [];
        });
    }

    private onResourceConfirm() {
        (this.$refs.resourceForm as ElForm).validate(async (valid) => {
            try {
                if (this.resourceDialogTitle === "新增资源") {
                    await addResource(this.resourceForm);
                } else {
                    await updateResource(this.resourceForm);
                }
                await this.getResourceList();
                this.$message.success("操作成功！");
                this.isResourceDialogVisible = false;
            } catch {
                this.$message.error("操作失败，请重试！");
            }
        });
    }
    private onResourceCancel() {
        this.isResourceDialogVisible = false;
    }

    private onDelete(scope: any) {
        this.$confirm("确认删除该资源？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                deleteResource({ id: scope.row.id })
                    .then(async () => {
                        await this.getResourceList();
                        this.$message.success("删除成功！");
                    })
                    .catch((err) => {
                        this.$message.error(err.msg);
                    });
            })
            .catch((err) => {});
    }
    private async onEdit(scope: any) {
        if (!this.categoryList.length) {
            await this.getCategoryList();
        }
        let row = scope.row;
        this.resourceDialogTitle = "编辑资源信息";
        this.resourceForm.id = row.id;
        this.resourceForm.name = row.resourceName;
        this.resourceForm.url = row.url;
        this.resourceForm.categoryId = row.categoryId;
        this.resourceForm.description = row.description;
        this.isResourceDialogVisible = true;
    }

    private async onAddResource() {
        if (!this.categoryList.length) {
            await this.getCategoryList();
        }
        this.resourceDialogTitle = "新增资源";
        this.resourceForm.id = undefined;
        this.resourceForm.name = undefined;
        this.resourceForm.url = undefined;
        this.resourceForm.description = undefined;
        this.isResourceDialogVisible = true;
        this.$nextTick(() => {
            (this.$refs.resourceForm as ElForm).clearValidate();
        });
    }
}
</script>
