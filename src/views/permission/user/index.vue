<template>
  <div class="app-container">
    <div class="app-card" style="height: 100%;">
      <div class="app-card-title">用户管理</div>
      <div style="display: flex; justify-content: space-between;">
        <div style="display: flex; align-items: center;">
          <span>账号状态：</span>
          <el-radio-group size="small" v-model="userStatus">
            <el-radio-button :label="0">全部</el-radio-button>
            <el-radio-button :label="1">正常</el-radio-button>
            <el-radio-button :label="-1">停用</el-radio-button>
          </el-radio-group>
        </div>
        <div style="display: flex; align-items: center;">
          <el-button v-if="$checkBtnPermission('sys:user:add')" @click="onAddUser" type="primary" icon="el-icon-plus">添加用户</el-button>
          <!-- <el-button @click="onChangeUserStatus" type="danger" icon="el-icon-close">操作离职</el-button> -->
        </div>
      </div>
      <div class="simple-line"></div>
      <el-table :header-cell-style="commmonTableHeaderCellStyle" stripe :data="userList" size="medium">
        <el-table-column prop="username" label="用户名"></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="isEnable" label="用户状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.isEnable !== 1"  type="danger" plain>停用</el-tag>
            <el-tag type="success" v-else>正常</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="部门"></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column prop="phoneNumber" label="手机"></el-table-column>
    
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button @click="onEdit(scope)" type="info" style="padding:6px 6px">
              <svg-icon style="margin-right:8px" name="reset_psw"/>重置密码
            </el-button>
            <el-button @click="onDelete(scope)" type="danger" style="padding:6px 6px" icon="el-icon-delete">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog
      :title="infoDialogTitle" :close-on-click-modal="false"
      :visible.sync="isInfoDialogVisible"
      width="30%"
    >
      <el-form
        :model="infoForm"
        ref="infoForm"
        :rules="infoRules"
        label-width="100px"
        :inline="false"
        size="normal"
      >
        <el-form-item prop="realName"  label="姓名：" v-if="isAdd">
           <el-autocomplete
              v-model="infoForm.realName"
              :fetch-suggestions="querySearchOne"
              :trigger-on-focus="false"
              class="inline-input"  style="width:90%"
              placeholder="先选择ERP中员工姓名"
              value-key="name"
              @select="onSelectMember"
          >
            <template slot-scope="{item}">
              <div style="padding:2px 0 4px;overflow:hidden">
                <span>{{item.name}}</span>
                <div style="line-height:1;color:#aaa">
                  <span style="margin-right:10px">{{findDptName(item.orgDepartmentId)}}</span>
                  <span>{{item.email}}</span>
                </div>
              </div>
              
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item prop="username" label="用户名：" v-if="isAdd">
         <el-input style="width:90%" :disabled="!isAdd" v-model="infoForm.username"></el-input>
      
        </el-form-item>
        <el-form-item prop="password" label="密码：">
          <el-input class="disable-autofill"  autocomplete="new-password" style="width:90%" v-model="infoForm.password">
          </el-input>
        </el-form-item>
        
        <!-- <el-form-item prop="email" label="邮箱：">
          <el-input style="width:90%" v-model="infoForm.email"></el-input>
        </el-form-item> -->
        <!-- <el-form-item prop="note" label="备注：">
          <el-input style="width:90%" v-model="infoForm.note"></el-input>
        </el-form-item> -->
      </el-form>
      <span slot="footer">
        <el-button @click="onInfoCancel">取消</el-button>
        <el-button type="primary" @click="onInfoConfirm">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { apiGetMemberList,apiGetUserList, apiAddUser, apiUpdateUser, apiDeleteUser, apiUpdateUserDepartment, apiUpdateUserStatus} from "@/api/users"
import { apiGetDeptList } from "@/api/department"
import { Form as ElForm } from "element-ui";
interface IInfoForm {
  userId?: number;
  memberId: undefined,
  username?: string;
  departmentId?: string;
  isEnable?: number;
  password?: string;
  email?: string;
  note?: string;
  icon?: string;
  realName?: string
}
@Component({
  name: "PagePermission",
})
export default class extends Vue {
  private userList = [];
  private originUserList = [];
  private userStatus = 0;
  private selectUserList: any[] = []
  private departmentList:any[] = [];
  private memberList:any[]=[];
  private isInfoDialogVisible: boolean = false;
  private isAdd: boolean = true;
  private infoForm: IInfoForm = {
    userId: undefined,
    username: undefined,
    memberId: undefined,
    departmentId: undefined,
    isEnable: undefined,
    password: undefined,
    email: undefined,
    note: undefined,
    icon: undefined,
    realName: undefined
  };

  get infoDialogTitle(): string {
    return this.isAdd ? "新增用户" : "修改密码";
  }
  get infoRules():any{
     return {
      username: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
      departmentId: [
        { required: this.isAdd, message: "部门不能为空", trigger: "blur" },
      ],
      password: [
        { required: this.isAdd, message: "密码不能为空", trigger: "blur" },
      ],
    }
  }
  created() {
    this.getUserList();
  }
  @Watch('userStatus')
  watchUserStatus(){
    this.userList = this.filterTableData()
  }
  private findDptName(id:string):string{
    console.log(this.departmentList)
    return this.departmentList.find((item:any)=>item.departmentId==id)?.name||'';
  }
  private getOADepartmentList(){
    return apiGetDeptList({}).then((res) => {
      this.departmentList = res.data.data;
    });
  }
  getUserList(){
    return apiGetUserList({}).then((res) => {
      this.originUserList = res.data.data;
      this.userList = this.filterTableData()
    });
  }
  onSelectMember(){
    let index=this.memberList.findIndex(d=>d.name==this.infoForm.realName);
    if(index==-1){
      this.$message.error("选择用户异常");
      return;
    }
    this.infoForm.memberId=this.memberList[index].memberId;
  }
  private querySearchOne(queryString: string, callback: any) {
    apiGetMemberList({name:this.infoForm.realName}).then(res=>{
      let data=res.data.data || {};
      this.memberList=data.memberInfoList || [];
      callback(this.memberList);
    }).catch(err=>{})

  }
  private onInfoConfirm() {
    (this.$refs.infoForm as ElForm).validate(async (valid) => {
      if (valid) {
        try {
          if (this.infoDialogTitle === "新增用户") {
           console.log(this.infoForm)
            await apiAddUser(this.infoForm);
          } else {
            await apiUpdateUser(this.infoForm);
          }
          this.getUserList();
          this.$message.success("操作成功！");
          this.isInfoDialogVisible = false;
        } catch {
          this.$message.error("操作失败，请重试！");
        }
      }
    });
  }
  private onInfoCancel() {
    this.isInfoDialogVisible = false;
  }

  private onDelete(scope: any) {
    this.$confirm("确认删除该用户？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    })
      .then(() => {
        apiDeleteUser({ userId: scope.row.userId })
          .then(() => {
            this.getUserList();
            this.$message.success("删除成功！");
          })
          .catch((err) => {
            this.$message.error(err.msg);
          });
      })
      .catch((err) => {});
  }
  private onEdit(scope: any) {
    // if(!this.departmentList.length){
    //   this.getOADepartmentList();
    // }
    // this.isAdd = false;
    // let row = scope.row;
    // this.infoForm.userId = row.userId;
    // this.infoForm.username = row.username;
    // this.infoForm.password = row.password;
    // this.infoForm.email = row.email;
    // this.infoForm.departmentId = row.departmentId;
    // this.infoForm.isEnable = row.isEnable;
    // this.infoForm.realName = row.realName;
    // this.infoForm.note = row.note;
    // this.isInfoDialogVisible = true;
    this.$confirm("该用户将重置密码为用户名，确认操作？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    }).then(()=>{
      apiUpdateUser({userId:scope.row.userId,password:scope.row.username}).then(()=>{
        this.$message.success("密码重置成功！")
      });
    })
  }

  private onAddUser() {
    if(!this.departmentList.length){
      this.getOADepartmentList();
    }
    this.isAdd = true;
    this.infoForm.password = undefined;
    this.infoForm.userId = undefined;
    this.infoForm.username = undefined;
    this.infoForm.email = undefined;
    this.infoForm.departmentId = undefined;
    this.infoForm.isEnable = undefined;
    this.infoForm.realName = undefined;
    this.isInfoDialogVisible = true;
    this.$nextTick(() => {
      (this.$refs.infoForm as ElForm).clearValidate();
    });
  }

  private onSelectUserChange(val:any) {
    this.selectUserList = val;
  }

  private filterTableData(){
    if(!this.originUserList){
      return []
    }else{
      if(this.userStatus==0){
        return this.originUserList
      }else if(this.userStatus==1){
        return this.originUserList.filter((item:any)=>item.isEnable==1)
      }else{
        return this.originUserList.filter((item:any)=>item.isEnable!=1)
      }
    }
  }
  private onChangeUserStatus() {
    if (this.selectUserList.length === 0) {
      this.$message.warning('请先勾选要操作离职的成员')
      return
    }
    const postData = {
      userIdList: this.selectUserList.map(item=>item.userId),
      isEnable: 0,
    }
    apiUpdateUserStatus(postData).then(res=>{
      this.getUserList();
    }).catch(err=>{
      console.log(err)
    })
  }
}
</script>
<style lang="scss">
  .disable-autofill{
    .el-input__inner {
      -webkit-text-security: disc !important;
    }
  }
  .icon-box {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    background: #FFF;
    border: 1px solid #DCDFE6;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    i {
      font-size: 30px;
      width: 18px;
      height: 18px;
    }
  }
  .icon-box:hover {
    background: #EFF0F1;
  }
  .icon-box:active {
    background: #DEE0E3;
  }

  .column-checkboxgroup {
    ul {
      margin: 0;
      padding: 0;
      li {
        height: 30px;
        font-size: 18px;
        list-style-type:none;
      }
    }
  }
  .department-container {

    height: 400px;
    overflow-y: auto;

    .list {
      height: 40px;
      width: 100%;

      .list-item {
        border-radius: 2px;
        /*background-color: #ebf3ff;*/
        padding: 10px;
        box-sizing: border-box;
        width: 100%;
        height: 40px;
        line-height: 20px;
        cursor: pointer;
        margin: 0;
        display: flex;
        justify-content: space-between;
      }
    }
    .list-item:hover {
      background-color: #EFF0F1;
    }
    .active {
      background-color: #ebf3ff;
      color: #37f;
    }
  }
</style>
