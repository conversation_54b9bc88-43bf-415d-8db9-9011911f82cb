<template>
    <div class="app-container">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">菜单列表</div>
                <el-button
                    v-if="p_AddMenu"
                    @click="onAddGrandParentMenu"
                    type="primary"
                    icon="el-icon-plus"
                >
                    添加一级菜单
                </el-button>
            </div>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="menulist"
                row-key="id"
                stripe
                highlight-current-row
                :default-expand-all="false"
                class="form_table"
                ref="table"
            >
                <el-table-column prop="title" label="标题"></el-table-column>
                <el-table-column width="80" prop="icon" label="图标">
                    <template slot-scope="scope">
                        <svg-icon
                            width="14px"
                            height="14px"
                            :name="scope.row.icon"
                            class="disabled"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="path" label="路由"></el-table-column>
                <el-table-column
                    prop="redirect"
                    label="重定向"
                ></el-table-column>
                <el-table-column
                    prop="component"
                    label="组件"
                ></el-table-column>
                <el-table-column prop="type" label="类型"></el-table-column>
                <el-table-column
                    prop="authFlag"
                    label="权限标识"
                ></el-table-column>
                <el-table-column width="80" prop="hidden" label="是否隐藏">
                    <template slot-scope="scope">
                        {{ scope.row.hidden ? "是" : "否" }}
                    </template>
                </el-table-column>
                <el-table-column width="80" prop="noCache" label="禁用缓存">
                    <template slot-scope="scope">
                        {{ scope.row.noCache ? "是" : "否" }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="p_AssginResource || p_AddMenu || p_DeleteMenu"
                    label="操作"
                    width="300"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_EditMenu"
                            @click="onEdit(scope)"
                            type="success"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-edit"
                        >
                            编辑
                        </el-button>
                        <el-button
                            @click="onAssignResource(scope)"
                            v-if="
                                scope.row.type === 'BUTTON' && p_AssginResource
                            "
                            type="warning"
                            style="padding: 6px 6px"
                            icon="el-icon-share"
                            size="medium"
                        >
                            分配资源
                        </el-button>
                        <el-button
                            @click="onAddSubMenu(scope)"
                            v-if="scope.row.type !== 'BUTTON' && p_AddMenu"
                            type="primary"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-plus"
                        >
                            添加子菜单
                        </el-button>
                        <el-button
                            v-if="p_DeleteMenu"
                            @click="onDelete(scope)"
                            type="danger"
                            style="padding: 6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                        >
                            删除
                        </el-button>
                        <i
                            class="el-icon-sort drag-btn"
                            title="同级移动"
                            v-if="
                                scope.row.type !== 'BUTTON' && p_AssginResource
                            "
                            style="margin-left: 6px"
                        ></i>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <el-dialog
            :title="menuDialogTitle"
            :visible.sync="isMenuDialogVisible"
            :close-on-click-modal="false"
            width="30%"
        >
            <el-form
                :model="menuForm"
                ref="menuForm"
                :rules="menuRules"
                label-width="100px"
            >
                <el-form-item prop="name" label="路由名称：">
                    <el-input v-model="menuForm.name"></el-input>
                </el-form-item>
                <el-form-item prop="title" label="菜单栏标题：">
                    <el-input v-model="menuForm.title"></el-input>
                </el-form-item>
                <el-form-item prop="parentId" label="父级：">
                    <el-input disabled v-model="menuForm.parentId"></el-input>
                </el-form-item>
                <el-form-item prop="icon" label="菜单栏图标：">
                    <el-input v-model="menuForm.icon"></el-input>
                </el-form-item>
                <el-form-item prop="path" label="路由地址：">
                    <el-input v-model="menuForm.path" placeholder="绝对路径"></el-input>
                </el-form-item>
                <el-form-item prop="redirect" label="重定向地址：">
                    <el-input v-model="menuForm.redirect"></el-input>
                </el-form-item>
                <el-form-item prop="component" label="组件路径：">
                    <el-input v-model="menuForm.component" placeholder="Layout或组件路径">
                        <template slot="prepend">@/views/</template>
                        <template slot="append">/index.vue</template>
                    </el-input>
                </el-form-item>
                <el-form-item prop="type" label="路由类型：">
                    <el-select style="width: 100%" v-model="menuForm.type">
                        <el-option
                            value="DIRECTORY"
                            label="DIRECTORY"
                        ></el-option>
                        <el-option value="MENU" label="MENU"></el-option>
                        <el-option value="BUTTON" label="BUTTON"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="authFlag" label="权限标识：">
                    <el-input v-model="menuForm.authFlag"></el-input>
                </el-form-item>
                <el-form-item prop="hidden" label="是否隐藏：">
                    <el-select
                        clearable
                        style="width: 100%"
                        v-model="menuForm.hidden"
                    >
                        <el-option :value="true" label="是"></el-option>
                        <el-option :value="false" label="否"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="noCache" label="禁用缓存：">
                    <el-select
                        clearable
                        style="width: 100%"
                        v-model="menuForm.noCache"
                    >
                        <el-option :value="true" label="是"></el-option>
                        <el-option :value="false" label="否"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="onMenuCancel">取消</el-button>
                <el-button type="primary" @click="onMenuConfirm">
                    确认
                </el-button>
            </span>
        </el-dialog>

        <el-dialog
            :destroy-on-close="true"
            title="分配资源"
            :visible.sync="isAssignResourceDialogVisible"
            width="500px"
        >
            <el-tree
                v-if="isAssignResourceDialogVisible"
                ref="resourceTree"
                :data="resourceTree"
                :props="{ children: 'children', label: 'resourceName' }"
                show-checkbox
                node-key="id"
                :default-checked-keys="defaultResources"
            >
                <template slot-scope="{ data }">
                    <span v-if="!!data.url"
                        >{{ data.resourceName }}
                        <span style="color: #8590a6">{{ data.url }}</span>
                    </span>
                    <span v-else>{{ data.resourceName }} </span>
                </template>
            </el-tree>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isAssignResourceDialogVisible = false">
                    取 消
                </el-button>
                <el-button type="primary" @click="onConfirmAssignResource">
                    确 定
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { getMenuTree, addMenu, updateMenu, deleteMenu } from "@/api/menu";
import { Form as ElForm, Tree as ElTree } from "element-ui";
import { getResourceListByMenu, getResourceTree } from "@/api/resource";
import { assignResource } from "@/api/roles";
import Sortable from "sortablejs";
interface IMenuForm {
    parentId?: number;
    name?: string;
    path?: string;
    icon?: string;
    component?: string;
    id?: number;
    type?: string;
    title?: string;
    noCache?: boolean;
    hidden?: boolean;
    authFlag?: string;
    redirect?: string;
}
@Component({
    name: "PagePermission",
})
export default class extends Vue {
    private menulist: any[] = [];
    private resourceTree: any[] = [];
    private defaultResources: any[] = [];
    private menuId: number = 0;
    private isMenuDialogVisible: boolean = false;
    private isAssignResourceDialogVisible: boolean = false;
    private menuDialogTitle: string = "";
    private menuForm: IMenuForm = {
        parentId: undefined,
        name: undefined,
        path: undefined,
        icon: undefined,
        component: undefined,
        type: undefined,
        title: undefined,
        noCache: undefined,
        hidden: undefined,
        authFlag: undefined,
        redirect: undefined,
    };

    private menuRules = {};
    // 权限
    get p_AddMenu() {
        return this.$checkBtnPermission("sys:menu:add");
    }
    get p_EditMenu() {
        return this.$checkBtnPermission("sys:menu:edit");
    }
    get p_DeleteMenu() {
        return this.$checkBtnPermission("sys:menu:delete");
    }
    get p_AssginResource() {
        return this.$checkBtnPermission("sys:menu:resource");
    }
    created() {
        this.getMenuList();
    }
    private getMenuList() {
        return getMenuTree({}).then((res) => {
            this.menulist = res.data.data || [];
            this.rowDrop();
        });
    }
    private onMenuConfirm() {
        (this.$refs.menuForm as ElForm).validate(async (valid) => {
            try {
                if (this.menuDialogTitle === "新增菜单") {
                    await addMenu(this.menuForm);
                } else {
                    await updateMenu(this.menuForm);
                }
                this.$router.push(`/redirect/permission/menu`);
                this.$message.success("操作成功！");
                this.isMenuDialogVisible = false;
            } catch {
                this.$message.error("操作失败，请重试！");
            }
        });
    }
    private onMenuCancel() {
        this.isMenuDialogVisible = false;
    }
    private onAddSubMenu(scope: any) {
        this.menuDialogTitle = "新增菜单";
        this.menuForm.parentId = scope.row.id;
        this.menuForm.name = undefined;
        this.menuForm.title = undefined;
        this.menuForm.icon = "";
        this.menuForm.path = "";
        this.menuForm.component = "";
        this.menuForm.type = "";
        this.menuForm.noCache = true;
        this.menuForm.hidden = false;
        this.menuForm.authFlag = "";
        this.isMenuDialogVisible = true;
        this.$nextTick(() => {
            (this.$refs.menuForm as ElForm).clearValidate();
        });
    }
    private onDelete(scope: any) {
        this.$confirm("确认删除该菜单？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                deleteMenu({ id: scope.row.id })
                    .then(async () => {
                        this.$router.push(`/redirect/permission/menu`);
                        this.$message.success("删除成功！");
                    })
                    .catch((err) => {
                        this.$message.error(err.msg);
                    });
            })
            .catch((err) => {});
    }
    private onEdit(scope: any) {
        let row = scope.row;
        this.menuDialogTitle = "编辑菜单信息";
        this.menuForm.type = row.type;
        this.menuForm.id = row.id;
        this.menuForm.parentId = row.parentId;
        this.menuForm.name = row.name;
        this.menuForm.title = row.title;
        this.menuForm.path = row.path;
        this.menuForm.component = row.component;
        this.menuForm.icon = row.icon;
        this.menuForm.noCache = row.noCache;
        this.menuForm.hidden = row.hidden;
        this.menuForm.authFlag = row.authFlag;
        this.menuForm.redirect = row.redirect;
        this.isMenuDialogVisible = true;
    }

    private onAddGrandParentMenu() {
        this.menuDialogTitle = "新增菜单";
        this.menuForm.parentId = 0;
        this.menuForm.name = undefined;
        this.menuForm.title = undefined;
        this.menuForm.icon = "icon";
        this.menuForm.path = "";
        this.menuForm.component = "";
        this.menuForm.type = "DIRECTORY";
        this.menuForm.redirect = "";
        this.menuForm.noCache = true;
        this.menuForm.hidden = false;
        this.menuForm.authFlag = "";
        this.isMenuDialogVisible = true;
        this.$nextTick(() => {
            (this.$refs.menuForm as ElForm).clearValidate();
        });
    }

    private async onAssignResource(scope: any) {
        this.menuId = scope.row.id;
        this.defaultResources = [];
        //获取资源列表
        if (!this.resourceTree.length) {
            await getResourceTree({}).then((res) => {
                this.resourceTree = res.data.data || [];
            });
        }

        getResourceListByMenu({ menuId: scope.row.id }).then((res) => {
            let resources: any[] = res.data.data;
            resources.forEach((resource) => {
                this.defaultResources.push(resource.id);
            });
            this.isAssignResourceDialogVisible = true;
        });
    }

    private onConfirmAssignResource() {
        let checkedKeys: number[] = (
            this.$refs.resourceTree as ElTree
        ).getCheckedKeys();
        let numbers = checkedKeys.filter((key) => key > 0);
        assignResource({ menuId: this.menuId, resourceIdList: numbers })
            .then(() => {
                this.$message.success("保存成功！");
            })
            .catch(() => {
                this.$message.success("操作失败，请重试！");
            });
        this.isAssignResourceDialogVisible = false;
    }
    private findItemByDraggableIndex(targetIdx): any {
        // TODO: 优化
        const len1 = this.menulist.length;
        let ret = null;
        let curIdx = -1;
        let flag = false;
        for (let i = 0; i < len1; i++) {
            curIdx++;
            if (curIdx === targetIdx) {
                ret = this.menulist[i];
                flag = true;
            }
            if (flag) {
                break;
            }
            const child = this.menulist[i].children || [];
            const len2 = child.length;
            for (let j = 0; j < len2; j++) {
                curIdx++;
                if (curIdx === targetIdx) {
                    ret = child[j];
                    flag = true;
                }
                if (flag) {
                    break;
                }
                const grandChild = child[j].children || [];
                const len3 = grandChild.length;
                for (let k = 0; k < len3; k++) {
                    curIdx++;
                    if (curIdx === targetIdx) {
                        ret = grandChild[k];
                        flag = true;
                    }
                    if (flag) {
                        break;
                    }
                }
            }
        }
        console.log(curIdx);
        return ret;
    }
    private rowDrop() {
        this.$nextTick(() => {
            const tbodyElement = document.querySelector(
                ".form_table .el-table__body-wrapper tbody"
            ) as HTMLElement;
            
            if (tbodyElement) {
                this.sortable1 = Sortable.create(tbodyElement, {
                    handle: ".drag-btn",
                    onEnd: async (obj) => {
                        const { newIndex, oldIndex } = obj;
                        console.log(obj, { newIndex, oldIndex });
                        const newForm = this.findItemByDraggableIndex(newIndex);
                        const oldForm = this.findItemByDraggableIndex(oldIndex);
                        if (newForm.parentId != oldForm.parentId) {
                            this.$message.error("只能同一层级之间拖拽！");
                        } else {
                            const newSort = newForm.sort;
                            const oldSort = oldForm.sort;
                            await updateMenu({ id: newForm.id, sort: oldSort });
                            await updateMenu({ id: oldForm.id, sort: newSort });
                        }
                        this.$router.push(`/redirect/permission/menu`);
                    },
                });
            }
        });
    }
}
</script>
