<template>
  <div class="app-container">
    <div class="app-card" style="height:100%;padding:0">
      <div class="side-menu" style="float:left">
        <div style="width:auto;position:relative;height:100%;padding:16px 16px 60px;">
          <div class="list" :key="item.id" v-for="item in roleList" >
            <div class="list-item" :class="{'active':activeRoleId == item.id}" @click="()=>{activeRoleId = item.id}">
              <div>
                <svg-icon name="user" style="margin-right:14px;"/>
                <span>{{item.name}}</span>
              </div>
              <el-dropdown v-if="(p_EditRole||p_DeleteRole)&&$isSuperAdmin()" trigger="click">
                <div class="more">
                  <span><svg-icon name="dot" style="width:16px;height:16px;" /></span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <span v-if="p_EditRole" style="display:block;width:100%;" @click="onShowEditRoleDialog(item)">编辑</span>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <span v-if="p_DeleteRole" style="display:block;width:100%;" @click="onDeleteRole(item)">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </div>
              </el-dropdown>
            </div>
          </div>
          <div v-if="p_AddRole&&$isSuperAdmin()" class="circle-btn-wrapper" @click="onAddRole">
            <div class="circle-button-text"><span>新增角色</span></div>
            <div class="circle-btn"></div>
          </div>
        </div>
      </div>
      <div class="content-card">
        <div style="display:flex;justify-content:space-between;">
          <div class="role-title">
            <div class="head-title">
              {{activeRole.name}}
            </div>
            <span class="count-number">{{roleUserList.length}}</span>
          </div>
          <div>
            <el-button v-if="p_AddRoleUser" @click="onAddMember" type="primary" icon="el-icon-plus" :loading="isAddRoleUserLoading">添加员工</el-button>
            <el-button v-if="p_AssginPermission&&$isSuperAdmin()" @click="onPermissionConfig" icon="el-icon-share" type="warning" >角色权限</el-button>
            <el-button v-if="p_DeleteRoleUser" @click="onBatchDeleteMember" icon="el-icon-delete" type="danger">批量移除员工</el-button>
          </div>
        </div>
        <div class="simple-line"></div>
        <div>
          <el-table
            @selection-change="handleSelectionChange"
            :data="roleUserList"
            :header-cell-style="commmonTableHeaderCellStyle" stripe
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="name" label="姓名"></el-table-column>
            <el-table-column prop="phoneNumber" label="手机"></el-table-column>
            <el-table-column prop="departmentName" label="所属部门"></el-table-column>
            <el-table-column label="操作" width="150" v-if="p_DeleteRoleUser">
              <template slot-scope="scope">
                <!-- <el-button @click="onEdit(scope)" type="success" style="padding:6px 6px" size="small">编辑</el-button> -->
                <el-button v-if="p_DeleteRoleUser" @click="onDeleteRoleUser(scope)" type="danger" style="padding:6px 6px" size="medium" icon="el-icon-delete">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <el-dialog
      title="添加员工" :close-on-click-modal="false"
      :visible.sync="isAddMemberVisible"
      width="800px"
    >
      <div class="add-member-dialog">
        <div class="left-part">
          <div style="width:auto;position:relative;">
            <!-- <el-input
              placeholder="请输入姓名"
              prefix-icon="el-icon-search"
              v-model="addMemberUserNameSearch">
            </el-input> -->
            <el-checkbox-group v-model="addMemberUserCheckList" @change="onAddMemberUserChange">
              <div class="list" v-for="item in allUserList" :key="`realNameLeft`+item.userId">
                <div class="list-item" @click="()=>{}">
                  <el-checkbox :label="item.userId" style="width:100%;height:40px;" :disabled="roleUserList.some(a=>a.userId===item.userId)">
                    <span>{{item.name}}</span><span style="color:#aaa;margin-left:10px;font-size:12px">{{item.roleNameList ? item.roleNameList.join(", ") : ""}}</span>
                  </el-checkbox>
                </div>
              </div>
            </el-checkbox-group>
          </div>
        </div>
        <div class="right-part">
          <div v-show="addMemberChosenUserList.length > 0" style="font-size:14px;color:#18263c;margin:6px 0 16px 4px;">已选： {{addMemberChosenUserList.length}}名用户</div>
          <div class="list" v-for="item in addMemberChosenUserList" :key="`realNameRight`+item.userId">
            <div class="list-item" @click="()=>{}">
              <span>{{item.name}}</span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button @click="isAddMemberVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmAddMember">确定</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :title="roleDialogTitle"
      :visible.sync="isRoleDialogVisible"
      width="30%"
    >
      <el-form :model="roleForm" ref="roleForm" :rules="infoRules" label-width="100px">
        <el-form-item prop="name" label="名称：">
          <el-input v-model="roleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="描述：">
          <el-input v-model="roleForm.description"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="isRoleDialogVisible=false">取消</el-button>
        <el-button type="primary" @click="onAddRoleConfirm">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog 
      :destroy-on-close="true" 
      :close-on-click-modal="false"  
      title="分配权限" 
      :visible.sync="showAssginPermissionDialog" 
      width="20%"
    >
      <el-tree 
        v-if="showAssginPermissionDialog" 
        ref="menuTree" 
        :data="menuTree" 
        :props="{children: 'children',label: 'title'}" 
        show-checkbox node-key="id" 
        :default-checked-keys="selectedMenu">
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAssginPermissionDialog = false">取 消</el-button>
        <el-button type="primary" @click="onConfirmAssiginPermission">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { addRole, updateRole, getRoleList, assignUserRole, deleteRole, apiBatchAssignUserRole, apiBatchDeleteUserRole, assignMenu } from '@/api/roles'
import { getDefaultMenuIdListByRole, getMenuTree } from '@/api/menu';
import { apiGetUserListByRole, apiGetUserList} from '@/api/users'
import { Form as ElForm, Tree as ElTree } from 'element-ui'
@Component({
  name: 'rolemanage',
})
export default class extends Vue {
  private showAssginPermissionDialog = false
  private menuTree = [];
  private selectedMenu: number[] = [];
  private curUserId:number|null = null
  private roleList:any[] = []
  private activeRoleId = 0
  private activeRole:any = {}
  private roleUserList:any[] = []
  private isAddRoleUserLoading:boolean = false;
  private allUserList:any[] = []
  private userRoleList:number[] = []
  private addMemberUserCheckList:any[] = []
  //显示为角色添加员工面板
  private isAddMemberVisible:boolean = false
  //显示新增/编辑角色面板
  private isRoleDialogVisible: boolean = false
  private roleDialogTitle: string = ''
  private addMemberUserNameSearch:string = ''
  private addMemberChosenUserList:any[] = []
  private roleForm:{
    id?:number,name?:string,description?:number,status:number
  } = {
    id:undefined,name:undefined,description:undefined,status:1
  }
  private infoRules = {
    username: [{ required:true,message:'用户名不能为空', trigger: 'blur' }],
    departmentId: [{ required:true,message:'部门不能为空', trigger: 'blur' }],
  }
  private seletedIdList:any[] = []
  // 权限
  get p_AddRole(){
    return this.$checkBtnPermission('sys:role:add_role');
  }
  get p_EditRole(){
    return this.$checkBtnPermission('sys:role:edit_role');
  }
  get p_DeleteRole(){
    return this.$checkBtnPermission('sys:role:delete_role');
  }
  get p_AddRoleUser(){
    return this.$checkBtnPermission('sys:role:add_role_user')
  }
  get p_DeleteRoleUser(){
    return this.$checkBtnPermission('sys:role:delete_role_user')
  }
  get p_AssginPermission(){
    return this.$checkBtnPermission('sys:role:assign_permission')
  }
  @Watch("activeRoleId")
  watchActiveRoleId(newVal:number, oldVal:number) {
    this.activeRole = this.roleList.find((item:any) => {
      return item.id === newVal
    })
    this.getActiveRoleUserList()
  }

  created(){
    this.getRoleList();
  }
    
  private getRoleList(){
    getRoleList({}).then(res=>{
      this.roleList = res.data.data || [];
      if (this.roleList.length > 0) {
        this.activeRoleId = this.roleList[0]['id'];
      }
    })
  }

  // 移除角色员工
  private onBatchDeleteMember(){
    if(this.seletedIdList.length<1)return
    this.$confirm('确认删除该员工的角色？','提示',{confirmButtonText:'确定',cancelButtonText:'取消'}).then(()=>{
      let postData = {
        roleId: this.activeRoleId,
        userIdList: this.seletedIdList
      }
      apiBatchDeleteUserRole(postData).then(res=>{
        this.getActiveRoleUserList()
      }).catch(err=>{
        this.$message.error(err.msg)
      })
    })
  }
  private handleSelectionChange(val:any){
    this.seletedIdList = [];
    val.forEach((item:any)=>{
      this.seletedIdList.push(item.userId)
    })
  }
  // 角色权限
  private onConfirmAssiginPermission() {
    let checkedKeys: number[] = (this.$refs.menuTree as ElTree).getCheckedKeys();
    let halfCheckedKeys = (this.$refs.menuTree as ElTree).getHalfCheckedKeys()
    checkedKeys = checkedKeys.concat(halfCheckedKeys)
    assignMenu({ roleId: this.activeRoleId, menuIdList: checkedKeys })
        .then(() => {
            this.$message.success("保存成功！");
            this.showAssginPermissionDialog = false
        })
        .catch(() => {
            this.$message.success("操作失败，请重试！");
        });
  }


  private getActiveRoleUserList() {
    apiGetUserListByRole({roleId: this.activeRoleId}).then(res=>{
      this.roleUserList = res.data.data
    }).catch(err=>{
      console.log(err)
    })
  }

  private onAddRoleConfirm(){
    (this.$refs.roleForm as ElForm).validate(async (valid) =>{
      try{
        if(this.roleDialogTitle==='新增角色'){
          await addRole(this.roleForm)
        }else{
          await updateRole(this.roleForm)
        }
        this.getRoleList()
        this.$message.success('操作成功！')
        this.isRoleDialogVisible = false;
      }catch{
        this.$message.error('操作失败，请重试！')
        this.isRoleDialogVisible = false;
      }
    })
  }

  private onDeleteRoleUser(scope:any) {
    this.$confirm('确认删除该员工的角色？','提示',{confirmButtonText:'确定',cancelButtonText:'取消'}).then(()=>{
      let row = scope.row
      let postData = {
        roleId: this.activeRoleId,
        userIdList: [row.userId]
      }
      apiBatchDeleteUserRole(postData).then(res=>{
        this.getActiveRoleUserList()
      }).catch(err=>{
        this.$message.error(err.msg)
      })
    })

  }

  private onDeleteRole(row: any){
    this.$confirm('确认删除该角色？','提示',{confirmButtonText:'确定',cancelButtonText:'取消'})
      .then(()=>{
        deleteRole({id:row.id}).then(async ()=>{
          this.getRoleList()
          this.$message.success('删除成功！')
        })
        .catch(err=>{
          this.$message.error(err.msg)
        })
      })
      .catch(err=>{})

  }
  private onEdit(scope: any){
    let row = scope.row
    this.roleDialogTitle = '编辑角色信息'
    this.roleForm.id = row.id
    this.roleForm.name = row.name
    this.roleForm.description = row.description
    this.roleForm.status = row.status
    this.isRoleDialogVisible = true
  }

  private async onPermissionConfig() {
    await getDefaultMenuIdListByRole({ roleId: this.activeRoleId }).then((res) => {
      this.selectedMenu = res.data.data || [];
    });
    await getMenuTree({}).then((res) => {
      this.menuTree = res.data.data || [];
    });
    this.showAssginPermissionDialog = true;
  }

  private onAddRole(){
    this.roleDialogTitle = '新增角色'
    this.roleForm.id = undefined
    this.roleForm.name = undefined
    this.roleForm.description = undefined
    this.roleForm.status = 1
    this.isRoleDialogVisible = true
    this.$nextTick(()=>{
      (this.$refs.roleForm as ElForm).clearValidate()
    })
  }

  onShowEditRoleDialog(row:any) {
    this.roleDialogTitle = '编辑角色信息'
    this.roleForm.id = row.id
    this.roleForm.name = row.name
    this.roleForm.description = row.description
    this.roleForm.status = row.status
    this.isRoleDialogVisible = true
  }

  private onAddMemberUserChange(val:any) {
    let addMemberChosenUserList = this.allUserList.reduce<any>((array, item:any) => {
      const isChosen = this.addMemberUserCheckList.find(subItem => subItem === item.userId);
      if (isChosen) {
        array.push(item);
      }
      return array;
    }, []) 
    this.addMemberChosenUserList = addMemberChosenUserList;
  }

  // 添加员工 
  private getUserList(){
    return apiGetUserList({}).then(res=>{
      this.allUserList = res.data.data || [];
    })
  }

  private async onAddMember(){
    this.isAddRoleUserLoading = true;
    await this.getUserList();
    
    this.addMemberChosenUserList = this.allUserList.reduce<any>((array, item:any) => {
      const isAlready = this.roleUserList.find(subItem => subItem.userId == item.userId);
      if (isAlready) {
        array.push(item);
      }
      return array;
    }, [])
    this.addMemberUserCheckList = this.roleUserList.map(item=>item.userId);
    this.isAddRoleUserLoading = false;
    this.isAddMemberVisible = true
  }

  private onConfirmAddMember() {
      let userIdList = this.addMemberChosenUserList.map(item=>item.userId);
      let postData = {
        roleIdList: [this.activeRoleId],
        userIdList: userIdList,
      }
      apiBatchAssignUserRole(postData).then(res=>{
        this.addMemberChosenUserList = []
        this.isAddMemberVisible = false
        this.getActiveRoleUserList()
      }).catch(err=>{
        this.isAddMemberVisible = false
      })
  }
}
</script>
<style lang="scss" scoped>
  .side-menu {
    margin-right: 0;
    border-radius: 0;
    border-right: 1px solid #dee0e3;
    width: 288px;
    height: 100%;
    flex: 0 0 288px;

    .circle-btn-wrapper {
      position: fixed;
      // left: 24px;
      bottom: 8px;
      padding-bottom: 10px;
      cursor: pointer;

      .circle-btn {
        height: 40px;
        width: 40px;
        background: #3370ff url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCI+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMTAuMTExLjExMXY3Ljc3N2g3Ljc3OHYyLjIyM0gxMC4xMXY3Ljc3OEg3Ljg5VjEwLjExbC03Ljc3OC4wMDFWNy44OWg3Ljc3N1YuMTFoMi4yMjN6Ii8+PC9zdmc+) no-repeat 50%;
        color: white;
        border-radius: 50%;
        border: none;
        cursor: pointer;
        position: absolute;
        top: 0;
        left: 0;
        /*box-shadow: 0 5px 10px 0 rgba(0,0,0,.2);*/
      }

      .circle-button-text {
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        padding-left: 40px;
        box-shadow: 0 5px 10px 0 rgba(0,0,0,.2);
        background: #3370ff;
        color: #fff;

        transition: width .3s ease-in-out;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .circle-btn-wrapper:hover {
      .circle-button-text {
        width: 116px;
      }
    }
    .circle-btn-wrapper:active {
      .circle-btn {
        background-color: #245bdb;
      }
      .circle-button-text {
        background-color: #245bdb;
      }
    }
  }

  .list {
    height: 40px;
    width: 100%;
    cursor: pointer;
    .list-item {
      border-radius: 2px;
      /*background-color: #ebf3ff;*/
      padding: 10px;
      box-sizing: border-box;
      width: 100%;
      height: 40px;
      line-height: 20px;
      margin: 0;
      display: flex;
      justify-content: space-between;

      .more {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        line-height: 20px;
        text-align: center;
      }

      .more:hover {
        background: #bacefd;
      }
    }

    .list-item:hover {
      background-color: #EFF0F1;
    }

    .active {
      background-color: #ebf3ff;
      color: #37f;
    }
  }

  .content-card {
    height: 100%;
    padding: 24px 44px;
    background-color: #fff;
    flex-grow: 1;
    display: flex;
    flex-direction: column;

    .role-title {
      width: auto;
      display: flex;
      align-items: center;
      .head-title {
        color: #1f2329;
        font-size: 16px;
        font-weight: 500;
      }
      .count-number {
        margin-left: 15px;
        line-height: 21px;
        font-size: 15px;
        color: #8f959e;
      }
    }
    .role-title:before {
      content: "";
      border-left: 4px solid #3370ff;
      height: 16px;
      margin-right: 20px;
    }
  }

  .add-member-dialog {

    display: flex;
    align-items: center;
    height: 550px;

    .left-part {
      width: 50%;
      padding: 16px 16px;
      border-radius: 4px 0 0 4px;
      border: 1px solid #dee0e3;
      border-right: none;
      height: 100%;
      overflow: auto;
    }
    .right-part {
      flex: 1;
      padding: 16px;
      border-radius: 0 4px 4px 0;
      border: 1px solid #dee0e3;
      height: 100%;
      overflow: auto;
    }
  }
</style>
