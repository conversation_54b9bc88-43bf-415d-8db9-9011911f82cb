<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="demand-dialog"
        :close-on-click-modal="false"
        title="需求单"
        width="1000px"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            :rules="detailFormRules"
            label-width="80px"
        >
            <el-row>
                <el-col :span="6">
                    <el-form-item label="井号: " prop="wellNumber">
                        <FuzzySelect placeholder="" type="WELL_NUMBER" v-model="detailForm.wellNumber"></FuzzySelect>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业号: " prop="jobNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.kitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="目的地: ">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.destination"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="需求日期: ">
                        <el-date-picker
                            v-model="detailForm.demandDate"
                            style="width: calc(100%)"
                            type="date"
                            placeholder=""
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.originInfoDetailList"
            style="margin-bottom: 20px"
            default-expand-all
            :header-cell-style="commmonTableHeaderCellStyle"
        >
            <el-table-column label="需求物品名称" prop="itemName" width="220" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.itemName }}
                    </span>
                    <el-input
                        v-else
                        size="small"
                        v-model="scope.row.itemName"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="需求数量" prop="requireAmount" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.requireAmount }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.requireAmount"
                    ></InputNumber>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.note }}
                    </span>
                    <el-input v-else v-model="scope.row.note"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="float: right"
            type="primary"
            @click="onAddRow"
        >
            添加元素
        </el-button>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onHandleSave(true)" :loading="isConfirmLoading">确定并发送邮件</el-button>
            <el-button type="primary" @click="onHandleSave(false)" :loading="isConfirmLoading">确 定</el-button>
        </span>
        <EmailDialog email-business-type="DEMAND_ORDER" :emailBusinessId="demandId" ref="emailDialogRef"></EmailDialog>
    </el-dialog>
</template>
<script>
import FuzzySelect from "@/components/FuzzySelect";
import { apiAddDemand, apiGetDemandInfo, apiUpdateDemand } from "@/api/demand";
import EmailDialog from "@/components/EmailDialog/index.vue";
export default {
    name: "DemandDialog",
    components: { FuzzySelect, EmailDialog },
    data(){
        return {
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                originInfoDetailList: [],
                actualInfoDetailList: [],
            },
            detailFormRules: {
                wellNumber: [{ required: true, message: "请填写井号", trigger: "blur" }],
                jobNumber: [{ required: true, message: "请填写作业号", trigger: "blur" }],
            },
            isConfirmLoading: false,
            editObject: { editRow: false, editRowIndex: -1 },
            preObject: {},
            operType: "ADD",
            isDialogVisible: false,
            fuzzyWellNumberList: [],
            demandId: null,
        }
    },
    methods: {
        getInitForm() {
            return {
                jobNumber: null,
                wellNumber: null,
                kitNumber: null,
                destination: null,
                demandDate: new Date().Format("yyyy-MM-dd"),
                originInfoDetailList: [],
                actualInfoDetailList: [],
            };
        },
        getInitDetailItem() {
            return {
                itemName: null,
                serialNumber: null,
                requireAmount: null,
                serialNumberList: [],
                serialNumberListStr: null,
                note: null,
            };
        },

        onHandleSave(email=false) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            if (this.detailForm.originInfoDetailList.length == 0) {
                this.$message.error("请先添加需求项");
                return;
            }
            this.$refs["detailForm"].validate((valid) => {
                if (!valid) return;
                this.saveEditData(email);
            });
        },
        saveEditData(email=false) {
            //save data
            if (!this.detailForm.originInfoDetailList?.length) {
                this.$message.error("请添加需求项");
                return;
            }
            this.isConfirmLoading = true;
            if (this.operType === "ADD") {
                //add
                apiAddDemand(this.detailForm).then((res) => {
                    this.isDialogVisible = false;
                    this.$emit("initData");
                    this.$message.success("添加成功");
                    this.demandId = res.data.data;
                    this.$nextTick(()=>{
                        email && this.$refs.emailDialogRef.showDialog();
                    })
                }).finally(() =>{
                    this.isConfirmLoading = false;
                });
            } else {
                //update
                apiUpdateDemand(this.detailForm).then((res) => {
                    this.$message.success("更新成功");
                    this.$emit("initData");
                    this.isDialogVisible = false;
                    email && this.$refs.emailDialogRef.showDialog();
                }).finally(() =>{
                    this.isConfirmLoading = false;
                });
            }
        },
        async showDialog(type, demandId) {
            this.editObject.editRowIndex = -1;
            this.operType = type;
            this.detailForm = this.getInitForm();
            if (type === "DETAIL") {
                this.demandId = demandId;
                await apiGetDemandInfo({ demandId: demandId }).then((res) => {
                    this.detailForm = res.data.data;
                });
            }
            this.isDialogVisible = true;
        },

        onAddRow() {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.preObject = {};
            this.detailForm.originInfoDetailList.push(this.getInitDetailItem());
            this.editObject.editRow = true;
            this.editObject.editRowIndex =
                this.detailForm.originInfoDetailList.length - 1;
        },

        onEditRow(scope) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.preObject = { ...scope.row };
            this.editObject.editRowIndex = scope.$index;
        },

        onDeleteRow(scope) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.detailForm.originInfoDetailList.splice(scope.$index, 1);
        },

        async onSaveRow(scope) {
            if (!scope.row.itemName || !scope.row.requireAmount) {
                this.$message.error("请填写仪器名称及其数量");
                return;
            }
            this.editObject.editRowIndex = -1;
        },

        onCancelRow(scope) {
            if (Object.keys(this.preObject).length) {
                this.$set(this.detailForm.originInfoDetailList, scope.$index, {
                    ...this.preObject,
                });
            } else {
                this.detailForm.originInfoDetailList.pop();
            }
            this.editObject.editRowIndex = -1;
            this.preObject = {};
        },
    }
    
}
</script>
<style lang="scss">
.demand-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
