<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="需求单"
        width="1000px"
    >
        <el-form ref="detailForm" style="margin-top: -20px;" :model="detailForm" class="thin-margin-form" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="井号: " prop="wellNumber">
                        <span>{{ detailForm.wellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业号: " prop="jobNumber">
                        <span>{{ detailForm.jobNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <span>{{ detailForm.kitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="目的地: ">
                        <span>{{ detailForm.destination }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="需求日期: ">
                        <span>{{ detailForm.demandDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-tabs v-model="activeName">
            <el-tab-pane label="出库信息" name="stock">
                <el-table
                    :data="detailForm.actualInfoDetailList"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    style="margin-bottom: 20px;"
                    v-if="activeName === 'stock'"
                >
                    <el-table-column label="需求物品名称" prop="itemName" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="仪器类型" prop="deviceType" align="center" width="180">
                    </el-table-column>
                    <el-table-column label="序列号" prop="serialNumberListStr" width="220" align="center">
                    </el-table-column>
                    <el-table-column label="需求数量" prop="requireAmount" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="实发数量" prop="actualAmount" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="备注" prop="note" align="center">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="原始需求" name="demand">
                <el-table
                    :data="detailForm.originInfoDetailList"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    style="margin-bottom: 20px;"
                    v-if="activeName === 'demand'"
                >
                    <el-table-column label="需求物品名称" prop="itemName" width="120" align="center">
                    </el-table-column>
                    <el-table-column label="需求数量" prop="requireAmount" width="200" align="center">
                    </el-table-column>
                    <el-table-column label="备注" prop="note" align="center">
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>
<script>
import { apiGetDemandInfo } from "@/api/demand";
export default {
    name: "DemandDetailDialog",
    data(){
        return {
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                originInfoDetailList: [],
                actualInfoDetailList: [],
            },
            isConfirmLoading: false,
            operType: "ADD",
            isDialogVisible: false,
            activeName: "stock",
        }
    },
    methods: {
        async showDialog(demandId) {
            await apiGetDemandInfo({ demandId: demandId }).then((res) => {
                const data = res.data.data || {};
                this.detailForm = data;
            });
            this.isDialogVisible = true;
        },
    }
    
}
</script>
