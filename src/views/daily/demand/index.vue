<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">需求单</div>
                <el-button
                    style="float: right;"
                    type="primary"
                    @click="onAdd"
                >
                    新增需求单
                </el-button>
            </div>
            <el-form :model="searchForm" label-width="auto" style="margin-top: 10px; width: 100%;">
                <el-form-item label="井号">
                    <el-input @change="initData" v-model="searchForm.wellNumber" clearable style="width: 200px;"></el-input>
                </el-form-item>
            </el-form>
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
            >
                <el-table-column
                    label="需求单号"
                    prop="demandNumber"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="联系人"
                    width="120"
                    prop="contactUser"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="联系方式"
                    prop="contactNumber"
                    width="120"
                    align="center"
                ></el-table-column>
                <!-- <el-table-column
                    label="需求日期"
                    prop="demandDate"
                    width="100"
                    align="center"
                ></el-table-column> -->
                <el-table-column
                    label="目的地"
                    prop="destination"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="创建人"
                    prop="createBy"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="createTime"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="状态"
                    prop="receiveStatus"
                    width="100"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RECEIVE_STATUS" v-if="scope.row.receiveStatus == 'RECEIVED'" tagValue="RECEIVED" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else tagValue="SUBMITTED" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="接收人"
                    prop="receiveBy"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receiveTime"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="140"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="onDetail(scope.row)"
                            v-if="scope.row.receiveStatus=='RECEIVED'"
                        >
                            详情
                        </el-button>
                        <el-button
                            type="text"
                            @click="onEdit(scope.row)"
                            v-else
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            @click="onExport(scope.row)"
                        >
                            下载
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <DemandDialog
            @initData="initData"
            ref="demandDialogRef"
        />
        <DetailDialog ref="detailDialogRef" />
    </div>
</template>

<script>
import { apiGetDemandList, apiExportDemand } from "@/api/demand";
import DemandDialog from "./demandDialog.vue";
import DetailDialog from "./detailDialog.vue";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
export default {
    name: "Demand",
    components: { DemandDialog, DetailDialog },
    data() {
        return {
            isLoading: false,
            orderBy: "",
            orderType: "",
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {
                toolType: "MWD",
                wellNumber: "",
            },
        };
    },
    computed: {
        p_add() {
            return this.$checkBtnPermission("sys:daily:repiar:add");
        },
        p_delete() {
            return this.$checkBtnPermission("sys:daily:repiar:delete");
        },
        p_update() {
            return this.$checkBtnPermission("sys:daily:repiar:edit");
        },
        p_export() {
            return this.$checkBtnPermission("sys:daily:repair:export");
        },
    },
    mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.initData();
    },
    methods: {

        initData() {
            apiGetDemandList(
                {
                    ...this.searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },

        onExport(row){
            this.isLoading = true;
            apiExportDemand({demandId: row.demandId}).then(res=>{
                const blob = new Blob([res.data], {type: "application/xlsx;charset=UTF-8"});
                const fileName =`需求单-${row.wellNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                //释放内存
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
            this.isLoading = false;
            });
        },
        onAdd() {
            this.$refs.demandDialogRef.showDialog("ADD")
        },
        onEdit(row) {
            this.$refs.demandDialogRef.showDialog("DETAIL", row.demandId);
        },
        onDetail(row) {
            this.$refs.detailDialogRef.showDialog(row.demandId);
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.initData();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.initData();
        }

    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
