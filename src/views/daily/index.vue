<template>
    <div class="app-container" style="height:100%;overflow:hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">
                <span>作业列表</span>
            </div>
            <el-form :model="searchForm" label-width="auto">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="井号">
                            <el-input @change="initData(true)" v-model="searchForm.wellNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="作业号">
                            <el-input @change="initData(true)" v-model="searchForm.jobNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="区块">
                            <el-input @change="initData(true)" v-model="searchForm.blocks" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="服务类型">
                            <el-select v-model="searchForm.jobType" @change="initData(true)" clearable placeholder="" style="width: 100%;">
                                <el-option v-for="item in jobTypeList" :key="item.id" :label="item.name" :value="item.id">
                                    {{ item.name }}
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="工程状态">
                            <el-select v-model="searchForm.jobStatus" @change="initData(true)" clearable placeholder="" style="width: 100%;">
                                <el-option label="完井" :value="1"></el-option>
                                <el-option label="施工中" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="开钻日期">
                            <el-date-picker
                                style="width: calc(100%)"
                                placement="bottom-start"
                                clearable
                                @change="initData(true)"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.dateInDaterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="完钻日期">
                            <el-date-picker
                                style="width: calc(100%)"
                                placement="bottom-start"
                                clearable
                                @change="initData(true)"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.dateOutDaterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :data="tableData"
                stripe
                style="margin-top: 10px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column label="井号" prop="wellNumber">
                </el-table-column>
                <el-table-column label="作业号" prop="jobNumber">
                </el-table-column>
                <el-table-column label="区块" prop="blocks">
                </el-table-column>
                <el-table-column label="服务类型" prop="jobType">
                    <template slot-scope="{row}">
                        <span>{{ getJobTypeStr(row.jobType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="总趟数" prop="totalRun">
                    <template slot-scope="{row}">
                        <router-link tag="a" style="color: blue" :to="`/mwd-tool-maintain/deviceinfo?wellNumber=${row.wellNumber}`" v-if="p_MwdRunDeviceInfo">{{ row.totalRun }}</router-link>
                        <span v-else>{{ row.totalRun }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="失效趟数" prop="failureRun">
                </el-table-column>
                <el-table-column label="创建日期" prop="createTime">
                </el-table-column>
                <el-table-column label="开钻日期" prop="dateIn">
                </el-table-column>
                <el-table-column label="完钻日期" prop="dateOut">
                </el-table-column>
                <el-table-column label="工程状态" prop="jobStatus">
                    <template slot-scope="{row}">
                        <el-tag
                            :type="
                                row.jobStatus === 1 ? 'success' : 'danger'
                            "
                        >
                            {{
                                row.jobStatus === 1 ? "完井" : "施工中"
                            }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="160px" prop="deviceType">
                    <template slot-scope="scope">
                        <router-link tag="a" target="_blank" style="color: #004e7e" :to="`/daily/detail?jobid=${scope.row.jobId}`" v-if="p_JobView">
                            查看
                        </router-link>
                        <el-button type="text" @click="onDelete(scope)" v-if="p_JobDelete">
                            删除
                        </el-button>
                        <el-button type="text" @click="onLoadOut(scope)" v-if="scope.row.loadOutId">
                            仪器列表
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetJobList, apiDeleteJob } from "@/api/mwd";
import { Form as ElForm } from "element-ui/types/element-ui";
import getDict from "@/utils/getDict";
import { init } from "echarts";

@Component({})
export default class extends Vue {
    private tableData: any[] = [];
    private total = 1;
    private currentPage = 1;
    private jobTypeList:any[] = []
    private searchForm:any = {
        wellNumber: null,
        jobNumber: null,
        blocks: null,
        jobType: null,
        jobStatus: null,
        dateInDaterange: [],
        dateOutDaterange: [],
    }
    get p_MwdRunDeviceInfo(){
        return this.$checkBtnPermission('sys:mwd:deviceinfo');
    }
    get p_JobDelete(){
        return this.$checkBtnPermission('sys:job:delete');
    }
    get p_JobView(){
        return this.$checkBtnPermission('sys:dailydetail:view');
    }
    async mounted() {
        this.searchForm.wellNumber = this.$route.query.wellNumber;
        this.searchForm.jobNumber = this.$route.query.jobNumber; // 添加这一行，获取作业号查询参数
        [this.jobTypeList] = await getDict([9]);
        await this.initData();
    }
    private getJobTypeStr(key){
        return this.jobTypeList.find(item=>item.id==key)?.name||""
    }
    handleSelect(){
        this.initData()
    }
    async initData(bool=false) {
        if(bool){
            this.currentPage = 1;
        }
        if(this.searchForm.dateInDaterange && this.searchForm.dateInDaterange.length > 0){
            this.searchForm.dateInStart = this.searchForm.dateInDaterange[0];
            this.searchForm.dateInEnd = this.searchForm.dateInDaterange[1];
        }else{
            this.searchForm.dateInStart = undefined;
            this.searchForm.dateInEnd = undefined;
        }
        if(this.searchForm.dateOutDaterange && this.searchForm.dateOutDaterange.length > 0){
            this.searchForm.dateOutStart = this.searchForm.dateOutDaterange[0];
            this.searchForm.dateOutEnd = this.searchForm.dateOutDaterange[1];
        }else{
            this.searchForm.dateOutStart = undefined;
            this.searchForm.dateOutEnd = undefined;
        }
        apiGetJobList(this.searchForm,{ current: this.currentPage, size: 10 }).then(res=>{
            this.tableData = res.data?.data?.records || [];
            this.total = res.data.data.total;
        })
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.initData();
    }
    private onAddWell() {
        this.operType = "ADD";
        this.form = {
            id: undefined,
            wellNumber: "",
            wellCategory: undefined,
            wellType: undefined,
            blocks: "",
            province: "",
            description: "",
        };
        this.showDialog = true;
        this.$nextTick(() => {
            (this.$refs.form as ElForm).clearValidate();
        });
    }

    private onEdit(scope: any) {
        this.operType = "EDIT";
        this.form = Object.assign(this.form, scope.row);
        this.showDialog = true;
    }
    private onView(scope:any){
        this.$router.push(`/daily/detail?jobid=${scope.row.jobId}`)
    }
    private onDelete(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteJob({jobId:scope.row.jobId}).then(() => {
                    this.initData(true);
                });
            })
            .catch(() => {});
    }
    private async onLoadOut(scope: any) {
        const { deviceType, loadOutId } = scope.row;
        const device = deviceType === 16010 ? 'kit' : 'twelllink';
        window.open(`/warehouse/${device}/load/detail?id=${loadOutId}`, '_blank');
    }
}
</script>
