export const DELETE_STATUS = 0;
export const ADD_STATUS = 1;
export const UPDATE_STATUS = 2;
export const wellActivityLength = 20;
export const getWellActivityTmp: any = function () {
    return {
        date: '', startTime: '', endTime: '', startDepth: '', endDepth: '', segLength: '', drillDuration: '',
        drillType: '', drillPressure: '', groundDrillRate: '', md: '', deviation: '', direction: '',
        toolFace: '', note: ''
    }
}
let wellActivity = new Array(wellActivityLength).fill(null).map(() => {
    return getWellActivityTmp()
})
export function getInitWell(): any {
    return {
        date: '',
        weather: '',
        reporter: '',
        operatorCompany: '',
        wellNumber: '',
        drillingMachine: '',
        depth: '',
        casingShoe: '',
        casingSize: '',
        boreholeSize: '',
        designSize: '',
        tripDrillNum: '',
        mudSystem: '',
        density: '',
        viscosity: '',
        oilContent: '',
        solidContent: '',
        sandContent: '',
        temp: '',
        drillMan: '',
        drillNozzle: '',
        drillIntoNum: '',
        drillTime: '',
        screwMan: '',
        screwSize: '',
        screwIntoNum: '',
        screwTime: '',
        knMan: '',
        knSize: '',
        knIntoNum: '',
        knTime: '',
        wpMan: '',
        wpSize: '',
        wpIntoNum: '',
        wpTime: '',
        drillPos: '',
        drillDepth: '',
        remainFootage: '',
        dailyFootage: '',
        odDrillTime: '',
        odCircleTime: '',
        cumDrillTime: '',
        cumCircleTime: '',
        sildeDrillTime: '',
        rcDrillTime: '',
        zeroLength: '',
        ss: '',
        cc: '',
        pp: '',
        friction: '',
        torque: '',
        manager: '',
        deLeader: '',
        de: '',
        ieLeader: '',
        ie: '',
        perArrTime: '',
        equArrTime: '',
        serPro: '',
        bha: '',
        intoTurnTime: '',
        outerTurnTime: '',
        tripDrillTime: '',
        tripCircleTime: '',
        statistic: [
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '定向'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '复合'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '起下钻'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '修理'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '划眼测斜接立柱'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '处理复杂'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '待命'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '其他'
            }
        ],
        activity: wellActivity
    }
}
export const WELL = getInitWell()

export const mwdActivityLength = 20;
export const getMwdActivityTmp: any = function () {
    return {
        startTime: '', endTime: '', depthFrom: '', depthTo: '', activity: '', spp: '', rpm: '', ss: '', torque: '', fr: '', note: ''
    }
}
let mwdActivity = new Array(mwdActivityLength).fill(null).map(() => {
    return getMwdActivityTmp()
})
export function getInitMWD(): any {
    return {
        date: '',
        operator: '',
        kit: '',
        startDate: '',
        constructionDays: '',
        superintendent: '',
        dd1: '',
        dd2: '',
        mwd1: '',
        mwd2: '',
        run: '',
        startDepth: '',
        endDepth: '',
        mudType: '',
        mudDen: '',
        viscosity: '',
        solidContent: '',
        sandContent: '',
        oilContent: '',
        ph: '',
        pv: '',
        vp: '',
        fl: '',
        temp: '',
        imo: '',
        dao: '',
        magEc: '',
        gammaDist: '',
        atBitDist: '',
        orfice: '',
        poppit: '',
        plsw: '',
        nmag: '',
        ndip: '',
        summary: [
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "循环时间"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "电池1"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "电池2"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "探管"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "伽马"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "接收器"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "脉冲"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "无磁钻铤"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "近钻头"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "绝缘短节"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "底部总成"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "弹扶双公扶正器"
            },
            {
                "seqNum": '',
                "start": '',
                "today": '',
                "total": '',
                "type": "定向接头"
            }
        ],
        activity: mwdActivity,
    }
}
export const MWD = getInitMWD()