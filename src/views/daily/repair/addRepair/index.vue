<template>
    <div class="app-container repair-create" style="height: calc(100vh - 50px);position: relative;;height: 100%; padding-left: 0;padding-right: 0; padding-bottom: 0; font-family: PingFangSC, PingFang SC;">
        <!-- <SelectWell v-if="step==='SelectWell'" /> -->
        <SelectDevice :repairForm="repairForm" @changeStep="changeStep" v-show="step==='SelectDevice'" />
        <CreateForm :repairForm="repairForm" @onConfirmAdd="onConfirmAdd" @onEditDevice="onEditDevice" @changeStep="changeStep" v-show="step==='CreateForm'" />
        <AddDevice :repairForm="repairForm" @onCancelEditDevice="onCancelEditDevice" @onConfirmEditDevice="onConfirmEditDevice" :repairDetailForm="repairDetailForm" @changeStep="changeStep" v-show="step==='AddDevice'" />
    </div>
</template>
<script>
import { apiAddRepair } from '@/api/repair';
import AddDevice from './AddDevice.vue';
import CreateForm from './CreateForm.vue';
import SelectDevice from './SelectDevice.vue';
import SelectWell from './SelectWell.vue';
import { Notify } from 'vant';

export default {
    name: "AddRepair",
    components: { SelectWell, SelectDevice, CreateForm, AddDevice },
    data(){
        return {
            step: 'SelectDevice',
            repairForm: {
                toolType: "MWD",
                jobNumber: null,
                kitNumber: null,
                returnDate: new Date().Format("yyyy-MM-dd"),
                customerName: null,
                contactUser: null,
                contactNumber: null,
                deviceGroupList: [],
                deviceList: [],
                repairDetailList: [],
                reportType: ""
            },
            repairDetailForm: {
                deviceType: null,
                serialNumber: null,
                inWellHour: null,
                circulateHrs: null,
                circulateBht: null,
                maxBht: null,
                run: null,
                returnReason: null,
                failureStatus: null,
                failureType: null,
                vssStatus: null,
                note: null
            },
            originDetailForm: null,
        }
    },
    mounted(){
        console.log(this.$route.path)
    },
    methods: {
        initRepairDetailForm(){
            return {
                deviceType: null,
                serialNumber: null,
                inWellHour: null,
                circulateHrs: null,
                circulateBht: null,
                maxBht: null,
                run: null,
                returnReason: null,
                failureStatus: null,
                failureType: null,
                vssStatus: null,
                note: null
            }
        },
        onEditDevice(item = null){
            this.step = "AddDevice";
            this.repairDetailForm = item ? { ...item } : this.initRepairDetailForm();
            this.originDetailForm = item;
        },
        onConfirmEditDevice(){
            if(this.originDetailForm){
                const index = this.repairForm.repairDetailList.findIndex((detail)=>detail === this.originDetailForm);
                Object.assign(this.repairForm.repairDetailList[index], this.repairDetailForm);
            }else{
                this.repairForm.repairDetailList.push({ ...this.repairDetailForm });
            }
            this.step = "CreateForm";
        },
        onCancelEditDevice(){
            this.step = "CreateForm";
        },
        changeStep(step){
            this.step = step;
        },
        onConfirmAdd(){
            if (!this.repairForm.repairDetailList?.length) {
                Notify({ type: 'danger', message: '请添加要维修的仪器' });
                return;
            }
            apiAddRepair(this.repairForm).then(() => {
                Notify({ type: 'success', message: '添加成功' });
                this.initStatus();
            });
        },
        initStatus(){
            this.step = 'SelectDevice';
            this.repairForm = {
                toolType: "MWD",
                jobNumber: null,
                kitNumber: null,
                returnDate: new Date().Format("yyyy-MM-dd"),
                customerName: null,
                contactUser: null,
                contactNumber: null,
                deviceGroupList: [],
                deviceList: [],
                repairDetailList: [],
            };
            this.repairDetailForm = this.initRepairDetailForm();
            this.originDetailForm = null;
        },
    }
}
</script>
<style lang="scss">
.device-list-container{
    .device-box{
     &.selected{
        background: rgb(0, 78, 126);
     }   
    }
}
.repair-create{
    .van-field__label{
        font-weight: bold;
        color: #333333
    }
    .cell-title{
        font-weight: bold;
        font-size: 16px;
        height: 36px;
        line-height: 40px;
        padding-left: 6px;
        background-color: #fff;
        &::before {
            display: inline-block;
            width:6px;
            height: 20px;
            vertical-align: sub;
            background: #004E7E;
            margin-right: 4px;
            content: "";
        }
    }
}
</style>