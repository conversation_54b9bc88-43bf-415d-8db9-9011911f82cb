<template>
    <div>
        <div style="height: calc(100vh - 100px);overflow-y: auto;">
            <VanSearch label="井号" @change="onWellChange" input-align="right" type="WELL_LIST_WITH_JOBS" v-model="repairForm.wellNumber" placeholder="选择井" />
            <VanSelect @change="onJobChange" v-model="repairForm.jobNumber" :options="jobList" value-key="jobNumber" label-key="jobNumber" input-align="right" name="作业号" label="作业号" placeholder="作业号" />
            <van-cell-group style="margin-top: 6px;">
                <div class="cell-title">仪器类型</div>
                <van-collapse v-model="activeNames">
                    <van-collapse-item :title="item.invName" :name="item.invName" v-for="item in repairForm.deviceGroupList" :key="item.invName">
                        <template #title>
                            <span style="font-weight: bold;font-size: 14px;">{{ item.invName }}</span>
                        </template>
                        <el-row :gutter="20">
                            <el-col :span="12" v-for="device in item.deviceList" :key="device.serialNumber">
                                <div class="device-box" :class="{selected: device.selected}" @click="onClickDevice(device)">
                                    <span>
                                        {{ device.serialNumber }}
                                    </span>
                                </div>
                            </el-col>
                        </el-row>
                    </van-collapse-item>
                </van-collapse>
            </van-cell-group>
        </div>
        
        <el-button style="position: fixed; bottom: -10px;width: calc(100% - 20px);left: 50%;transform: translate(-50%, -50%)" type="primary" @click="onNext">下一步</el-button>
    </div>
</template>
<script>
import { apiGetDailyReportBaseinfo, apiGetDailyReportInfo, apiJobInfo } from "@/api/mwd";
import { apiGetToolInfoFromReport } from "@/api/repair";
import VanSearch from "@/components/mobile/VanSearch";
import VanSelect from "@/components/mobile/VanSelect";
import { Notify } from "vant";
export default {
    name: "MobileRepairSelectDevice",
    components: {
        VanSearch,
        VanSelect
    },
    data(){
        return {
            jobNumber: null,
            jobId: null,
            jobList: [],
            activeNames: [],
            deviceGroupList: []
        }
    },
    props: {
        repairForm: Object
    },
    methods: {
        getDataBySerialNumber(serialNumber) {
            return new Promise((resolve, reject)=>{
                apiGetToolInfoFromReport({
                    wellNumber: this.repairForm.wellNumber,
                    jobNumber: this.repairForm.jobNumber,
                    reportType: this.repairForm.reportType === "RSS_TOOLSONSITE" ? "RSS_REPORT" : undefined,
                    serialNumber: serialNumber,
                }).then((res) => {
                    resolve(res.data?.data || {})
                }).catch(err=>{
                    reject(err)
                });
            })
        },
        async onNext(){
            if(!(this.repairForm.wellNumber&&this.repairForm.jobNumber)){
                Notify({ type: 'danger', message: '请选择井号和作业号' });
                return;
            }
            const deviceList = this.repairForm.deviceGroupList.reduce((acc, cur) => {
                return acc.concat(cur.deviceList.filter((item) => item.selected));
            }, []);
            const dataList = await Promise.all(deviceList.map((item)=>this.getDataBySerialNumber(item.serialNumber)))
            this.repairForm.repairDetailList = deviceList.map((item,index)=>{
                const dataItem = dataList[index];
                return {
                    invName: item.invName,
                    serialNumber: item.serialNumber,
                    inWellHour: dataItem.inWellHour,
                    circulateBht: dataItem.circulateBht,
                    maxBht: dataItem.maxBht,
                    circulateHrs: dataItem.circulateHrs,
                    run: dataItem.run, // 这里不是在编辑状态, 保持原数据即可
                    returnReason: null,
                    failureStatus: null,
                    failureType: null,
                    vssStatus: null,
                    note: null
                }
            });
            this.$emit("changeStep", "CreateForm")
        },
        onWellChange(item){
            this.jobList = item.jobList;
            this.repairForm.jobNumber = this.jobList[0].jobNumber;
            this.repairForm.jobId = this.jobList[0].jobId;
            this.getDeviceList();
        },
        onJobChange(item){
            this.repairForm.jobNumber = item.jobNumber;
            this.repairForm.jobId = item.jobId;
            this.getDeviceList();
        },
        async getDeviceList(){
            // 获取job信息中的kitNumber
            const baseInfo = await apiJobInfo({jobId: this.repairForm.jobId}).then(res=>{
                return res.data?.data || {};
            })
            this.repairForm.kitNumber = baseInfo.kitNumber;
            const reportType = baseInfo.jobType == 8007 ? "RSS_TOOLSONSITE" : "TOOLSONSITE";
            this.repairForm.reportType = reportType;
            // 获取现场工具列表
            const baseInfoList = await apiGetDailyReportBaseinfo({ jobId: this.repairForm.jobId, reportType }).then((res) => {
                return res.data?.data || [];
            });
            const reportId = baseInfoList[0].id;
            const toolList = await apiGetDailyReportInfo({ reportType, reportId }).then((res) => {
                return res.data?.data?.toolList || [];
            })
            // 工具列表按invName分组
            const deviceGroupList = [];
            toolList.forEach((tool) => {
                if(!tool.serialNumber) return;
                tool.selected = false;
                const deviceGroup = deviceGroupList.find((item) => item.invName === tool.invName);
                if (deviceGroup) {
                    deviceGroup.deviceList.push(tool);
                } else {
                    deviceGroupList.push({
                        invName: tool.invName,
                        deviceList: [tool],
                    });
                }
            });
            this.repairForm.deviceGroupList = deviceGroupList;
            // 默认全部展开
            this.activeNames = deviceGroupList.map((item) => item.invName);
        },
        onClickDevice(device){
            device.selected = !device.selected;
        }
    }
}
</script>
<style lang="scss" scoped>
.device-box {
    text-align: center;
    border-radius: 2px;
    margin-bottom: 12px;
    padding: 6px;
    color: #303133;
    background-color: #F3F5F9;
    &.selected{
        color: #409EFF;
        background-color: #D9ECFF;
    }
}
</style>