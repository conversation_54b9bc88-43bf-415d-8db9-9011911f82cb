<template>
    <div>
        <div style="height: calc(100vh - 126px);overflow-y: auto;">
            <van-cell-group>
                <van-form label-width="100px">
                    <van-field v-model="repairDetailForm.invName" input-align="right" name="仪器名称" label="仪器名称" placeholder="仪器名称"></van-field>
                    <van-field v-model="repairDetailForm.serialNumber" @blur="onSerialNumberChange" input-align="right" name="序列号" label="序列号" placeholder="序列号"></van-field>
                    <van-field v-model="repairDetailForm.inWellHour" input-align="right" name="入井时间" label="入井时间" placeholder="入井时间"></van-field>
                    <van-field v-model="repairDetailForm.circulateHrs" input-align="right" name="循环时间" label="循环时间" placeholder="循环时间"></van-field>
                    <van-field v-model="repairDetailForm.circulateBht" input-align="right" name="循环温度" label="循环温度" placeholder="循环温度"></van-field>
                    <van-field v-model="repairDetailForm.maxBht" input-align="right" name="最高温度" label="最高温度" placeholder="最高温度"></van-field>
                    <van-field v-model="repairDetailForm.run" @blur="onRunChange" input-align="right" name="趟次" label="趟次" placeholder="多个趟次请用逗号隔开"></van-field>
                    <van-field v-model="repairDetailForm.returnReason" type="textarea" rows="1" autosize input-align="right" name="返回原因" label="返回原因" placeholder="返回原因"></van-field>
                    <VanSelect
                        v-model="repairDetailForm.failureStatus"
                        :options="[{value:1, label:'是'}, {value:0, label:'否'}]" 
                        value-key="value" 
                        label-key="label" 
                        input-align="right" 
                        name="是否存在故障" 
                        label="是否存在故障" 
                        placeholder="是否存在故障"
                    />
                    <van-field v-model="repairDetailForm.failureDescription" type="textarea" rows="1" autosize input-align="right" name="故障描述" label="故障描述" placeholder="故障描述"></van-field>
                    <van-field v-model="repairDetailForm.vssStatus" input-align="right" name="是否有震动超标" label="是否有震动超标" placeholder="是否有震动超标"></van-field>
                    <van-field v-model="repairDetailForm.note" type="textarea" rows="1" autosize input-align="right" name="备注" label="备注" placeholder="备注"></van-field>
                </van-form>
            </van-cell-group>
        </div>
        <div style="position: fixed; width: 100%; bottom: 0; padding: 10px;background: white;">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-button style="width: 100%;" @click="onLast">返回</el-button>
                </el-col>
                <el-col :span="16">
                    <el-button type="primary" style="width: 100%;" @click="onConfirm">确定</el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>
import { apiGetToolInfoFromReport } from "@/api/repair";
import VanSelect from "@/components/mobile/VanSelect";
export default {
    name: "AddRepairAddDevice",
    components: { VanSelect },
    data(){
        return {
            deviceTypeList: []
        }
    },
    props: {
        repairDetailForm: {
            type: Object,
            default: () => ({})
        },
        repairForm: {
            type: Object,
            default: () => ({})
        }
    },
    methods: {
        onLast(){
            this.$emit("onCancelEditDevice")
        },
        onConfirm(){
            this.$emit("onConfirmEditDevice")
        },
        onDeviceTypeChange(deviceType){
            console.log(deviceType)
        },
        getDataBySerialNumber(serialNumber) {
            return new Promise((resolve, reject)=>{
                apiGetToolInfoFromReport({
                    wellNumber: this.repairForm.wellNumber,
                    jobNumber: this.repairForm.jobNumber,
                    reportType: this.repairForm.reportType === "RSS_TOOLSONSITE" ? "RSS_REPORT" : undefined,
                    serialNumber: serialNumber,
                }).then((res) => {
                    resolve(res.data?.data || {})
                }).catch(err=>{
                reject(err)
                });
            })
        },
        onSerialNumberChange() {
            const serialNumber = this.repairDetailForm.serialNumber;
            if(!serialNumber){
                return;
            }
            this.getDataBySerialNumber(serialNumber).then(data=>{
                this.repairDetailForm.invName = this.repairDetailForm.invName || data.invName;
                this.repairDetailForm.inWellHour = this.repairDetailForm.inWellHour || data.inWellHour;
                this.repairDetailForm.circulateHrs = this.repairDetailForm.circulateHrs || data.circulateHrs;
                this.repairDetailForm.circulateBht = this.repairDetailForm.circulateBht || data.maxBht;
                this.repairDetailForm.maxBht = this.repairDetailForm.maxBht || data.maxBht;
                this.repairDetailForm.run = data.run;
            })
        },
        splitString(input) {
            // 使用正则表达式匹配空格、中文逗号或英文逗号
            return input.split(/[\s,，]+/).filter(Boolean);
        },
        onRunChange() {
            const run = this.repairDetailForm.run;
            if(!run){
                return;
            }
            const runList = this.splitString(run);
            this.repairDetailForm.run = runList.join(",");
        }
    }
}
</script>
<style lang="scss" scoped>
</style>