<template>
    <div>
        <div style="height: calc(100vh - 126px);overflow-y: auto;">
            <div class="top-well-container">
                <svg-icon width="22" height="22" name="ketouji"></svg-icon>
                <span style="margin-left: 6px;">{{ repairForm.wellNumber }}</span>
            </div>
            
            <van-cell-group style="margin-top: 10px;">
                <div class="cell-title">基本类型</div>
                <van-form>
                    <VanSelect v-model="repairForm.toolType" :options="toolTypeList" value-key="value" label-key="label" input-align="right" name="返修类型" label="返修类型" placeholder="返修类型" />
                    <van-field v-model="repairForm.jobNumber" input-align="right" name="作业号" label="作业号" placeholder="作业号"></van-field>
                    <van-field v-model="repairForm.kitNumber" input-align="right" name="kit箱" label="kit箱" placeholder="kit箱"></van-field>
                    <VanDatePicker v-model="repairForm.returnDate" input-align="right" name="返修日期" label="返修日期" placeholder="返修日期"></VanDatePicker>
                    <van-field v-model="repairForm.customerName" input-align="right" name="客户名称" label="客户名称" placeholder="客户名称"></van-field>
                    <van-field v-model="repairForm.contactUser" input-align="right" name="现场联系人" label="现场联系人" placeholder="现场联系人"></van-field>
                    <van-field v-model="repairForm.contactNumber" input-align="right" name="联系方式" label="联系方式" placeholder="联系方式"></van-field>
                </van-form>
            </van-cell-group>
            
            <van-cell-group style="margin-top: 10px;">
                <div class="cell-title">维修仪器 <el-button type="text" icon="el-icon-plus" style="float:right;margin-right: 16px" @click="onAddDevice">新增仪器</el-button></div>
                <div class="device-info-container" v-for="item in repairForm.repairDetailList" :key="item.serialNumber">
                    <div class="title-container">
                        <span class="device-title">
                            <span class="device-svg">
                                <svg-icon width="10" height="10" name="device2"></svg-icon>
                            </span>
                            <span class="device-name">
                                {{ item.invName }} {{ item.serialNumber }}
                            </span>
                        </span>
                        <span class="operators">
                            <el-button type="text" size="medium" icon="el-icon-delete" @click="onDelete(item)"></el-button>
                            <el-button type="text" icon="el-icon-edit-outline" @click="onEdit(item)"></el-button>
                        </span>
                    </div>
                    <div class="info-container">
                        <el-row :gutter="10">
                            <el-col :span="12">
                                <div class="name">入井时间</div>
                                <div class="content">{{ item.inWellHour }}</div>
                            </el-col>
                            <el-col :span="12">
                                <div class="name">循环时间</div>
                                <div class="content">{{ item.circulateHrs }}</div>
                            </el-col>
                            <el-col :span="12">
                                <div class="name">循环温度</div>
                                <div class="content">{{ item.circulateBht }}</div>
                            </el-col>
                            <el-col :span="12">
                                <div class="name">最高温度</div>
                                <div class="content">{{ item.maxBht }}</div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">趟次</div>
                                <div class="content">{{ item.run }}</div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">返回原因</div>
                                <div class="content">{{ item.returnReason }}</div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">是否存在故障</div>
                                <div class="content">
                                    <span v-if="item.failureStatus==1">是</span>
                                    <span v-if="item.failureStatus==0">否</span>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">故障描述</div>
                                <div class="content">{{ item.failureDescription }}</div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">是否有震动超标</div>
                                <div class="content">{{ item.vssStatus }}</div>
                            </el-col>
                            <el-col :span="24">
                                <div class="name">备注</div>
                                <div class="content">{{ item.note }}</div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </van-cell-group>
        </div>
        <div style="position: fixed; width: 100%; bottom: 0; padding: 10px;background: white;">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-button style="width: 100%;" @click="onLast">取消</el-button>
                </el-col>
                <el-col :span="16">
                    <el-button type="primary" style="width: 100%;" @click="onConfirmAdd">提交</el-button>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script>
import VanDatePicker from "@/components/mobile/VanDatePicker";
import VanSelect from "@/components/mobile/VanSelect";
export default {
    name: "AddRepairCreateForm",
    components: { VanDatePicker, VanSelect },
    data(){
        return {
            toolTypeList: [
                { value: "MWD", label: "MWD" }, 
                { value: "DOWNHOLE", label: "井下工具"}
            ]
        }
    },
    props: {
        repairForm: Object
    },
    methods: {
        onConfirmAdd(){
            this.$emit("onConfirmAdd")
        },
        onLast(){
            this.$emit("changeStep", "SelectDevice")
        },
        onAddDevice(){
            this.$emit("onEditDevice", null)
        },
        onDelete(item){
            this.repairForm.repairDetailList = this.repairForm.repairDetailList.filter(detail=>detail!==item)
        },
        onEdit(item){
            this.$emit("onEditDevice", item)
        },
    }
}
</script>
<style lang="scss" scoped>
.top-well-container{
    color: white;
    background-color: #DCDFE6;
    font-size: 16px;
    height: 34px;
    line-height: 34px;
    text-align: center;
}
.device-info-container{
    color: #303133;
    padding: 10px 14px;
    .title-container{
        font-size: 14px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .device-title{
            flex: 1;
            display: flex;
            .device-name{
                flex: 1;
                margin-left: 6px;
            }
            .device-svg{
                width: 18px;
                height: 18px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                background: #606266;
                color: #fff;
                border-radius: 50%;
            }
        }
        .operators{
            width: 60px;
            .el-button--text{
                padding: 0;
            }
        }

    }
    .info-container{
        background: #F5F7FA;
        padding: 9px 17px;
        margin: 10px;
        font-size: 12px;
        .el-col{
            margin-bottom: 10px;
            display: flex;
        }
        .name{
            width: 90px;
            color: #909399;
        }

        .content{
            flex: 1;
            color: #333;
            font-weight: bold;
            word-wrap: break-word;
            word-break: break-all;
        }
    }
    &.selected{
        background: #EBF5FF;
        .device-title{
            color: #409EFF;
            .device-svg{
                background: #409EFF;
            }
        }
        .info-container{
            background: #F5FAFF;
            .name{
                color: #909399;
            }
            .content{
                color: #409EFF;
            }
        }
    }
}
</style>