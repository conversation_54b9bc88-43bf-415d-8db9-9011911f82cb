<template>
    <div>
        <div>
            <div>
                <span>
                    选择井号
                </span>
            </div>
        </div>
        <div>
            <div>
                <span>选择仪器</span>
            </div>
            <div>
                <div class="device-list-container">
                    <div class="device-box" :class="{selected: item.isSelected}" v-for="item in deviceList" :key="item.id">
                        <div>
                            {{ item.name }}
                        </div>
                        <div>
                            {{ item.serialNumber }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "SelectWell",
    data(){
        return {
            deviceList: [
                {id: 1, name: "脉冲模块", serialNumber: "PG0202", isSelected: false},
                {id: 2, name: "脉冲模块", serialNumber: "PG0202", isSelected: false},
                {id: 3, name: "脉冲模块", serialNumber: "PG0202", isSelected: false},
            ]
        }
    }
}
</script>