<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="返修单"
        width="80%"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            :rules="detailFormRules"
            label-width="100px"
        >
            <el-row>
                <el-col :span="8">
                    <el-form-item label="返修单类型: " prop="toolType">
                        <el-select
                            v-model="detailForm.toolType"
                            style="width: 80%"
                        >
                            <el-option label="MWD" value="MWD"></el-option>
                            <el-option
                                label="井下工具"
                                value="UNDER_WELL"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="井号: " prop="wellNumber">
                        <el-select
                            v-model="detailForm.wellNumber"
                            filterable
                            remote
                            placeholder=""
                            :remote-method="remoteWellNumberMethod"
                            style="width:80%"
                        >
                            <el-option
                                v-for="item in fuzzyWellNumberList"
                                :key="item.value"
                                :label="item.value"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号: " prop="jobNumber">
                        <el-input
                            style="width: 80%"
                            v-model="detailForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <el-input
                            style="width: 80%"
                            v-model="detailForm.kitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返修日期: " prop="returnDate">
                        <el-date-picker
                            style="width: 80%"
                            v-model="detailForm.returnDate"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            type="date"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称: ">
                        <el-input
                            style="width: 80%"
                            v-model="detailForm.customerName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人: ">
                        <el-input
                            style="width: 80%"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="联系方式: ">
                        <el-input
                            style="width: 80%"
                            v-model="detailForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.repairDetailList"
            style="margin-bottom: 20px"
            default-expand-all
            :header-cell-style="commmonTableHeaderCellStyle"
        >
            <el-table-column label="仪器名称" prop="invName">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.invName }}
                    </span>
                    <el-input
                        v-else
                        size="small"
                        v-model="scope.row.invName"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="序列号">
                <template slot="header">序列号</template>
                <template slot-scope="scope">
                    <div v-if="editObject.editRowIndex !== scope.$index">
                        <span>{{ scope.row.serialNumber }}</span>
                    </div>
                    <el-autocomplete 
                        v-else
                        v-model="scope.row.serialNumber" 
                        :fetch-suggestions="querySearchValidSN"
                        style="width:100%"
                        @change="onSerialNumberChange(scope)" 
                    ></el-autocomplete>
                </template>
            </el-table-column>
            <el-table-column label="入井时间" prop="inWellHour">
                <template slot="header">
                    入井时间
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.inWellHour }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.inWellHour"
                    ></InputNumber>
                </template>
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
                <template slot="header">
                    循环时间
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateHrs }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.circulateHrs"
                    ></InputNumber>
                </template>
            </el-table-column>
            <el-table-column prop="circulateBht">
                <template slot="header">
                    循环温度
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateBht }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.circulateBht"
                    ></InputNumber>
                </template>
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht">
                <template slot="header">
                    最高温度
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.maxBht }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.maxBht"
                    ></InputNumber>
                </template>
            </el-table-column>

            <el-table-column label="run" prop="run" width="190px">
                <template slot="header">
                    趟次
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.run }}
                    </span>
                    <template v-else>
                        <el-select collapse-tags style="width:80%" multiple clearable size="small" v-model="scope.row.run">
                            <el-option v-for="item in runList" :key="item.id" :value="item.run" :label="item.run">
                            </el-option>
                        </el-select>
                        <i v-if="operType!=='ADD'" style="margin-left: 4px" class="el-icon-info" title="提供的趟次列表仅做编辑时使用，不具有参考性"></i>
                    </template>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.returnReason }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.returnReason"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="是否存在故障" prop="failureStatus">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <CustomTag v-if="scope.row.failureStatus==1" tagType="NEGATIVE_TRUE_FALSE" :tagValue="true" />
                        <CustomTag v-if="scope.row.failureStatus==0" tagType="NEGATIVE_TRUE_FALSE" :tagValue="false" />
                    </span>
                    <el-select
                        v-else
                        style="width: 100%"
                        v-model="scope.row.failureStatus"
                    >
                        <el-option label="是" :value="1"></el-option>
                        <el-option label="否" :value="0"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="故障描述" prop="failureDescription">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.failureDescription }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.failureDescription"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="是否有震动超标" width="120px" prop="vssStatus">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.vssStatus }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.vssStatus"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.note }}
                    </span>
                    <el-input v-else v-model="scope.row.note"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="float: right"
            type="primary"
            @click="onAddRow"
        >
            添加元素
        </el-button>
        <span slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onHandleSave" :loading="isConfirmLoading">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { Form as ElForm } from "element-ui/types/element-ui";
import {
    apiAddRepair,
    apiGetRepairInfo,
    apiUpdateRepair,
    apiGetToolInfoFromReport,
} from "@/api/repair";
import { apiCheckSerialNumber, apiGetFuzzySerialNumberList } from "@/api/tool-mantain";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { apiJobInfo } from "@/api/mwd";
import { splitRun } from "@/utils/constant.mwd";

interface IForm {
    jobNumber?: string;
    wellNumber?: string;
    kitNumber?: string;
    returnDate?: string;
    customerName?: string;
    contactUser?: string;
    repairDetailList: IRepairDetail[];
    toolType: string;
    contactNumber?: string;
}

interface IRepairDetail {
    invName?: string;
    serialNumber?: string;
    inWellHour?: number;
    circulateBht?: number;
    maxBht?: number;
    circulateHrs?: number;
    run?: number;
    returnReason?: string;
    failureStatus?: String,
    failureDescription?: String,
    vssStatus?: String,
    note?: string;
}
@Component({})
export default class extends Vue {
    detailFormRules = {
        wellNumber: [{ required: true, message: "*", trigger: "blur" }],
        jobNumber: [{ required: true, message: "*", trigger: "blur" }],
    };
    private isConfirmLoading = false;
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private operType = "ADD";
    private detailDialogVisible = false;
    private detailForm: IForm = this.getInitForm();
    private runList: any[] = []
    private fuzzyWellNumberList: any[] = [];
    
    @Prop({ default: "" }) private reportType!: String;
    private getInitForm(): IForm {
        return {
            jobNumber: "",
            wellNumber: "",
            kitNumber: "",
            contactUser: "",
            returnDate: "",
            customerName: "",
            repairDetailList: [],
            toolType: "",
            contactNumber: ""
        };
    }
    private getInitDetailItem(): IRepairDetail {
        return {
            invName: undefined,
            serialNumber: undefined,
            inWellHour: undefined,
            circulateBht: undefined,
            maxBht: undefined,
            circulateHrs: undefined,
            run: undefined,
            returnReason: undefined,
            failureStatus: undefined,
            failureDescription: undefined,
            vssStatus: undefined,
            note: undefined,
        };
    }

    onHandleSave() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        if (this.detailForm.repairDetailList.length == 0) {
            this.$message.error("请先添加返修工具");
            return;
        }
        (this.$refs["detailForm"] as ElForm).validate((valid) => {
            if (!valid) return;
            this.saveEditData();
        });
    }

    saveEditData() {
        //save data
        if (!this.detailForm.repairDetailList?.length) {
            this.$message.error("请添加维修条目");
            return;
        }
        this.isConfirmLoading = true;
        if (this.operType === "ADD") {
            //add
            apiAddRepair(this.detailForm).then(() => {
                this.detailDialogVisible = false;
                this.$emit("getRepairList");
                this.$emit("onAddRepairSuccess");
                this.$message.success("添加成功");
            }).finally(() =>{
                this.isConfirmLoading = false;
            });
        } else {
            //update
            apiUpdateRepair(this.detailForm).then((res) => {
                this.$message.success("更新成功");
                this.$emit("getRepairList");
                this.detailDialogVisible = false;
            }).finally(() =>{
                this.isConfirmLoading = false;
            });
        }
    }

    private async showDialog(type: string, { toolType, repairId, jobId, toolList } : any) {
        this.editObject.editRowIndex = -1;
        this.operType = type;
        this.detailForm = this.getInitForm();
        this.runList = Array.from({ length: 20 }).map((_,index)=>({id:index, run: String(index+1)}))
        if (type === "ADD") {
            if(jobId){
                await apiJobInfo({jobId}).then(res=>{
                    const info = res.data.data;
                    this.detailForm.kitNumber = info.kitNumber;
                    this.detailForm.jobNumber = info.jobNumber;
                    this.detailForm.wellNumber = info.wellNumber;
                })
            }
            this.detailForm.returnDate = new Date().Format("yyyy-MM-dd");
            if(toolType){
                this.detailForm.toolType = toolType;
            }
            if(toolList){
                const filledList = await Promise.all(toolList.map((tl)=>this.getDataBySerialNumber(tl.serialNumber)))
                this.detailForm.repairDetailList = toolList.map((item,index)=>{
                    const filledItem = filledList[index];
                    return {
                        invName: item.invName,
                        serialNumber: item.serialNumber,
                        inWellHour: (filledItem as any).inWellHour,
                        circulateBht: (filledItem as any).circulateBht,
                        maxBht: (filledItem as any).maxBht,
                        circulateHrs: (filledItem as any).circulateHrs,
                        run: (filledItem as any).run, // 这里不是在编辑状态, 保持原数据即可
                    }
                });
            }
        } else {
            // 这个Detail我也不知道有多少趟次 - 
            await apiGetRepairInfo({ repairId: repairId }).then((res) => {
                this.detailForm = res.data.data;
            });
        }
        this.detailDialogVisible = true;
    }

    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.detailForm.repairDetailList.push(this.getInitDetailItem());
        this.editObject.editRow = true;
        this.editObject.editRowIndex =
            this.detailForm.repairDetailList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        
        // 下面这两行顺序不能反，保证只有编辑状态的行数据中的run是Array, 其它情况全部为String
        this.preObject = { ...scope.row };
        if(scope.row.run){
            scope.row.run = String(scope.row.run).split(',')
        }
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.repairDetailList.splice(scope.$index, 1);
    }

    private async onSaveRow(scope: any) {
        // if(scope.row.serialNumber){
        //     if(await this.onValidateSN(scope)){
        //         this.editObject.editRowIndex = -1;
        //     }
        // }else{
        //     this.editObject.editRowIndex = -1;
        // }
        if(Array.isArray(scope.row.run)){
            scope.row.run = scope.row.run.join(',')
        }
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.$set(this.detailForm.repairDetailList, scope.$index, {
                ...this.preObject,
            });
        } else {
            this.detailForm.repairDetailList.pop();
        }
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
    private querySearchValidSN(query:string, cb:any){
        apiGetFuzzySerialNumberList({serialNumber:query}).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item})))
        })
    }
    private async onValidateSN(scope){
        const serialNumber = scope.row.serialNumber;
        const res = await apiCheckSerialNumber({ serialNumber });
        const code = res.data.data;
        const valid = code == 1 || code == 0;
        if(!valid){
            this.$message.error("无效序列号")
        }
        return valid
    }
    private getDataBySerialNumber(serialNumber) {
      return new Promise<any>((resolve, reject)=>{
        apiGetToolInfoFromReport({
          wellNumber: this.detailForm.wellNumber,
          jobNumber: this.detailForm.jobNumber,
          reportType: this.reportType || undefined,
          serialNumber: serialNumber,
        }).then((res) => {
          resolve(res.data?.data || {})
        }).catch(err=>{
          reject(err)
        });
      })
    }
    private onSerialNumberChange(scope: any) {
        const serialNumber = scope.row.serialNumber;
        if(!serialNumber){
            return;
        }
        this.getDataBySerialNumber(scope.row.serialNumber).then(data=>{
            scope.row.invName = scope.row.invName || data.invName;
            scope.row.inWellHour = scope.row.inWellHour || (data as any).inWellHour;
            scope.row.circulateHrs = scope.row.circulateHrs || (data as any).circulateHrs;
            scope.row.circulateBht = scope.row.circulateBht || (data as any).maxBht;
            scope.row.maxBht = scope.row.maxBht || (data as any).maxBht;
            // 这里是在编辑状态，要提前做成Array
            scope.row.run = splitRun((data as any).run);
        })
    }
    private remoteWellNumberMethod(query){
        const form = new FormData();
        form.append("wellNumberKey", query || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            this.fuzzyWellNumberList = data.map(item=>({value:item.wellNumber}))
        })
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
