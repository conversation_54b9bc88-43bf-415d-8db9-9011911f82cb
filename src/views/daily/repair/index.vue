<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">返修单管理</div>
                <!-- <el-button
                    style="float: right;"
                    type="primary"
                    @click="onAddRepairOrder"
                    v-if="p_add"
                >
                    新增返修单
                </el-button> -->
            </div>
            <el-form :model="searchForm" label-width="auto" style="margin-top: 10px; width: 100%; max-width: 500px;">
                <el-row>
                    <el-col :span="10">
                        <el-form-item label="返修单类型">
                            <el-select v-model="searchForm.toolType" @change="getRepairList">
                                <el-option label="MWD" value="MWD"></el-option>
                                <el-option
                                    label="井下工具"
                                    value="UNDER_WELL"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="14">
                        <el-form-item label="井号">
                            <el-input @change="getRepairList" v-model="searchForm.wellNumber" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
            >
                <el-table-column
                    width="190"
                    label="返修单号"
                    prop="repairCode"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                    width="80"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    align="center"
                ></el-table-column>
                <!-- <el-table-column
                    label="kit箱"
                    prop="kitNumber"
                ></el-table-column> -->
                <el-table-column
                    label="返修日期"
                    prop="returnDate"
                    sortable="custom"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="客户名称"
                    prop="customerName"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="联系方式"
                    prop="contactNumber"
                    width="110"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="创建人"
                    prop="createdBy"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="createTime"
                    align="center"
                    width="100"
                    sortable="custom"
                ></el-table-column>
                <!-- <el-table-column
                    label="状态"
                    prop="receiveStatus"
                    width="100"
                    align="center"
                    sortable="custom"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="RECEIVE_STATUS" v-if="scope.row.receiveStatus == 'RECEIVED'" tagValue="RECEIVED" />
                        <CustomTag tagType="RECEIVE_STATUS" v-else tagValue="SUBMITTED" />
                    </template>
                </el-table-column> -->
                <!-- <el-table-column
                    label="接收人"
                    prop="receivedBy"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="接收时间"
                    prop="receiveDate"
                    align="center"
                    width="100"
                ></el-table-column> -->
                <el-table-column
                    label="操作"
                    width="180"
                    align="center"
                    v-if="p_update || p_delete || p_export"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_update"
                            type="text"
                            @click="onDetail(scope.row)"
                        >
                            详情
                        </el-button>
                        <el-button
                            v-if="p_delete"
                            type="text"
                            @click="onDelete(scope.row)"
                        >
                            删除
                        </el-button>
                        <el-button
                            v-if="p_export"
                            type="text"
                            @click="onExport(scope.row)"
                        >
                            导出Excel
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <RepairDialog
            @getRepairList="getRepairList"
            ref="RepairDialog"
        />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetRepairList, apiDeleteRepair } from "@/api/repair";
import RepairDialog from "./repairDialog.vue";
import { apiExportRepairExcel } from "@/api/mwd";
const ORDER_TYPE_MAP: any = {
    descending: "desc",
    ascending: "asc",
};
@Component({
    components: { RepairDialog },
})
export default class extends Vue {
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private tableData: any = [];
    private searchForm = {
        toolType: "MWD",
        wellNumber: "",
    }
    get p_add() {
        return this.$checkBtnPermission("sys:daily:repiar:add");
    }
    get p_delete() {
        return this.$checkBtnPermission("sys:daily:repiar:delete");
    }
    get p_update() {
        return this.$checkBtnPermission("sys:daily:repiar:edit");
    }
    get p_export() {
        return this.$checkBtnPermission("sys:daily:repair:export");
    }
    mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.getRepairList();
    }

    getRepairList() {
        apiGetRepairList(
            {
                ...this.searchForm,
                orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
            },
            {
                current: this.currentPage,
                size: this.pageSize,
            }
        )
            .then((res) => {
                const data = res.data.data || {};
                this.tableData = data.records || [];
                this.total = data.total;
            })
            .catch((err) => {});
    }

    onExport(row: any){
        const form = new FormData();
        form.append("repairId", row.repairId);
        this.isLoading = true;
        apiExportRepairExcel(form).then(res=>{
            const blob = new Blob([res.data], {type: "application/xlsx;charset=UTF-8"});
            const fileName =`${row.repairCode}.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            //释放内存
            link.remove();
            window.URL.revokeObjectURL(link.href);
        }).finally(()=>{
          this.isLoading = false;
        });
    }
    onAddRepairOrder() {
        this.$router.push("/repair/add")
    }
    onDetail(row: any) {
        (this.$refs.RepairDialog as any).showDialog("DETAIL", { repairId: row.repairId});
    }

    onDelete(row: any) {
        this.$confirm("确认删除吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            const form = new FormData();
            form.append("repairId", row.repairId);
            apiDeleteRepair(form)
                .then(() => {
                    this.$message.success("操作成功！");
                    this.currentPage = 1;
                    this.getRepairList();
                })
                .catch((err) => {});
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getRepairList();
    }
    private handleSortChange({ prop, order }: any) {
        this.orderType = order && ORDER_TYPE_MAP[order];
        this.orderBy = prop;
        this.getRepairList();
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
