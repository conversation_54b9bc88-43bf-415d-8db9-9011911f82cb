<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="demand-dialog"
        :close-on-click-modal="false"
        title="调拨单"
        width="1200px"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            :rules="detailFormRules"
            label-width="100px"
        >
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发出井号: " prop="fromWellNumber">
                        <FuzzySelect placeholder="" type="WELL_NUMBER" v-model="detailForm.fromWellNumber"></FuzzySelect>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出Kit箱: " prop="fromKitNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.fromKitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出时间: " prop="sendDate">
                        <el-date-picker
                            v-model="detailForm.sendDate"
                            style="width: calc(100%)"
                            type="date"
                            placeholder=""
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发往井号: " prop="toWellNumber">
                        <FuzzySelect placeholder="" type="WELL_NUMBER" v-model="detailForm.toWellNumber"></FuzzySelect>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发往Kit箱: " prop="toKitNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.toKitNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <el-input
                            style="width: 100%"
                            v-model="detailForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.detailList"
            style="margin-bottom: 20px"
            default-expand-all
            :header-cell-style="commmonTableHeaderCellStyle"
        >
            <el-table-column label="物品名称" prop="itemName" width="220" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.itemName }}
                    </span>
                    <el-input
                        v-else
                        size="small"
                        v-model="scope.row.itemName"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="220" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.serialNumber }}
                    </span>
                    <el-input
                        v-else
                        size="small"
                        v-model="scope.row.serialNumber"
                        @change="onSerialNumberChange(scope.row)"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="数量" prop="amount" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="scope.row.serialNumber||editObject.editRowIndex !== scope.$index">
                        {{ scope.row.amount }}
                    </span>
                    <InputNumber
                        v-else
                        size="small"
                        controls-position="right"
                        style="width: 100%;"
                        v-model="scope.row.amount"
                    ></InputNumber>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.note }}
                    </span>
                    <el-input v-else v-model="scope.row.note"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="float: right"
            type="primary"
            @click="onAddRow"
        >
            添加元素
        </el-button>
        <span slot="footer" class="dialog-footer">
            <el-button @click="isDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="onHandleSave(true)" :loading="isConfirmLoading">确定并发送邮件</el-button>
            <el-button type="primary" @click="onHandleSave(false)" :loading="isConfirmLoading">确 定</el-button>
        </span>
        <EmailDialog email-business-type="TRANSFER_ORDER" :emailBusinessId="transferId" ref="emailDialogRef"></EmailDialog>
    </el-dialog>
</template>
<script>
import FuzzySelect from "@/components/FuzzySelect";
import { apiAddTransfer, apiGetTransferInfo, apiUpdateTransfer } from "@/api/transfer";
import EmailDialog from "@/components/EmailDialog/index.vue";
import { apiJobInfo } from "@/api/mwd";
export default {
    name: "EditDialog",
    components: { FuzzySelect, EmailDialog },
    data(){
        return {
            detailForm: {
                fromWellNumber: null,
                fromKitNumber: null,
                toWellNumber: null,
                toKitNumber: null,
                sendDate: null,
                contactUser: null,
                contactNumber: null,
                detailList: [],
            },
            detailFormRules: {
                fromWellNumber: [{ required: true, message: "请填写发出井号", trigger: "blur" }],
                toWellNumber: [{ required: true, message: "请填写发往井号", trigger: "blur" }],
            },
            isConfirmLoading: false,
            editObject: { editRow: false, editRowIndex: -1 },
            preObject: {},
            operType: "ADD",
            isDialogVisible: false,
            transferId: null,
        }
    },
    methods: {
        getInitForm() {
            return {
                fromWellNumber: null,
                fromKitNumber: null,
                toWellNumber: null,
                toKitNumber: null,
                sendDate: new Date().Format("yyyy-MM-dd"),
                contactUser: null,
                contactNumber: null,
                detailList: [],
            };
        },
        getInitDetailItem() {
            return {
                itemName: null,
                serialNumber: null,
                amount: null,
                note: null,
            };
        },
        onSerialNumberChange(row){
            if(row.serialNumber){
                row.amount = 1;
            }
        },
        onHandleSave(email=false) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            if (this.detailForm.detailList.length == 0) {
                this.$message.error("请先添加要调拨的物品");
                return;
            }
            this.$refs["detailForm"].validate((valid) => {
                if (!valid) return;
                this.saveEditData(email);
            });
        },
        saveEditData(email=false) {
            //save data
            if (!this.detailForm.detailList?.length) {
                this.$message.error("请添加要调拨的物品");
                return;
            }
            this.isConfirmLoading = true;
            if (this.operType === "ADD") {
                //add
                apiAddTransfer(this.detailForm).then((res) => {
                    this.isDialogVisible = false;
                    this.$emit("initData");
                    this.$message.success("添加成功");
                    this.transferId = res.data.data;
                    this.$nextTick(()=>{
                        email && this.$refs.emailDialogRef.showDialog();
                    })
                }).finally(() =>{
                    this.isConfirmLoading = false;
                });
            } else {
                //update
                apiUpdateTransfer(this.detailForm).then((res) => {
                    this.$message.success("更新成功");
                    this.$emit("initData");
                    this.isDialogVisible = false;
                    email && this.$refs.emailDialogRef.showDialog();
                }).finally(() =>{
                    this.isConfirmLoading = false;
                });
            }
        },
        async showDialog(type, params={}) {
            const { transferId, jobId, toolList } = params;
            this.editObject.editRowIndex = -1;
            this.operType = type;
            this.detailForm = this.getInitForm();
            if (type === "ADD") {
                if(jobId){
                    await apiJobInfo({jobId}).then(res=>{
                        const info = res.data.data;
                        this.detailForm.fromKitNumber = info.kitNumber;
                        this.detailForm.fromWellNumber = info.wellNumber;
                    })
                }
                if(toolList){
                    this.detailForm.detailList = toolList.map((item) => {
                        return {
                            itemName: item.invName,
                            serialNumber: item.serialNumber,
                            amount: item.serialNumber ? 1 : null,
                        };
                    });
                }
            } else{
                this.transferId = transferId;
                await apiGetTransferInfo({ transferId: transferId }).then((res) => {
                    this.detailForm = res.data.data;
                });
            }
            this.isDialogVisible = true;
        },

        onAddRow() {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.preObject = {};
            this.detailForm.detailList.push(this.getInitDetailItem());
            this.editObject.editRow = true;
            this.editObject.editRowIndex =
                this.detailForm.detailList.length - 1;
        },

        onEditRow(scope) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.preObject = { ...scope.row };
            this.editObject.editRowIndex = scope.$index;
        },

        onDeleteRow(scope) {
            if (this.editObject.editRowIndex >= 0) {
                this.$message.error("请先保存");
                return;
            }
            this.detailForm.detailList.splice(scope.$index, 1);
        },

        async onSaveRow(scope) {
            // TODO: 校验序列号
            if (!scope.row.itemName) {
                this.$message.error("请填写物品名称");
                return;
            }
            this.editObject.editRowIndex = -1;
        },

        onCancelRow(scope) {
            if (Object.keys(this.preObject).length) {
                this.$set(this.detailForm.detailList, scope.$index, {
                    ...this.preObject,
                });
            } else {
                this.detailForm.detailList.pop();
            }
            this.editObject.editRowIndex = -1;
            this.preObject = {};
        },
    }
    
}
</script>
<style lang="scss">
.demand-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
