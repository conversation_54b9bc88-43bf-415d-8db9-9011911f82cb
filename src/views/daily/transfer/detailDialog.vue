<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="调拨单详情"
        width="1000px"
    >
        <el-form ref="detailForm" style="margin-top: -20px;" class="thin-margin-form" :model="detailForm" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发出井号: " prop="fromWellNumber">
                        <span>{{ detailForm.fromWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出kit箱: " prop="fromKitNumber">
                        <span>{{ detailForm.fromKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出时间: " prop="sendDate">
                        <span>{{ detailForm.sendDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发往井号: " prop="toWellNumber">
                        <span>{{ detailForm.toWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发往kit箱: " prop="toKitNumber">
                        <span>{{ detailForm.toKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.detailList"
            :header-cell-style="commmonTableHeaderCellStyle"
            style="margin-bottom: 20px;"
            @selection-change="onSelectionChange"
        >
            <el-table-column type="selection" :selectable="isRowSelectable" width="55" v-if="operType==='RECEIVE'"></el-table-column>
            <el-table-column label="物品名称" prop="itemName" width="120" align="center">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="120" align="center">
            </el-table-column>
            <el-table-column label="数量" prop="amount" width="60" align="center">
            </el-table-column>
            <el-table-column label="备注" prop="note" align="center">
            </el-table-column>
            <el-table-column label="现场接收人" prop="fieldReceiveBy" width="100" align="center">
            </el-table-column>
            <el-table-column label="接收时间" prop="fieldReceiveDate" width="100" align="center">
            </el-table-column>
            <el-table-column label="操作" width="120" align="center" v-if="operType==='RECEIVE'">
                <template slot-scope="scope">
                    <el-button :disabled="scope.row.fieldReceiveStatus==='RECEIVED'" type="text" @click="onReceive(scope.row)">接收</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-button v-if="operType==='RECEIVE'" type="text" style="position: absolute; right: 50px; bottom: 10px;" @click="onBatchReceive" :loading="isConfirmLoading">批量接收</el-button>
    </el-dialog>
</template>
<script>
import { apiGetTransferInfo, apiReceiveTransferItem } from "@/api/transfer";
export default {
    name: "TransferDetailDialog",
    data(){
        return {
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                detailList: [],
            },
            isConfirmLoading: false,
            operType: "DETAIL", // DETAIL RECEIVE
            isDialogVisible: false,
        }
    },
    methods: {
        async showDialog(operType, transferId) {
            this.operType = operType;
            this.transferId = transferId;
            await this.initData();
            this.isDialogVisible = true;
        },
        async initData(){
            await apiGetTransferInfo({ transferId: this.transferId }).then((res) => {
                const data = res.data.data || {};
                this.detailForm = data;
            });
        },
        onReceive(row){
            this.$confirm("确定接收该物品吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiReceiveTransferItem({ detailIdList: [row.detailId] }).then(() => {
                    this.$message.success("接收成功");
                    this.initData();
                })
            });
        },
        onBatchReceive(){
            if(!this.selectedRows?.length){
                this.$message.error("请选择需要接收的物品");
                return;
            }
            this.$confirm("确定批量接收选中的物品吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.isConfirmLoading = true;
                const detailIdList = this.selectedRows.map((item) => item.detailId);
                apiReceiveTransferItem({ detailIdList }).then(() => {
                    this.$message.success("接收成功");
                    this.initData();
                }).finally(() => {
                    this.isConfirmLoading = false;
                });
            });
        },
        onSelectionChange(val){
            this.selectedRows = val;
        },
        isRowSelectable(row){
            return row.fieldReceiveStatus!=='RECEIVED'
        }
    }
    
}
</script>
