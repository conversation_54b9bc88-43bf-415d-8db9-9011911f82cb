<template>
    <div class="app-container" style="height: 100%; overflow: hidden" v-loading="isLoading">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">调拨单</div>
                <el-button
                    style="float: right;"
                    type="primary"
                    @click="onAdd"
                >
                    新增调拨单
                </el-button>
            </div>
            <el-form :model="searchForm" inline label-width="auto" style="margin-top: 10px; width: 100%;">
                <el-form-item label="井号">
                    <el-input @change="initData" v-model="searchForm.wellNumber" clearable style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item label="调拨类型">
                    <el-select v-model="searchForm.transferType" clearable @change="initData" style="width: 200px;">
                        <el-option v-for="item in transferTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-table
                :data="tableData"
                @sort-change="handleSortChange"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                :default-sort="{
                    order: 'descending',
                    prop: 'createTime'
                }"
            >
                <el-table-column
                    label="调拨单号"
                    prop="transferNumber"
                ></el-table-column>
                <!-- <el-table-column
                    label="调拨类型"
                    align="center"
                    prop=""
                >
                    <template slot-scope="scope">
                        <span>
                            {{ getTransferTypeStr(scope.row) }}
                        </span>
                    </template>
                </el-table-column> -->
                <el-table-column
                    label="发出井号"
                    prop="fromWellNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="发出Kit箱"
                    prop="fromKitNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="发往井号"
                    prop="toWellNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="发往Kit箱"
                    prop="toKitNumber"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="创建人"
                    prop="createBy"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="发出时间"
                    prop="sendDate"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="140"
                    align="center"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="onDetail(scope.row)"
                            v-if="scope.row.receiveStatus=='RECEIVED'"
                        >
                            详情
                        </el-button>
                        <el-button
                            type="text"
                            @click="onEdit(scope.row)"
                            v-else
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            @click="onExport(scope.row)"
                        >
                            下载
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <EditDialog
            @initData="initData"
            ref="editDialogRef"
        />
        <DetailDialog ref="detailDialogRef" />
    </div>
</template>

<script>
import { apiGetTransferOrderList, apiExportTransfer } from "@/api/transfer";
import EditDialog from "./editDialog.vue";
import DetailDialog from "./detailDialog.vue";
const ORDER_TYPE_MAP = {
    descending: "desc",
    ascending: "asc",
};
const transferTypeMap = {
    IN: {
        label: '调入',
        field: 'toWellNumber',
    }, // 发向某井 相当于 调入某井
    OUT: {
        label: '调出',
        field: 'fromWellNumber',
    }, // 来自某井 相当于 调出某井
};
const transferTypeList = [
    { label: "调出", value: "OUT" },
    { label: "调入", value: "IN" },
];
export default {
    name: "Transfer",
    components: { EditDialog, DetailDialog },
    data() {
        return {
            transferTypeList,
            isLoading: false,
            orderBy: "",
            orderType: "",
            currentPage: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchForm: {
                transferType: "",
                wellNumber: "",
            },
        };
    },
    computed: {
    },
    mounted() {
        this.orderBy = "createTime";
        this.orderType = "desc"
        this.initData();
    },
    methods: {
        getTransferTypeStr(row) {
            const { transferType, wellNumber } = this.searchForm;
            if (!wellNumber){
                return '';
            }
            if(transferType){
                return transferTypeMap[transferType].label;
            }else{
                return row.fromWellNumber === wellNumber ? '调出' : '调入';
            }
        },

        initData() {
            const searchForm = {
                [transferTypeMap[this.searchForm.transferType]?.field || "wellNumber"]: this.searchForm.wellNumber,
            };
            apiGetTransferOrderList(
                {
                    ...searchForm,
                    orderType: this.orderBy&&this.orderType ? this.orderType : undefined,
                    orderBy: this.orderBy&&this.orderType ? this.orderBy : undefined
                },
                {
                    current: this.currentPage,
                    size: this.pageSize,
                }
            )
                .then((res) => {
                    const data = res.data.data || {};
                    this.tableData = data.records || [];
                    this.total = data.total;
                })
                .catch((err) => {});
        },

        onExport(row){
            this.isLoading = true;
            apiExportTransfer({transferId: row.transferId}).then(res=>{
                const blob = new Blob([res.data], {type: "application/xlsx;charset=UTF-8"});
                const fileName =`调拨单-${row.wellNumber}.xlsx`;
                const link = document.createElement("a");
                link.style.display = "none";
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                //释放内存
                link.remove();
                window.URL.revokeObjectURL(link.href);
            }).finally(()=>{
            this.isLoading = false;
            });
        },
        onAdd() {
            this.$refs.editDialogRef.showDialog("ADD")
        },
        onEdit(row) {
            this.$refs.editDialogRef.showDialog("DETAIL", { transferId: row.transferId });
        },
        onDetail(row) {
            // DETAIL RECEIVE
            this.$refs.detailDialogRef.showDialog("RECEIVE",row.transferId);
        },
        onCurrentChange(currentPage) {
            this.currentPage = currentPage;
            this.initData();
        },
        handleSortChange({ prop, order }) {
            this.orderType = order && ORDER_TYPE_MAP[order];
            this.orderBy = prop;
            this.initData();
        }

    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
