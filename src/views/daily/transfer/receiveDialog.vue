<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="调拨单详情"
        width="1000px"
    >
        <el-form ref="detailForm" style="margin-top: -20px;" class="thin-margin-form" :model="detailForm" label-width="80px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发出井号: " prop="fromWellNumber">
                        <span>{{ detailForm.fromWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出kit箱: " prop="fromKitNumber">
                        <span>{{ detailForm.fromKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发出时间: " prop="sendDate">
                        <span>{{ detailForm.sendDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="发往井号: " prop="toWellNumber">
                        <span>{{ detailForm.toWellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="发往kit箱: " prop="toKitNumber">
                        <span>{{ detailForm.toKitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系人: " prop="contactUser">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: " prop="contactNumber">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.detailList"
            :header-cell-style="commmonTableHeaderCellStyle"
            style="margin-bottom: 20px;"
        >
            <el-table-column label="物品名称" prop="itemName" width="120" align="center">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber" width="220" align="center">
            </el-table-column>
            <el-table-column label="需求数量" prop="amount" width="120" align="center">
            </el-table-column>
            <el-table-column label="备注" prop="note" align="center">
            </el-table-column>
            <el-table-column label="现场接收人" prop="fieldReceiveBy" align="center">
            </el-table-column>
            <el-table-column label="接收时间" prop="fieldReceiveDate" align="center">
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                    <el-button :disabled="scope.row.fieldReceiveStatus==='RECEIVED'" type="text" @click="onReceive(scope.row)">接收</el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script>
import { apiGetTransferInfo } from "@/api/transfer";
export default {
    name: "TransferDetailDialog",
    data(){
        return {
            detailForm: {
                jobNumber: "",
                wellNumber: "",
                kitNumber: "",
                detailList: [],
            },
            isConfirmLoading: false,
            operType: "ADD",
            isDialogVisible: false,
        }
    },
    methods: {
        async showDialog(transferId) {
            await apiGetTransferInfo({ transferId: transferId }).then((res) => {
                const data = res.data.data || {};
                this.detailForm = data;
            });
            this.isDialogVisible = true;
        },
        onReceive(row){}
    }
    
}
</script>
