<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器施工统计</div>
            <el-form
                :inline="true"
                :model="searchForm"
                class="demo-form-inline"
            >
                <el-form-item label="年份">
                    <el-date-picker
                        v-model="searchForm.year"
                        format="yyyy"
                        value-format="yyyy"
                        type="year"
                        placeholder="年份"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item label="时间维度">
                    <el-select
                        v-model="searchForm.TimeDims"
                        placeholder="时间"
                        @change="onTimeSpanChange"
                    >
                        <el-option
                            :label="item.name"
                            :value="item.field"
                            :key="item.name"
                            v-for="item in timeDims"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="统计维度">
                    <el-select
                        v-model="searchForm.dimension"
                        placeholder=""
                        multiple
                        collapse-tags
                        clearable
                    >
                        <el-option
                            :label="item.name"
                            :value="item.field"
                            :key="item.name"
                            v-for="item in allDimensions"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSearch">查询</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData" style="width: 100%">
                <el-table-column
                    key="wellNumber"
                    prop="wellNumber"
                    fixed="left"
                    label="井号"
                    v-if="searchForm.TimeDims == 'DAY'"
                ></el-table-column>
                <el-table-column
                    key="jobType"
                    prop="jobType"
                    fixed="left"
                    label="业务类型"
                    v-if="searchForm.TimeDims == 'DAY'"
                ></el-table-column>
                <el-table-column
                    key="time"
                    prop="time"
                    fixed="left"
                    label="时间"
                    v-if="searchForm.TimeDims != 'DAY'"
                ></el-table-column>
                <el-table-column
                    v-for="item in searchForm.dimension"
                    :key="item"
                    :label="getDimension(item).name"
                >
                    <template v-if="item == 'FAILURE_REASON'">
                        <el-table-column
                            :key="
                                getDimension(item).name + 'failureReason.pulser'
                            "
                            label="脉冲"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.pulser
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="getDimension(item).name + 'failureReason.bna'"
                            label="底部总成"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.bna
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="getDimension(item).name + 'failureReason.dm'"
                            label="探管"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.dm
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="getDimension(item).name + 'failureReason.rec'"
                            label="接收器"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.rec
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="getDimension(item).name + 'failureReason.bat'"
                            label="M电池"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.bat
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="
                                getDimension(item).name + 'failureReason.gamma'
                            "
                            label="伽马"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.gamma
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="
                                getDimension(item).name + 'failureReason.atBit'
                            "
                            label="近钻头"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.atBit
                                }}
                            </template>
                        </el-table-column>
                        <el-table-column
                            :key="
                                getDimension(item).name + 'failureReason.other'
                            "
                            label="其它"
                        >
                            <template slot-scope="scope">
                                {{
                                    scope.row.failureReason &&
                                    scope.row.failureReason.other
                                }}
                            </template>
                        </el-table-column>
                    </template>
                    <template v-if="item === 'GAMMA'">
                        <el-table-column
                            :key="getDimension(item).name + 'azimuthGamma'"
                            prop="azimuthGamma"
                            label="远方位Gama"
                        ></el-table-column>
                        <el-table-column
                            :key="getDimension(item).name + 'naturalGamma'"
                            prop="naturalGamma"
                            label="自然Gama"
                        ></el-table-column>
                    </template>
                    <template
                        slot-scope="scope"
                        v-if="item != 'GAMMA' && item != 'FAILURE_REASON'"
                    >
                        {{ scope.row[getDimension(item).column] }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { statisticJob, statisticJobByTime } from "@/api/statistic";
import Vue from "vue";
@Component({})
export default class extends Vue {
    private searchForm = {
        year: null,
        dimension: [],
        TimeDims: "",
    };
    private tableData = [];
    private timeDims = [
        { name: "自然日", field: "DAY", column: "day" },
        { name: "月度", field: "MONTH", column: "month" },
        { name: "季度", field: "QUARTER", column: "quarter" },
        { name: "半年度", field: "HALFYEAR", column: "half-year" },
        { name: "年度", field: "YEAR", column: "year" },
    ];
    private allDimensions: any[] = [];
    //    private  failureReason={
    //             "pulser": 0,"bna": 0,"dm": 0,"rec": 0,"bat": 0,"gamma": 0,"atBit": 0,"other": 0
    //      }
    getDimension(field: any) {
        let obj = "";
        for (let i = 0; i < this.allDimensions.length; i++) {
            if (this.allDimensions[i].field == field) {
                obj = this.allDimensions[i];
                break;
            }
        }
        return obj;
    }
    onTimeSpanChange() {
        this.searchForm.dimension = [];
        this.allDimensions = [];

        if (this.searchForm.TimeDims == "DAY") {
            this.allDimensions = [
                {
                    name: "总趟数",
                    field: "TOTAL_RUN",
                    column: "totalRun",
                    group: "Day",
                },
                {
                    name: "伽马",
                    field: "GAMMA",
                    column: "azimuthGamma",
                    group: "Day",
                },
                {
                    name: "近钻",
                    field: "AT_BIT",
                    column: "atBit",
                    group: "Day",
                },
                {
                    name: "失效趟数",
                    field: "FAILURE_RUN",
                    column: "failureRun",
                    group: "Day",
                },
                {
                    name: "失效原因",
                    field: "FAILURE_REASON",
                    column: "failureReason",
                    group: "Day",
                },
                {
                    name: "施工井段",
                    field: "CONSTRUCTION",
                    column: "consDepthRange",
                    group: "Day",
                },
                {
                    name: "周期",
                    field: "PERIOD",
                    column: "period",
                    group: "Day",
                },
                {
                    name: "温度",
                    field: "TEMPERATURE",
                    column: "temperature",
                    group: "Day",
                },
                {
                    name: "垂深",
                    field: "TVD",
                    column: "totalFootage",
                    group: "Day",
                },
            ];
            return;
        }
        this.allDimensions = [
            {
                name: "井数",
                field: "WELL_COUNT",
                column: "wellCount",
                group: "Group",
            },
            {
                name: "仪器服务",
                field: "APPARATUS_COUNT",
                column: "apparatusCount",
                group: "Group",
            },
            {
                name: "大包服务",
                field: "PACKAGE_COUNT",
                column: "packageCount",
                group: "Group",
            },
            {
                name: "工程服务",
                field: "PROJECT_COUNT",
                column: "projectCount",
                group: "Group",
            },
            {
                name: "总进尺",
                field: "TOTAL_FOOLTAGE",
                column: "totalFootage",
                group: "Group",
            },
            {
                name: "总趟次",
                field: "TOTAL_RUN",
                column: "totalRun",
                group: "Group",
            },
            {
                name: "总失效趟次",
                field: "FAILURE_RUN",
                column: "failureRun",
                group: "Group",
            },
            {
                name: "失效率",
                field: "FAILURE_RATIO",
                column: "failureRate",
                group: "Group",
            },
            {
                name: "伽马",
                field: "GAMMA",
                column: "azimuthGamma",
                group: "Group",
            },
            { name: "近钻", field: "AT_BIT", column: "atBit", group: "Group" },
            {
                name: "失效原因",
                field: "FAILURE_REASON",
                column: "failureReason",
                group: "Group",
            },
        ];
    }
    onSearch() {
        //this.tableData=[]
        if (this.searchForm.TimeDims == "DAY") {
            let params = {
                year: this.searchForm.year,
                statisticsTypeList: this.searchForm.dimension,
            };
            statisticJob(params)
                .then((res) => {
                    let { code, message, data } = res.data;
                    this.tableData = data;
                })
                .catch((err) => {});
        } else {
            let params = {
                year: this.searchForm.year,
                unit: this.searchForm.TimeDims,
                statisticsTypeList: this.searchForm.dimension,
            };
            statisticJobByTime(params)
                .then((res) => {
                    let { code, message, data } = res.data;
                    this.tableData = data;
                })
                .catch((err) => {});
        }
    }
}
</script>
<style lang="scss"></style>
