<template>
    <div class="app-container" v-loading="loadingExportExcelAll">
        <div class="app-card" style="display: flex; height: 100%; padding: 0">
            <div class="content-card" style="position: relative;">
                <div style="margin-top:-18px !important;">
                    <div class="role-title" style="display: flex;">
                        <div class="head-title" style="flex-basis: 400px; flex-grow: 0">
                            <span v-if="wellNumber||jobNumber">{{ wellNumber }} <span style="color:#bbb"> / </span>Job# {{ jobNumber }}</span>
                        </div>
                        <div style="flex-grow: 1; display: flex; justify-content: flex-end;margin-bottom:5px;">
                            <div>
                                <el-button
                                    v-if="p_Complete"
                                    @click="onFullfillJob"
                                    type="primary"
                                    icon="el-icon-s-check"
                                >
                                    {{jobStatus == 1 ? '复工' : "竣工"}}
                                </el-button>
                                <el-button @click="onClickCompleteFile" type="primary">完井资料</el-button>
                                <el-tooltip class="item" effect="dark" placement="top-start">
                                    <div slot="content">{{ completeReportUploadMessage }}</div>
                                    <el-dropdown>
                                        <el-button v-if="p_DownloadTemplate||p_UploadReport||p_DownloadReport" type="primary" style="margin:0 10px">
                                            完井报告<i class="el-icon-arrow-down el-icon--right"></i>
                                        </el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-if="p_DownloadTemplate" @click.native="onOperateCompletedWell('TEMPLATE_DOWNLOAD')">模板下载</el-dropdown-item>
                                            <el-dropdown-item v-if="p_UploadReport" @click.native="onOperateCompletedWell('REPORT_UPLOAD')">报告上传</el-dropdown-item>
                                            <el-dropdown-item v-if="p_DownloadReport" @click.native="onOperateCompletedWell('REPORT_DOWNLOAD')">报告下载</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </el-tooltip>
                                <el-button @click="onExportAsExcel" v-if="p_ExportExcel" type="primary">导出Excel</el-button>
                            </div>
                        </div>
                    </div>
                    <el-upload
                        style="width:0;height: 0;"
                        action="string"
                        :show-file-list="false"
                        :before-upload="onUploadCompletedWellReport"
                    >
                        <div ref="ghostUploader"></div>
                    </el-upload>
                    <div style="color: #1f2329;font-size: 16px;font-weight: 500;">
                        <el-tabs :value="selectedBusinessType" type="border-card" class="daily-tabs">
                            <el-tab-pane v-for="item in reportTabs" :key="item.name" :label="item.name" :name="item.name">
                                <span slot="label" @click.prevent.stop style="display:inline-block;padding:0 10px">
                                       <el-dropdown @command="onHandleCommand"  trigger="click">
                                             <span class="el-dropdown-link">
                                                {{item.name}}<i class="el-icon-arrow-down el-icon--right"></i>
                                            </span>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item  v-for="obj in item.children" :command="obj.component" :key="obj.name">{{obj.name}}</el-dropdown-item>
                                            </el-dropdown-menu>
                                       </el-dropdown>
                                </span>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
                <div class="report-container" style="margin-top:0px;">
                    <div class="report">
                        <component
                            :ref="selectedReportType"
                            :selectedDate="selectedDate"
                            :selectedRssDate="selectedRssDate"
                            :job="jobNumber"
                            :well="wellNumber"
                            :selectedRun="selectedRun"
                            :selectedRssRun="selectedRssRun"
                            :is="selectedReportType"
                            :wellTypeList="wellTypeList"
                            :workConditionList="workConditionList"
                            :kitBoxId="kitBoxId"
                            :runList="runList"
                            :jobId="jobId"
                            :failedComponentList="failedComponentList"
                            :toolList="toolList"
                            :jobStatus="jobStatus"
                            :globalDisableEdit="globalDisableEdit"
                            @updateRunList="getJobInfo"
                            @updateCommonRun="updateCommonRun"
                            @updateCommonDate="updateCommonDate"
                            @updateCommonRssRun="updateCommonRssRun"
                            @updateCommonRssDate="updateCommonRssDate"
                        ></component>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog  width="310px" top="30vh" :close-on-click-modal="false" title="报告类型" :visible.sync="isCompletedWellReportOperateDialogVisible">
            <el-button type="primary" @click="onClickCompeletedReport('MWD')">仪器完井报告</el-button>
            <el-button type="primary" @click="onClickCompeletedReport('PROJECT')">工程完井报告</el-button>
        </el-dialog>
        <complete-file-dialog ref="completeFileDialog"></complete-file-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Watch, Vue } from "vue-property-decorator";
import CompleteFileDialog from "../complete-file/complete-file-dialog.vue";
import {
    apiUpdateJobStatus,
    apiJobInfo,
    apiGetCompltedWellReportTemplate,
    apiUploadCompltedWellReport,
    apiExportDailyReportExcel,
    apiGetDailyReportBaseinfo,
} from "@/api/mwd";
import { apiDownloadFile } from "@/api/file";
import BHA from "../Components/bha.vue";
import COVER from "../Components/cover.vue";
import FAILURE from "../Components/failure.vue";
import MWD from "../Components/mwd.vue";
import PROJECT from "../Components/project.vue";
import RIPROCEDURES from "../Components/rigIn.vue";
import ROPROCEDURES from "../Components/rigOut.vue";
import RUN from "../Components/runs.vue";
import SURVEYS from "../Components/surveys.vue";
import TOOLS from "../Components/tools.vue";
import TOOLSONSITE from "../Components/toolsOnsite.vue";
import PROJECTTOOLSONSITE from "../Components/toolList.vue";

import RSS_COVER from "../Components/rss_cover.vue";
import RSS_TOOLSONSITE from "../Components/rss_toolsonsite.vue";
import RSS_RIPROCEDURES from "../Components/rss_rip.vue";
import RSS_DAILY from "../Components/rss_daily.vue";
import RSS_RUN from "../Components/rss_run.vue";
import RSS_BHA from "../Components/rss_bha.vue";
import RSS_FAILURE from "../Components/rss_failure.vue";
import RSS_ROPROCEDURES from "../Components/rss_rop.vue";
import { IBusinessType } from "@/utils/constant";
import getDict from "@/utils/getDict";
@Component({
    name: "MwdDaily",
    components: {
        BHA,
        COVER,
        FAILURE,
        MWD,
        PROJECT,
        RIPROCEDURES,
        ROPROCEDURES,
        RUN,
        SURVEYS,
        TOOLS,
        TOOLSONSITE,
        PROJECTTOOLSONSITE,
        RSS_COVER,
        RSS_RIPROCEDURES,
        RSS_TOOLSONSITE,
        RSS_RUN,
        RSS_BHA,
        RSS_DAILY,
        RSS_FAILURE,
        RSS_ROPROCEDURES,
        CompleteFileDialog
    },
})
export default class extends Vue {
    private loadingExportExcelAll = false;
    private kitBoxId: number | null = null;
    private kitNumber = ""
    private runList: any[] = [];
    private failedComponentList: any[] = [];
    private toolList: any[] = [];
    private wellNumber = "";
    private wellTypeList: IBusinessType[] = [];
    private workConditionList: IBusinessType[] = [];
    private jobNumber: string = "";
    private selectedDate: string = "";
    private selectedRun: string = "";
    private selectedRssDate: string = "";
    private selectedRssRun: string = "";
    private selectedReportType: string = "RSS_COVER";
    private jobId = -1;
    //是否已完成作业：1 是 0否
    private jobStatus = 0;
    private globalDisableEdit = true;
    private reportTabs = [
        {
            name: "旋导日报",
            children: [
                { name: this.$REPORT_TYPE_MAP.RSS_COVER, component: "RSS_COVER" },
                { name: this.$REPORT_TYPE_MAP.RSS_TOOLSONSITE, component: "RSS_TOOLSONSITE" },
                { name: this.$REPORT_TYPE_MAP.RSS_RIPROCEDURES, component: "RSS_RIPROCEDURES" },
                { name: this.$REPORT_TYPE_MAP.RSS_DAILY, component: "RSS_DAILY" },
                { name: this.$REPORT_TYPE_MAP.RSS_RUN, component: "RSS_RUN" },
                { name: this.$REPORT_TYPE_MAP.RSS_BHA, component: "RSS_BHA" },
                { name: this.$REPORT_TYPE_MAP.RSS_FAILURE, component: "RSS_FAILURE" },
                { name: this.$REPORT_TYPE_MAP.RSS_ROPROCEDURES, component: "RSS_ROPROCEDURES" },
            ]
        },
        {
            name: "仪器日报",
            children: [
                { name: this.$REPORT_TYPE_MAP.COVER, component: "COVER" },
                { name: this.$REPORT_TYPE_MAP.TOOLSONSITE, component: "TOOLSONSITE" },
                { name: this.$REPORT_TYPE_MAP.RIPROCEDURES, component: "RIPROCEDURES" },
                { name: this.$REPORT_TYPE_MAP.MWD, component: "MWD" },
                { name: this.$REPORT_TYPE_MAP.RUN, component: "RUN" },
                { name: this.$REPORT_TYPE_MAP.SURVEYS, component: "SURVEYS" },
                { name: this.$REPORT_TYPE_MAP.BHA, component: "BHA" },
                { name: this.$REPORT_TYPE_MAP.FAILURE, component: "FAILURE" },
                { name: this.$REPORT_TYPE_MAP.ROPROCEDURES, component: "ROPROCEDURES" }
            ]
        },
        {
            name: "工程日报",
            children: [
                { name: this.$REPORT_TYPE_MAP.PROJECTTOOLSONSITE, component: "PROJECTTOOLSONSITE" },
                { name: this.$REPORT_TYPE_MAP.PROJECT, component: "PROJECT" }
            ]
        },
    ];
    private completedWellReportOperateType = "";
    private completedWellReportType = "";
    private isCompletedWellReportOperateDialogVisible = false;
    private jobFileList:any[] = []
    private completeFileList:any[] = []
    private isFileUploading = false
    get selectedBusinessType(){
        const item:any = this.reportTabs.find(tab=>!!tab.children.find(child=>child.component===this.selectedReportType));
        return item.name;
    }
    get p_Complete(){
        return this.$checkBtnPermission(`sys:daily:complete`)
    }
    get p_DownloadTemplate(){
        return this.$checkBtnPermission(`sys:daily:template_download`)
    }
    get p_UploadReport(){
        return this.$checkBtnPermission(`sys:daily:report_upload`)
    }
    get p_DownloadReport(){
        return this.$checkBtnPermission(`sys:daily:report_download`)
    }
    get p_ExportExcel(){
        return this.$checkBtnPermission(`sys:daily:job_export`)
    }
    get completeReportUploadMessage(){
        const len = this.jobFileList.length;
        if(len===0){
            return "完井报告未上传"
        }else if(len===1){
            const reportType = this.jobFileList[0].reportType;
            if(reportType==='MWD'){
                return "仪器报告已上传"
            }else{
                return "工程报告已上传"
            }
        }else{
            return "完井报告已全部上传"
        }
    }
    @Watch("selectedReportType")
    watchSelectedReportType() {
        this.getReport();
    }
    private async mounted() {
        this.jobId = Number(this.$route.query.jobid);
        const queryTab = this.$route.query.tab as string;
        if(!this.jobId){
            return;
        }
        [
            this.workConditionList,
            this.wellTypeList,
            this.failedComponentList,
        ] = await getDict([1, 2, 10]);
        // this.getReport();
        this.selectedReportType = queryTab || await this.initReportType();
        this.getReport();
        this.getJobInfo();
    }
    private async initReportType() {
        const coverBaseInfoList = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: "COVER",
        }).then((res) => {
            return res.data?.data;
        });
        if(coverBaseInfoList&&coverBaseInfoList.length){
            return "COVER";
        }
        return "RSS_COVER"; // 必须要返回一个
    }
    // 完井资料
    private onClickCompleteFile(){
      (this.$refs.completeFileDialog as any).openDialog(this.jobId);
    }
    // #region  完井报告
    private onOperateCompletedWell(type:string){
        this.completedWellReportOperateType = type;
        this.isCompletedWellReportOperateDialogVisible = true;
    }
    private onClickCompeletedReport(REPORT_TYPE:string){
        this.completedWellReportType = REPORT_TYPE;
        if(this.completedWellReportOperateType === "TEMPLATE_DOWNLOAD"){
            this.downloadCompletdWellReportTemplate({reportType:REPORT_TYPE,jobId:this.jobId})
        }else if(this.completedWellReportOperateType === "REPORT_UPLOAD"){
            (this.$refs.ghostUploader as any).click();
        }else if(this.completedWellReportOperateType === "REPORT_DOWNLOAD"){
            this.onDownloadCompletdWellReport();
        }

    }
    private onDownloadCompletdWellReport(){
        const tmp = this.jobFileList.find(item=>item.reportType===this.completedWellReportType);
        let fileId;
        if(tmp){
            fileId = tmp.fileId;
        }
        if(fileId){
            this.loadingExportExcelAll = true;
            this.isCompletedWellReportOperateDialogVisible = false;
            apiDownloadFile({ fileId }).then(res => {
                this.loadingExportExcelAll = false;
                const blob = new Blob([res.data], {
                    type: 'application/word;charset=UTF-8',
                });
                const fileName = `${fileId}.docx`;
                const link = document.createElement('a');
                link.style.display = 'none';
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
                //释放内存
                link.remove(); // 下载完成移除元素
                window.URL.revokeObjectURL(link.href);
            });
        }else{
            this.$message.error("暂无相关报告")
        }
    }
    private onUploadCompletedWellReport(file:File){
        const form = new FormData();
        form.append('jobId', String(this.jobId));
        form.append('reportType', this.completedWellReportType);
        form.append('file', file);
        apiUploadCompltedWellReport(form).then(()=>{
            this.getJobInfo();
            this.isCompletedWellReportOperateDialogVisible = false;
            this.$message.success('操作成功！')
        })
    }
    private downloadCompletdWellReportTemplate({reportType,jobId}:any){
        this.loadingExportExcelAll = true;
        this.isCompletedWellReportOperateDialogVisible = false;
        apiGetCompltedWellReportTemplate({reportType,jobId}).then(res=>{
            const wellNumber = this.wellNumber;
            const type = reportType=== 'MWD' ? "仪器" : "工程";
            this.loadingExportExcelAll = false;
            const blob = new Blob([res.data], {type: "application/word;charset=UTF-8"});
            const fileName =`${wellNumber}${type}完井报告.docx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            //释放内存
            link.remove();
            window.URL.revokeObjectURL(link.href);
        });
    }
    // #endregion

    private onHandleCommand(command:any){
        this.selectedReportType=command;
    }
    private getJobInfo() {
        apiJobInfo({ jobId: this.jobId }).then((res) => {
            const info = res.data.data;
            this.runList = info.runList;
            this.kitBoxId = info.kitBoxId;
            this.kitNumber = info.kitNumber;
            this.toolList = [];
            this.jobStatus = info.jobStatus;
            this.jobFileList = info.fileList || [];
            this.jobNumber = info.jobNumber;
            this.wellNumber = info.wellNumber;
            if (info.toolList == null) return;
            for (let i = 0; i < info.toolList.length; i++) {
                let tmp = info.toolList[i];
                if (
                    (tmp.serialNumber == null || tmp.serialNumber == "") &&
                    tmp.stockId != null &&
                    tmp.stockId != ""
                ) {
                    tmp.serialNumber = "auto_" + tmp.stockId;
                }
                this.toolList.push(tmp);
            }
        });
    }

    private getReport(reportType = this.selectedReportType) {
        this.$nextTick(() => {
            (this.$refs[reportType] as any).loadReport();
        });
    }
    private updateCommonRun(run: any) {
        this.selectedRun = run;
    }
    private updateCommonDate(date: any) {
        this.selectedDate = date;
    }
    private updateCommonRssRun(run: any) {
        this.selectedRssRun = run;
    }
    private updateCommonRssDate(date: any) {
        this.selectedRssDate = date;
    }
    // 竣工
    private onFullfillJob() {
        this.$confirm(`确认${this.jobStatus == 1 ? '复工' : '竣工'}吗？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                const jobStatus = this.jobStatus == 1 ? 0 : 1
                apiUpdateJobStatus({ jobId: this.jobId, jobStatus })
                    .then(() => {
                        this.getJobInfo();
                    })
                    .catch(() => {});
            })
            .catch(() => {});
    }
    private async onExportAsExcel(){
        this.loadingExportExcelAll = true;
        const serverJobType = await apiJobInfo({ jobId: this.jobId }).then(res=>res.data?.data?.jobType);
        const reportTypeList = serverJobType === 8007
            ? ["RSS_COVER", "RSS_TOOLSONSITE", "RSS_RIPROCEDURES", "RSS_DAILY", "RSS_RUN", "RSS_BHA", "RSS_FAILURE", "RSS_ROPROCEDURES",] 
            : ["COVER", "TOOLSONSITE", "RIPROCEDURES", "MWD", "RUN", "SURVEYS", "BHA", "FAILURE", "ROPROCEDURES"];
        apiExportDailyReportExcel({jobId:this.jobId,reportTypeList}).then(res=>{
            const blob = new Blob([res.data], {type: "application/xlsx;charset=UTF-8"});
            const fileName =`${this.wellNumber}_${this.jobNumber}_日报.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            //释放内存
            link.remove();
            window.URL.revokeObjectURL(link.href);
        }).finally(()=>{
          this.loadingExportExcelAll = false;
        });
    }
}
</script>
<style lang="css" scoped>
.el-tabs--border-card >>> .el-tabs__item.is-active {
    border-top: 4px solid #3370ff;
    border-bottom: none;
    padding-bottom: 40px;
}
.el-tabs--border-card >>> .el-tabs__item {
    transition: none;
}
.el-tabs--border-card >>> .el-tabs__content {
    display: none;
}
.el-tabs--border-card {
    box-shadow: none;
}
</style>
<style lang="scss">
.rich-text-cls{
  p{
    margin:5px 0;
  }
  line-height: 1;
}
.daily-tabs{
    .el-tabs__item{
        padding: 0 !important;
        &.is-focus{
            outline: none !important;
        }
    }
}
</style>
<style lang="scss" scoped>
.showHideBox {
    -ms-writing-mode: lr-tb;
    writing-mode: lr-tb;
    -ms-writing-mode: tb-rl;
    writing-mode: tb-rl;
    position: absolute;
    left: 0;
    text-align: center;
    letter-spacing: 5px;
    height: 135px;
    background: #fff;
    box-shadow: 1px 0 7px 0 hsla(0, 0%, 65.5%, 0.4);
    border-radius: 0 4px 4px 0;
    top: calc(50% - 135px);
    cursor: pointer;
}

.report-title {
    font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #333333;
}
.side-menu {
    margin-right: 0;
    border-radius: 0;
    border-right: 1px solid #dee0e3;
    width: 220px;
    height: 100%;
    flex: 0 0 220px;

    .circle-btn-wrapper {
        position: absolute;
        left: 24px;
        bottom: 8px;
        padding-bottom: 10px;
        cursor: pointer;

        .circle-btn {
            height: 40px;
            width: 40px;
            background: #3370ff
                url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCI+PHBhdGggZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMTAuMTExLjExMXY3Ljc3N2g3Ljc3OHYyLjIyM0gxMC4xMXY3Ljc3OEg3Ljg5VjEwLjExbC03Ljc3OC4wMDFWNy44OWg3Ljc3N1YuMTFoMi4yMjN6Ii8+PC9zdmc+)
                no-repeat 50%;
            color: white;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            position: absolute;
            top: 0;
            left: 0;
            /*box-shadow: 0 5px 10px 0 rgba(0,0,0,.2);*/
        }

        .circle-button-text {
            width: 40px;
            height: 40px;
            line-height: 40px;
            border-radius: 20px;
            padding-left: 40px;
            box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.2);
            background: #3370ff;
            color: #fff;

            transition: width 0.3s ease-in-out;
            overflow: hidden;
            white-space: nowrap;
        }
    }
    .circle-btn-wrapper:hover {
        .circle-button-text {
            width: 116px;
        }
    }
    .circle-btn-wrapper:active {
        .circle-btn {
            background-color: #245bdb;
        }
        .circle-button-text {
            background-color: #245bdb;
        }
    }
}

.list {
    height: 40px;
    width: 100%;
    cursor: pointer;
    .list-item {
        border-radius: 2px;
        /*background-color: #ebf3ff;*/
        padding: 10px;
        box-sizing: border-box;
        width: 100%;
        height: 40px;
        line-height: 20px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        &:hover .more {
            display: inline-block;
        }
        .more {
            display: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            line-height: 20px;
            text-align: center;
        }

        .more:hover {
            background: #bacefd;
        }
    }

    .list-item:hover {
        background-color: #eff0f1;
    }

    .active {
        background-color: #ebf3ff;
        color: #37f;
    }
}

.content-card {
    height: 100%;
    padding: 24px 12px;
    background-color: #fff;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-width: 1000px;
    overflow: hidden;
    .role-title {
        width: auto;
        display: flex;
        align-items: center;
        .head-title {
            color: #1f2329;
            font-size: 16px;
            font-weight: 500;
            margin-right: 40px;
        }
        .count-number {
            margin-left: 15px;
            line-height: 21px;
            font-size: 15px;
            color: #8f959e;
        }
    }
    .role-title:before {
        content: "";
        border-left: 4px solid #3370ff;
        height: 16px;
        margin-right: 20px;
    }
}

.report-container {
    padding-left: 0px;
    // display: none;
    .report {
        height: calc(100vh - 242px);

        display: flex;
        // justify-content: space-around;
        .key-list {
            flex: 160px 0 0;
            padding-right: 10px;
            .search {
                padding: 16px 40px;
            }
            .list-title {
                height: 28px;
                line-height: 28px;
                padding-left: 20px;
                margin-bottom: 0px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #737677;
            }
            .list-content {
                overflow-y: overlay;
                max-height: calc(100vh - 260px);
                &::-webkit-scrollbar {
                    width: 5px;
                    height: 8px;
                    z-index: 999;
                    position: fixed;
                }
                &::-webkit-scrollbar-thumb {
                    border-radius: 3px;
                    background: #666666;
                    width: 6px;
                }
                &::-webkit-scrollbar-track {
                    background: white;
                }
                .report-list-item {
                    height: 36px;
                    font-size: 14px;
                    line-height: 36px;
                    padding: 0 20px;
                    color: #818899;
                    position: relative;
                    cursor: pointer;
                    user-select: none;
                    .el-icon-date {
                        margin-right: 10px;
                    }
                    &:hover {
                        background: rgba(228, 242, 253, 0.5);
                    }
                    .list-more-icon {
                        display: none;
                    }
                    &.checked {
                        background: #e4f2fd;
                    }
                    &:hover {
                        .list-more-icon {
                            position: absolute;
                            float: right;
                            top: 6px;
                            right: 6px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            width: 24px;
                            height: 24px;
                            border-radius: 50%;
                            &:hover {
                                background: rgba(3, 14, 44, 0.04);
                            }
                            i {
                                transform: rotate(90deg);
                                font-weight: 200;
                            }
                        }
                    }
                }
            }
        }
        .content {
            // overflow: auto;
            flex: 1;
            // padding: 10px 20px;
            // min-width: 1390px;
            height: calc(100% - 20px);
            width: 100%;
            padding-right: 5px;
            // padding-left: 1px;
            // overflow: auto;
            //height: calc(3000px);
            &::-webkit-scrollbar {
                width: 5px;
                height: 8px;
                z-index: 999;
                position: fixed;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 3px;
                background: #666666;
                width: 6px;
            }
            &::-webkit-scrollbar-track {
                background: white;
            }
            .mwd-container {
                height: calc(100%);
                overflow: auto;
                padding-right: 4px;
                padding-bottom: 10px;
                padding-top: 10px;
                &::-webkit-scrollbar {
                    width: 5px;
                    height: 8px;
                    z-index: 999;
                    position: fixed;
                }
                &::-webkit-scrollbar-thumb {
                    border-radius: 3px;
                    background: #666666;
                    width: 6px;
                }
                &::-webkit-scrollbar-track {
                    background: white;
                }
            }
        }
    }
}
</style>
<style lang="scss">

.project-dropdown{
  //设置高度才能显示出滚动条 !important
  max-height:400px;
  overflow: auto;
  .project-dropdown-item{
    position: relative;
    line-height: 1;
    span{
      line-height: 26px;
    }
    &:hover{
      .el-icon-circle-close{
        display: inline-block;
      }
    }
    .el-icon-circle-close{
      vertical-align: middle;
      display: none;
      position: absolute;
      bottom: 6px
    }
  }
}
.project-dropdown::-webkit-scrollbar
{
    width: 5px;
    height: 5px;
    background-color: #F5F5F5;
}
.project-dropdown::-webkit-scrollbar-track
{
    //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    border-radius: 10px;
    background-color: #F5F5F5;
}
</style>