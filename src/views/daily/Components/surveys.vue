<template>
    <div class="content" style="position:relative" v-loading="showLoading">
        <div
            style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
        >
            <span
                class="report-title"
                style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
            >
                {{ $REPORT_TYPE_MAP.SURVEYS }}
            </span>
            <el-select
                v-model="selectedSurveyId"
                @change="onSelectRun"
                placeholder="请选择测斜"
                style="margin-left:10px"
            >
                <el-option
                    v-for="(item, key) in currentSurveyList"
                    :key="`run${key}`"
                    :label="`测斜-${item.surveyNumber}`"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted&&!globalDisableEdit">
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left:10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left:10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div v-if="showReport" class="mwd-container">
            <div class="content-container2" style="margin-bottom:20px;">
                <!-- block -->
                <div class="line">
                    <div class="box-border">
                        <div class="line" style="flex:1">
                            <div class="line">
                                <div class="title2 center" style="flex:20% 0 0">
                                    公司/COMPANY
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <el-input
                                        class="mwd-input"
                                        :disabled="!isEdit"
                                        v-model="report.company"
                                    ></el-input>
                                </div>
                                <div class="title2 center" style="flex:20% 0 0">
                                    磁偏角/MAG DEC °
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <InputNumber
                                        class="mwd-input"
                                        :disabled="!isEdit"
                                        v-model="report.magDec"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line">
                                <div class="title2 center" style="flex:20% 0 0">
                                    井名/WELL NAME
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <el-input
                                        class="mwd-input"
                                        disabled
                                        v-model="well"
                                    ></el-input>
                                </div>
                                <div class="title2 center" style="flex:20% 0 0">
                                    测斜零长/BIT TO SENSOR m
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <InputNumber
                                        class="mwd-input"
                                        :disabled="!isEdit"
                                        v-model="report.bitToSensor"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line">
                                <div class="title2 center" style="flex:20% 0 0">
                                    工单号/JOB NUMBER
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <el-input
                                        class="mwd-input"
                                        disabled
                                        v-model="job"
                                    ></el-input>
                                </div>
                                <div class="title2 center" style="flex:20% 0 0">
                                    钻头类型/BIT TYPE
                                </div>
                                <div class="text left" style="flex:1 0 0">
                                    <el-input
                                        class="mwd-input"
                                        :disabled="!isEdit"
                                        v-model="report.bitType"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- block -->
                <div class="box-border" style="border-top:none">
                    <div class="line">
                        <div class="center title" style="flex:1">日期</div>
                        <div class="center title" style="flex:1">钻头位置 m</div>
                        <div class="center title" style="flex:1">测深 m</div>
                        <div class="center title" style="flex:1">井斜 °</div>
                        <div class="center title" style="flex:1">方位 °</div>
                        <div class="center title" style="flex:1">磁倾角 °</div>
                        <div class="center title" style="flex:1">磁场强度 nT</div>
                        <div class="center title" style="flex:1">重力场 gu</div>
                        <div class="center title" style="flex:1">电池电压 V</div>
                        <div class="center title" style="flex:1">温度 ℃</div>
                        <div class="center title" style="flex:1">地层</div>
                        <div class="center title" style="flex:1">岩芯</div>
                        <div class="center title" style="flex:200px 0 0">
                            备注
                        </div>
                    </div>
                    <div class="line" style="flex:8">
                        <div class="center title" style="flex:1">DATE</div>
                        <div class="center title" style="flex:1">BIT DEPTH</div>
                        <div class="center title" style="flex:1">
                            SURVEY DEPTH
                        </div>
                        <div class="center title" style="flex:1">INC.</div>
                        <div class="center title" style="flex:1">AZI.</div>
                        <div class="center title" style="flex:1">DipA</div>
                        <div class="center title" style="flex:1">MagF</div>
                        <div class="center title" style="flex:1">Grav</div>
                        <div class="center title" style="flex:1">BatV</div>
                        <div class="center title" style="flex:1">Temp</div>
                        <div class="center title" style="flex:1"></div>
                        <div class="center title" style="flex:1"></div>
                        <div class="center title" style="flex:200px 0 0">
                            Comments
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.activityList"
                        :key="`activityList` + key"
                    >
                        <i
                            v-if="
                                report.activityList.length >
                                    toolsSurveysActivityLength && isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size:15px;"
                            @click="onClickDelete('activityList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size:15px;"
                            v-if="isEdit"
                            @click="onClickAdd('activityList', key)"
                        ></i>
                        <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.bitDepth"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                @change="onSurveyDepthChange(key)"
                                v-model="item.surveyDepth"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.inc"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.azi"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.dipa"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.magf"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.grav"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.batv"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                type="text"
                                v-model="item.temp"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.formation"
                            >
                            </div>
                            <div class="center text" style="flex:1">
                            <input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.rockCore"
                            >
                            </div>
                            <div class="center text" style="flex:200px 0 0">
                            <input
                                class="mwd-input"
                                type="textarea"
                                :disabled="!isEdit"
                                v-model="item.comments"
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    toolsSurveysActivityLength,
    getInitSURVEYS,
    getSurveysActivityTmp as activityListTmp,
} from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "SURVEYS";
export default {
    name: "SURVEYS",
    data() {
        return {
            getInitSURVEYS,
            report: {},
            toolsSurveysActivityLength,
            showReport: false,
            isEdit: false,
            showLoading: true,
            currentSurveyList: [],
            selectedSurveyId: null
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        run: String,
        job: String,
        jobId: Number,
        jobStatus: Number,
        globalDisableEdit:Boolean,
        well: String
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    created() {
        this.report = getInitSURVEYS();
    },
    methods: {
        activityListTmp,
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        onSelectRun() {
            this.loadReport();
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentSurveyList = baseInfo.data?.data || [];
            const idx = this.currentSurveyList.findIndex((item) => {
                return item.id === this.selectedSurveyId;
            });
            if (idx != -1) {
                this.getReportInfo(this.selectedSurveyId);
            } else {
                let reportId = this.currentSurveyList[0]?.id;
                if (reportId) {
                    this.selectedSurveyId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedSurveyId = null;
                    this.hideReport();
                }
            }
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
};
</script>
<style lang="scss">
.center.text{
  input{
    border: none;
    text-align: center;
    width: 100% !important;
    height: 100% !important;
    &:focus{
      outline: none;
    }
  }
}

</style>
