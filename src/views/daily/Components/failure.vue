<template>
    <ImageViewer class="content failure-report" v-loading="showLoading">
        <div
            style="
                z-index: 999;
                height: 50px;
                display: flex;
                align-items: center;
                min-width: 700px;
                background: white;
                border-bottom: 1px dashed #eee;
            "
        >
            <span
                class="report-title"
                style="
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #333333;
                "
            >
                {{ $REPORT_TYPE_MAP.FAILURE }}
            </span>
            <el-select
                style="margin-left: 10px"
                v-model="selectedDateId"
                @change="onSelectDate"
            >
                <el-option
                    v-for="(item, key) in currentDateList"
                    :key="`date${key}`"
                    :label="item.date"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted && !globalDisableEdit">
                <el-popover
                    v-model="showAddPopover"
                    trigger="click"
                    placement="right-start"
                >
                    <div class="add-popover">
                        <div class="add-popover-title">新增</div>

                        <div class="calender" style="width: 400px">
                            <el-calendar v-model="orginDateAdd"> </el-calendar>
                        </div>

                        <div class="add-popover-btn">
                            <el-button
                                @click="onConfirmAddFailure"
                                size="small"
                                type="primary"
                            >
                                确 定
                            </el-button>
                            <el-button
                                @click="showAddPopover = false"
                                size="small"
                            >
                                取 消
                            </el-button>
                        </div>
                    </div>

                    <el-button
                        slot="reference"
                        style="margin-left: 10px"
                        type="primary"
                        size="small"
                    >
                        <span style="font-size: 14px">
                            <i class="el-icon-plus"></i>
                            新增失效报告
                        </span>
                    </el-button>
                </el-popover>
                <template v-if="enableEdit">
                    <el-button
                        @click="onClickDeleteFailure"
                        type="danger"
                        icon="el-icon-delete"
                        style="margin-left: 10px"
                    >
                        删除报告
                    </el-button>
                    <el-button
                        v-if="!isEdit"
                        type="primary"
                        @click="onEditReport"
                        style="margin-left: 10px"
                    >
                        编辑
                    </el-button>
                    <el-button
                        v-if="isEdit"
                        style="margin-left: 10px"
                        type="primary"
                        @click="onSaveReport"
                    >
                        保存
                    </el-button>
                    <el-button
                        v-if="isEdit"
                        style=""
                        @click="onCancelEditReport"
                    >
                        取消
                    </el-button>
                </template>
            </template>
        </div>
        <div
            v-if="showReport"
            class="mwd-container"
            style="margin-bottom: 20px"
        >
            <div class="content-container2">
                <div class="line">
                    <div class="line" style="flex: 1; margin-bottom: 10px">
                        <div class="line" style="flex: 400px 0 0; padding: 5px">
                            <div class="info-title">工程师MWD Operator：</div>

                            <el-input
                                class="mwd-input basic-info-input"
                                :disabled="!isEdit"
                                v-model="report.operator"
                            ></el-input>
                        </div>
                        <div class="line" style="flex: 300px 0 0; padding: 5px">
                            <div class="info-title">Kit #：</div>
                            <!-- TODO: kitId -> kit  -->
                            <el-input
                                class="mwd-input basic-info-input"
                                :disabled="!isEdit"
                                v-model="report.kit"
                            ></el-input>
                        </div>
                        <div class="line" style="flex: 300px 0 0; padding: 5px">
                            <div class="info-title">趟次：</div>
                            <el-select
                                @change="onRunChanged"
                                placeholder=""
                                class="mwd-input basic-info-input"
                                :disabled="!isEdit"
                                v-model="report.run"
                            >
                                <el-option
                                    v-for="run in runList"
                                    :label="run.run"
                                    :key="run.id"
                                    :value="run.run"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
                <div class="box-border">
                    <div class="line">
                        <div class="title2 center" style="flex: 25% 0 0">
                            客户Client Company
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.client"
                            ></el-input>
                        </div>
                        <div class="title2 center" style="flex: 25% 0 0">
                            井队号Drilling Rig
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.drillingRig"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title2 center" style="flex: 25% 0 0">
                            井号Well Name
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.wellNumber"
                            ></el-input>
                        </div>
                        <div class="title2 center" style="flex: 25% 0 0">
                            入井时间 Downhole Time
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.inHoleDate"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title2 center" style="flex: 25% 0 0">
                            位置Location
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.location"
                            ></el-input>
                        </div>
                        <div class="title2 center" style="flex: 25% 0 0">
                            失效时间Failure Time
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-date-picker
                                class="mwd-input"
                                :disabled="!isEdit"
                                format="yyyy-MM-dd HH:mm"
                                value-format="yyyy-MM-dd HH:mm"
                                v-model="report.failureDate"
                            ></el-date-picker>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title2 center" style="flex: 25% 0 0">
                            区块Field
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.field"
                            ></el-input>
                        </div>
                        <div class="title2 center" style="flex: 25% 0 0">
                            报告日期Report Date
                        </div>
                        <div class="text left" style="flex: 25% 0 0">
                            <el-date-picker
                                class="mwd-input"
                                :disabled="!isEdit"
                                value-format="yyyy-MM-dd"
                                v-model="report.reportDate"
                            ></el-date-picker>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <!-- TODO: 拆！ -->
                    <div class="line">
                        <div class="title center" style="flex: 100% 0 0">
                            MWD TOOLS
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            部件名称Components
                        </div>
                        <div class="title center" style="flex: 1 0 0">
                            序列号Serial #
                        </div>
                        <div class="title center" style="flex: 1 0 0">数量</div>
                        <div class="title center" style="flex: 1 0 0">
                            循环Hours
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.toolList"
                        :key="`toolList` + item.stockId + key"
                    >
                        <i
                            v-if="
                                report.toolList.length > failureToolLen &&
                                isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size: 15px"
                            @click="onClickDelete('toolList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size: 15px"
                            v-if="isEdit"
                            @click="onClickAdd('toolList', key)"
                        ></i>
                        <div class="text center" style="flex: 1 0 0">
                            <el-input
                                v-if="!isEdit"
                                class="mwd-input"
                                disabled
                                v-model="item.invName"
                            ></el-input>
                            <el-select
                                v-else
                                clearable
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.invName"
                                @change="onInvNameChange(item, key)"
                            >
                                <el-option
                                    v-for="invName in uniqueInvNameList"
                                    :key="invName"
                                    :label="invName"
                                    :value="invName"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <el-select
                                clearable
                                @change="onSerialNumberChange(item, key)"
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.serialNumber"
                            >
                                <el-option
                                    v-for="serialNumber in filterSNList(item.invName)"
                                    :key="serialNumber"
                                    :label="serialNumber"
                                    :value="serialNumber"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit || !!item.serialNumber"
                                v-model="item.quantity"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.total"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1">
                            失效环境Pre Failure Conditions
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            排量Flow Rate L/S
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.flowRate"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            限流环Orfice
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.orfice"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            蘑菇头Poppet
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.poppet"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            脉宽PulseWth
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.pulsewth"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            井下时间Run Hours
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runHours"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            测深Meas. Depth
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.measDepth"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            井斜Inc
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.inc"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            方位Azm
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.azm"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            磁偏角MagDec
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.magdec"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            温度Temp
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.temp"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 1 0 0">
                            电池电压Battery Volts:
                        </div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.batteryVolts"
                            ></InputNumber>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="title center" style="flex: 100% 0 0">
                            失效描述SYMPTOMS OF FAILURE
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text center"
                            style="flex: 100% 0 0; height: 250px; width: 100%"
                            v-if="isEdit"
                        >
                            <quill-editor
                                class="editor"
                                ref="myTextEditor"
                                v-model="report.failureSymptoms"
                                :options="editorOption"
                                @blur="onEditorBlur($event)"
                                @focus="onEditorFocus($event)"
                                @ready="onEditorReady($event)"
                            />
                            <form
                                action
                                method="post"
                                enctype="multipart/form-data"
                                id="uploadFormMulti"
                            >
                                <input
                                    style="display: none"
                                    :id="uniqueId"
                                    type="file"
                                    name="file"
                                    multiple
                                    accept="image/jpg,image/jpeg,image/png,image/gif"
                                    @change="uploadImg('uploadFormMulti')"
                                />
                            </form>
                        </div>
                        <div
                            class="text left"
                            v-else
                            style="
                                flex: 100% 0 0;
                                width: 100%;
                                min-height: 120px;
                                height: auto;
                            "
                        >
                            <div
                                v-html="report.failureSymptoms"
                                class="rich-text-cls"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="title center" style="flex: 100% 0 0">
                            {{ `采取措施及效果ACTIONS TAKEN & RESULTS` }}
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text left"
                            style="
                                flex: 100% 0 0;
                                width: 100%;
                                min-height: 120px;
                                height: auto;
                            "
                        >
                            <div
                                v-html="report.actionTaken"
                                class="rich-text-cls"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="title center" style="flex: 100% 0 0">
                            {{
                                `地面检查及结果SURFACE INSPECTION AND FINDINGS`
                            }}
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text left"
                            style="
                                flex: 100% 0 0;
                                width: 100%;
                                min-height: 120px;
                                height: auto;
                            "
                        >
                            <div
                                v-html="report.surfaceInspection"
                                class="rich-text-cls"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="title center" style="flex: 100% 0 0">
                            {{ `车间调查结果Shop inspectionc` }}
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text left"
                            style="
                                flex: 100% 0 0;
                                width: 100%;
                                min-height: 120px;
                                height: auto;
                            "
                        >
                            <div
                                v-html="report.shopInspectionc"
                                class="rich-text-cls"
                            ></div>
                        </div>
                    </div>
                    <div class="file-list" v-if="fileList&&fileList.length">
                        <div class="file-item" v-for="item in fileList" :key="item.massId">
                            <span :title="item.massFileName" class="file-title">
                                {{ item.massFileName }}
                            </span>
                            <a :href="item.massFilePath">
                                <i class="el-icon-download oper-icon"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="box-border" style="border-top:none">
                    <div class="line">
                        <div class="title center" style="flex:100% 0 0">
                            改进计划/措施
                        </div>
                    </div>
                    <div class="line">
                        <div class="text left" style="flex:100% 0 0;width:100%;min-height:120px;height:auto">
                            <div v-html="report.improvementPlanMeasures"></div>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="line" style="flex: 20% 0 0">
                            <div
                                class="title center"
                                style="flex: 1; height: auto"
                            >
                                FAILED COMPONENT(S)2:
                            </div>
                        </div>
                        <div class="line" style="flex: 1">
                            <div class="line">
                                <div
                                    @click="
                                        isEdit &&
                                            (component.failed =
                                                !component.failed)
                                    "
                                    v-for="component in report.componentList"
                                    :key="component.componentId"
                                    class="text center"
                                    style="flex: 14.28% 0 0; cursor: pointer"
                                    :class="{ failed: component.failed }"
                                >
                                    {{ component.name }}
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 14.28% 0 0"
                                    v-for="i in 7 -
                                    (report.componentList.length % 7 || 7)"
                                    :key="i"
                                ></div>
                            </div>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 20% 0 0">
                            井下时间TOTAL DOWNTIME:
                        </div>
                        <div class="text center" style="flex: 22.9% 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.totalDowntime"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 22.9% 0 0">
                            发现失效原因PROBLEM RECTIFIED
                        </div>
                        <div class="text left" style="flex: 1 0 0">
                            <el-select
                                v-model="report.findProblem"
                                :disabled="!isEdit"
                                class="mwd-input"
                                placeholder=""
                            >
                                <el-option
                                    v-for="item in findProblemList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </ImageViewer>
</template>

<script>
import {
    getInitFAILURE,
    failureToolLen,
    getFailureToolTmp as toolListTmp,
} from "./data";
import {
    apiAddDailyReport,
    apiDeleteDailyReport,
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiGetFailureShopFile,
    apiUpdateDailyReport,
    getToolRunToolList,
} from "@/api/mwd";
import dealReport from "./init";
import getDict from '@/utils/getDict';
const REPORT_TYPE = "FAILURE";
import ImageViewer from "@/components/ImageViewer/index.vue";
export default {
    name: "FAILURE",
    components: { ImageViewer },
    data() {
        let self = this;
        return {
            getInitFAILURE,
            failureToolLen,
            uniqueId: "uniqueId",
            uploadType: "image",
            insertPosition: -1,
            report: {},
            showReport: false,
            kitBoxItemList: [],
            runListByRun: [],
            selectedDateId: null,
            currentDateList: [],
            showAddPopover: false,
            orginDateAdd: "",
            isEdit: false,
            findProblemList: [
                { id: 1, name: "是YES" },
                { id: 2, name: "否NO" },
                { id: 3, name: "其他OTHER" },
            ],
            //content: ``,
            editorOption: {
                modules: {
                    toolbar: {
                        container: [
                            ["bold", "italic", "underline", "strike"],
                            [{ header: 1 }, { header: 2 }],
                            [{ list: "ordered" }, { list: "bullet" }],
                            [{ script: "sub" }, { script: "super" }],
                            [{ size: ["small", false, "large", "huge"] }],
                            [{ color: [] }, { background: [] }],
                            [{ align: [] }],
                            ["clean"],
                            ["link", "image"],
                        ],
                        handlers: {
                            image() {
                                self.uploadType = "image";

                                let quill = self.$refs.myTextEditor.quill;
                                // 获取光标所在位置
                                document.getElementById("uniqueId").click();
                                self.insertPosition =
                                    quill.getSelection().index;
                            },
                            video() {
                                self.uploadType = "video";

                                let quill = self.$refs.myTextEditor.quill;
                                // 获取光标所在位置
                                self.insertPosition =
                                    quill.getSelection().index;
                            },
                        },
                    },
                },
            },
            showLoading: true,
            fileList: [
            ],
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        selectedDate: String,
        run: String,
        job: String,
        jobId: Number,
        kitBoxId: Number,
        runList: Array,
        failedComponentList: Array,
        toolList: Array,
        jobStatus: Number,
        globalDisableEdit: Boolean,
    },
    computed: {
        uniqueInvNameList() {
            return [...new Set(this.toolList.map((item) => item.invName))];
        },
        // 库房确认的失效报告不能编辑！
        enableEdit() {
            return this.report.isConfirm !== 1;
        },
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    watch: {
        selectedDateId(newVal) {
            let map = this.currentDateList.find((item) => {
                return item.id == newVal;
            });
            if (map) {
                this.$emit("updateCommonDate", map.date);
            }
        },
    },
    mounted() {},
    methods: {
        toolListTmp,
        uploadImg() {
            let quill = this.$refs.myTextEditor.quill;
            let img = document.getElementById(this.uniqueId).files[0];
            var reader = new FileReader();
            reader.readAsDataURL(img);
            reader.onload = (ev) => {
                //文件读取成功完成时触发
                var dataURL = ev.target.result;
                // 插入图片，res为服务器返回的图片链接地址
                quill.insertEmbed(
                    this.insertPosition,
                    this.uploadType,
                    dataURL
                );
                // 调整光标到最后
                quill.setSelection(this.insertPosition + 1);
            };
        },
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },

        onEditorBlur(editor) {
            //console.log("editor blur!", editor);
        },
        onEditorFocus(editor) {
            //console.log("editor focus!", editor);
        },
        onEditorReady(editor) {
            //console.log("editor ready!", editor);
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },
        onInvNameChange(item, key) {
            // TODO: 清空改行所有值
            let tmp = this.toolListTmp();
            tmp.invName = item.invName;
            this.report.toolList[key] = Object.assign(this.report.toolList[key], tmp);
        },
        filterSNList(invName) {
            const filterToolList = this.toolList.filter(
                (tool) => (!invName || tool.invName === invName) && tool.serialNumber
            );
            const serialNumberList = filterToolList.map((tool) => tool.serialNumber);
            return [...new Set(serialNumberList)];
        },
        onSerialNumberChange(item, key) {
            if (!item.invName) {
                this.report.toolList[key].invName = this.toolList.find(
                (tool) => tool.serialNumber === item.serialNumber
                )?.invName;
            }
            this.report.toolList[key].serialNumber = item.serialNumber;
        },
        DateDiff(interval, start, end) {
            start = new Date(start);
            end = new Date(end);
            interval = interval.toUpperCase();
            let hours = 0;
            if (end > start) {
                hours = parseFloat(
                    Math.abs(end - start) /
                        eval("(objInterval." + interval + ")")
                ).toFixed(2);
            }
            return hours;
        },
        onCalculateTotalTime() {
            //report.failureDate report.inHoleDate report.totalDowntime
            this.report.totalDowntime = this.DateDiff(
                "H",
                this.report.inHoleDate,
                this.report.failureDate
            );
        },
        checkDuplicate(arr) {
            let nonEmptyArr = arr
                .map((item) => item.serialNumber)
                .reduce((acc, cur) => {
                    if (cur) {
                        return acc.concat(cur);
                    }
                    return acc;
                }, []);
            let nonEmptySet = new Set(nonEmptyArr);
            return nonEmptyArr.length !== nonEmptySet.size;
        },
        updateReport() {
            if (!this.report.run && this.report.run !== 0) {
                this.$message.error("请选择趟次！");
                return;
            }
            if (this.checkDuplicate(this.report.toolList)) {
                this.$message.error("工具列表中有重复的序列号！");
                return;
            }
            apiUpdateDailyReport({
                reportType: "FAILURE",
                ...this.report,
            }).then(() => {
                this.$emit("setIsEditAsFalse");
            });
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentDateList = baseInfo.data?.data || [];
            this.currentDateList.reverse();
            const idx = this.currentDateList.findIndex((item) => {
                return item.date === this.selectedDate;
            });
            let reportId;
            if (idx != -1) {
                reportId = this.currentDateList[idx].id;
                this.selectedDateId = reportId;
                this.getReportInfo(reportId);
            } else {
                reportId = this.currentDateList[0]?.id;
                if (reportId) {
                    this.selectedDateId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedDateId = null;
                    this.hideReport();
                }
            }
        },
        onSelectDate() {
            this.loadReport();
        },
        onClickDeleteFailure() {
            let form = new FormData();
            form.append("reportId", String(this.selectedDateId));
            form.append("reportType", REPORT_TYPE);
            this.$confirm("确认要删除该报告吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiDeleteDailyReport(form).then(() => {
                    this.loadReport();
                });
            });
        },
        onConfirmAddFailure() {
            const date = this.orginDateAdd || new Date();
            const dateAdd = date.Format("yyyy-MM-dd");
            if (this.currentDateList.some((item) => item.date === dateAdd)) {
                this.$message.error("报告日期不能重复！");
                return;
            }
            const initReport = this.getInitFAILURE();
            let postReport = Object.assign(initReport, {
                date: dateAdd,
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            apiAddDailyReport(postReport).then(() => {
                this.loadReport();
                this.showAddPopover = false;
            });
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            })
                .then(async (res) => {
                    this.report = dealReport(res.data.data, REPORT_TYPE);
                    this.getFailureShopFileList();
                    if(!this.report.componentList?.length){
                        const [failedComponentList] = await getDict([10]);
                        this.report.componentList = failedComponentList.map(item=>{
                            return {
                                componentId: item.id,
                                name: item.name,
                                failed: false
                            }
                        })
                    }
                    this.$nextTick(() => {
                        this.showReport = true;
                    });
                })
                .finally(() => {
                    this.showLoading = false;
                });
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
        getCurrentSerialNumberList(invName) {
            if (!invName) {
                return [];
            }
            let idx = this.kitBoxItemList.findIndex(
                (item) => item.invName === invName
            );
            return idx < 0
                ? []
                : this.kitBoxItemList[idx].itemSpecificList || [];
        },
        onRunChanged() {
            this.getRunToolList();
        },
        getRunToolList() {
            getToolRunToolList({
                jobId: this.jobId,
                run: this.report.run,
            }).then((res) => {
                this.report.toolList = res.data.data || [this.toolListTmp()];
            });
        },
        
        getFailureShopFileList() {
            apiGetFailureShopFile({
                jobId: this.jobId,
                date: this.selectedDate,
            },{
                current:1,
                size: 99999
            }).then(res=>{
                console.log(res)
                this.fileList = res.data.data.records || [];
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.img-container {
    position: relative;
    height: 100%;
    &:hover {
        img {
            filter: brightness(0.2);
        }
        .img-icon-container {
            opacity: 1;
        }
    }
    .img-icon-container {
        width: 100%;
        height: 20px;
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        opacity: 0;
        .img-icon {
            margin-left: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
    }
}
.failed {
    background-color: #f56c6c !important;
}
.problem {
    background-color: #f56c6c !important;
}

.info-title {
    //   flex: 150px 0 0;
    color: #3c4d73;
}
.basic-info-input {
    border-bottom: 1px solid #ccd3d9;
    //   max-width: 200px;
    flex: 50% 0 0;
}
</style>

<style lang="scss" scoped>
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>