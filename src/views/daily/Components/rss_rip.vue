<template>
  <ImageViewer class="content rigin-report" v-loading="showLoading" style="position:relative">
    <div
      style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
    </div>
    <div v-if="showReport" class="mwd-container">
      <div class="content-container2">
        <div class="basic-info">
          <div class="info-container">
            <div class="info-title" style="flex: 60px 0 0;">
              Engineer：
            </div>
            <el-input
              :disabled="!isEdit"
              v-model="report.operator"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title" style="flex: 160px 0 0;">
              工单号Job Number：
            </div>
            <el-input
              disabled
              v-model="job"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
        </div>
        <div class="box-border">
          <div class="line">
            <div class="title center" style="flex:60px 0 0">#</div>
            <div class="title center" style="flex:1">
              项目Action
            </div>
            <div class="title center" style="flex:200px 0 0">
              日期Date
            </div>
            <div class="title center" style="flex:200px 0 0">
              Initial
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              1
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              维护部同意出库确认单
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[0].date"
              ></el-date-picker>

              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[0].date"
                            class="mwd-input"
                        ></el-input> -->
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[0].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              2
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              所有到井工具探伤报告
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[1].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[1].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[1].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              3
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              工厂测试合格报告
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[2].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[2].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              4
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              标定报告（探管/伽马）
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[3].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[3].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              5
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              上井清单
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[4].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[4].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              6
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              服务类型、井位置（定位、路况、天气情况）、有无工作间、住宿
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[5].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[5].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              7
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              钻井设计、实钻数据、本井参数（GT/BT/TAC/ROP/DIP/WH COORDINATES）、NO-GO-ZONE
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[6].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[6].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              8
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              滤子型号
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[7].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[7].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              9
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              9.5寸、10寸钳头（装钻头）
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[8].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[8].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              10
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              温度情况
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[9].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[9].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              11
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              施工难点（井下异常）
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[10].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[10].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              12
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              工具打捞图
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.actionList[11].date"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[11].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div
              class="text left"
              style="flex:100% 0 0;width:100%;min-height:120px;height:auto"
            >
              <div v-html="report.screenShoot" class="rich-text-cls"></div>
            </div>
          </div>
        </div>
        <div class="basic-info">
          <div class="info-container">
            <div class="info-title">MWD Engineer:</div>
            <el-input
              :disabled="!isEdit"
              v-model="report.mwdOEngineer"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">Signature & Date:</div>
            <!-- <el-input
                        :disabled="!isEdit"
                        v-model="report.signatureDate"
                        class="mwd-input basic-info-input"
                    ></el-input> -->
            <el-date-picker
              class="mwd-input basic-info-input"
              v-model="report.signatureDate"
              type="date"
              placeholder=""
              :disabled="!isEdit"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </div>
      </div>
      <el-dialog
        :visible.sync="showImgDialog"
        class="big-img-dialog"
        :close-on-click-modal="false"
      >
        <img width="100%" :src="report.screenShoot" alt="" />
      </el-dialog>
    </div>
  </ImageViewer>
</template>

<script>
import { getInitRSS_RIP_REPORT } from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_RIPROCEDURES";
import ImageViewer from "@/components/ImageViewer/index.vue";
export default {
  name: "RSS_RIPROCEDURES",
  components: { ImageViewer },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    run: String,
    job: String,
    jobId: Number,
    jobStatus: Number,
    globalDisableEdit:Boolean
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  data() {
    return {
      REPORT_TYPE,
      getInitRSS_RIP_REPORT,
      dialogImageUrl: "",
      showImgDialog: false,
      report: {},
      showReport: false,
      isEdit: false,
      showLoading: true,
    };
  },
  created() {
    this.report = getInitRSS_RIP_REPORT();
  },
  methods: {
    async loadReport() {
        this.showLoading = true;
        const baseInfo = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: REPORT_TYPE,
        });
        const baseInfoList = baseInfo.data?.data || [];
        if (baseInfoList.length === 0) {
            this.$message.error("暂无相关数据");
            this.showLoading = false;
            return;
        }
        const reportId = baseInfoList[0].id;
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        }).then((res) => {
            this.report = dealReport(res.data.data, REPORT_TYPE);
            this.$nextTick(() => {
                this.showReport = true;
            });
        }).finally(()=>{
            this.showLoading = false;
        });
    },
    hideReport() {
        this.showLoading = false;
        this.showReport = false;
    },
  },
};
</script>
<style lang="scss">
.rich-text-cls{
  p{
    margin:5px 0;
  }
  line-height: 1;
}
.auto {
  height: auto !important;
}
</style>
<style lang="scss" scoped>
.img-container {
  position: relative;
  height: 100%;
  &:hover {
    img {
      filter: brightness(0.2);
    }
    .img-icon-container {
      opacity: 1;
    }
  }
  .img-icon-container {
    width: 100%;
    height: 20px;
    position: absolute;
    top: 50%;
    display: flex;
    justify-content: center;
    opacity: 0;
    .img-icon {
      margin-left: 20px;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
  }
}
.basic-info {
  display: flex;
  justify-content: flex-start;
  // line-height: 3.6;
  margin-left: 10px;
  height: 40px;
  .info-container {
    display: flex;
    align-items: center;
    .info-title {
      flex: 150px 0 0;
    }
    .basic-info-input {
      border-bottom: 1px solid #ccd3d9;
      min-width: 200px;
      max-width: 260px;
    }
  }
  div {
    flex: 400px 0 0;
    text-align: right;
  }
}
</style>
