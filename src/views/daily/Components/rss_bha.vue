<template>
  <div class="content" v-loading="showLoading">
    <div
      style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
      <el-select
          v-model="selectedRunId"
          @change="onSelectRun"
          placeholder="请选择趟次"
          style="margin-left: 10px"
      >
          <el-option
              v-for="(item, key) in currentRunList"
              :key="`run${key}`"
              :label="item.run"
              :value="item.id"
          ></el-option>
      </el-select>
    </div>
    <div class="mwd-container" v-if="showReport">
      <div class="content-container2">
        <div class="basic-info">
          <div class="info-container">
            <div class="info-title">公司Company：</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.company"
              :disabled="!isEdit"
              ref="defaultFocusInput"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">开始时间Start Date:</div>
            <el-date-picker
              class="mwd-input basic-info-input"
              v-model="report.startDate"
              type="date"
              placeholder=""
              :disabled="!isEdit"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
          <div class="info-container">
            <div class="info-title">组合BHA #:</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.run"
              disabled
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">承包商Contractor：</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.contractor"
              :disabled="!isEdit"
            ></el-input>
          </div>
        </div>
        <div class="basic-info">
          <div class="info-container">
            <div class="info-title">井号Well Name：</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.wellNumber"
              :disabled="true"
            ></el-input>
          </div>

          <div class="info-container">
            <div class="info-title">井队号Rig No.：</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.rigNo"
              :disabled="!isEdit"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">位置Location No：</div>
            <el-input
              class="mwd-input basic-info-input"
              v-model="report.location"
              :disabled="!isEdit"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">工单号Job No：</div>
            <el-input
              disabled
              class="mwd-input basic-info-input"
              v-model="job"
            ></el-input>
          </div>
        </div>
        <div class="basic-info">
          <div class="info-container" style="flex: 1">
            <div class="info-title">入井井深Depth In m：</div>
            <InputNumber
              class="mwd-input basic-info-input"
              v-model="report.depthIn"
              :disabled="!isEdit"
            ></InputNumber>
          </div>
          <div class="info-container" style="flex: 1">
            <div class="info-title">出井井深Depth Out m：</div>
            <InputNumber
              class="mwd-input basic-info-input"
              v-model="report.depthOut"
              :disabled="!isEdit"
            ></InputNumber>
          </div>
          <div class="info-container" style="flex: 2">
            <div class="info-title" style="flex: 350px 0 0">
              该钻具组合目的Objective For Running BHA：
            </div>
            <el-input
              class="mwd-input left basic-info-input"
              v-model="report.purpose"
              style="max-width: 2020px"
              :disabled="!isEdit"
            ></el-input>
          </div>
        </div>
        <div class="box-border">
          <div class="line">
            <div class="title center" style="flex: 1">
              项目ITEM No.
            </div>
            <div class="title center" style="flex: 1">
              名称Description
            </div>
            <div class="title center" style="flex: 1">
              厂家Vendor
            </div>
            <div class="title center" style="flex: 1">
              编号SERIAL #
            </div>
            <div class="title center" style="flex: 1">外径OD mm</div>
            <div class="title center" style="flex: 1">内径ID mm</div>
            <div class="title center" style="flex: 1">Gauge</div>
            <div class="title center" style="flex: 1">
              公扣Pin Connection
            </div>
            <div class="title center" style="flex: 1">
              母扣Box Connection
            </div>
            <div class="title center" style="flex: 1">
              长度Length m
            </div>
            <div class="title center" style="flex: 1">
              总长Total Length m
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.itemList"
            :key="`itemList` + key"
          >
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.itemNo"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.description"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.vendor"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.serialNum"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <InputNumber
                class="mwd-input"
                v-model="item.outerDiameter"
                :disabled="!isEdit"
              ></InputNumber>
            </div>
            <div class="text center" style="flex: 1">
              <InputNumber
                class="mwd-input"
                v-model="item.innerDiameter"
                :disabled="!isEdit"
              ></InputNumber>
            </div>
            <div class="text center" style="flex: 1">
              <InputNumber
                class="mwd-input"
                v-model="item.gauge"
                :disabled="!isEdit"
              ></InputNumber>
            </div>
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.pinConnection"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <el-input
                class="mwd-input"
                v-model="item.boxConnection"
                :disabled="!isEdit"
              ></el-input>
            </div>
            <div class="text center" style="flex: 1">
              <InputNumber
                class="mwd-input"
                v-model="item.len"
                @change="onCalculateVal('itemList', item, key)"
                :disabled="!isEdit"
              ></InputNumber>
            </div>
            <div class="text center" style="flex: 1">
              <!-- :disabled="!isEdit" -->
              <InputNumber
                class="mwd-input"
                v-model="item.totalLen"
                disabled
              ></InputNumber>
            </div>
          </div>
        </div>
      </div>
      
      <div class="line">
        <div
          class="text left"
          style="flex:100% 0 0;width:100%;min-height:120px;height:auto"
        >
          <div v-html="report.cadence" class="rich-text-cls"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInitRSS_BHA_REPORT,
} from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_BHA";
export default {
  name: "RSS_BHA",
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    selectedRssRun: String,
    job: String,
    jobId: Number,
    jobStatus: Number,
    globalDisableEdit: Boolean,
  },
  data() {
    return {
      REPORT_TYPE,
      getInitRSS_BHA_REPORT,
      report: {},
      showReport: false,
      isEdit: false,
      currentRunList: [],
      selectedRunId: null,
      showLoading: true
    };
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  created() {
    this.report = getInitRSS_BHA_REPORT();
  },
  methods: {
    onSelectRun() {
        let map = this.currentRunList.find((item) => {
            return item.id == this.selectedRunId;
        });
        if (map) {
            this.$emit("updateCommonRssRun", map.run);
        }
        this.loadReport();
    },
    async loadReport() {
        this.showLoading = true;
        const baseInfo = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: REPORT_TYPE,
        });
        this.currentRunList = baseInfo.data?.data || [];
        const idx = this.currentRunList.findIndex((item) => {
            return item.run === this.selectedRssRun;
        });
        let reportId;
        if (idx != -1) {
            reportId = this.currentRunList[idx].id;
            this.selectedRunId = reportId;
            this.getReportInfo(reportId);
        } else {
            reportId = this.currentRunList[0]?.id;
            if (reportId) {
                this.selectedRunId = reportId;
                this.getReportInfo(reportId);
            } else {
                this.selectedRunId = null;
                this.hideReport();
            }
        }
    },
    getReportInfo(reportId) {
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        }).then((res) => {
            this.report = dealReport(res.data.data, REPORT_TYPE);
            this.$nextTick(() => {
                this.showReport = true;
            });
        }).finally(()=>{
            this.showLoading = false;
        });
    },
    hideReport() {
        this.showLoading = false;
        this.showReport = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.basic-info {
  display: flex;
  justify-content: space-between;
  // line-height: 3.6;
  height: 40px;
  .info-container {
    padding-right: 10px;
    display: flex;
    align-items: center;
    .info-title {
      flex: 190px 0 0;
      text-align: left;
    }
    .basic-info-input {
      border-bottom: 1px solid #ccd3d9;
      max-width: 230px;
    }
  }
  div {
    flex: 1;
    text-align: right;
  }
}
</style>
