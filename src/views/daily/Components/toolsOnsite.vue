<template>
    <div class="content" style="position:relative" v-loading="showLoading">
        <div
            style="z-index:999;padding: 10px 0; background:white;border-bottom:1px dashed #eee;overflow: hidden;"
        >
            <span
                class="report-title"
                style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <template v-if="!isJobCompleted&&!globalDisableEdit">
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left:10px"
                >
                    编辑
                </el-button>
                <el-button
                    v-if="isEdit"
                    style="margin-left:10px"
                    type="primary"
                    @click="onSaveReport"
                >
                    保存
                </el-button>
                <el-button v-if="isEdit" style="" @click="onCancelEditReport">
                    取消
                </el-button>
            </template>
            
            <el-button v-if="$checkBtnPermission('sys:daily:repiar:add') && showReport" style="margin-left:10px" type="primary" @click="onCreateRepair">
                创建返修单
            </el-button>
            <el-button v-if="$checkBtnPermission('sys:daily:transfer:all') && showReport" style="margin-left:10px" type="primary" @click="onCreateTransfer">
                创建调拨单
            </el-button>
            <el-button v-if="$checkBtnPermission('sys:daily:demand:all') && showReport" style="margin-left:10px" type="primary" @click="onCreateDemand">
                创建需求单
            </el-button>
            <el-button @click="onClassify" style="float: right;" type="primary">{{ isClassified ? '平铺显示' : '分类显示' }}</el-button>
        </div>
        <div class="mwd-container" v-if="showReport">
            <div class="content-container2">
                <div class="box-border">
                    <div class="line">
                        <div class="title center" style="flex:1">
                            仪器清单/LIST OF ALL DIRECTIONAL TOOLS ON SITE
                        </div>
                    </div>
                    
                    <template v-if="!isClassified">
                        <div class="line">
                            <div class="title center" style="flex:100px 0 0">
                                选择 <el-checkbox v-model="isSelectAll"></el-checkbox>
                            </div>
                            <div class="title center" style="flex:300px 0 0">
                                仪器类型/TOOL TYPE
                            </div>
                            <div class="title center" style="flex:600px 0 0">
                                序列号/SERIAL NUMBER
                            </div>
                            <div
                                class="title center"
                                title="备注栏填写施工期间XX工具何时从XX转到本井，工具状态，XX时间转到其他井，转井时工具状态"
                                style="flex:1"
                            >
                                备注
                            </div>
                            <div class="title center" style="flex:100px 0 0">
                                是否井下
                            </div>
                        </div>
                        <div
                            class="line hover-line"
                            v-for="(item, key) in report.toolList"
                            :key="`toolList` + key"
                        >
                            <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                                <el-checkbox
                                    style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                                    v-model="selectList[key]"
                                ></el-checkbox>
                            </div>
                            <div class="text left" style="flex:300px 0 0;height:auto">
                                <el-input
                                    :disabled="!isEdit"
                                    v-model="item.invName"
                                    class="mwd-input"
                                    spellcheck="false"
                                    ref="defaultFocusInput"
                                ></el-input>
                            </div>
                            <div class="text left" style="flex:600px 0 0;position:relative;height:auto">
                                <el-input
                                    :disabled="!isEdit"
                                    v-model="item.serialNumber"
                                    class="mwd-input"
                                    spellcheck="false"
                                ></el-input>
                            </div>
                            <div class="text left" style="flex:1;height:auto;position: relative;">
                                <el-input
                                :disabled="!isEdit"
                                class="mwd-input textarea-ofh"
                                type="textarea"
                                :autosize="{ minRows: 1 }"
                                v-model="item.description"
                                spellcheck="false"
                                ></el-input>
                            </div>
                            <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                                <el-checkbox
                                style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                                v-if="item.invName || item.serialNumber"
                                :disabled="!isEdit"
                                v-model="item.inTheHule"
                                ></el-checkbox>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="line">
                            <div class="title center" style="flex:600px 0 0">
                                序列号/SERIAL NUMBER
                            </div>
                            <div
                                class="title center"
                                style="flex:1"
                            >
                                备注
                            </div>
                            <div class="title center" style="flex:100px 0 0">
                                是否井下
                            </div>
                        </div>
                        <el-collapse v-model="activeNames">
                            <el-collapse-item class="custom-collapse-item" v-for="(tools,key) in invNameToolsMap" :key="key" :title="key" :name="key">
                                <template slot="title">
                                    <div style="flex: calc(100% - 32px) 0 0; text-align: center;">{{ key }}</div>
                                </template>
                                <div class="">
                                    <div
                                        class="line hover-line"
                                        v-for="(item, key) in tools"
                                        :key="`toolList` + key + item.serialNumber"
                                    >
                                        <div class="text center text-content" style="flex:600px 0 0;position:relative;height:auto;display: flex; justify-content: center; align-items: center;">
                                            <span>
                                                {{ item.serialNumber }}
                                            </span>
                                        </div>
                                        <div class="text left text-content" style="flex:1;height:auto;position: relative;">
                                            {{ item.description }}
                                        </div>
                                        <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                                            <el-checkbox
                                                style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                                                v-if="item.invName || item.serialNumber"
                                                disabled
                                                v-model="item.inTheHule"
                                            ></el-checkbox>
                                        </div>
                                    </div>
                                </div>
                            </el-collapse-item>
                        </el-collapse>
                    </template>
                </div>
            </div>
        </div>
        <RepairDialog @onAddRepairSuccess="onAddRepairSuccess" ref="repairDialog" />
        <DemandDialog @onAddDemandSuccess="onAddDemandSuccess" ref="demandDialog" />
        <TransferDialog @onAddTransferSuccess="onAddTransferSuccess" ref="transferDialog" />
    </div>
</template>

<script>
import {
    getToolsOnsiteToolTmp as toolListTmp,
    toolsOnsiteToolLength,
    getInitTOOLSONSITE,
} from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "TOOLSONSITE";
import RepairDialog from "../repair/repairDialog.vue"
import DemandDialog from "../demand/demandDialog.vue"
import TransferDialog from "../transfer/editDialog.vue"
export default {
    name: "TOOLSONSITE",
    components: { RepairDialog, TransferDialog, DemandDialog },
    data() {
        return {
            REPORT_TYPE,
            getInitTOOLSONSITE,
            toolsOnsiteToolLength,
            report: {},
            showReport: false,
            isEdit: false,
            showLoading: true,
            selectList: [],
            isClassified: false,
            activeNames: [],
            invNameToolsMap: {},
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        run: String,
        job: String,
        jobId: Number,
        toolList: Array,
        jobStatus: Number,
        globalDisableEdit:Boolean
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
        isSelectAll: {
            get(){
                if(!this.selectList.length){
                    return false;
                }
                return this.selectList.every((select,index)=>{
                    const targetRow = this.report.toolList[index];
                    if(!this.selectCommonFilter(targetRow)){
                        return true;
                    }
                    return select;
                })
            },
            set(v){
                if(v){
                    this.selectList = this.selectList.map((_,index)=>{
                        const targetRow = this.report.toolList[index];
                        if(!this.selectCommonFilter(targetRow)){
                            return false;
                        }
                        return true;
                    })
                }else{
                    this.selectList = this.selectList.map(()=>false)
                }
            }
        }
    },
    created() {
        this.report = getInitTOOLSONSITE();
    },
    methods: {
        getInvNameToolsMap(){
            return this.report.toolList.reduce((acc, item) => {
                if (item.invName) {
                    acc[item.invName] = acc[item.invName] || [];
                    acc[item.invName].push(item);
                }
                return acc;
            }, {});
        },
        onClassify(){
            if(this.isClassified){
                this.isClassified = false
            }else{
                this.invNameToolsMap = this.getInvNameToolsMap();
                this.activeNames = Object.keys(this.invNameToolsMap);
                this.isClassified = true;
            }
        },
        selectCommonFilter(item){
            return item.invName || item.serialNumber
        },
        toolListTmp,
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },

        checkDuplicate(arr) {
            let nonEmptyArr = arr
                .map((item) => item.serialNumber)
                .reduce((acc, cur) => {
                    if (cur) {
                        return acc.concat(cur);
                    }
                    return acc;
                }, []);
            let nonEmptySet = new Set(nonEmptyArr);
            return nonEmptyArr.length !== nonEmptySet.size;
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        updateReport() {
            if (this.checkDuplicate(this.report.toolList)) {
                this.$message.error("工具列表中有重复的序列号！");
                return;
            }
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            const baseInfoList = baseInfo.data?.data || [];
            if (baseInfoList.length === 0) {
                this.$message.error("暂无相关数据");
                this.showLoading = false;
                return;
            }
            const reportId = baseInfoList[0].id;
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                    if(!this.selectList.length){
                        this.selectList = this.report.toolList.map(()=>false)
                    }
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
        toolListFilterByInvName(invName) {
            if (!invName) {
                return this.toolList;
            }
            return this.toolList.filter((item) => {
                return item.invName === invName;
            });
        },
        onSerialNumberChange(item, key) {
            if (item.serialNumber) {
                let idx = this.toolList.findIndex(
                    (tool) => tool.serialNumber == item.serialNumber
                );
                let tmp = this.toolList[idx];
                // TODO: another way
                this.$set(this.report.toolList, key, tmp);
            }
        },
        onInvNameChange(item, key) {
            let tmp = this.toolListTmp();
            tmp.invName = item.invName;
            this.report.toolList[key] = Object.assign(
                this.report.toolList[key],
                tmp
            );
        },
        async onCreateRepair() {
            const loading = this.$loading({
                lock: true,
                text: '正在获取数据...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$nextTick(async ()=>{
                try{
                    await this.$refs.repairDialog.showDialog(
                        "ADD",
                        {
                            toolType: "MWD",
                            jobId: this.jobId,
                            toolList: this.report.toolList
                                .filter((_, key) => this.selectList[key])
                                .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                        }
                    );
                } finally {
                    loading.close();
                }
            })
        },
        async onCreateDemand() {
            const loading = this.$loading({
                lock: true,
                text: '正在获取数据...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$nextTick(async ()=>{
                try{
                    await this.$refs.demandDialog.showDialog(
                        "ADD",
                        {
                            toolType: "MWD",
                            jobId: this.jobId,
                            toolList: this.report.toolList
                                .filter((_, key) => this.selectList[key])
                                .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                        }
                    );
                } finally {
                    loading.close();
                }
            })
        },
        async onCreateTransfer() {
            const loading = this.$loading({
                lock: true,
                text: '正在获取数据...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$nextTick(async ()=>{
                try{
                    await this.$refs.transferDialog.showDialog(
                        "ADD",
                        {
                            jobId: this.jobId,
                            toolList: this.report.toolList
                                .filter((_, key) => this.selectList[key])
                                .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                        }
                    );
                } finally {
                    loading.close();
                }
            })
        },
        onAddRepairSuccess(){
            this.selectList = this.selectList.map(()=>false);
        },
        onAddDemandSuccess(){
            this.selectList = this.selectList.map(()=>false);
        },
        onAddTransferSuccess(){
            this.selectList = this.selectList.map(()=>false);
        },
    },
};
</script>
