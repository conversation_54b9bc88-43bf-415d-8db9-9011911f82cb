<template>
  <div class="content" style="position:relative" v-loading="showLoading">
    <div
      style="z-index:999;padding: 10px 0; background:white;border-bottom:1px dashed #eee;overflow: hidden;"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
      <el-button v-if="$checkBtnPermission('sys:daily:repiar:add') && showReport" style="margin-left:10px" type="primary" @click="onCreateRepair">
          创建返修单
      </el-button>
      <el-button v-if="$checkBtnPermission('sys:daily:transfer:all') && showReport" style="margin-left:10px" type="primary" @click="onCreateTransfer">
          创建调拨单
      </el-button>
      <el-button v-if="$checkBtnPermission('sys:daily:demand:all') && showReport" style="margin-left:10px" type="primary" @click="onCreateDemand">
          创建需求单
      </el-button>
      <el-button @click="onClassify" style="float: right;" type="primary">{{ isClassified ? '平铺显示' : '分类显示' }}</el-button>
    </div>
    <div class="mwd-container" v-if="showReport">
      <div class="content-container2">
        <div class="box-border">
          <div class="line">
            <div class="title center" style="flex:1">
              仪器清单/LIST OF ALL DIRECTIONAL TOOLS ON SITE
            </div>
          </div>
          
          <template v-if="!isClassified">
            <div class="line">
              <div class="title center" style="flex:100px 0 0">
                选择 <el-checkbox v-model="isSelectAll"></el-checkbox>
              </div>
              <div class="title center" style="flex:300px 0 0">
                仪器类型/TOOL TYPE
              </div>
              <div class="title center" style="flex:600px 0 0">
                序列号/SERIAL NUMBER
              </div>
              <div
                class="title center"
                style="flex:1"
              >
                状态（到井/井下/出井/离井-日期）
              </div>
              <div
                class="title center"
                style="flex:1"
              >
                备注
              </div>
              <div class="title center" style="flex:100px 0 0">
                是否井下
              </div>
            </div>
            <div
              class="line hover-line"
              v-for="(item, key) in report.toolList"
              :key="`toolList` + key + item.serialNumber"
            >
              <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                <el-checkbox
                  style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                  v-model="selectList[key]"
                ></el-checkbox>
              </div>
              <div class="text left" style="flex:300px 0 0;height:auto">
                <el-input
                  :disabled="!isEdit"
                  v-model="item.invName"
                  class="mwd-input"
                  spellcheck="false"
                  ref="defaultFocusInput"
                ></el-input>
              </div>
              <div class="text left" style="flex:600px 0 0;position:relative;height:auto">
                <el-input
                  :disabled="!isEdit"
                  v-model="item.serialNumber"
                  class="mwd-input"
                  spellcheck="false"
                ></el-input>
              </div>
              <div class="text left desc" style="flex:1;height:auto;position: relative;" :style="`background-color:${getCurrentStatusColor(item.status)}`">
                <el-input
                  disabled
                  class="mwd-input textarea-ofh"
                  type="textarea"
                  :autosize="{ minRows: 1 }"
                  v-model="item.status"
                  spellcheck="false"
                ></el-input>
              </div>
              <div class="text left" style="flex:1;height:auto;position: relative;">
                <el-input
                  :disabled="!isEdit"
                  class="mwd-input textarea-ofh"
                  type="textarea"
                  :autosize="{ minRows: 1 }"
                  v-model="item.description"
                  spellcheck="false"
                ></el-input>
              </div>
              <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                <el-checkbox
                  style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                  v-if="item.invName || item.serialNumber"
                  :disabled="!isEdit"
                  v-model="item.inTheHule"
                ></el-checkbox>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="line">
              <!-- <div class="title center" style="flex:300px 0 0">
                仪器类型/TOOL TYPE <i @click="onSort('invName')" class="el-icon-sort" style="cursor: pointer;" title="排序"></i>
              </div> -->
              <div class="title center" style="flex:600px 0 0">
                序列号/SERIAL NUMBER
              </div>
              <div
                class="title center"
                style="flex:1"
              >
                状态（到井/井下/出井/离井-日期）
              </div>
              <div
                class="title center"
                style="flex:1"
              >
                备注
              </div>
              <div class="title center" style="flex:100px 0 0">
                是否井下
              </div>
            </div>
            <el-collapse v-model="activeNames">
              <el-collapse-item class="custom-collapse-item" v-for="(tools,key) in invNameToolsMap" :key="key" :title="key" :name="key">
                <template slot="title">
                  <div style="flex: calc(100% - 32px) 0 0; text-align: center;">{{ key }}</div>
                </template>
                <div class="">
                  
                  <div
                    class="line hover-line"
                    v-for="(item, key) in tools"
                    :key="`toolList` + key + item.serialNumber"
                  >
                    <div class="text center text-content" style="flex:600px 0 0;position:relative;height:auto;display: flex; justify-content: center; align-items: center;">
                      <span>
                        {{ item.serialNumber }}
                      </span>
                    </div>
                    <div class="text left text-content" style="flex:1;height:auto;position: relative;" :style="`background-color:${getCurrentStatusColor(item.status)}`">
                      {{ item.status }}
                    </div>
                    <div class="text left text-content" style="flex:1;height:auto;position: relative;">
                      {{ item.description }}
                    </div>
                    <div class="text center checkbox" style="flex:100px 0 0;height:auto;position: relative;">
                      <el-checkbox
                        style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                        v-if="item.invName || item.serialNumber"
                        disabled
                        v-model="item.inTheHule"
                      ></el-checkbox>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </template>
        </div>
      </div>
    </div>
    <RepairDialog @onAddRepairSuccess="onAddRepairSuccess" ref="repairDialog" reportType="RSS_REPORT" />
    <DemandDialog @onAddDemandSuccess="onAddDemandSuccess" ref="demandDialog" />
    <TransferDialog @onAddTransferSuccess="onAddTransferSuccess" ref="transferDialog" />
  </div>
</template>

<script>
import { getInitRSS_TOOLSONSITE } from "./data";
import { apiGetDailyReportBaseinfo, apiGetDailyReportInfo } from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_TOOLSONSITE";
import RepairDialog from "../repair/repairDialog.vue"
import DemandDialog from "../demand/demandDialog.vue"
import TransferDialog from "../transfer/editDialog.vue"
export default {
  name: "RSS_TOOLSONSITE",
  components: { RepairDialog, TransferDialog, DemandDialog },
  data() {
    return {
      REPORT_TYPE,
      getInitRSS_TOOLSONSITE,
      report: {},
      showReport: false,
      isEdit: false,
      showLoading: true,
      selectList: [],
      isClassified: false,
      activeNames: [],
      invNameToolsMap: {},
    };
  },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    run: String,
    job: String,
    jobId: Number,
    rssToolList: Array,
    jobStatus: Number,
    globalDisableEdit:Boolean
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
    isSelectAll: {
      get(){
        if(!this.selectList.length){
          return false;
        }
        return this.selectList.every((select,index)=>{
          const targetRow = this.report.toolList[index];
          if(!this.selectCommonFilter(targetRow)){
            return true;
          }
          if(targetRow.hasReturn){
            return true;
          }
          return select;
        })
      },
      set(v){
        if(v){
          this.selectList = this.selectList.map((_,index)=>{
            const targetRow = this.report.toolList[index];
            if(!this.selectCommonFilter(targetRow)){
              return false;
            }
            if(targetRow.hasReturn){
              return false;
            }
            return true;
          })
        }else{
          this.selectList = this.selectList.map(()=>false)
        }
      }
    }
  },
  created() {
    this.report = getInitRSS_TOOLSONSITE();
  },
  methods: {
    getInvNameToolsMap(){
      return this.report.toolList.reduce((acc, item) => {
        if (item.invName) {
          acc[item.invName] = acc[item.invName] || [];
          acc[item.invName].push(item);
        }
        return acc;
      }, {});
    },
    onClassify(){
      if(this.isClassified){
        this.isClassified = false
      }else{
        this.invNameToolsMap = this.getInvNameToolsMap();
        this.activeNames = Object.keys(this.invNameToolsMap);
        this.isClassified = true;
      }
    },
    getCurrentStatusColor(str){
      const COLUMNS = [
        { label: '到井', value: 'arrive', color: '' },
        { label: '入井', value: 'in', color: '#83E28E'  },
        { label: '出井', value: 'out', color: '#EEF175' },
        { label: '离井', value: 'leave', color: '#F47B72'  }
      ];
      if (!str) return '';
      str = str.trim();
      // 根据字符串的最后两个字符来判断当前状态
      const lastTwoChars = str.slice(-2);
      if(lastTwoChars){
          const item = COLUMNS.find(col => col.label === lastTwoChars);
          if (item) {
              return item.color;
          }
      }
      return '';
    },
    selectCommonFilter(item){
      return item.invName || item.serialNumber
    },
    async loadReport() {
        this.showLoading = true;
        const baseInfo = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: REPORT_TYPE,
        });
        const baseInfoList = baseInfo.data?.data || [];
        if (baseInfoList.length === 0) {
            this.$message.error("暂无相关数据");
            this.showLoading = false;
            return;
        }
        const reportId = baseInfoList[0].id;
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        }).then((res) => {
            this.report = dealReport(res.data.data, REPORT_TYPE);
            this.$nextTick(() => {
                this.showReport = true;
                if(!this.selectList.length){
                    this.selectList = this.report.toolList.map(()=>false)
                }
            });
        }).finally(()=>{
            this.showLoading = false;
        });
    },
    hideReport() {
        this.showLoading = false;
        this.showReport = false;
    },
    async onCreateRepair() {
        const loading = this.$loading({
            lock: true,
            text: '正在获取数据...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$nextTick(async ()=>{
            try{
                await this.$refs.repairDialog.showDialog(
                    "ADD",
                    {
                        toolType: "MWD",
                        jobId: this.jobId,
                        toolList: this.report.toolList
                            .filter((_, key) => this.selectList[key])
                            .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                    }
                );
            } finally {
                loading.close();
            }
        })
    },
    async onCreateDemand() {
        const loading = this.$loading({
            lock: true,
            text: '正在获取数据...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$nextTick(async ()=>{
            try{
                await this.$refs.demandDialog.showDialog(
                    "ADD",
                    {
                        toolType: "MWD",
                        jobId: this.jobId,
                        toolList: this.report.toolList
                            .filter((_, key) => this.selectList[key])
                            .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                    }
                );
            } finally {
                loading.close();
            }
        })
    },
    async onCreateTransfer() {
        const loading = this.$loading({
            lock: true,
            text: '正在获取数据...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$nextTick(async ()=>{
            try{
                await this.$refs.transferDialog.showDialog(
                    "ADD",
                    {
                        jobId: this.jobId,
                        toolList: this.report.toolList
                            .filter((_, key) => this.selectList[key])
                            .map(item=>({invName:item.invName, serialNumber:item.serialNumber}))
                    }
                );
            } finally {
                loading.close();
            }
        })
    },
    onAddRepairSuccess(){
        this.selectList = this.selectList.map(()=>false);
    },
    onAddDemandSuccess(){
        this.selectList = this.selectList.map(()=>false);
    },
    onAddTransferSuccess(){
        this.selectList = this.selectList.map(()=>false);
    },
  },
  beforeDestroy(){
    if(this.hasChange){
      this.hasChange = false;
      this.$emit("getJobInfo")
    }
  },
};
</script>
<style lang="scss">
.el-collapse-item.custom-collapse-item .el-collapse-item__content{
  padding: 0;
}
.el-collapse-item.custom-collapse-item {
  .el-collapse-item__content{
    padding: 0;
  }
  .el-collapse-item__header{
    background-color: #F0F0F0;
    height: 34px;
    font-size: 16px;
    color: #7195AB;
    border-bottom: 1px solid #E8E8E8;
  }
}
.text-content{
  padding: 6px !important;
  line-height: 1.5 !important;
  font-size: 16px !important;
}
</style>