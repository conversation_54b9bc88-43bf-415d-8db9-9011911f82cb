<template>
    <div class="content" v-loading="showLoading">
        <div
            style="
                z-index: 999;
                height: 50px;
                display: flex;
                align-items: center;
                min-width: 700px;
                background: white;
                border-bottom: 1px dashed #eee;
            "
        >
            <span
                class="report-title"
                style="
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #333333;
                "
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <el-select
                v-model="selectedRunId"
                @change="onSelectRun"
                placeholder="请选择趟次"
                style="margin-left: 10px"
            >
                <el-option
                    v-for="(item, key) in currentRunList"
                    :key="`run${key}`"
                    :label="item.run"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted && !globalDisableEdit">
                <el-button
                    style="margin-left: 10px"
                    icon="el-icon-refresh"
                    type="warning"
                    @click="onRefreshData()"
                >
                    手动汇总
                </el-button>
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left: 10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left: 10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div class="mwd-container" v-if="showReport">
            <div class="content-container2">
                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title">公司Company：</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.company"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">开始时间Start Date:</div>
                        <el-date-picker
                            class="mwd-input basic-info-input"
                            v-model="report.startDate"
                            type="date"
                            placeholder=""
                            :disabled="!isEdit"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </div>
                    <div class="info-container">
                        <div class="info-title">组合BHA #:</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.run"
                            disabled
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">承包商Contractor：</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.contractor"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>
                </div>
                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title">井号Well Name：</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.wellNumber"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>

                    <div class="info-container">
                        <div class="info-title">井队号Rig No.：</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.rigNo"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">位置Location No：</div>
                        <el-input
                            class="mwd-input basic-info-input"
                            v-model="report.location"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">工单号Job No：</div>
                        <el-input
                            disabled
                            class="mwd-input basic-info-input"
                            v-model="job"
                        ></el-input>
                    </div>
                </div>
                <div class="basic-info">
                    <div class="info-container" style="flex: 1">
                        <div class="info-title">入井井深Depth In m：</div>
                        <InputNumber
                            class="mwd-input basic-info-input"
                            v-model="report.depthIn"
                            :disabled="!isEdit"
                        ></InputNumber>
                    </div>
                    <div class="info-container" style="flex: 1">
                        <div class="info-title">出井井深Depth Out m：</div>
                        <InputNumber
                            class="mwd-input basic-info-input"
                            v-model="report.depthOut"
                            :disabled="!isEdit"
                        ></InputNumber>
                    </div>
                    <div class="info-container" style="flex: 2">
                        <div class="info-title" style="flex: 350px 0 0">
                            该钻具组合目的Objective For Running BHA：
                        </div>
                        <el-input
                            class="mwd-input left basic-info-input"
                            v-model="report.purpose"
                            style="max-width: 2020px"
                            :disabled="!isEdit"
                        ></el-input>
                    </div>
                </div>
                <div class="box-border">
                    <div class="line">
                        <div class="title center" style="flex: 1">
                            项目ITEM No.
                        </div>
                        <div class="title center" style="flex: 1">
                            名称Description
                        </div>
                        <div class="title center" style="flex: 1">
                            厂家Vendor
                        </div>
                        <div class="title center" style="flex: 1">
                            编号SERIAL #
                        </div>
                        <div class="title center" style="flex: 1">
                            外径OD mm
                        </div>
                        <div class="title center" style="flex: 1">
                            内径ID mm
                        </div>
                        <div class="title center" style="flex: 1">Gauge</div>
                        <div class="title center" style="flex: 1">
                            公扣Pin Connection
                        </div>
                        <div class="title center" style="flex: 1">
                            母扣Box Connection
                        </div>
                        <div class="title center" style="flex: 1">
                            长度Length m
                        </div>
                        <div class="title center" style="flex: 1">
                            总长Total Length m
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.itemList"
                        :key="`itemList` + key"
                    >
                        <i
                            v-if="
                                report.itemList.length > bhaItemLength && isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size: 15px"
                            @click="onClickDelete('itemList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size: 15px"
                            v-if="isEdit"
                            @click="onClickAdd('itemList', key)"
                        ></i>
                        <div class="text center" style="flex: 1">
                            <el-input
                                class="mwd-input"
                                v-model="item.itemNo"
                                :disabled="!isEdit"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                class="mwd-input"
                                v-model="item.description"
                                :disabled="!isEdit"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                class="mwd-input"
                                v-model="item.vendor"
                                :disabled="!isEdit"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                class="mwd-input"
                                v-model="item.serialNum"
                                :disabled="!isEdit"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.outerDiameter"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.innerDiameter"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.gauge"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.pinConnection"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.boxConnection"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                class="mwd-input"
                                v-model="item.len"
                                @change="onCalculateVal('itemList', item, key)"
                                :disabled="!isEdit"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <!-- :disabled="!isEdit" -->
                            <InputNumber
                                class="mwd-input"
                                v-model="item.totalLen"
                                disabled
                            ></InputNumber>
                        </div>
                    </div>
                </div>
            </div>
            <div class="basic-info">
                <div class="info-container">
                    <div class="info-title" style="flex: 300px 0 0">
                        测斜零长Survey Sensor to Bit m：
                    </div>
                    <InputNumber
                        class="mwd-input basic-info-input"
                        v-model="report.surveySensor"
                        :disabled="!isEdit"
                    ></InputNumber>
                </div>
                <div class="info-container">
                    <div class="info-title" style="flex: 300px 0 0">
                        伽马零长Gamma Sensor to Bit m：
                    </div>
                    <InputNumber
                        class="mwd-input basic-info-input"
                        v-model="report.gammaSensor"
                        :disabled="!isEdit"
                    ></InputNumber>
                </div>
            </div>
            <div class="basic-info">
                <div class="info-container">
                    <div class="info-title" style="flex: 300px 0 0">
                        BHA Results：
                    </div>
                    <el-input
                        class="mwd-input left basic-info-input"
                        v-model="report.bhaResults"
                        style="max-width: 2020px"
                        :disabled="!isEdit"
                    ></el-input>
                </div>
            </div>
            <div class="basic-info">
                <div class="info-container">
                    <div class="info-title" style="flex: 300px 0 0">
                        起钻原因Reason for POOH：
                    </div>
                    <el-input
                        class="mwd-input left basic-info-input"
                        v-model="report.reasonForPooh"
                        style="max-width: 2020px"
                        :disabled="!isEdit"
                    ></el-input>
                </div>
            </div>
            <div class="basic-info">
                <div class="info-container">
                    <div class="info-title" style="flex: 300px 0 0">
                        定向井工程师Directional Drillers：
                    </div>
                    <el-input
                        class="mwd-input left basic-info-input"
                        v-model="report.directionalDrillers"
                        style="max-width: 2020px"
                        :disabled="!isEdit"
                    ></el-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getInitBHA,
    bhaItemLength,
    getBhaItemTmp as itemListTmp,
} from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
    dailyReportRefresh,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "BHA";
export default {
    name: "BHA",
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        selectedRun: String,
        job: String,
        jobId: Number,
        jobStatus: Number,
        globalDisableEdit: Boolean,
    },
    data() {
        return {
            REPORT_TYPE,
            getInitBHA,
            report: {},
            bhaItemLength,
            showReport: false,
            isEdit: false,
            currentRunList: [],
            selectedRunId: null,
            showLoading: true
        };
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    created() {
        this.report = getInitBHA();
    },
    methods: {
        itemListTmp,
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },
        onCalculateVal(listname, curritem, key) {
            // let previousTotal=key==0?0:this.report[listname][key-1].totalLen;
            // this.report[listname][key].totalLen=curritem.len+previousTotal;
            for (let i = key; i < this.report[listname].length; i++) {
                let previousTotal =
                    i == 0 ? 0 : this.report[listname][i - 1].totalLen;
                this.report[listname][i].totalLen =
                    this.report[listname][i].len + previousTotal;
            }
        },
        checkDuplicate(arr) {
            let nonEmptyArr = arr
                .map((item) => item.serialNumber)
                .reduce((acc, cur) => {
                    if (cur) {
                        return acc.concat(cur);
                    }
                    return acc;
                }, []);
            let nonEmptySet = new Set(nonEmptyArr);
            return nonEmptyArr.length !== nonEmptySet.size;
        },
        onRefreshData() {
            let params = {
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            };
            let map = this.currentRunList.find(
                (item) => item.id == this.selectedRunId
            );
            params.run = map.run;
            dailyReportRefresh(params).then((res) => {
                this.loadReport();
                this.$message.success("汇总统计完成");
            });
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        onSelectRun() {
            let map = this.currentRunList.find((item) => {
                return item.id == this.selectedRunId;
            });
            if (map) {
                this.$emit("updateCommonRun", map.run);
            }
            this.loadReport();
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentRunList = baseInfo.data?.data || [];
            const idx = this.currentRunList.findIndex((item) => {
                return item.run === this.selectedRun;
            });
            let reportId;
            if (idx != -1) {
                reportId = this.currentRunList[idx].id;
                this.selectedRunId = reportId;
                this.getReportInfo(reportId);
            } else {
                reportId = this.currentRunList[0]?.id;
                if (reportId) {
                    this.selectedRunId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedRunId = null;
                    this.hideReport();
                }
            }
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.basic-info {
    display: flex;
    justify-content: space-between;
    // line-height: 3.6;
    height: 40px;
    .info-container {
        padding-right: 10px;
        display: flex;
        align-items: center;
        .info-title {
            flex: 162px 0 0;
            text-align: left;
        }
        .basic-info-input {
            border-bottom: 1px solid #ccd3d9;
            max-width: 230px;
        }
    }
    div {
        flex: 1;
        text-align: right;
    }
}
</style>
