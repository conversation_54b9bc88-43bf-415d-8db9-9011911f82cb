<template>
  <div class="content" style="position:relative" v-loading="showLoading">
    <div
      style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
      <el-select
        style="margin-left:10px"
        v-model="selectedDateId"
        @change="onSelectDate"
      >
        <el-option
          v-for="(item, key) in currentDateList"
          :key="`date${key}`"
          :label="item.date"
          :value="item.id"
        ></el-option>
      </el-select>
    </div>
    <div class="mwd-container" v-if="showReport">
      <div class="content-container2">
        <div style="margin-bottom:10px">
          <span style="margin-left:10px">
            支持箱：<el-input
              spellcheck="false"
              :disabled="!isEdit"
              v-model="report.kit"
              style="width:200px"
            ></el-input>
          </span>
        </div>
        <div class="box-border">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              基本信息GENERAL INFO
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              工单号Job #
            </div>
            <div style="flex:1 0 0" class="text">
              {{ job }}
            </div>
            <div style="flex:1 0 0" class="title center">
              开始日期Start Date
            </div>
            <div style="flex:1 0 0" class="text">
              <el-date-picker
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.startDate"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div style="flex:1 0 0" class="title center">
              施工天数Day #
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.constructionDays"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              负责人/CELL MANAGER
            </div>
            <div style="flex:1 0 0" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.cellManager"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              DD
            </div>
            <div style="flex:1 0 0" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.dd1"
              ></el-input>
            </div>
            <div style="flex:1 0 0" class="title center">
              DD
            </div>
            <div style="flex:1 0 0" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.dd2"
              ></el-input>
            </div>
            <div style="flex:1 0 0" class="title center">
              MWD
            </div>
            <div style="flex:1 0 0" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.mwd1"
              ></el-input>
            </div>
            <div style="flex:1 0 0" class="title center">
              MWD
            </div>
            <div style="flex:1 0 0" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.mwd2"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              趟次Run #
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.run"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              开始深度Start Depth
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.startDepth"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              结束深度End Depth
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.endDepth"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              温度Temp ℃
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.temp"
              ></InputNumber>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              仪器基础数据MWD DATA
            </div>
          </div>
          
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              总修正角TAC
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.tac"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              脉宽Plsw
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.plsw"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              伽马零长Gamma Dist
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.gammaDist"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              脉冲高度PlsH
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.plsH"
              ></InputNumber>
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              近钻伽马零长At bit Dist
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.atBitDist"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              磁倾角Ndip
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.ndip"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              测斜零长Bit-ElecC Dist
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.bitElecCDist"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              磁场强度Nmag
            </div>
            <div style="flex:1 0 0" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.nmag"
              ></InputNumber>
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              电池电压Batv
            </div>
            <div style="flex:1" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.batv"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              重力场Gt
            </div>
            <div style="flex:1" class="text">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.gt"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="title center">
              BPA编号SN
            </div>
            <div style="flex:1" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.bpaSn"
              ></el-input>
            </div>
            <div style="flex:1 0 0" class="title center">
              BPA使用时间Hours
            </div>
            <div style="flex:1" class="text">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.bpaHrs"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              泥浆数据 MUD DATA
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0;height:auto" class="text center">
              <el-input
                spellcheck="false"
                type="textarea"
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.mudData"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              DAILY SUMMARY每日汇总 - HOURS时间
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              名称
            </div>
            <div style="flex:2 0 0" class="title center">
              序列号
            </div>
            <div style="flex:1 0 0" class="title center">
              Start开始
            </div>
            <div style="flex:1 0 0" class="title center">
              Today今天
            </div>
            <div style="flex:1 0 0" class="title center">
              Total总计
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.toolList"
            :key="key"
          >
            <div style="flex:1 0 0" class="text center">
              <el-input
                clearable
                placeholder=""
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.invName"
              >
              </el-input>
            </div>
            <div class="text center" style="flex:2 0 0;position:relative">
              
              <el-input
                clearable
                placeholder=""
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.serialNumber"
              >
              </el-input>
            </div>
            <div style="flex:1 0 0" class="text center">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.start"
              ></InputNumber>
            </div>
            <div style="flex:1 0 0" class="text center">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.today"
              ></InputNumber>
            </div>
            <div style="flex:1 1 0" class="text center not-input">
              <InputNumber
                class="mwd-input"
                disabled
                v-model="item.total"
              ></InputNumber>
            </div>

          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              24小时计划Planned Changes for next 24h
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0;height:auto" class="text center">
              <el-input
                spellcheck="false"
                type="textarea"
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.plan"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              OOS
            </div>
          </div>
          <div class="line">
            <div style="flex:100px 0 0" class="title center">
              OOS(Y/N)
            </div>
            <div style="flex:300px 0 0" class="title center">
              Type
            </div>
            <div style="flex:1 0 0" class="title center">
              备注
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.oosList"
            :key="`oosList${key}`"
          >
            <div class="text center" style="flex:100px 0 0; height: auto;">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.oos"
              ></el-input>
            </div>
            <div class="text center auto" style="flex:300px 0 0; height: auto;line-height: 1.5;padding: 4px">
              {{ item.type }}
            </div>
            <div class="text left" style="flex:1 0 0; height: auto;">
              <el-input
                type="textarea"
                :autosize="{ minRows: 1 }"
                :disabled="!isEdit"
                spellcheck="false"
                class="mwd-input left textarea-ofh"
                v-model="item.note"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center px10" style="flex:1">
              开始时间Start Time
            </div>
            <div class="title center px10" style="flex:1">
              结束时间End Time
            </div>
            <div class="title center px10" style="flex:1">
              起始井深mFrom
            </div>
            <div class="title center px12" style="flex:1">
              结束井深mTo
            </div>
            <div class="title center px12" style="flex:1">
              Activity工况
            </div>
            <div class="title center px12" style="flex:1">
              MODE模式
            </div>
            <div class="title center px12" style="flex:1">
              钻压KN
            </div>
            <div class="title center px12" style="flex:1">
              SPP立压MPA
            </div>
            <div class="title center px12" style="flex:1">
              转速RPM
            </div>
            <div class="title center px10" style="flex:1">
              扭矩Torque KN.M
            </div>
            <div class="title center px12" style="flex:1">
              排量FR L/S
            </div>
            <div class="title center px12" style="flex:400px 0 0;">
              TI/TA/BF/WF/SF/SD
            </div>
            <div class="title center px12" style="flex:1">
              备注Note
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.activityList"
            :key="`activityList${key}`"
          >
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.startTime"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.endTime"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.depthFrom"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.depthTo"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.activity"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.mode"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.drillPressure"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.spp"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.rpm"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.torque"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <InputNumber
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.fr"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:400px 0 0;height:auto">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.info"
              ></el-input>
            </div>
            <div class="text center" style="flex:1;height:auto">
              <el-input
                spellcheck="false"
                :disabled="!isEdit"
                class="mwd-input textarea-ofh"
                type="textarea"
                :autosize="{ minRows: 1 }"
                v-model="item.note"
              ></el-input>
            </div>
          </div>
        </div>

        <div class="daily-summary">
          <h3>工作汇报</h3>
        </div>
        <div class="box-border">
          <div style="display:flex">
            <div
              v-for="(item, key) in report.workSummaryList"
              :key="key"
              style="flex:1 0 0;"
            >
              <div class="line">
                <div style="flex:1 0 0" class="title center">
                  {{ item.time }}
                </div>
              </div>
              <!-- <div style="text-align:center">{{ item.time }}</div> -->
              <div class="line">
                <div style="flex:1 0 0;height:auto" class="text">
                  <el-input
                    spellcheck="false"
                    class="mwd-input"
                    type="textarea"
                    :autosize="{ minRows: 50, maxRows: 50 }"
                    style="width:100%;"
                    v-model="item.content"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_DAILY";
export default {
  name: "RSS_DAILY",
  data() {
    return {
      REPORT_TYPE,
      report: {},
      showReport: false,
      selectedDateId: null,
      currentDateList: [],
      showAddPopover: false,
      orginDateAdd: "",
      isEdit: false,
      showLoading: true,
    };
  },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    selectedRssDate: String,
    job: String,
    jobId: Number,
    selectedJob: String,
    workConditionList: Array,
    rssToolList: Array,
    jobStatus: Number,
    globalDisableEdit: Boolean,
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  watch: {
    selectedDateId(newVal) {
      let map = this.currentDateList.find((item) => {
          return item.id == newVal;
      });
      if (map) {
          this.$emit("updateCommonRssDate", map.date);
      }
    },
  },
  created() {},
  methods: {
    async loadReport() {
      this.showLoading = true;
      const baseInfo = await apiGetDailyReportBaseinfo({
          jobId: this.jobId,
          reportType: REPORT_TYPE,
      });
      this.currentDateList = baseInfo.data?.data || [];
      const idx = this.currentDateList.findIndex((item) => {
          return item.date === this.selectedRssDate;
      });
      let reportId;
      if (idx != -1) {
          reportId = this.currentDateList[idx].id;
          this.selectedDateId = reportId;
          this.getReportInfo(reportId);
      } else {
          reportId = this.currentDateList[0]?.id;
          if (reportId) {
              this.selectedDateId = reportId;
              this.getReportInfo(reportId);
          } else {
              this.selectedDateId = null;
              this.hideReport();
          }
      }
    },
    onSelectDate() {
        this.loadReport();
    },
    getReportInfo(reportId) {
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        })
            .then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            })
            .finally(() => {
                this.showLoading = false;
            });
    },
    hideReport() {
      this.showLoading = false;
      this.showReport = false;
    },
  },
};
</script>
<style lang="scss">
.textarea-ofh .el-textarea__inner{
  overflow: hidden;
  min-height: 26px !important;
}
</style>