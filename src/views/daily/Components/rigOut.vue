<template>
    <ImageViewer class="content rigout-report" v-loading="showLoading" style="position:relative">
        <div
            style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
        >
            <span
                class="report-title"
                style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <template v-if="!isJobCompleted&&!globalDisableEdit">
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left:10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left:10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div v-if="showReport" class="mwd-container">
            <div class="content-container2">
                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title" style="flex:70px 0 0">
                            Operator：
                        </div>
                        <el-input
                            :disabled="!isEdit"
                            v-model="report.operator"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">Job Number：</div>
                        <el-input
                            disabled
                            v-model="job"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                </div>
                <div class="box-border">
                    <div class="line">
                        <div class="title center" style="flex:60px 0 0">#</div>
                        <div class="title center" style="flex:1">
                            项目Action
                        </div>
                        <div class="title center" style="flex:200px 0 0">
                            日期Date
                        </div>
                        <div class="title center" style="flex:200px 0 0">
                            Initial
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            1
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            At the end of well, complete all paperwork, save
                            your logged files, gamma databases, anything
                            electronic you might have on your USB flash card.<br />
                            完井后完成所有日报，保存所有数据文件，伽马数据库，及其他本井所有电子文档。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[0].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[0].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[0].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            2
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Print gamma logs, both MD and TVD, 1:200 and
                            1:500.<br />
                            保存1:200和1:500的垂深和斜深伽马图。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[1].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[1].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[1].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            3
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Rig down all lines and surface equipment. Note
                            anything that might damaged or not working correctly
                            and report it to office.<br />
                            打包所有线缆和其他工具，标记出有损坏和会影响后续工作的地方上报公司。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[2].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[2].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[2].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            4
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            When tool on surface, tap test it, check pulser,
                            look it over well. Note and report any problems and
                            any wash you may see.<br />
                            仪器出井后，进行整体测试，检查脉冲，有任何问题或者表面有任何冲蚀，都需要记录和汇报
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[3].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[3].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[3].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            5
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            IF your string has problems or failure down hole, do
                            not just take it appart without consulting the
                            office and diagnosing the problem.<br />
                            仪器在井下失效，严禁在不做任何汇报和任何测试的情况下拆卸仪器串。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[4].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[4].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[4].initial"
                                class="mwd-input"
                            ></el-input>
                            <!-- <el-date-picker class="mwd-input basic-info-input"
                               v-model="report.actionList[4].initial"
                        type="date" placeholder="" :disabled="!isEdit" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker> -->
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            6
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            MARK SERIAL #S AND HOURS are on all MWD tools, this
                            is critical for future jobs.<br />
                            在所有仪器串上写上部件号和使用情况，方便后续工作参考。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[5].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[5].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[5].initial"
                                class="mwd-input"
                            ></el-input>
                            <!-- <el-date-picker class="mwd-input basic-info-input"
                               v-model="report.actionList[5].initial"
                        type="date" placeholder="" :disabled="!isEdit" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker> -->
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            7
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Make sure you got your pipe screen(s), transducer,
                            pickup plate, J latch, float, pickup nubbin, remote
                            terminal, or anything you used on the rig floor.<br />
                            确保滤纸、传感器、打捞头、吊绳、等所有工具装箱。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[6].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[6].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[6].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            8
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            When rigging out, pickup any left over tape or cut
                            off zip ties. Do NOT leave garbage around the
                            rig/lease, this does get back to us and reflects
                            very poorly on the company.<br />
                            收拾完毕后收拾好所有垃圾，不要遗留任何垃圾在现场，避免给公司造成不良影响。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[7].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[7].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[7].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            9
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Go through the check list again and be confident you
                            have everything.<br />
                            根据检查表再次检查，确保带走所有东西。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[8].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[8].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[8].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            10
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Strap your kit and any subs you may have.<br />
                            固定好仪器箱和所有接头。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[9].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[9].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[9].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            11
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            At the shop check with shop guys,sign you name,sent
                            Return Equipment List.<br />
                            和车间一起清单仪器，签字确认。并发送仪器返修单。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[10].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                class="mwd-input basic-info-input"
                                v-model="report.actionList[10].date"
                                type="date"
                                placeholder=""
                                :disabled="!isEdit"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[10].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            12
                        </div>
                        <div
                            class="title center auto"
                            style="flex:1;height:auto"
                        >
                            起出地面测试Screen Shoot Of Surface test
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text center"
                            style="flex:100% 0 0;height:250px;width:100%;"
                            v-if="isEdit"
                        >
                            <quill-editor
                                class="editor"
                                style="width:100%;"
                                ref="myTextEditor"
                                v-model="report.screenShoot"
                                :options="editorOption"
                                @blur="onEditorBlur($event)"
                                @focus="onEditorFocus($event)"
                                @ready="onEditorReady($event)"
                            />
                            <form
                                action
                                method="post"
                                enctype="multipart/form-data"
                                id="uploadFormMulti"
                            >
                                <input
                                    style="display:none"
                                    :id="uniqueId"
                                    type="file"
                                    name="file"
                                    multiple
                                    accept="image/jpg,image/jpeg,image/png,image/gif"
                                    @change="uploadImg('uploadFormMulti')"
                                />
                            </form>
                        </div>
                        <div
                            class="text left"
                            v-else
                            style="flex:100% 0 0;width:100%;min-height:120px;height:auto"
                        >
                            <div v-html="report.screenShoot" class="rich-text-cls"></div>
                        </div>
                    </div>
                </div>

                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title">MWD Operator：</div>
                        <el-input
                            :disabled="!isEdit"
                            v-model="report.mwdOperator"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">Signature & Date:</div>
                        <!-- <el-input
                        :disabled="!isEdit"
                        v-model="report.signatureDate"
                        class="mwd-input basic-info-input"
                    ></el-input> -->
                        <el-date-picker
                            class="mwd-input basic-info-input"
                            v-model="report.signatureDate"
                            type="date"
                            placeholder=""
                            :disabled="!isEdit"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </div>
                </div>
            </div>
            <el-dialog
                :visible.sync="showImgDialog"
                :close-on-click-modal="false"
                class="big-img-dialog"
            >
                <img width="100%" :src="report.screenShoot" alt="" />
            </el-dialog>
        </div>
    </ImageViewer>
</template>

<script>
import { getInitROPROCEDURES } from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "ROPROCEDURES";
import ImageViewer from "@/components/ImageViewer/index.vue";
export default {
    name: "ROPROCEDURES",
    components: { ImageViewer },
    created() {
        this.report = getInitROPROCEDURES();
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        run: String,
        job: String,
        jobId: Number,
        jobStatus: Number,
        globalDisableEdit:Boolean
    },
    data() {
        let self = this;
        return {
            REPORT_TYPE,
            getInitROPROCEDURES,
            dialogImageUrl: "",
            showImgDialog: false,
            report: {},
            showReport: false,
            uniqueId: "uniqueId",
            uploadType: "image",
            insertPosition: -1,
            isEdit: false,
            editorOption: {
                modules: {
                    toolbar: {
                        container: [
                            ["bold", "italic", "underline", "strike"],
                            [{ header: 1 }, { header: 2 }],
                            [{ list: "ordered" }, { list: "bullet" }],
                            [{ script: "sub" }, { script: "super" }],
                            [{ size: ["small", false, "large", "huge"] }],
                            [{ color: [] }, { background: [] }],
                            [{ align: [] }],
                            ["clean"],
                            ["link", "image"],
                        ],
                        handlers: {
                            image() {
                                self.uploadType = "image";

                                let quill = self.$refs.myTextEditor.quill;
                                // 获取光标所在位置
                                document.getElementById("uniqueId").click();
                                self.insertPosition = quill.getSelection().index;
                            },
                            video() {
                                self.uploadType = "video";

                                let quill = self.$refs.myTextEditor.quill;
                                // 获取光标所在位置
                                self.insertPosition = quill.getSelection().index;
                            },
                        },
                    },
                },
            },
            showLoading: true,
        };
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    methods: {
        uploadImg() {
            let quill = this.$refs.myTextEditor.quill;
            let img = document.getElementById(this.uniqueId).files[0];
            var reader = new FileReader();
            reader.readAsDataURL(img);
            reader.onload = (ev) => {
                //文件读取成功完成时触发
                var dataURL = ev.target.result;
                // 插入图片，res为服务器返回的图片链接地址
                quill.insertEmbed(
                    this.insertPosition,
                    this.uploadType,
                    dataURL
                );
                // 调整光标到最后
                quill.setSelection(this.insertPosition + 1);
            };
        },

        onEditorBlur(editor) {
            //console.log("editor blur!", editor);
        },
        onEditorFocus(editor) {
            // console.log("editor focus!", editor);
        },
        onEditorReady(editor) {
            //console.log("editor ready!", editor);
        },
        onDeleteScreenShoot() {
            this.$confirm("确认要删除该图片吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.report.screenShoot = "";
                this.updateReport(true);
            });
        },
        async writeFile(file) {
            var reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = (e) => {
                this.report.screenShoot = reader.result;
                this.updateReport(true);
            };
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            const baseInfoList = baseInfo.data?.data || [];
            if (baseInfoList.length === 0) {
                this.$message.error("暂无相关数据");
                this.showLoading = false;
                return;
            }
            const reportId = baseInfoList[0].id;
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
    },
};
</script>
<style lang="scss">
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.auto {
    height: auto !important;
}
.upload-btn {
    width: 100px;
    height: 100px;
    border: 1px dashed grey;
    border-radius: 5px;
    text-align: center;
    line-height: 100px;
}
</style>
<style lang="scss" scoped>
.img-container {
    position: relative;
    height: 100%;
    &:hover {
        img {
            filter: brightness(0.2);
        }
        .img-icon-container {
            opacity: 1;
        }
    }
    .img-icon-container {
        width: 100%;
        height: 20px;
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        opacity: 0;
        .img-icon {
            margin-left: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
    }
}
.basic-info {
    display: flex;
    justify-content: flex-start;
    // line-height: 3.6;
    margin-left: 10px;
    height: 40px;
    .info-container {
        display: flex;
        align-items: center;
        .info-title {
            flex: 120px 0 0;
            color: #3c4d73;
        }
        .basic-info-input {
            border-bottom: 1px solid #ccd3d9;
            min-width: 200px;
            max-width: 260px;
        }
    }
    div {
        flex: 400px 0 0;
        text-align: right;
    }
}
</style>
