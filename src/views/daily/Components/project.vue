<template>
    <div class="content" v-loading="showLoading">
        <div
            style="
                z-index: 999;
                height: 50px;
                display: flex;
                align-items: center;
                min-width: 700px;
                background: white;
                border-bottom: 1px dashed #eee;
            "
        >
            <span
                class="report-title"
                style="
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #333333;
                "
            >
                {{ $REPORT_TYPE_MAP.PROJECT }}
            </span>
            <el-select
                style="margin-left: 10px"
                v-model="selectedDateId"
                @change="onSelectDate"
            >
                <el-option
                    v-for="(item, key) in currentDateList"
                    :key="`date${key}`"
                    :label="item.date"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted && !globalDisableEdit">
                <el-popover
                    v-model="showAddPopover"
                    trigger="click"
                    placement="right-start"
                >
                    <div class="add-popover">
                        <div class="add-popover-title">新增</div>

                        <div class="calender" style="width: 400px">
                            <el-calendar v-model="orginDateAdd"> </el-calendar>
                        </div>

                        <div class="add-popover-btn">
                            <el-button
                                @click="onConfirmAddDate"
                                size="small"
                                type="primary"
                                >确 定</el-button
                            >
                            <el-button
                                @click="showAddPopover = false"
                                size="small"
                                >取 消</el-button
                            >
                        </div>
                    </div>

                    <el-button
                        slot="reference"
                        style="margin-left: 10px"
                        type="primary"
                        size="small"
                        ><span style="font-size: 14px"
                            ><i class="el-icon-plus"></i> 新增日报</span
                        ></el-button
                    >
                </el-popover>
                <el-button
                    @click="onClickDeleteDailyReport"
                    type="danger"
                    icon="el-icon-delete"
                    style="margin-left: 10px"
                    >删除日报</el-button
                >
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left: 10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left: 10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div class="mwd-container" v-if="showReport">
            <div class="content-container2">
                <div class="box-border">
                    <div class="line">
                        <div style="flex: 1" class="title center">基本信息</div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 14% 0 0">
                            接手基本情况
                        </div>
                        <div class="title center" style="flex: 14% 0 0">
                            钻井液性能
                        </div>
                        <div class="title center" style="flex: 28% 0 0">
                            工具情况
                        </div>
                        <div class="title center" style="flex: 28% 0 0">
                            当日数据
                        </div>
                        <div class="title center" style="flex: 1">项目经理</div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.manager"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            作业公司
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.operatorCompany"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            泥浆体系
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.mudSystem"
                            ></el-input>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            钻头厂家/编号
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                v-if="isEdit"
                                @change="onUpdateToolData('drillMan')"
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillMan"
                            ></el-input>
                            <span v-else style="font-size:13px" :title="report.drillMan?`${report.drillMan} / ${report.drillSerialNumber}`:''">{{report.drillMan}}</span>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            <el-popover trigger="click" placement="top" width="300">
                                <div style="font-size:16px;font-weight:bold;margin-bottom:4px">螺杆详情</div>
                                <el-input
                                    type="textarea"
                                    v-if="isEdit"
                                    v-model="report.screwDetail"
                                ></el-input>
                                <span v-else>{{report.screwDetail || '无数据'}}</span>
                                <span slot="reference" style="cursor:pointer">
                                    <span style="border-bottom:1px solid;margin-right:4px">螺杆厂家/编号</span>
                                    <i class="el-icon-info"></i>
                                </span>
                            </el-popover>
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                v-if="isEdit"
                                @change="onUpdateToolData('screwMan')"
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.screwMan"
                            ></el-input>
                            <span v-else style="font-size:13px" :title="report.screwMan?`${report.screwMan} / ${report.screwSerialNumber}`:''">{{report.screwMan}}</span>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            震击器厂家/编号
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                v-if="isEdit"
                                @change="onUpdateToolData('knMan')"
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.knMan"
                            ></el-input>
                            <span v-else style="font-size:13px" :title="report.knMan?`${report.knMan} / ${report.knSerialNumber}`:''">{{report.knMan}}</span>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            水力厂家/编号
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                v-if="isEdit"
                                @change="onUpdateToolData('wpMan')"
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.wpMan"
                            ></el-input>
                            <span v-else style="font-size:13px" :title="report.wpMan?`${report.wpMan} / ${report.wpSerialNumber}`:''">{{report.wpMan}}</span>
                        </div>
                        <div class="title center px12" style="flex: 1">
                            定向工程师(带队)
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.deLeader"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            井名
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.wellNumber"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            密度(g/cm³)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.density"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            钻头喷嘴
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillNozzle"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            螺杆尺寸
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.screwSize"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            震击器尺寸
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.knSize"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            水力尺寸/位置
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.wpSize"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 1">
                            定向工程师
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.de"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            钻机
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillingMachine"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            粘度(s)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.viscosity"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            入井次数
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillIntoNum"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            入井次数
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.screwIntoNum"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            入井次数
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.knIntoNum"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            入井次数
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.wpIntoNum"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 1">
                            仪器工程师(带队)
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.ieLeader"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            井深
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.depth"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            %含油
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.oilContent"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            钻头纯钻时间(h)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillTime"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            螺杆循环时间(h)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.screwTime"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            震击器入井时间(h)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.knTime"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            水力循环时间(h)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.wpTime"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 1">
                            仪器工程师
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.ie"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            套管鞋(m)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.casingShoe"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            %固含
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.solidContent"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 56% 0 0">
                            当日数据
                        </div>
                        <div class="title center" style="flex: 1">
                            人员到井时间
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.perArrTime"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            套管尺寸(mm)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.casingSize"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            %含砂
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.sandContent"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            所钻层位
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.drillPos"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            单日纯钻(h)
                        </div>
                        <div class="text center not-input" style="flex: 6% 0 0">
                            <InputNumber
                                disabled
                                class="mwd-input"
                                v-model="report.odDrillTime"
                            ></InputNumber>
                        </div>
                        <div class="title center px12" style="flex: 8% 0 0">
                            滑动钻时(min/m)
                        </div>
                        <div class="text center not-input" style="flex: 6% 0 0">
                            <InputNumber
                                disabled
                                class="mwd-input"
                                v-model="report.sildeDrillTime"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            排量(L/S)
                        </div>
                        <div class="text center" style="flex: 6% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.cc"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 1">
                            设备到井时间
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.equArrTime"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="line" style="flex: 84% 0 0">
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    井眼尺寸(mm)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.boreholeSize"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    循环温度(℃)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.temp"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    井深(m)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        @change="onDrillDepthChange"
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.drillDepth"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    单日循环(h)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        @change="onCircleTimeChange"
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.odCircleTime"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center px12"
                                    style="flex: 9.523% 0 0"
                                >
                                    复合钻时(min/m)
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="report.rcDrillTime"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    泵压(Mpa)
                                </div>
                                <div class="text center" style="flex: 1">
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.pp"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    设计井深
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.designSize"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                ></div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                ></div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    所剩进尺(m)
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="report.remainFootage"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    累计纯钻(h)
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="report.cumDrillTime"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    仪器零长(m)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.zeroLength"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center px12"
                                    style="flex: 9.523% 0 0"
                                >
                                    上提/下放摩阻(T)
                                </div>
                                <div class="text center" style="flex: 1">
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.friction"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    入井趟数
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <el-select
                                        placeholder=""
                                        clearable
                                        @change="onRunChanged"
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.run"
                                    >
                                        <el-option
                                            v-for="run in runList"
                                            :label="run.run"
                                            :key="run.id"
                                            :value="run.run"
                                        ></el-option>
                                    </el-select>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                ></div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                ></div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    日进尺(m)
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="report.dailyFootage"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    累计循环(h)
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="report.cumCircleTime"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 9.523% 0 0"
                                >
                                    仪器信号强度
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 7.142% 0 0"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.ss"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="title center px12"
                                    style="flex: 9.523% 0 0"
                                >
                                    钻进扭矩(KN.m)
                                </div>
                                <div class="text center" style="flex: 1">
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.torque"
                                    ></InputNumber>
                                </div>
                            </div>
                        </div>

                        <div
                            class="title center"
                            style="flex: 1; height: auto; line-height: 84px"
                        >
                            我方提供服务
                        </div>
                        <div class="text center" style="flex: 1; height: auto">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.serPro"
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            钻进数据统计
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 8% 0 0">
                            钻进方式
                        </div>
                        <div class="title center" style="flex: 6% 0 0">
                            地层
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            岩性
                        </div>
                        <div class="title center" style="flex: 6% 0 0">
                            段长(m)
                        </div>
                        <div class="title center" style="flex: 14% 0 0">
                            时间(h)
                        </div>
                        <div class="title center" style="flex: 8% 0 0">
                            机械钻速(m/h)
                        </div>
                        <div class="title center" style="flex: 28% 0 0">
                            钻具组合
                        </div>
                        <div class="title center" style="flex: 1">
                            趟钻时间统计
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="line"
                            style="flex: 1; box-sizing: border-box"
                        >
                            <div
                                v-for="(statistic, key) in report.statisticList"
                                :key="'statistic' + key"
                                class="line"
                                style="flex: 100% 0 0"
                            >
                                <div class="title center" style="flex: 16% 0 0">
                                    {{ statistic.type }}
                                </div>
                                <div class="text center" style="flex: 12% 0 0">
                                    <el-input
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="statistic.formation"
                                    ></el-input>
                                </div>
                                <div class="text center" style="flex: 16% 0 0">
                                    <el-input
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="statistic.rockCore"
                                    ></el-input>
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 12% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="statistic.segLength"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 28% 0 0"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="statistic.time"
                                    ></InputNumber>
                                </div>
                                <div
                                    class="text center not-input"
                                    style="flex: 1"
                                >
                                    <InputNumber
                                        disabled
                                        class="mwd-input"
                                        v-model="statistic.drillRate"
                                    ></InputNumber>
                                </div>
                            </div>
                        </div>
                        <!-- TODO:字段缺失 -->
                        <div class="text center" style="flex:28% 0 0;height:auto;">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.bha"
                                class="mwd-input"
                                type="textarea"
                                :autosize="{minRows:16,maxRows:16}"
                                :spellcheck="false"
                            ></el-input>
                        </div>

                        <div class="line" style="flex: 22% 0 0">
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    本趟入转盘面时间
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <el-input
                                        @change="onUpdateTripTotalTime"
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.intoTurnTime"
                                    ></el-input>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    本趟出转盘面时间
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <el-input
                                        @change="onUpdateTripTotalTime"
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.outerTurnTime"
                                    ></el-input>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    纯钻时间
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        v-model="report.tripDrillTime"
                                        class="mwd-input"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    循环时间
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.tripCircleTime"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div class="line" style="flex: 100% 0 0">
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    本趟入井总时间(min)
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <InputNumber
                                        :disabled="!isEdit"
                                        class="mwd-input"
                                        v-model="report.tripTotalTime"
                                    ></InputNumber>
                                </div>
                            </div>
                            <div
                                class="line"
                                style="flex: 100% 0 0; height: auto"
                            >
                                <div
                                    class="title center"
                                    style="flex: 1; height: auto"
                                >
                                    起钻原因
                                </div>
                                <div
                                    class="text center"
                                    style="flex: 1; height: auto"
                                >
                                    <el-input
                                        :disabled="!isEdit"
                                        v-model="report.pullDrillRs"
                                        class="mwd-input"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            详细工况
                        </div>
                    </div>
                    <div class="line">
                        <div class="line" style="flex: 92% 0 0">
                            <div class="line">
                                <div class="title center" style="flex: 1 0 0">
                                    日期
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    开始时间
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    结束时间
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    起始井深
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    结束井深
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    段长
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    钻进时长
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    钻进方式
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    钻压
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    地面转速
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    测深
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    井斜
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    方位
                                </div>
                                <div class="title center" style="flex: 1">
                                    工具面
                                </div>
                            </div>
                            <div class="line">
                                <div class="title center" style="flex: 1 0 0">
                                    dd-mm-yy
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 1 0 0"
                                ></div>
                                <div
                                    class="title center"
                                    style="flex: 1 0 0"
                                ></div>
                                <div class="title center" style="flex: 1 0 0">
                                    m
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    m
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    m
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    min
                                </div>
                                <div
                                    class="title center"
                                    style="flex: 1 0 0"
                                ></div>
                                <div class="title center" style="flex: 1 0 0">
                                    t
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    c/min
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    m
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    °
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    °
                                </div>
                                <div class="title center" style="flex: 1 0 0">
                                    °
                                </div>
                            </div>
                        </div>
                        <div
                            class="title center"
                            style="flex: 1; height: auto; line-height: 4"
                        >
                            异常分析
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.activityList"
                        :key="`activity${key}`"
                    >
                        <i
                            v-if="
                                report.activityList.length >
                                    projectActivityLength && isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size: 15px"
                            @click="onClickDelete('activityList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size: 15px"
                            v-if="isEdit"
                            @click="onClickAdd('activityList', key)"
                        ></i>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                v-model="item.date"
                                class="mwd-input"
                            ></el-date-picker>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <!-- <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                value-format="HH:mm"
                                :picker-options="{
                                    start: '00:00',
                                    step: '00:05',
                                    end: '23:50',
                                }"
                                v-model="item.startTime"
                            ></el-time-select> -->
                            <el-input
                                :disabled="!isEdit"
                                v-model="item.startTime"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <!-- <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.endTime"
                                value-format="HH:mm"
                                :picker-options="{
                                    minTime: item.startTime,
                                    start: '00:00',
                                    step: '00:05',
                                    end: '23:50',
                                }"
                            ></el-time-select> -->
                            <el-input
                                :disabled="!isEdit"
                                v-model="item.endTime"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.startDepth"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.endDepth"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                @change="onUpdateStatisticData"
                                :disabled="!isEdit"
                                v-model="item.segLength"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                @change="onUpdateStatisticData"
                                :disabled="!isEdit"
                                v-model="item.drillDuration"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <!-- <el-select
                                clearable
                                @change="onUpdateStatisticData"
                                placeholder=""
                                :disabled="!isEdit"
                                v-model="item.drillType"
                                class="mwd-input"
                            >
                                <el-option
                                    v-for="drillType in drillTypeList"
                                    :key="drillType"
                                    :label="drillType"
                                    :value="drillType"
                                >
                                </el-option>
                            </el-select> -->
                            <el-input
                                :disabled="!isEdit"
                                v-model="item.drillType"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.drillPressure"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.groundDrillRate"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.md"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.deviation"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.direction"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 6.571% 0 0">
                            <InputNumber
                                :disabled="!isEdit"
                                v-model="item.toolFace"
                                class="mwd-input"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                v-model="item.note"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {
    getInitPROJECT,
    projectActivityLength,
    getProjectActivityTmp as activityListTmp,
} from "./data";
import {
    apiAddDailyReport,
    apiDeleteDailyReport,
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
    getProjectData,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "PROJECT";
export default {
    name: "PROJECT",
    data() {
        return {
            getInitPROJECT,
            report: {},
            showReport: false,
            projectActivityLength,
            drillTypeList: [
                "定向",
                "复合",
                "起下钻",
                "修理",
                "划眼测斜接立柱",
                "处理复杂",
                "待命",
                "其他",
            ],
            lastCumCycle: 0,
            lastCumDrill: 0,
            isEdit: false,
            selectedDateId: null,
            currentDateList: [],
            showAddPopover: false,
            orginDateAdd: "",
            showLoading: true
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        selectedDate: String,
        run: String,
        job: String,
        runList: Array,
        jobId: Number,
        jobStatus: Number,
        globalDisableEdit: Boolean,
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    watch: {
        selectedDateId(newVal) {
            let map = this.currentDateList.find((item) => {
                return item.id == newVal;
            });
            if (map) {
                this.$emit("updateCommonDate", map.date);
            }
        },
    },
    created() {
        this.report = getInitPROJECT();
    },
    methods: {
        onUpdateTripTotalTime() {
            if (this.report.outerTurnTime && this.report.intoTurnTime) {
                var s1 = this.report.intoTurnTime;
                s1 = new Date(s1);
                var s2 = this.report.outerTurnTime;
                var days = s2.getTime() - s1.getTime();
                var time = parseInt(days / (1000 * 60 * 60 * 24));
                this.report.tripTotalTime = time;
            }
        },
        onUpdateStatisticData() {
            let dxdc = 0, //定向段长
                dxsj = 0, //定向时间(min)
                fhdc = 0, //复合段长
                fhsj = 0; //复合时间(min)
            this.report.statisticList.forEach((item, key) => {
                const drillType = item.type;
                let totalSegLength = 0;
                let totalTime = 0;
                this.report.activityList.forEach((act) => {
                    if (act.drillType === drillType) {
                        totalSegLength += Number(act.segLength);
                        totalTime += Number(act.drillDuration);
                    }
                });
                if (drillType === "定向") {
                    dxdc = totalSegLength;
                    dxsj = totalTime;
                }
                if (drillType === "复合") {
                    fhdc = totalSegLength;
                    fhsj = totalTime;
                }

                this.report.statisticList[key].segLength =
                    totalSegLength || null;
                this.report.statisticList[key].time =
                    Number((totalTime / 60).toFixed(2)) || null;
                if (totalSegLength && totalTime) {
                    this.report.statisticList[key].drillRate = Number(
                        ((totalSegLength / totalTime) * 60).toFixed(2)
                    );
                } else {
                    this.report.statisticList[key].drillRate = null;
                }
            });

            // 总段长
            const totalSegLength = this.report.statisticList.reduce(
                (acc, cur) =>
                    cur.segLength ? acc + Number(cur.segLength) : acc,
                0
            );
            // 日进尺=总段长
            this.report.dailyFootage = totalSegLength;

            // 井深=昨日井深+总段长
            // 所剩进尺=设计井深-井深

            // 纯钻min
            let drillTime = Number(dxsj) + Number(fhsj);
            // 单日纯钻=定向钻进时长+复合钻进时长
            this.report.odDrillTime = (drillTime / 60).toFixed(2);
            // 滑动钻时=定向钻进时长/定向段长
            this.report.sildeDrillTime = dxdc
                ? (Number(dxsj) / Number(dxdc)).toFixed(2)
                : null;
            // 复合钻时=复合钻进时长/复合段长
            this.report.rcDrillTime = fhdc
                ? (Number(fhsj) / Number(fhdc)).toFixed(2)
                : null;
            // 累计纯钻
            this.report.cumDrillTime =
                Number(this.lastCumDrill) + Number((drillTime / 60).toFixed(2));
        },
        // 所剩进尺=设计井深-井深
        onDrillDepthChange() {
            this.report.remainFootage =
                Number(this.report.designSize) - Number(this.report.drillDepth);
        },
        // 累计循环=昨日累计循环+单日循环
        onCircleTimeChange() {
            this.report.cumCircleTime =
                Number(this.lastCumCycle) + Number(this.report.odCircleTime);
        },
        // 入井趟次变化
        onRunChanged() {
            getProjectData({ jobId: this.jobId, run: this.report.run }).then(
                (res) => {
                    let tripDrillTime = res.data.data.tripDrillTime;
                    let tripCircleTime = res.data.data.tripCircleTime;
                    this.report.tripDrillTime =
                        Number(this.report.odDrillTime) + Number(tripDrillTime);
                    this.report.tripCircleTime =
                        Number(this.report.odCircleTime) +
                        Number(tripCircleTime);
                }
            );
        },
        onUpdateToolData(type) {
            if (type === "drillMan") {
                getProjectData({
                    jobId: this.jobId,
                    drillMan: this.report.drillMan,
                }).then((res) => {
                    this.report.drillTime = res.data.data.drillTime;
                });
            } else if (type === "screwMan") {
                getProjectData({
                    jobId: this.jobId,
                    screwMan: this.report.screwMan,
                }).then((res) => {
                    this.report.screwTime = res.data.data.screwTime;
                });
            } else if (type === "knMan") {
                getProjectData({
                    jobId: this.jobId,
                    knMan: this.report.knMan,
                }).then((res) => {
                    this.report.knTime = res.data.data.knTime;
                });
            } else if (type === "wpMan") {
                getProjectData({
                    jobId: this.jobId,
                    wpMan: this.report.wpMan,
                }).then((res) => {
                    this.report.wpTime = res.data.data.wpTime;
                });
            }
        },
        activityListTmp,
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
            this.onUpdateStatisticData();
            // this.onSegLengthChange(item)
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        // TODO: prop -> selectedDate
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentDateList = baseInfo.data?.data || [];
            const idx = this.currentDateList.findIndex((item) => {
                return item.date === this.selectedDate;
            });
            let reportId;
            if (idx != -1) {
                reportId = this.currentDateList[idx].id;
                this.selectedDateId = reportId;
                this.getReportInfo(reportId);
            } else {
                reportId = this.currentDateList[0]?.id;
                if (reportId) {
                    this.selectedDateId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedDateId = null;
                    this.hideReport();
                }
            }
        },
        onSelectDate() {
            this.loadReport();
        },
        onClickDeleteDailyReport() {
            let form = new FormData();
            form.append("reportId", String(this.selectedDateId));
            form.append("reportType", REPORT_TYPE);
            this.$confirm("确认要删除该日报吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiDeleteDailyReport(form).then(() => {
                    this.loadReport();
                });
            });
        },
        onConfirmAddDate() {
            const date = this.orginDateAdd || new Date();
            const dateAdd = date.Format("yyyy-MM-dd");
            if (this.currentDateList.some((item) => item.date === dateAdd)) {
                this.$message.error("报告日期不能重复！");
                return;
            }
            const initReport = this.getInitPROJECT();
            let postReport = Object.assign(initReport, {
                date: dateAdd,
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            apiAddDailyReport(postReport).then(() => {
                this.loadReport();
                this.showAddPopover = false;
            });
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.lastCumCycle =
                    Number(this.report.cumCircleTime) -
                    Number(this.report.odCircleTime);
                this.lastCumDrill =
                    Number(this.report.cumDrillTime) -
                    Number(this.report.odDrillTime);
                this.$nextTick(() => {
                    this.showReport = true;
                });
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
    beforeDestroy() {
        this.showReport = false;
    },
};
</script>
