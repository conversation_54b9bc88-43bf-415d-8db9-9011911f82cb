<template>
  <!--  -->
    <div class="content" style="position: relative" v-loading="showLoading">
      <div style="z-index: 999;height: 50px;display: flex;align-items: center;min-width: 700px;background: white;border-bottom: 1px dashed #eee;">
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
    </div>
    <div v-if="showReport" class="mwd-container">
      <!--  -->
      <div
        class="content-container2"
        style="margin-bottom: 10px; width: 90% important"
      >
        <!-- block -->
        <div class="line" style="border:2px solid #BAD0DC;">
          <div class="line">
            <div class="title center" style="flex:40% 0 0">
              概述/MWD SUMMARY
            </div>
            <div class="title center" style="flex: 1 0 0px;">
              工单号/Job# {{ job }}
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 40% 0 0">
              客户/CLIENT:
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.client"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
                        <div class="title2 center" style="flex: 40% 0 0">
              井号/WELL NAME :
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.wellNumber"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 40% 0 0">
              井位置/LOCATION:
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.location"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 40% 0 0">
              区块/FIELD:
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.field"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 40% 0 0">
              钻井承包商/DRILLING CONTRACTOR:
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.drillContractor"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 40% 0 0">
              我方提供服务/SERVICE TYPE:
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.serviceType"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
        </div>

        <div
          class="line"
          style="display:flex;border-left:2px solid #BAD0DC;border-right:2px solid #BAD0DC;"
        >
          <!-- block -->
          <div style="flex: 40% 0 0">
            <div class="line">
              <div class="title center" style="flex: 100% 0 0">
                人员信息PERSONNEL
              </div>
            </div>

            <div class="line">
              <div class="title2 center" style="flex: 50% 0 0">
                定向井工程师/DD
              </div>
              <div class="text left" style="flex: 50% 0 0">
                <el-input
                  v-model="report.personnelList[0].name"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 50% 0 0">
                定向井工程师/DD
              </div>
              <div class="text left" style="flex: 50% 0 0">
                <el-input
                  v-model="report.personnelList[1].name"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 50% 0 0">
                仪器工程师/MWD
              </div>
              <div class="text left" style="flex: 50% 0 0">
                <el-input
                  v-model="report.personnelList[2].name"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 50% 0 0">
                仪器工程师/MWD
              </div>
              <div class="text left" style="flex: 50% 0 0">
                <el-input
                  v-model="report.personnelList[3].name"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 50% 0 0">
                负责人/CELL MANAGER
              </div>
              <div class="text left" style="flex: 50% 0 0">
                <el-input
                  v-model="report.personnelList[4].name"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
          </div>
          <!-- block -->
          <div style="flex: 1">
            
            <div class="line">
              <div class="title2 center" style="flex: 1 0 0">
                开钻日期/DATE IN
              </div>
              <div class="text left" style="flex:1 0 0">
                <el-date-picker
                  style="width:100%"
                  class="mwd-input"
                  v-model="report.dateIn"
                  :disabled="!isEdit"
                  type="date"
                  placeholder=""
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 1 0 0">
                完钻日期/DATE OUT
              </div>
              <div class="text left" style="flex: 1 0 0">
                <el-date-picker
                  style="width: 100%"
                  class="mwd-input"
                  v-model="report.dateOut"
                  :disabled="!isEdit"
                  type="date"
                  placeholder=""
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 1 0 0">
                工具下入趟数/TOTAL MWD RUNS
              </div>
              <div class="text left" style="flex: 1 0 0">
                <InputNumber
                  v-model="report.totalMwdRuns"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></InputNumber>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 1 0 0">
                本井BPA使用时间/TOTAL TIME OF BPA Hrs
              </div>
              <div class="text left" style="flex:1 0 0">
                <el-input
                  v-model="report.totalBpaTimeHrs"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex:1 0 0">
                本井最高温度/MAX TEMP
              </div>
              <div class="text left" style="flex: 1 0 0">
                <el-input
                  v-model="report.maxTemp"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></el-input>
              </div>
            </div>
            <div class="line">
              <div class="title2 center" style="flex: 1 0 0">
                仪器失效/MWD FAILURES
              </div>
              <div class="text left" style="flex: 1 0 0">
                <InputNumber
                  v-model="report.mwdFailures"
                  :disabled="!isEdit"
                  class="mwd-input"
                ></InputNumber>
              </div>
            </div>
          </div>
        </div>
        <div
          class="line"
          style="border: 2px solid #bad0dc; border-bottom: none"
        >
          <div class="title center" style="flex: 1 0 0">
            井型/WELL TYPE
          </div>
          <div class="text left" style="flex: 1 0 0">
            <el-select
                v-model="report.wellType"
                :disabled="!isEdit"
                class="mwd-input"
                placeholder=""
            >
                <el-option
                    v-for="item in wellTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                ></el-option>
            </el-select>
          </div>
        </div>
        <div
          class="line"
          style="border: 2px solid #bad0dc; border-bottom: none"
        >
          <div class="title center" style="flex: 1 0 0">
            井眼尺寸/HOLE SIZE(mm)
          </div>
          <div class="text center not-input" style="flex: 1 0 0">
            <InputNumber
              v-if="report.holeInfo"
              v-model="report.holeInfo.holeSize"
              disabled
              class="mwd-input"
            ></InputNumber>
          </div>
          <div class="title center" style="flex: 1 0 0">
            从FROM(m)
          </div>
          <div class="text center not-input" style="flex: 1 0 0">
            <InputNumber
              v-if="report.holeInfo"
              v-model="report.holeInfo.from"
              disabled
              class="mwd-input"
            ></InputNumber>
          </div>
          <div class="title center" style="flex: 1 0 0">到TO(m)</div>
          <div class="text center not-input" style="flex: 1 0 0">
            <InputNumber
              v-if="report.holeInfo"
              v-model="report.holeInfo.to"
              disabled
              class="mwd-input"
            ></InputNumber>
          </div>
        </div>
        <div class="line" style="border: 2px solid #bad0dc;">
          <div class="line">
            <div class="title center" style="flex: 1 0 0">
              仪器使用时间/MWD USE(hrs)
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 1 0 0">
              井下/DOWNHOLE
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.downHole"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 1 0 0">
              循环/CIRCULATING
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.circulating"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex: 1 0 0">
              纯钻/DRILLING
            </div>
            <div class="text center" style="flex: 1 0 0">
              <el-input
                v-model="report.drilling"
                :disabled="!isEdit"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="line" style="border:2px solid #BAD0DC; border-top: none;">
          <div class="line">
              <div style="flex:1 0 0" class="title center">
                JOB COMMENTS
              </div>
          </div>
          <div class="line">
              <div style="flex:1 0 0;height:auto" class="text center">
                  <el-input
                    type="textarea"
                    class="mwd-input"
                    v-model="report.jobComments"
                    :disabled="!isEdit"
                  ></el-input>
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInitRSS_COVER_REPORT,
} from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
} from "@/api/mwd";
const REPORT_TYPE = "RSS_COVER";
import dealReport from "./init";
export default {
  name: "RSS_COVER",
  data() {
    return {
      REPORT_TYPE,
      report: {},
      showReport: false,
      reportId: -1,
      isEdit: false,
      showLoading: true,
    };
  },
  props: {
    username: String,
    electronStore: Object,
    date: String,
    run: String,
    job: String,
    jobId: Number,
    wellTypeList: Array,
    runList: Array,
    jobStatus: Number,
    globalDisableEdit: Boolean,
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  created() {
    this.report = getInitRSS_COVER_REPORT();
  },
  methods: {
    async loadReport() {
        this.showLoading = true;
        const baseInfo = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: REPORT_TYPE,
        });
        const baseInfoList = baseInfo.data?.data || [];
        if (baseInfoList.length === 0) {
            this.$message.error("暂无相关数据");
            this.showLoading = false;
            return;
        }
        const reportId = baseInfoList[0].id;
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        })
            .then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.report.holeInfo = this.report.holeInfo || {};
                this.$nextTick(() => {
                    this.showReport = true;
                });
            })
            .finally(() => {
                this.showLoading = false;
            });
    },
    hideReport() {
      this.showLoading = false;
      this.showReport = false;
    },
  },
};
</script>
