
import {
    getInitMWD,getMwdToolTmp,mwdToolLength, getMwdActivityTmp, getProjectActivityTmp, projectActivityLength, mwdActivityLength, getInitPROJECT,
    getToolsBhaTmp, toolsBhaListLength,
    getToolsDeviationTmp, toolsDeviationLength,
    getToolsDeviceTmp, toolsDeviceLength,
    getToolsDrillTmp, toolsDrillLength,
    getToolsActivityTmp, toolsActivityLength,
    getInitTOOLS,
    bhaItemLength, getBhaItemTmp, getInitBHA,
    runToolListLength ,getRunToolListTmp, runSummaryListLength, getRunSummaryListTmp, getInitRUN,
    failureToolLen, getFailureToolTmp, getInitFAILURE,
    toolsSurveysActivityLength, getSurveysActivityTmp, getInitSURVEYS,
    roActionList<PERSON>ength, getROActionListTmp, getInitROPROCEDURES,
    riActionListLength, getRIActionListTmp, getInitRIPROCEDURES,
    coverPersonnelList, coverHoleInfoLen, getCoverHoleInfoTmp,
    toolsOnsiteToolLength,getToolsOnsiteToolTmp,getInitTOOLSONSITE,
    projectToolsOnSiteToolLength,getProjectToolsOnSiteToolTmp,getInitPROJECTTOOLSONSITE,
    getInitRSS_DAILY_REPORT,
    getRssDailyActivityTmp,
    getRssDailyToolTmp,
    getInitRSS_BHA_REPORT,
    rssBhaItemLength,
    getRssBhaItemTmp,
    getInitRSS_RUN_REPORT,
    rssRunSummaryListLength,
    getRssRunSummaryListTmp,
    rssRunToolListLength,
    getRssRunToolListTmp,
    getInitRSS_ROP_REPORT,
    rssRopActionListLength,
    getRssROActionListTmp,
    getInitRSS_RIP_REPORT,
    rssRiActionListLength,
    getRssRIActionListTmp,
    getInitRSS_TOOLSONSITE,
    rssToolsOnsiteToolLength,
    getRssToolsOnsiteToolTmp,
    getRssCoverHoleInfoTmp,
    rssCoverHoleInfoLen,
    rssCoverPersonnelList,
    rssOosLength,
    getRssOosTmp
} from './data'


export default function dealReport(report,reportType) {
    // if(report==null) return null;
    if (reportType === 'MWD') {
        if (!report.activityList) {
            report.activityList = []
        }
        if (!report.toolList) {
            report.toolList = []
        }
        if (!report.workSummaryList) {
            report.workSummaryList =  [{time:'07:00',content:''},{time:'11:00',content:''},{time:'15:00',content:''},{time:'19:00',content:''},{time:'21:00',content:''}]
        }
        report = Object.assign({}, getInitMWD(), report)
        const activityLen = report.activityList.length;
        if (activityLen < mwdActivityLength) {
            for (let i = 0; i < mwdActivityLength - activityLen; i++) {
                report.activityList.push(getMwdActivityTmp())
            }
        }
        const toolLen = report.toolList.length;
        if (toolLen < mwdToolLength) {
            for (let i = 0; i < mwdToolLength - toolLen; i++) {
                report.toolList.push(getMwdToolTmp())
            }
        }
        return report
    } else if (reportType === 'RSS_DAILY') {
        if (!report.activityList) {
            report.activityList = []
        }
        if (!report.toolList) {
            report.toolList = []
        }
        if (!report.oosList) {
            report.oosList = []
        }
        report = Object.assign({}, getInitRSS_DAILY_REPORT(), report)
        const activityLen = report.activityList.length;
        if (activityLen < mwdActivityLength) {
            for (let i = 0; i < mwdActivityLength - activityLen; i++) {
                report.activityList.push(getRssDailyActivityTmp())
            }
        }
        const toolLen = report.toolList.length;
        if (toolLen < mwdToolLength) {
            for (let i = 0; i < mwdToolLength - toolLen; i++) {
                report.toolList.push(getRssDailyToolTmp())
            }
        }
        
        const oosLen = report.oosList.length;
        if (oosLen < rssOosLength) {
            for (let i = 0; i < rssOosLength - oosLen; i++) {
                report.oosList.push(getRssOosTmp())
            }
        }
        return report
    } else if (reportType === "PROJECT") {
        if (!report.activityList) {
            report.activityList = []
        }
        report = Object.assign(getInitPROJECT(), report)
        const activityLen = report.activityList.length;
        if (activityLen < projectActivityLength) {
            for (let i = 0; i < projectActivityLength - activityLen; i++) {
                report.activityList.push(getProjectActivityTmp())
            }
        }
        return report
    } else if (reportType === "TOOLS") {
        if (!report.activityList) {
            report.activityList = []
        }
        if (!report.drillList) {
            report.drillList = []
        }
        if (!report.deviceList) {
            report.deviceList = []
        }
        if (!report.deviationList) {
            report.deviationList = []
        }
        if (!report.bhaList) {
            report.bhaList = []
        }
        report = Object.assign(getInitTOOLS(), report)
        const activityLen = report.activityList.length;
        if (activityLen < toolsActivityLength) {
            for (let i = 0; i < toolsActivityLength - activityLen; i++) {
                report.activityList.push(getToolsActivityTmp())
            }
        }
        const drillLen = report.drillList.length;
        if (drillLen < toolsDrillLength) {
            for (let i = 0; i < toolsDrillLength - drillLen; i++) {
                report.drillList.push(getToolsDrillTmp())
            }
        }
        const deviceLen = report.deviceList.length;
        if (deviceLen < toolsDeviceLength) {
            for (let i = 0; i < toolsDeviceLength - deviceLen; i++) {
                report.deviceList.push(getToolsDeviceTmp())
            }
        }
        const deviationLen = report.deviationList.length;
        if (deviationLen < toolsDeviationLength) {
            for (let i = 0; i < toolsDeviationLength - deviationLen; i++) {
                report.deviationList.push(getToolsDeviationTmp())
            }
        }
        const bhaLen = report.bhaList.length;
        if (bhaLen < toolsBhaListLength) {
            for (let i = 0; i < toolsBhaListLength - bhaLen; i++) {
                report.bhaList.push(getToolsBhaTmp())
            }
        }
        return report
    } else if (reportType === "BHA") {
        if (!report.itemList) {
            report.itemList = []
        }
        report = Object.assign(getInitBHA(), report)
        const itemLen = report.itemList.length;
        if (itemLen < bhaItemLength) {
            for (let i = 0; i < bhaItemLength - itemLen; i++) {
                report.itemList.push(getBhaItemTmp())
            }
        }
        return report
    } else if (reportType === 'RSS_BHA') {
        if (!report.itemList) {
            report.itemList = []
        }
        report = Object.assign(getInitRSS_BHA_REPORT(), report)
        const itemLen = report.itemList.length;
        if (itemLen < rssBhaItemLength) {
            for (let i = 0; i < rssBhaItemLength - itemLen; i++) {
                report.itemList.push(getRssBhaItemTmp())
            }
        }
        return report
    } else if (reportType === 'RUN') {
        if (!report.summaryList) {
            report.summaryList = []
        }
        if (!report.toolList) {
            report.toolList = []
        }
        report = Object.assign(getInitRUN(), report)
        const summaryLen = report.summaryList.length;
        if (summaryLen < runSummaryListLength) {
            for (let i = 0; i < runSummaryListLength - summaryLen; i++) {
                report.summaryList.push(getRunSummaryListTmp())
            }
        }
        const toolLen = report.toolList.length;
        if (toolLen < runToolListLength) {
            for (let i = 0; i < runToolListLength - toolLen; i++) {
                report.toolList.push(getRunToolListTmp())
            }
        }
        return report
    } else if (reportType === 'RSS_RUN') {
        if (!report.summaryList) {
            report.summaryList = []
        }
        if (!report.toolList) {
            report.toolList = []
        }
        if (!report.oosList) {
            report.oosList = []
        }
        report = Object.assign(getInitRSS_RUN_REPORT(), report)
        const summaryLen = report.summaryList.length;
        if (summaryLen < rssRunSummaryListLength) {
            for (let i = 0; i < rssRunSummaryListLength - summaryLen; i++) {
                report.summaryList.push(getRssRunSummaryListTmp())
            }
        }
        const toolLen = report.toolList.length;
        if (toolLen < rssRunToolListLength) {
            for (let i = 0; i < rssRunToolListLength - toolLen; i++) {
                report.toolList.push(getRssRunToolListTmp())
            }
        }
        
        const oosLen = report.oosList.length;
        if (oosLen < rssOosLength) {
            for (let i = 0; i < rssOosLength - oosLen; i++) {
                report.oosList.push(getRssOosTmp())
            }
        }
        return report
    } else if (reportType === 'FAILURE') {
        if (!report.toolList) {
            report.toolList = []
        }
        report = Object.assign(getInitFAILURE(), report)
     
        const toolLen = report.toolList.length;
        if (toolLen < failureToolLen) {
            for (let i = 0; i < failureToolLen - toolLen; i++) {
                report.toolList.push(getFailureToolTmp())
            }
        }
        return report
    } else if (reportType === 'SURVEYS') {
        if (!report.activityList) {
            report.activityList = []
        }
        report = Object.assign(getInitSURVEYS(), report)
        const activityLen = report.activityList.length;
        if (activityLen < toolsSurveysActivityLength) {
            for (let i = 0; i < toolsSurveysActivityLength - activityLen; i++) {
                report.activityList.push(getSurveysActivityTmp())
            }
        }
        return report
    } else if (reportType === 'ROPROCEDURES') {
        if (!report.actionList) {
            report.actionList = []
        }
        report = Object.assign(getInitROPROCEDURES(), report)
        const actionLen = report.actionList.length;
        if (actionLen < roActionListLength) {
            for (let i = 0; i < roActionListLength - actionLen; i++) {
                report.actionList.push(getROActionListTmp())
            }
        }
        return report
    } else if (reportType === 'RSS_ROPROCEDURES') {
        if (!report.actionList) {
            report.actionList = []
        }
        report = Object.assign(getInitRSS_ROP_REPORT(), report)
        const actionLen = report.actionList.length;
        if (actionLen < rssRopActionListLength) {
            for (let i = 0; i < rssRopActionListLength - actionLen; i++) {
                report.actionList.push(getRssROActionListTmp())
            }
        }
        return report
    } else if (reportType === 'RIPROCEDURES') {
        if (!report.actionList) {
            report.actionList = []
        }
        report = Object.assign(getInitRIPROCEDURES(), report)
        const actionLen = report.actionList.length;
        if (actionLen < riActionListLength) {
            for (let i = 0; i < riActionListLength - actionLen; i++) {
                report.actionList.push(getRIActionListTmp())
            }
        }
        return report
    } else if (reportType === 'RSS_RIPROCEDURES') {
        if (!report.actionList) {
            report.actionList = []
        }
        report = Object.assign(getInitRSS_RIP_REPORT(), report)
        const actionLen = report.actionList.length;
        if (actionLen < rssRiActionListLength) {
            for (let i = 0; i < rssRiActionListLength - actionLen; i++) {
                report.actionList.push(getRssRIActionListTmp())
            }
        }
        return report
    } else if(reportType==='TOOLSONSITE'){
        if(!report.toolList){
            report.toolList = []
        }
        report = Object.assign(getInitTOOLSONSITE(),report )
        const toolLen = report.toolList.length;
        if(toolLen<toolsOnsiteToolLength){
            for(let i=0;i<toolsOnsiteToolLength-toolLen;i++){
                report.toolList.push(getToolsOnsiteToolTmp())
            }
        }
        return report
    } else if(reportType === 'RSS_TOOLSONSITE'){
        if(!report.toolList){
            report.toolList = []
        }
        report = Object.assign(getInitRSS_TOOLSONSITE(),report )
        const toolLen = report.toolList.length;
        if(toolLen<rssToolsOnsiteToolLength){
            for(let i=0;i<rssToolsOnsiteToolLength-toolLen;i++){
                report.toolList.push(getRssToolsOnsiteToolTmp())
            }
        }
        return report
    } else if(reportType==='PROJECTTOOLSONSITE'){
        if(!report){
            report = getInitPROJECTTOOLSONSITE();
        }
        if(!report.toolList){
            report.toolList = []
        }
        report = Object.assign(getInitPROJECTTOOLSONSITE(),report )
        const toolLen = report.toolList.length;
        if(toolLen<projectToolsOnSiteToolLength){
            for(let i=0;i<projectToolsOnSiteToolLength-toolLen;i++){
                report.toolList.push(getProjectToolsOnSiteToolTmp())
            }
        }
        return report
    } else if (reportType === 'COVER') {
        if(!report.holeInfoList){
            report.holeInfoList = []
        }
        const holeInfoLen = report.holeInfoList.length;
        if(holeInfoLen<coverHoleInfoLen){
            for(let i=0;i<coverHoleInfoLen-holeInfoLen;i++){
                report.holeInfoList.push(getCoverHoleInfoTmp())
            }
        }
        if (!(report.personnelList && report.personnelList.length)) {
            report.personnelList = coverPersonnelList
        }
        return report
    } else if (reportType === 'RSS_COVER') {
        if(!report.holeInfoList){
            report.holeInfoList = []
        }
        const holeInfoLen = report.holeInfoList.length;
        if(holeInfoLen<rssCoverHoleInfoLen){
            for(let i=0;i<rssCoverHoleInfoLen-holeInfoLen;i++){
                report.holeInfoList.push(getRssCoverHoleInfoTmp())
            }
        }
        if (!(report.personnelList && report.personnelList.length)) {
            report.personnelList = rssCoverPersonnelList
        }
        return report
    } else {
        return report
    }

}
