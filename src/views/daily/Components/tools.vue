<template>
    <div class="mwd-container" v-if="showReport" v-loading="showLoading">
        <div class="content-container2">
            
            <div class="line">
                <div class="line box-border" style="flex:1">
                    <div class="line">
                        <div style="flex:1" class="title center">作业统计</div>
                    </div>
                    <div class="line">
                        <div style="flex:1" class="title center">人员到井日期</div>
                        <div class="text center" style="flex:1">
                            <el-input 
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workPaDate"
                            ></el-input>
                        </div>
                    </div>
                     <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            设备到井日期
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workDaDate"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/累计待命时间(hr)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workCurstTime"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workCumstTime"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/累计井下时间(hr)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workCurdwTime"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workCumdwTime"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            入井深度/累计进尺(m)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workEntryDepth"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.workCumFootage"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                disabled
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                disabled
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="line box-border" style="flex:1;border-left:none">
                    <div class="line">
                        <div style="flex:1" class="title center">
                            本趟钻统计 (第一趟钻)
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            本次入井日期和时间
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runEntryDate"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            平均机械钻速 (m/hr)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runAvgRop"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当前/下钻时 井深 (m)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCurDepth"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runDrillDepth"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/累计井下时间 (hr)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCurstTime"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCumstTime"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/累计 循环时间
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCurclTime"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCumclTime"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/累计 钻进时间
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCurdrTime"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCumdrTime"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:50% 0 0">
                            当日/本趟钻 进尺 (m)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCurFootage"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.runCumFootage"
                            ></InputNumber>
                        </div>
                    </div>
                </div>
                <div class="line box-border" style="flex:1;border-left:none">
                    <div class="line">
                        <div style="flex:1" class="title center">
                            泥浆性能
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">
                            密度(g/cm3)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudDen"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">含砂(%)</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudSandCon"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">碱度</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudAlk"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">固相(%)</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudSolidPha"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">
                            塑性粘度(mPa.s)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudPlsVis"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">
                            API失水(ml)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudWaterLoss"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">屈服值(pa)</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudYeVal"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">泥饼(mm)</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudCake"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">
                            漏斗粘度(s)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudFunVis"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">
                            D&I温度(oC)
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudDiTemp"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">静切力(pa)</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudSsFor"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1">泥浆体系</div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudSys"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1"></div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                disabled
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:1"></div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                disabled
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="line box-border" style="flex:1;border-left:none">
                    <div class="line">
                        <div class="title center" style="flex:1">
                            钻头情况
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">型号</div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitType"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:1">IADC</div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitIadc"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">水眼</div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitWaterEye"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:1">TFA</div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitTfa"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:25% 0 0">
                            制造商：
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitMan"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex:1" class="title center">
                            测点到钻头距离
                        </div>
                        <div style="flex:1" class="title center">
                            MWD情况
                        </div>
                    </div>
                    <div class="line">
                        <div class="title left px10" style="flex:1">
                            井斜方位传感器
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.daSensorDist"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:25% 0 0">
                            频率/速度
                        </div>
                        <div class="text left" style="flex:25% 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdSpeed"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">Gamma</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.gammaDist"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:25% 0 0">
                            噪音比值：
                        </div>
                        <div class="text left" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdNoiseRatio"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">近钻头测点</div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.nearBitDist"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:1 0 0">
                            SPT1/2强度：
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdSptInt"
                            ></InputNumber>
                        </div>
                    </div>
                </div>
            </div>
            <div class="box-border" style="border-top:none">
            <div class="line">
                <div style="flex:1" class="title center">
                    钻具组合
                </div>
            </div>
            <div class="line">
                <div class="title center" style="flex:1">
                    钻具名称
                </div>
                <div class="title center" style="flex:1">
                    序列号
                </div>
                <div class="title center" style="flex:1">
                    外径(mm)
                </div>
                <div class="title center" style="flex:1">
                    长度(m)
                </div>
                <div class="title center" style="flex:1">
                    累计长度(m)
                </div>
            </div>
            <div
                class="line hover-line"
                v-for="(item, key) in report.bhaList"
                :key="`bha` + key"
            >
                <i v-if="report.bhaList.length>toolsBhaListLength&&isEdit" class="el-icon-close hover-icon delete" @click="onClickDelete('bhaList',key)"></i>
                <i class="el-icon-plus hover-icon plus"  v-if="isEdit" @click="onClickAdd('bhaList',key)"></i>
                <div class="text center" style="flex:20% 0 0">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.name"
                    ></el-input>
                </div>
                <div class="text center" style="flex:20% 0 0">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.serialNum"
                    ></el-input>
                </div>
                <div class="text center" style="flex:20% 0 0">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.outerDiameter"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:20% 0 0">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.length"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:20% 0 0">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.cumLength"
                    ></InputNumber>
                </div>
            </div>
            </div>
            <div class="box-border" style="border-top:none">
            <div class="line">
                <div class="title center" style="flex:60% 0 0">
                    测斜记录
                </div>
                <div class="title center" style="flex:20% 0 0">
                    测点到钻头
                </div>
                <div class="text center" style="flex:1">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="report.reportDate"
                    ></el-input>
                </div>
            </div>
            <div class="line">
                <div class="title center" style="flex:1">测深(m)</div>
                <div class="title center" style="flex:1">井斜角</div>
                <div class="title center" style="flex:1">方位角</div>
                <div class="title center px14" style="flex:1;">
                    狗腿度°/30m
                </div>
                <div class="title center" style="flex:1">位移(m)</div>
            </div>
            <div
                class="line hover-line"
                v-for="(item, key) in report.deviationList"
                :key="`deviation` + key"
            >
                <i v-if="report.deviationList.length>toolsDeviationLength&&isEdit" class="el-icon-close hover-icon delete" @click="onClickDelete('deviationList',key)"></i>
                <i class="el-icon-plus hover-icon plus"  v-if="isEdit" @click="onClickAdd('deviationList',key)"></i>
                <div class="text center" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.md"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.wellAngle"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.azimuth"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.doglegDegree"
                    ></InputNumber>
                </div>
                <div class="text center" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.movement"
                    ></InputNumber>
                </div>
            </div>
            </div>
            <div class="box-border" style="border-top:none">
             <div class="line">
                 <div style="flex:1" class="title center">
                    目前钻进参数
                 </div>
            </div>
            <div class="line">
                <div class="center title" style="flex:1">钻压(吨)</div>
                <div class="center title" style="flex:1">
                    转速(转/分)
                </div>
                <div class="center title" style="flex:1">
                    排量(升/秒)
                </div>
                <div class="center title" style="flex:1">泵压(MPa)</div>
            </div>
            <div
                class="line hover-line"
                v-for="(item, key) in report.drillList"
                :key="`drill` + key"
            >
                <i v-if="report.drillList.length>toolsDrillLength&&isEdit" class="el-icon-close hover-icon delete" @click="onClickDelete('drillList',key)"></i>
                <i class="el-icon-plus hover-icon plus"  v-if="isEdit" @click="onClickAdd('drillList',key)"></i>
                <div class="center text" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.wob"
                    ></InputNumber>
                </div>
                <div class="center text" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.speed"
                    ></InputNumber>
                </div>
                <div class="center text" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.displacement"
                    ></InputNumber>
                </div>
                <div class="center text" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.pumpPressure"
                    ></InputNumber>
                </div>
            </div>
            </div>
            <div class="box-border" style="border-top:none">
            <div class="line">
                <div style="flex:1" class="title center">
                    仪器设备情况
                </div>
            </div>
            <div class="line">
                <div class="center title" style="flex:20% 0 0">
                    目前位置
                </div>
                <div class="center title" style="flex:1">
                    部件名和序列号
                </div>
                <div class="center title" style="flex:1">井下时间</div>
            </div>
            <div
                class="line hover-line"
                v-for="(item, key) in report.deviceList"
                :key="`device` + key"
            >
                <i v-if="report.deviceList.length>toolsDeviceLength&&isEdit" class="el-icon-close hover-icon delete" @click="onClickDelete('deviceList',key)"></i>
                <i class="el-icon-plus hover-icon plus"  v-if="isEdit" @click="onClickAdd('deviceList',key)"></i>
                <div class="center text" style="flex:20% 0 0">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.position"
                    ></el-input>
                </div>
                <div class="center text" style="flex:1">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.nameSerialNum"
                    ></el-input>
                </div>
                <div class="center text" style="flex:1">
                    <InputNumber
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.downTime"
                    ></InputNumber>
                </div>
            </div>
            </div>
            <div class="box-border" style="border-top:none">
            <div class="line">
                <div style="flex:1" class="title center">
                    施工简况
                </div>
            </div>
            <div class="line">
                <div class="center title" style="flex:20% 0 0">起止时间</div>
                <div class="center title" style="flex:1">施工内容</div>
            </div>
            <div
                class="line hover-line"
                v-for="(item, key) in report.activityList"
                :key="`activity` + key"
            >
                <i v-if="report.activityList.length>toolsActivityLength&&isEdit" class="el-icon-close hover-icon delete" @click="onClickDelete('activityList',key)"></i>
                <i class="el-icon-plus hover-icon plus"  v-if="isEdit" @click="onClickAdd('activityList',key)"></i>
                <div class="center text" style="flex:20% 0 0">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="
                            item.startEndTime
                        "
                    ></el-input>
                </div>
                <div class="center text" style="flex:1">
                    <el-input
                        class="mwd-input"
                        :disabled="!isEdit"
                        v-model="item.content"
                    ></el-input>
                </div>
            </div>
            </div>          
        </div>
    </div>
</template>

<script>
import {
    toolsBhaListLength,
    toolsDrillLength,
    toolsDeviceLength,
    toolsDeviationLength,
    toolsActivityLength,
    getInitTOOLS,
    getToolsBhaTmp as bhaListTmp,
    getToolsDeviationTmp as deviationListTmp,
    getToolsDeviceTmp as deviceListTmp,
    getToolsDrillTmp as drillListTmp,
    getToolsActivityTmp as activityListTmp,
} from "./data";
import { apiGetDailyReportInfo, apiUpdateDailyReport } from "@/api/mwd";
import dealReport from "./init";
export default {
    name: "TOOLS",
    data() {
        return {
            toolsBhaListLength,
            toolsDrillLength,
            toolsDeviceLength,
            toolsDeviationLength,
            toolsActivityLength,
            report: {},
            showReport: false,
            showLoading: true
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        run: String,
        job: String,
        isEdit:Boolean
    },
    created() {
        this.report = getInitTOOLS();
    },
    methods: {
        bhaListTmp,
        deviationListTmp,
        deviceListTmp,
        drillListTmp,
        activityListTmp,
        onClickAdd(item ,key){
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item ,key){
            this.report[item].splice(key,1);
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType:'TOOLS',
                ...this.report
            }).then(()=>{
                this.$emit('setIsEditAsFalse')
            });
        },
        loadReport(reportId, reportType) {
            apiGetDailyReportInfo({
                reportType,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, reportType);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.big-title {
    width: 100%;
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 24px;
    font-weight: 500;
}
</style>
