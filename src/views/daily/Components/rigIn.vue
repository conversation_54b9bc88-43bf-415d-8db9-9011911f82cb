<template>
    <ImageViewer class="content rigin-report" v-loading="showLoading" style="position:relative">
        <div
            style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
        >
            <span
                class="report-title"
                style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <template v-if="!isJobCompleted&&!globalDisableEdit">
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left:10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left:10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div v-if="showReport" class="mwd-container">
            <div class="content-container2">
                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title" style="flex: 60px 0 0;">
                            Operator：
                        </div>
                        <el-input
                            :disabled="!isEdit"
                            v-model="report.operator"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title" style="flex: 160px 0 0;">
                            工单号Job Number：
                        </div>
                        <el-input
                            disabled
                            v-model="job"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                </div>
                <div class="box-border">
                    <div class="line">
                        <div class="title center" style="flex:60px 0 0">#</div>
                        <div class="title center" style="flex:1">
                            项目Action
                        </div>
                        <div class="title center" style="flex:200px 0 0">
                            日期Date
                        </div>
                        <div class="title center" style="flex:200px 0 0">
                            Initial
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            1
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Before leaving, check KIT for all tools and parts
                            that you may require for the job you are going on.
                            Use inventory list for a guide.<br />
                            上井前按照下表，检查上井所需的所有工具零部件。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[0].date"
                            ></el-date-picker>

                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[0].date"
                            class="mwd-input"
                        ></el-input> -->
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[0].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            2
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Look over your pulsers and dir. Modules, note hours
                            and condition.Pulsers with hours, test them if time
                            permits and note operation<br />
                            检查探管和脉冲：检查探管使用时间状态。脉冲使用时间、循环时间。时间允许的情况下进行测试，按照操作流程。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[1].date"
                            class="mwd-input"
                        ></el-input> -->
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[1].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[1].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            3
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Check what kind of job it is, camp or non, get rig
                            directions, check radio if you need it. Make sure
                            you got your paperwork in order.<br />
                            确定服务类型，是否提供办公、住宿，井对定位，测试对讲机。确保日报信息正确。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[2].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[2].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            4
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Load MWD kit box and UBHO/GAP subs (IF needed).<br />
                            清点仪器箱，定向接头，绝缘短节
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[3].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[3].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            5
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            On Location, take out all the stuff you need to rig
                            up, keep your tools warm and dry. Note if you are
                            missing anything and notify office.<br />
                            如需要现场所有人员需加入钻台准备工作，保证工具干燥适温。如有漏装东西请及时联系后台。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[4].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[4].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            6
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Rig up downhole and surface equipment and check
                            operation If gamma make sure you get good gamma
                            counts, etc.<br />
                            钻前准备井下和地面工具，检查工作状态是否良好。若使用伽马检查伽马计数是否正确。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[5].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[5].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            7
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Check ALL BHA ID for potential tool restrictions for
                            wireline. 54mm MIN.<br />
                            检查所有底部钻具组合内径，最小不小于54mm.
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[6].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[6].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            8
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Discuss with DD operating parameters for the well,
                            flow rates mag/grav switchover, pulswidth,
                            magnetics, etc..<br />
                            咨询定向井工程师本井参数：排量，磁性重力转换，脉宽等。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[7].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[7].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            9
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Write program, save and print out. Store program to
                            both rec/trans LOAD program from Transmitter,
                            print/save; from Receiver, print/save; Compare and
                            make sure both are programmed properly. Check and
                            clear any previous DAO procedures, if you switch
                            receivers dbl check this<br />
                            编程时注意保存写入与下载备份，确保写入下载正常。钻具角差清零，更换接收端需要双向检查。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[8].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[8].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            10
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Tighten tool, With DD present, highside and roll
                            test MWD tool.Print the highside/roll test and both
                            of you sign it<br />
                            打紧工具，校正高边并做旋转测试，保存截图。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[9].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[9].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            11
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Use Qtalk to ask the tool/receiver standard check
                            questions. Printscreen Make sure to ask TFO?(Tx)
                            (should equal to IMO on your highside printout)
                            mdec? , batd, invf? Diaa? Etc. IF YOU USE ROLL test
                            program triple check your highside and tfo. It can
                            clear your TFO AVOID using it<br />
                            检查各项基础信息，尽量避免使用旋转测试界面，它会将高边清零。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[10].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[10].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            12
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Note all your tool hours and SNs, strap tool, Bit to
                            Sensor/gamma/UBHO<br />
                            记下所有仪器编号，算好工具各项零长。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.actionList[11].date"
                            ></el-date-picker>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[11].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            13
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Make sure you log all data files, name them by job#
                            and run Have all your print screens from Qtalk,
                            roll, whatever saved<br />
                            所有log数据储存再已工单号命名的文件夹内，所有截图一并保存。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[12].date"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[12].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            14
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Watch DD set motor and scribe, double check his work
                            With DD present seat Muleshoe in sub all the way in
                            Highside Muleshoe to mud motor scribe, tighten
                            screws and check<br />
                            校高边与DD双向检查，同时和DD仪器校好循环套高边，并紧固顶丝。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[13].date"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[13].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>

                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            15
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Pickup MWD tool with caution, dbl. Check that it is
                            seated w/DD<br />
                            连接仪器时DD/MWD双向检查。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[14].date"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[14].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            16
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Shallow test when possible, note all parameters,
                            make sure all is well (note pump pressure, strokes,
                            depth, pulse amplitude, etc)<br />
                            浅层测试时关注所有测试参数，确保所有数据正常。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[15].date"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[15].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            17
                        </div>
                        <div class="text left auto" style="flex:1;height:auto">
                            Between bit runs check Muleshoe/set screws. If
                            re-running tool replace polypack and check for wash,
                            loose bow springs, washed popits, etc<br />
                            趟钻之间下钻前需要检查循环套，顶丝。下仪器之前确保检查底部总成，更换Y圈，检查弹簧蘑菇头的冲蚀。
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[16].date"
                                class="mwd-input"
                            ></el-input>
                        </div>
                        <div class="text center auto" style="flex:200px 0 0">
                            <el-input
                                :disabled="!isEdit"
                                v-model="report.actionList[16].initial"
                                class="mwd-input"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="title center auto"
                            style="flex:60px 0 0;height:auto;"
                        >
                            18
                        </div>
                        <div
                            class="title center auto"
                            style="flex:1;height:auto"
                        >
                            车间测试截图Screen shoot of shop test
                        </div>
                    </div>
                    <div class="line">
                        <div
                            class="text left"
                            style="flex:100% 0 0;width:100%;min-height:120px;height:auto"
                        >
                            <div v-html="report.screenShoot" class="rich-text-cls"></div>
                        </div>
                    </div>
                </div>
                <div class="basic-info">
                    <div class="info-container">
                        <div class="info-title">MWD Operator：</div>
                        <el-input
                            :disabled="!isEdit"
                            v-model="report.mwdOperator"
                            class="mwd-input basic-info-input"
                        ></el-input>
                    </div>
                    <div class="info-container">
                        <div class="info-title">Signature & Date:</div>
                        <!-- <el-input
                        :disabled="!isEdit"
                        v-model="report.signatureDate"
                        class="mwd-input basic-info-input"
                    ></el-input> -->
                        <el-date-picker
                            class="mwd-input basic-info-input"
                            v-model="report.signatureDate"
                            type="date"
                            placeholder=""
                            :disabled="!isEdit"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </div>
                </div>
            </div>
            <el-dialog
                :visible.sync="showImgDialog"
                class="big-img-dialog"
                :close-on-click-modal="false"
            >
                <img width="100%" :src="report.screenShoot" alt="" />
            </el-dialog>
        </div>
    </ImageViewer>
</template>

<script>
import { getInitRIPROCEDURES } from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RIPROCEDURES";
import ImageViewer from "@/components/ImageViewer/index.vue";
export default {
    name: "RIPROCEDURES",
    components: { ImageViewer },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        run: String,
        job: String,
        jobId: Number,
        jobStatus: Number,
        globalDisableEdit:Boolean
    },
    computed: {
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    data() {
        return {
            REPORT_TYPE,
            getInitRIPROCEDURES,
            dialogImageUrl: "",
            showImgDialog: false,
            report: {},
            showReport: false,
            isEdit: false,
            showLoading: true,
        };
    },
    created() {
        this.report = getInitRIPROCEDURES();
    },
    methods: {
        onDeleteScreenShoot() {
            this.$confirm("确认要删除该图片吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                this.report.screenShoot = "";
                this.updateReport(notSave);
            });
        },
        async writeFile(file) {
            var reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = (e) => {
                this.report.screenShoot = reader.result;
                this.updateReport(notSave);
            };
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        updateReport() {
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            const baseInfoList = baseInfo.data?.data || [];
            if (baseInfoList.length === 0) {
                this.$message.error("暂无相关数据");
                this.showLoading = false;
                return;
            }
            const reportId = baseInfoList[0].id;
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
    },
};
</script>
<style lang="scss">
.auto {
    height: auto !important;
}
</style>
<style lang="scss" scoped>
.img-container {
    position: relative;
    height: 100%;
    &:hover {
        img {
            filter: brightness(0.2);
        }
        .img-icon-container {
            opacity: 1;
        }
    }
    .img-icon-container {
        width: 100%;
        height: 20px;
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        opacity: 0;
        .img-icon {
            margin-left: 20px;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
    }
}
.basic-info {
    display: flex;
    justify-content: flex-start;
    // line-height: 3.6;
    margin-left: 10px;
    height: 40px;
    .info-container {
        display: flex;
        align-items: center;
        .info-title {
            flex: 120px 0 0;
        }
        .basic-info-input {
            border-bottom: 1px solid #ccd3d9;
            min-width: 200px;
            max-width: 260px;
        }
    }
    div {
        flex: 400px 0 0;
        text-align: right;
    }
}
</style>
