<template>
  <div class="content" v-loading="showLoading">
    <div
      style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
      <el-select
        v-model="selectedRunId"
        @change="onSelectRun"
        placeholder="请选择趟次"
        style="margin-left:10px"
      >
        <el-option
          v-for="(item, key) in currentRunList"
          :key="`run${key}`"
          :label="item.run"
          :value="item.id"
        ></el-option>
      </el-select>
    </div>

    <div v-if="showReport" class="mwd-container" ref="mwd-container">
      <div class="content-container2" style="min-width:1000px;">
        <div class="box-border">
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              趟次RUN #
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                disabled
                v-model="report.run"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              入井时间DATE IN
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-date-picker
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.dateIn"
                type="datetime"
                placeholder=""
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
              ></el-date-picker>
            </div>

            <div class="title center" style="flex:230px 0 0">
              出井时间DATE OUT
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-date-picker
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.dateOut"
                type="datetime"
                placeholder=""
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
              ></el-date-picker>
            </div>
          </div>
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              最高温度℃
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.maxBht"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              入井深度DEPTH IN m
            </div>
            <div class="text center" style="flex:1 0 0">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.depthIn"
              ></InputNumber>
            </div>
            <div class="title center" style="flex:230px 0 0">
              出井深度DEPTH OUT m
            </div>
            <div class="text center" style="flex:1 0 0">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.depthOut"
              ></InputNumber>
            </div>
          </div>
          
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              钻头厂家BIT MFG.
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.bitMfg"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              钻头类型INSERT/TOOTH/PDC
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.insertToothPdc"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              钻头型号BIT MODEL
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.bitModel"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              螺杆厂家MOTOR MFG.
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.motorMfg"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              螺杆尺寸MOTOR SIZE inch
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.motorSize"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              定子/转子M.LOBES/STAGE
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.mlobesStage"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              BPA编号SN
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.bpaSn"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              BPA使用时间Bypass time hrs
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.bpaHrs"
              ></el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              排量FLOW RATE L/S
            </div>
            <div class="text center" style="flex:1 0 0">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.flowRate"
              ></InputNumber>
            </div>
          </div>
          <div class="line">
            <div class="title center" style="flex:230px 0 0">
              泥浆类型MUD TYPE
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="report.mudType"
                placeholder=""
              >
              </el-input>
            </div>
            <div class="title center" style="flex:230px 0 0">
              泥浆密度 g/cm³
            </div>
            <div class="text center" style="flex:1">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.mudDen"
              ></InputNumber>
            </div>
            <div class="title center" style="flex:230px 0 0">
              井眼尺寸mm
            </div>
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.holeSize"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              泥浆数据 MUD DATA
            </div>
          </div>
          <div class="line">
            <div style="flex:1 0 0;height:auto" class="text center">
              <el-input
                spellcheck="false"
                type="textarea"
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.mudData"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center" style="flex:1 0 0">
              工具串TARTAN MWD TOOLS
            </div>
          </div>
          <div class="line">
            <div class="title center" style="flex:1 0 0">
              名称COMPONENT
            </div>
            <!-- <div class="title center" style="flex:1 0 0">
              存货编码
            </div> -->
            <div class="title center" style="flex:1 0 0">
              序列号SERIAL #
            </div>
            <div class="title center" style="flex:1 0 0">
              起始时间START CIRC.hrs
            </div>
            <div class="title center" style="flex:1 0 0">
              总循环时间TOTAL CIRC. hrs
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.toolList"
            :key="`toolList` + key"
          >
            <div class="text center" style="flex:1 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.invName"
              ></el-input>
            </div>
            <div class="text center" style="flex:1 0 0;position:relative">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.serialNumber"
              ></el-input>
            </div>
            <div class="text center" style="flex:1 0 0">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.start"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1 0 0">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="item.total"
              ></InputNumber>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div style="flex:1 0 0" class="title center">
              OOS
            </div>
          </div>
          <div class="line">
            <div style="flex:100px 0 0" class="title center">
              OOS(Y/N)
            </div>
            <div style="flex:300px 0 0" class="title center">
              Type
            </div>
            <div style="flex:1 0 0" class="title center">
              备注
            </div>
          </div>
          <div
            class="line hover-line"
            v-for="(item, key) in report.oosList"
            :key="`oosList${key}`"
          >
            <div class="text center" style="flex:100px 0 0; height: auto;">
              <el-input
                :disabled="!isEdit"
                class="mwd-input"
                v-model="item.oos"
              ></el-input>
            </div>
            <div class="text left" style="flex:300px 0 0; height: auto;line-height: 1.5;padding: 4px">
              <span>{{ item.type }}</span>
            </div>
            
            <div class="text left" style="flex:1 0 0; height: auto;">
              <el-input
                type="textarea"
                :autosize="{ minRows: 1 }"
                :disabled="!isEdit"
                spellcheck="false"
                class="mwd-input left textarea-ofh"
                v-model="item.note"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center" style="flex:4">
              起钻原因REASON TO PULL OUT OF HOLE
            </div>
          </div>
          <div class="line">
            <div class="center title" style="flex:1">钻头BIT</div>
            <div class="center title" style="flex:1">螺杆MOTOR</div>
            <div class="center title" style="flex:1">工具TOOL</div>
            <div class="center title" style="flex:1">其他OTHER</div>
          </div>
          <div class="line">
            <div class="text center" style="flex:1">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.bitReason"
              ></el-input>
            </div>
            <div class="text center" style="flex:1">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.motorReason"
              ></el-input>
            </div>
            <div class="text center" style="flex:1">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.toolReason"
              ></el-input>
            </div>
            <div class="text center" style="flex:1">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.otherReason"
              ></el-input>
            </div>
          </div>
        </div>
        
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center" style="flex:1">
              仪器运转数据MWD OPERATING DATA
            </div>
          </div>
          <div class="line">
            <div class="center title" style="flex:1">
              井下脉冲高度 psi
            </div>
            <div class="center title" style="flex:1">
              井下DOWN HOLE TIME hrs
            </div>
            <div class="center title" style="flex:1">
              循环CIRCULATING hrs
            </div>
            <div class="center title" style="flex:1">
              纯钻DRILLING hrs
            </div>
          </div>
          <div class="line">
            <div class="text center" style="flex:1">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.psi"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.dhtData"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.circulatingData"
              ></InputNumber>
            </div>
            <div class="text center" style="flex:1">
              <InputNumber
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.drillingData"
              ></InputNumber>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInitRSS_RUN_REPORT,
  rssRunSummaryListLength,
  rssRunToolListLength,
} from "./data";
import {
    apiGetDailyReportInfo,
    apiGetDailyReportBaseinfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_RUN";
export default {
  name: "RSS_RUN",
  data() {
    return {
      REPORT_TYPE,
      report: {},
      rssRunSummaryListLength,
      rssRunToolListLength,
      showReport: false,
      timespan: "",
      selectedRunId: null,
      isEdit: false,
      currentRunList: [],
      runAdd: "",
      showAddPopover: false,
      showLoading: true
    };
  },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    selectedRssRun: String || Number,
    job: String,
    rssToolList: Array,
    jobId: Number || String,
    jobStatus: Number,
    globalDisableEdit:Boolean
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  created() {
    this.report = getInitRSS_RUN_REPORT();
  },
  methods: {
    onSelectRun() {
      let map = this.currentRunList.find((item) => {
          return item.id == this.selectedRunId;
      });
      if (map) {
          this.$emit("updateCommonRssRun", map.run);
      }
      this.loadReport();
    },
    async loadReport() {
      this.showLoading = true;
      const baseInfo = await apiGetDailyReportBaseinfo({
          jobId: this.jobId,
          reportType: REPORT_TYPE,
      });
      this.currentRunList = baseInfo.data?.data || [];
      const idx = this.currentRunList.findIndex((item) => {
          return item.run === this.selectedRssRun;
      });
      let reportId;
      if (idx != -1) {
          reportId = this.currentRunList[idx].id;
          this.selectedRunId = reportId;
          this.getReportInfo(reportId);
      } else {
          reportId = this.currentRunList[0]?.id;
          if (reportId) {
              this.selectedRunId = reportId;
              this.getReportInfo(reportId);
          } else {
              this.selectedRunId = null;
              this.hideReport();
          }
      }
    },
    getReportInfo(reportId) {
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        }).then((res) => {
            this.report = dealReport(res.data.data, REPORT_TYPE);
            this.$nextTick(() => {
                this.showReport = true;
            });
        }).finally(()=>{
            this.showLoading = false;
        });
    },
    hideReport() {
      this.showLoading = false;
      this.showReport = false;
    },
  },
};
</script>
<style lang="scss">
.textarea-ofh .el-textarea__inner{
  overflow: hidden;
  min-height: 26px !important;
}
.mwd-input.el-date-editor.el-input__inner {
  width: 280px !important;
}
.mwd-input .el-range-separator {
  width: 20px;
}
</style>
