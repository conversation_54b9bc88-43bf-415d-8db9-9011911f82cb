<template>
  <ImageViewer class="content failure-report" v-loading="showLoading">
    <div
      style="z-index:999;min-width:700px;height: 50px;padding: 4px 0;background:white;border-bottom:1px dashed #eee;overflow: hidden;"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
      <el-select
        style="margin-left:10px"
        v-model="selectedDateId"
        @change="onSelectDate"
      >
        <el-option
            v-for="(item, key) in currentDateList"
            :key="`date${key}`"
            :label="item.date"
            :value="item.id"
        ></el-option>
      </el-select>
      <el-button type="text" v-if="originMapsFile" :title="originMapsFile.name" @click="onClickOriginMapsFile" style="float: right;">
        原始故障报告文件
      </el-button>
    </div>
    <div v-if="showReport" class="mwd-container" style="margin-bottom:20px;">
      <div class="content-container2">
        <div class="box-border">
          <div class="line">
            <div class="title2 center" style="flex:25% 0 0">
              井号 Well Number
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.wellNumber"
              ></el-input>
            </div>
            <div class="title2 center" style="flex:25% 0 0">
              作业号 Job Number
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.jobNumber"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex:25% 0 0">
              失效时间 Incident Date 
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-date-picker
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.incidentDate"
                type="datetime"
                placeholder=""
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
              ></el-date-picker>
            </div>
            <div class="title2 center" style="flex:25% 0 0">
              失效趟次 Run#
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.run"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex:25% 0 0">
              失效地点 Failure Location
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.failureLocation"
              ></el-input>
            </div>
            <div class="title2 center" style="flex:25% 0 0">
              失效报告人 Incident Reporter
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.incidentReporter"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex:25% 0 0">
              工况模式 Activity Mode
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.activityMode"
              ></el-input>
            </div>
            <div class="title2 center" style="flex:25% 0 0">
              失效时循环时间 Circulcation Hrs.at Failure
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.circulateHrsAtFailure"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title2 center" style="flex:25% 0 0">
              失效类型 Failure Type
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.failureType"
              ></el-input>
            </div>
            <div class="title2 center" style="flex:25% 0 0">
              建议失效序列号 Suggested Failure Serial No
            </div>
            <div class="text left" style="flex:25% 0 0">
              <el-input
                class="mwd-input"
                :disabled="!isEdit"
                v-model="report.suggestedFailureSn"
              ></el-input>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center" style="flex:100% 0 0">
              失效描述 Incident Description
            </div>
          </div>
          <div class="line">
            <div class="text left" style="flex:100% 0 0;width:100%;min-height:120px;height:auto">
              <div v-html="report.incidentDescription"></div>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top: none">
          <div class="line">
            <div class="title center" style="flex: 100% 0 0">
              {{ `车间调查结果Shop inspectionc` }}
            </div>
          </div>
          <div class="line">
            <div class="text left" style="flex: 100% 0 0; width: 100%; min-height: 120px; height: auto;">
              <div v-html="report.shopInspectionc" class="rich-text-cls"></div>
            </div>
          </div>
          <div class="file-list" v-if="otherFileList&&otherFileList.length">
            <div class="file-item" v-for="item in otherFileList" :key="item.massId">
              <span :title="item.massFileName" class="file-title">
                {{ item.massFileName }}
              </span>
              <a :href="item.massFilePath">
                <i class="el-icon-download oper-icon"></i>
              </a>
            </div>
          </div>
        </div>
        
        <div class="box-border" style="border-top:none">
          <div class="line">
            <div class="title center" style="flex:100% 0 0">
              改进计划/措施
            </div>
          </div>
          <div class="line">
            <div class="text left" style="flex:100% 0 0;width:100%;min-height:120px;height:auto">
              <div v-html="report.improvementPlanMeasures"></div>
            </div>
          </div>
        </div>
        <div class="box-border" style="border-top: none">
          <div class="line" v-if="report.rssFailedPartsList && report.rssFailedPartsList.length">
            <div class="line" style="flex: 20% 0 0">
              <div
                  class="title center"
                  style="flex: 1; height: auto"
              >
                  失效部件:
              </div>
            </div>
            <div class="line" style="flex: 1">
              <div class="line">
                <div
                  v-for="component in report.rssFailedPartsList"
                  :key="component.deviceType"
                  class="text center"
                  style="flex: 14.28% 0 0; cursor: pointer"
                  :class="{ failed: component.failed }"
                >
                  {{ getDeviceName(component.deviceType) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ImageViewer>
</template>

<script>
import { getInitRSS_FAILURE } from "./data";
import { apiGetDailyReportBaseinfo, apiGetDailyReportInfo, apiGetFailureShopFile } from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_FAILURE";
import ImageViewer from "@/components/ImageViewer/index.vue";
import { apiPreviewMassFile } from "@/api/file";
import { rssFailedPartsList } from "@/utils/constant";
export default {
  name: "RSS_FAILURE",
  components: { ImageViewer },
  data() {
    return {
      REPORT_TYPE,
      getInitRSS_FAILURE,
      report: {},
      showReport: false,
      currentSelectedDate: null,
      currentDateList: [],
      isEdit: false,
      fileList: [],
      showLoading: false,
      selectedDateId: null,
    };
  },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    selectedDate: String,
    run: String,
    job: String,
    jobId: Number,
    kitBoxId: Number,
    runList: Array,
    toolList: Array,
    jobStatus: Number,
    currentWell: String,
    currentJob: String,
    globalDisableEdit: Boolean,
  },
  computed: {
      // 库房确认的失效报告不能编辑！
      enableEdit() {
          return this.report.isConfirm !== 1;
      },
      isJobCompleted() {
          return this.jobStatus === 1;
      },
      originMapsFile(){
          return this.fileList.find(item => item.isUnique === 'Y');
      },
      otherFileList() {
          return this.fileList.filter(item => item.isUnique !== 'Y');
      },
  },
  methods: {
    getDeviceName(deviceType){
      return rssFailedPartsList.find(item => item.deviceType === deviceType)?.name || deviceType;
    },
    async loadReport() {
      this.showLoading = true;
      const baseInfo = await apiGetDailyReportBaseinfo({
          jobId: this.jobId,
          reportType: REPORT_TYPE,
      });
      this.currentDateList = baseInfo.data?.data || [];
      this.currentDateList.reverse();
      const idx = this.currentDateList.findIndex((item) => {
          return item.id === this.selectedDateId;
      });
      let reportId;
      if (idx != -1) {
        this.selectedDate = this.currentDateList[idx].date;
        this.getReportInfo(this.selectedDateId);
      } else {
        reportId = this.currentDateList[0]?.id;
        if (reportId) {
          this.selectedDate = this.currentDateList[0].date;
          this.selectedDateId = reportId;
          this.getReportInfo(reportId);
        } else {
          this.selectedDateId = null;
          this.hideReport();
        }
      }
    },
    onSelectDate() {
      this.loadReport();
    },
    getReportInfo(reportId) {
      apiGetDailyReportInfo({
        reportType: REPORT_TYPE,
        reportId,
      }).then(async (res) => {
        this.report = dealReport(res.data.data, REPORT_TYPE);
        this.getFailureShopFileList();
        this.$nextTick(() => {
          this.showReport = true;
        });
      }).finally(() => {
        this.showLoading = false;
      });
    },
    hideReport() {
      this.showLoading = false;
      this.showReport = false;
    },
    getFailureShopFileList() {
      apiGetFailureShopFile(
        { jobId: this.jobId, date: this.selectedDate },
        { current:1, size: 99999 }
      ) .then(res=>{
        this.fileList = res.data.data.records || [];
      })
    },
    onClickOriginMapsFile(){
      apiPreviewMassFile({massId: this.originMapsFile.massId}).then(res=>{
        const fileUrl = res.data.data.previewPath;
        window.open(fileUrl, '_blank');
      })
    }
  },
};
</script>
<style lang="scss" scoped>
.img-container {
  position: relative;
  height: 100%;
  &:hover {
    img {
      filter: brightness(0.2);
    }
    .img-icon-container {
      opacity: 1;
    }
  }
  .img-icon-container {
    width: 100%;
    height: 20px;
    position: absolute;
    top: 50%;
    display: flex;
    justify-content: center;
    opacity: 0;
    .img-icon {
      margin-left: 20px;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
  }
}
.failed {
  background-color: #f56c6c !important;
}
.problem {
  background-color: #f56c6c !important;
}

.info-title {
  //   flex: 150px 0 0;
  color: #3c4d73;
}
.basic-info-input {
  border-bottom: 1px solid #ccd3d9;
  //   max-width: 200px;
  flex: 50% 0 0;
}
</style>

<style lang="scss" scoped>
.file-list{
    margin-left: 4px;
    margin-top: 4px;
    .file-item{
        background-color:#ecf5ff;
        height: 26px;
        line-height: 26px;
        padding-left: 4px;
        color:#409eff;
        width: 350px;
        margin-bottom: 4px;
        .file-title{
            width: 300px; 
            overflow: hidden;
            white-space: nowrap; 
            text-overflow: ellipsis;
            display: inline-block;
        }
        .oper-icon{
            display: none;
            vertical-align:super;
            margin-right:6px;
        }
        &:hover{
           .oper-icon{
                display: inline-block;
           } 
        }
    }
}
</style>