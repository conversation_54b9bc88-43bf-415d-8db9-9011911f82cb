<template>
    <div
        style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
        <template>
            <span v-if="selectedReportType === 'COVER'" class="report-title">
                COVER
            </span>
            <span
                v-if="selectedReportType === 'TOOLSONSITE'"
                class="report-title"
            >
                TOOLSONSITE
            </span>
            <span v-if="selectedReportType === 'SURVEYS'" class="report-title">
                SURVEYS
            </span>
            <span v-if="selectedReportType === 'FAILURE'" class="report-title">
                FAILURE
            </span>
            <span v-if="selectedReportType === 'RUN'" class="report-title">
                RUN
            </span>
            <span v-if="selectedReportType === 'BHA'" class="report-title">
                底部钻具组合BHA
            </span>
            <span
                v-if="selectedReportType === 'ROPROCEDURES'"
                class="report-title"
            >
                ROPROCEDURES
            </span>
            <span
                v-if="selectedReportType === 'RIPROCEDURES'"
                class="report-title"
            >
                RIPROCEDURES
            </span>

            <span v-if="selectedReportType === 'MWD'" class="report-title">
                仪器日报
            </span>
            <span v-if="selectedReportType === 'PROJECT'" class="report-title">
                工程日报
            </span>
            <span v-if="selectedReportType === 'TOOLS'" class="report-title">
                工具日报
            </span>
        </template>

        <template v-if="dailyReportTypeList.includes(selectedReportType)">
            <el-select
                style="margin-left:10px"
                v-model="selectedDateId"
                @change="onSelectDate"
            >
                <el-option
                    v-for="(item, key) in currentDateList"
                    :key="`date${key}`"
                    :label="item.date"
                    :value="item.id"
                ></el-option>
            </el-select>
            <el-popover
                v-model="showAddPopover"
                trigger="click"
                placement="right-start"
            >
                <div class="add-popover">
                    <div class="add-popover-title">
                        新增
                    </div>

                    <div class="calender" style="width:400px">
                        <el-calendar v-model="orginDateAdd"> </el-calendar>
                    </div>

                    <div class="add-popover-btn">
                        <el-button
                            @click="onConfirmAddDate"
                            size="small"
                            type="primary"
                            >确 定</el-button
                        >
                        <el-button @click="showAddPopover = false" size="small"
                            >取 消</el-button
                        >
                    </div>
                </div>

                <el-button
                    slot="reference"
                    style="margin-left:10px;"
                    type="primary"
                    size="small"
                    v-if="jobStatus != 1"
                    ><span style="font-size:14px"
                        ><i class="el-icon-plus"></i> 新增日报</span
                    ></el-button
                >
            </el-popover>
            <el-button
                @click="onClickDeleteDailyReport"
                type="danger"
                icon="el-icon-delete"
                v-if="jobStatus != 1"
                style="margin-left:10px"
                >删除日报</el-button
            >
        </template>
        <template v-if="runReportTypeList.includes(selectedReportType)">
            <!-- <span>趟次列表：</span> -->
            <el-select
                v-model="selectedRunId"
                @change="onSelectRun"
                placeholder="请选择趟次"
                style="margin-left:10px"
            >
                <el-option
                    v-for="(item, key) in currentRunList"
                    :key="`run${key}`"
                    :label="item.run"
                    :value="item.id"
                ></el-option>
            </el-select>
            <el-popover
                v-if="selectedReportType === 'RUN'"
                v-model="showAddPopover"
                trigger="click"
                placement="right-start"
            >
                <div class="add-popover">
                    <div class="add-popover-title">
                        新增
                    </div>
                    <div class="add-popover-form">
                        <div class="form-name">趟次：</div>
                        <el-input
                            clearable
                            placeholder="请输入趟次"
                            v-model.trim="runAdd"
                        ></el-input>
                    </div>
                    <div class="add-popover-btn">
                        <el-button
                            @click="onConfirmAddRun"
                            size="small"
                            type="primary"
                        >
                            确 定
                        </el-button>
                        <el-button @click="showAddPopover = false" size="small">
                            取 消
                        </el-button>
                    </div>
                </div>

                <el-button
                    v-if="selectedReportType === 'RUN' && jobStatus != 1"
                    slot="reference"
                    style="margin-left:10px;"
                    type="primary"
                    size="small"
                >
                    <span style="font-size:14px">
                        <i class="el-icon-plus"></i>
                        新增趟次
                    </span>
                </el-button>
            </el-popover>
            <el-button
                v-if="selectedReportType === 'RUN' && jobStatus != 1"
                @click="onClickDeleteRunReport"
                type="danger"
                icon="el-icon-delete"
                style="margin-left:10px"
                >删除趟次</el-button
            >
        </template>
        <!-- TODO: 新增失效报告 -->
        <template v-if="selectedReportType === 'FAILURE'">
            <el-select
                style="margin-left:10px"
                v-model="selectedDateId"
                @change="onSelectDate"
            >
                <el-option
                    v-for="(item, key) in currentDateList"
                    :key="`date${key}`"
                    :label="item.date"
                    :value="item.id"
                ></el-option>
            </el-select>
            <el-popover
                v-model="showAddPopover"
                trigger="click"
                placement="right-start"
            >
                <div class="add-popover">
                    <div class="add-popover-title">
                        新增
                    </div>

                    <div class="calender" style="width:400px">
                        <el-calendar v-model="orginDateAdd"> </el-calendar>
                    </div>

                    <div class="add-popover-btn">
                        <el-button
                            @click="onConfirmAddFailure"
                            size="small"
                            type="primary"
                            >确 定</el-button
                        >
                        <el-button @click="showAddPopover = false" size="small"
                            >取 消</el-button
                        >
                    </div>
                </div>

                <el-button
                    slot="reference"
                    style="margin-left:10px;"
                    type="primary"
                    size="small"
                    v-if="jobStatus != 1"
                    ><span style="font-size:14px"
                        ><i class="el-icon-plus"></i> 新增失效报告</span
                    ></el-button
                >
            </el-popover>
            <el-button
                @click="onClickDeleteFailure"
                type="danger"
                v-if="jobStatus != 1"
                icon="el-icon-delete"
                style="margin-left:10px"
                >删除报告</el-button
            >
        </template>
        <template>
            <el-button
                style="margin-left:10px"
                icon="el-icon-refresh"
                type="warning"
                @click="onRefreshData()"
                v-if="
                    (selectedReportType === 'COVER' ||
                        selectedReportType === 'RUN' ||
                        selectedReportType === 'BHA') &&
                        jobStatus != 1
                "
            >
                手动汇总
            </el-button>
            <el-button
                v-if="!isEdit && jobStatus != 1"
                type="primary"
                @click="onEditReport"
                style="margin-left:10px"
                >编辑</el-button
            >
            <el-button
                v-if="isEdit"
                style="margin-left:10px"
                type="primary"
                @click="onSaveReport"
                >保存</el-button
            >
            <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                >取消</el-button
            >
        </template>
    </div>
</template>
