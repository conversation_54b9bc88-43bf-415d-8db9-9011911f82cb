// let tmp = [];
// for (let i in 20) {
//     tmp.push({
//         date: '', startTime: '', endTime: '', startDepth: '', endDepth: '', segLength: '', drillDuration: '',
//         drillType: '', drillPressure: '', groundDrillRate: '', md: '', deviation: '', direction: '',
//         toolFace: '', note: ''
//     })
// }
function getToolListTmp(){
    return {
        description: null,
        inTheHule: false,
        invClassCode: null,
        invClassName: null,
        invCode: null,
        invName: null,
        partNumber: null,
        run: null,
        serialNumber: null,
        start: null,
        stockId: null,
        today: null,
        total: null,
    }
}

export const DELETE_STATUS = 0;
export const ADD_STATUS = 1;
export const UPDATE_STATUS = 2;

// 工程日报
export const wellActivityLength = 20;
export const getWellActivityTmp = function () {
    return {
        date: '', startTime: '', endTime: '', startDepth: '', endDepth: '', segLength: '', drillDuration: '',
        drillType: '', drillPressure: '', groundDrillRate: '', md: '', deviation: '', direction: '',
        toolFace: '', note: ''
    }
}
let wellActivity = new Array(wellActivityLength).fill(null).map(() => {
    return getWellActivityTmp()
})
export function getInitWell() {
    return {
        date: '',
        weather: '',
        reporter: '',
        operatorCompany: '',
        wellNumber: '',
        drillingMachine: '',
        depth: '',
        casingShoe: '',
        casingSize: '',
        boreholeSize: '',
        designSize: '',
        tripDrillNum: '',
        mudSystem: '',
        density: '',
        viscosity: '',
        oilContent: '',
        solidContent: '',
        sandContent: '',
        temp: '',
        drillMan: '',
        drillNozzle: '',
        drillIntoNum: '',
        drillTime: '',
        screwMan: '',
        screwSize: '',
        screwIntoNum: '',
        screwTime: '',
        knMan: '',
        knSize: '',
        knIntoNum: '',
        knTime: '',
        wpMan: '',
        wpSize: '',
        wpIntoNum: '',
        wpTime: '',
        drillPos: '',
        drillDepth: '',
        remainFootage: '',
        dailyFootage: '',
        odDrillTime: '',
        odCircleTime: '',
        cumDrillTime: '',
        cumCircleTime: '',
        sildeDrillTime: '',
        rcDrillTime: '',
        zeroLength: '',
        ss: '',
        cc: '',
        pp: '',
        friction: '',
        torque: '',
        manager: '',
        deLeader: '',
        de: '',
        ieLeader: '',
        ie: '',
        perArrTime: '',
        equArrTime: '',
        serPro: '',
        bha: '',
        intoTurnTime: '',
        outerTurnTime: '',
        tripDrillTime: '',
        tripCircleTime: '',
        statistic: [
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '定向'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '复合'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '起下钻'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '修理'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '划眼测斜接立柱'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '处理复杂'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '待命'
            },
            {
                "drillRate": '',
                "formation": '',
                "rockCore": '',
                "segLength": '',
                "time": '',
                "type": '其他'
            }
        ],
        activity: wellActivity
    }
}
export const WELL = getInitWell()

// PROJECT
export const projectActivityLength = 1;
export const getProjectActivityTmp = function () {
    return {
        date: '', startTime: '', endTime: '', startDepth: '', endDepth: '', segLength: '', drillDuration: '',
        drillType: '', drillPressure: '', groundDrillRate: '', md: '', deviation: '', direction: '',
        toolFace: '', note: ''
    }
}
let projectActivity = new Array(projectActivityLength).fill(null).map(() => {
    return getProjectActivityTmp()
})
export function getInitPROJECT() {
    return {
        reportType:'PROJECT',
        date: '',
        weather: '',
        reporter: '',
        operatorCompany: '',
        wellNumber: '',
        drillingMachine: '',
        depth: '',
        casingShoe: '',
        casingSize: '',
        boreholeSize: '',
        designSize: '',
        tripDrillNum: '',
        mudSystem: '',
        density: '',
        viscosity: '',
        oilContent: '',
        solidContent: '',
        sandContent: '',
        temp: '',
        drillMan: '',
        drillNozzle: '',
        drillIntoNum: '',
        drillTime: '',
        screwMan: '',
        screwSize: '',
        screwIntoNum: '',
        screwTime: '',
        knMan: '',
        knSize: '',
        knIntoNum: '',
        knTime: '',
        wpMan: '',
        wpSize: '',
        wpIntoNum: '',
        wpTime: '',
        drillPos: '',
        drillDepth: '',
        remainFootage: '',
        dailyFootage: '',
        odDrillTime: '',
        odCircleTime: '',
        cumDrillTime: '',
        cumCircleTime: '',
        sildeDrillTime: '',
        rcDrillTime: '',
        zeroLength: '',
        ss: '',
        cc: '',
        pp: '',
        friction: '',
        torque: '',
        manager: '',
        deLeader: '',
        de: '',
        ieLeader: '',
        ie: '',
        perArrTime: '',
        equArrTime: '',
        serPro: '',
        bha: '',
        intoTurnTime: '',
        outerTurnTime: '',
        tripDrillTime: '',
        tripCircleTime: '',
        statisticList: null,
        activityList: projectActivity
    }
}

// 仪器日报 
export const mwdToolLength = 1;
export const getMwdToolTmp = getToolListTmp
let mwdSummary = new Array(mwdToolLength).fill(null).map(() => {
    return getMwdToolTmp()
})
export const mwdActivityLength = 2;
export const getMwdActivityTmp = function () {
    return {
        startTime: '', endTime: '', depthFrom: '', depthTo: '', activity: '', spp: '', rpm: '', ss: '', torque: '', fr: '', note: ''
    }
}
let mwdActivity = new Array(mwdActivityLength).fill(null).map(() => {
    return getMwdActivityTmp()
})
export function getInitMWD() {
    return {
        reportType:'MWD',
        date: '',
        operator: '',
        kit: '',
        startDate: '',
        constructionDays: '',
        superintendent: '',
        dd1: '',
        dd2: '',
        mwd1: '',
        mwd2: '',
        run: '',
        startDepth: '',
        endDepth: '',
        mudType: '',
        mudDen: '',
        viscosity: '',
        solidContent: '',
        sandContent: '',
        oilContent: '',
        ph: '',
        pv: '',
        vp: '',
        fl: '',
        temp: '',
        imo: '',
        dao: '',
        magEc: '',
        gammaDist: '',
        atBitDist: '',
        orfice: '',
        poppit: '',
        plsw: '',
        nmag: '',
        ndip: '',
        summaryList: mwdSummary,
        activityList: mwdActivity,
        workSummaryList: [{time:'07:00',content:''},{time:'11:00',content:''},{time:'15:00',content:''},{time:'19:00',content:''},{time:'21:00',content:''}]
    }
}
export const MWD = getInitMWD()

// RSS_DAILY 
export const rssDailyToolLength = 1;
export const getRssDailyToolTmp = getToolListTmp
let rssDailySummary = new Array(rssDailyToolLength).fill(null).map(() => {
    return getRssDailyToolTmp()
})
export const rssDailyActivityLength = 2;
export const getRssDailyActivityTmp = function () {
    return {
        startTime: '', endTime: '', depthFrom: '', depthTo: '', activity: '', spp: '', rpm: '', ss: '', torque: '', fr: '', note: ''
    }
}
let rssDailyActivity = new Array(rssDailyActivityLength).fill(null).map(() => {
    return getRssDailyActivityTmp()
})
export const rssOosLength = 1;
export const getRssOosTmp = () => ({ type:null, note: null, oos: null,});
// let rssOos = new Array(rssOosLength).fill(null).map(() => {
//     return getRssOosTmp()
// })
export const rssOos = [
    { type: "横向振动Lateral Vibration", oos: "", note: "" },
    { type: "轴向震动Axial Vibration", oos: "", note: "" },
    { type: "切向震动Tangential Vibration", oos: "", note: "" },
    { type: "粘滑Stick Slip", oos: "", note: "" },
    { type: "Sand Content", oos: "", note: "" },
    { type: "氯离子含量Cl", oos: "", note: "" },
    { type: `其他Other:碎片Debris (Non LCM)/震击Jarring/堵漏材料LCM/排量Flow rate/泡酸Pumping acid and etc`, oos: "", note: "" },
]
export function getInitRSS_DAILY_REPORT() {
    return {
        reportType:'RSS_DAILY',
        date: '',
        operator: '',
        kit: '',
        startDate: '',
        constructionDays: '',
        superintendent: '',
        dd1: '',
        dd2: '',
        mwd1: '',
        mwd2: '',
        run: '',
        startDepth: '',
        endDepth: '',
        mudType: '',
        mudDen: '',
        viscosity: '',
        solidContent: '',
        sandContent: '',
        oilContent: '',
        ph: '',
        pv: '',
        vp: '',
        fl: '',
        temp: '',
        imo: '',
        dao: '',
        magEc: '',
        gammaDist: '',
        atBitDist: '',
        orfice: '',
        poppit: '',
        plsw: '',
        nmag: '',
        ndip: '',
        oosList: [{type: null, oos: null, note: null}],
        summaryList: rssDailySummary,
        activityList: rssDailyActivity,
        workSummaryList: [{time:'07:00',content:''},{time:'11:00',content:''},{time:'15:00',content:''},{time:'19:00',content:''},{time:'23:00',content:''}]
    }
}
// export function getInitMWD() {
//     return {
//         summaryList: mwdSummary,
//         activityList: mwdActivity,
//     }
// }
export const RSS_DAILY = getInitRSS_DAILY_REPORT()
// COVER
export function getCoverHoleInfoTmp(){
    return { holeSize:'',from:'',to:''}
}
export const coverHoleInfoLen = 3;
const coverHoleInfoList = new Array(coverHoleInfoLen).fill(null).map(() => {
    return getCoverHoleInfoTmp()
})
export const coverPersonnelList = [{type:'定向井工程师/DD',name:''},{type:'定向井工程师/DD',name:''},{type:'仪器工程师/MWD',name:''},{type:'仪器工程师/MWD',name:''},{type:"负责人Co. Man",name:''}]
export function getInitCOVER() {
    return {
        reportType:'COVER', field: '', client: '', wellNumber: '', location: '', drillContractor: '', mudPump: '', drillPipe: '', province: '', dateIn: '', dateOut: '', totalMwdRuns: '', gamma: '', mwdFailures: '', buildHold: '', horizontal: '', sidetrack: '', reentry: '', holdVert: '', electrical: '', circulating: '', drilling: '', comment: '', stype: '', personnelList: coverPersonnelList,holeInfoList:coverHoleInfoList
    }
}

// RSS_COVER
export function getRssCoverHoleInfoTmp(){
    return { holeSize:'',from:'',to:''}
}
export const rssCoverHoleInfoLen = 3;
const rssCoverHoleInfoList = new Array(rssCoverHoleInfoLen).fill(null).map(() => {
    return getRssCoverHoleInfoTmp()
})
export const rssCoverPersonnelList = [{type:'定向井工程师/DD',name:''},{type:'定向井工程师/DD',name:''},{type:'仪器工程师/MWD',name:''},{type:'仪器工程师/MWD',name:''},{type:"负责人Co. Man",name:''}]
export function getInitRSS_COVER_REPORT() {
    return {
        reportType:'RSS_COVER', client: '', wellNumber: '', location: '', drillContractor: '', mudPump: '', drillPipe: '', province: '', dateIn: '', dateOut: '', totalMwdRuns: '', gamma: '', mwdFailures: '', buildHold: '', horizontal: '', sidetrack: '', reentry: '', holdVert: '', electrical: '', circulating: '', drilling: '', comment: '', stype: '', personnelList: rssCoverPersonnelList,holeInfoList:rssCoverHoleInfoList
    }
}
// FAILURE
export const getFailureToolTmp = getToolListTmp;
export const failureToolLen = 1;
const failureToolList = new Array(failureToolLen).fill(null).map(() => {
    return getFailureToolTmp()
})
export function getInitFAILURE(){
    return {
        reportType:'FAILURE',kitId:'',operator:'',client:'',wellNumber:'',location:'',field:'',drillingRig:'',inHoleDate:'',failureDate:'',reportDate:'',failureSymptoms:'',actionTaken:'',surfaceInspection:'',shopInspectionc:'',surfaceFailed:'',pulserFailed:'',dmFailed:'',bat1Failed:'',bat2Failed:'',rfdFailed:'',gammaFailed:'',downholeFailed:'',transducerFailed:'',cablesFailed:'',computersFailed:'',softwareFailed:'',rfdBoxFailed:'',otherFailed:'',totalDowntime:'',findProblem:'',flowRate:'',orfice:'',poppet:'',pulsewth:'',runHours:'',measDepth:'',inc:'',azm:'',magdec:'',temp:'',batteryVolts:'',toolList:failureToolList,
    }
}
// RS_FAILURE
export const getInitRSS_FAILURE = () => {
    return {
        reportType: 'RSS_FAILURE',
        wellNumber: null,
        jobNumber: null,
        run: null,
        date: null,
        incidentDate: null,
        incidenteDescription: null,
        incidentReporter: null,
        failureLocation: null,
        activityMode: null,
        circulateHrsAtFailure: null,
        failureType: null,
        suggestedFailureSn: null,
        mapsFile: null
    }
}
// RSS_BHA
export function getRssBhaItemTmp(){
    return {itemNo:'',description:'',vendor:'',serialNum:'',outerDiameter:'',innerDiameter:'',gauge:'',pinConnection:'',boxConnection:'',len:'',totalLen:'',}
}
export const rssBhaItemLength =1
let rssBhaItemList = new Array(rssBhaItemLength).fill(null).map(() => {
    return getRssBhaItemTmp()
})
export function getInitRSS_BHA_REPORT(){
    return {
        reportType:'RSS_BHA',run:'',company:'',contractor:'',rigNo:'',startDate:'',wellNumber:'',location:'',depthIn:'',depthOut:'',bha:'',purpose:'',surveySensor:'',gammaSensor:'',bhaResults:'',reasonForPooh:'',directionalDrillers:'',itemList:rssBhaItemList,
    }
}

// BHA
export function getBhaItemTmp(){
    return {itemNo:'',description:'',vendor:'',serialNum:'',outerDiameter:'',innerDiameter:'',gauge:'',pinConnection:'',boxConnection:'',len:'',totalLen:'',}
}
export const bhaItemLength =1
let bhaItemList = new Array(bhaItemLength).fill(null).map(() => {
    return getBhaItemTmp()
})
export function getInitBHA(){
    return {
        reportType:'BHA',run:'',company:'',contractor:'',rigNo:'',startDate:'',wellNumber:'',location:'',depthIn:'',depthOut:'',bha:'',purpose:'',surveySensor:'',gammaSensor:'',bhaResults:'',reasonForPooh:'',directionalDrillers:'',itemList:bhaItemList,
    }
}

// RSS_RIPROCEDURES
export function getRssRIActionListTmp(){
    return { date:'',initial:''}
}
export const rssRiActionListLength = 12
const rssRiActionList = new Array(rssRiActionListLength).fill(null).map(() => {
    return getRssRIActionListTmp()
})
export function getInitRSS_RIP_REPORT(){
    return {
        reportType:'RSS_RIPROCEDURES',operator:'',mwdOperator:'',signatureDate:'',screenShoot:'',actionList:rssRiActionList,
    }
}

// RIPROCEDURES
export function getRIActionListTmp(){
    return { date:'',initial:''}
}
export const riActionListLength = 17
const riActionList = new Array(riActionListLength).fill(null).map(() => {
    return getRIActionListTmp()
})
export function getInitRIPROCEDURES(){
    return {
        reportType:'RIPROCEDURES',operator:'',mwdOperator:'',signatureDate:'',screenShoot:'',actionList:riActionList,
    }
}

// ROPROCEDURES
export function getROActionListTmp(){
    return { date:'',initial:''}
}
export const roActionListLength = 11
const roActionList = new Array(roActionListLength).fill(null).map(() => {
    return getROActionListTmp()
})
export function getInitROPROCEDURES(){
    return {
        reportType:'ROPROCEDURES',operator:'',mwdOperator:'',signatureDate:'',screenShoot:'',actionList:roActionList,
    }
}
// RSS_ROPROCEDURES
export function getRssROActionListTmp(){
    return { date:'',initial:''}
}
export const rssRopActionListLength = 9
const rssRopActionList = new Array(rssRopActionListLength).fill(null).map(() => {
    return getRssROActionListTmp()
})
export function getInitRSS_ROP_REPORT(){
    return {
        reportType:'RSS_ROPROCEDURES',operator:'',mwdOperator:'',signatureDate:'',screenShoot:'',actionList:rssRopActionList,
    }
}
// RUN
export function getRunSummaryListTmp(){
    return { date:'',time:'',depth:'',description:'',}
}
export const runSummaryListLength = 2
const runSummaryList = new Array(runSummaryListLength).fill(null).map(() => {
    return getRunSummaryListTmp()
})
export const getRunToolListTmp = getToolListTmp;
export const runToolListLength = 1
const runToolList = new Array(runToolListLength).fill(null).map(() => {
    return getRunToolListTmp()
})

// RSS_RUN
export function getRssRunSummaryListTmp(){
    return { date:'',time:'',depth:'',description:'',}
}
export const rssRunSummaryListLength = 2
const rssRunSummaryList = new Array(rssRunSummaryListLength).fill(null).map(() => {
    return getRssRunSummaryListTmp()
})
export const getRssRunToolListTmp = getToolListTmp;
export const rssRunToolListLength = 1
const rssRunToolList = new Array(rssRunToolListLength).fill(null).map(() => {
    return getRssRunToolListTmp()
})

export function getInitRSS_RUN_REPORT(){
    return {
        reportType:'RSS_RUN',run:'',holeSize:'',flowRate:'',mudType:'',mudWeight:'',mudViscosity:'',sandSolids:'',dateIn:'',dateOut:'',depthIn:'',depthOut:'',pulseWidth:'',orificeId:'',popTipOd:'',mwdMonelId:'',mwdMonelOd:'',driftAzimuth:'',magDec:'',distBitGamma:'',distBitElec:'',imoDeg:'',atBitDist:'',
        bitMfg:'',insertToothPdc:'',bitModel:'',motorMfg:'',motorSize:'',deg:'',bitReason:'',motorReason:'',mwdReason:'',otherReason:'',surfData:'',btmData:'',electricalData:'',circulatingData:'',drillingData:'',mlobesStage:'',summaryList:rssRunSummaryList,toolList:rssRunToolList,
        oosList: [{type: null, oos: null, note: null}],
    }
}
export function getInitRUN(){
    return {
        reportType:'RUN',run:'',holeSize:'',flowRate:'',mudType:'',mudWeight:'',mudViscosity:'',sandSolids:'',dateIn:'',dateOut:'',depthIn:'',depthOut:'',pulseWidth:'',orificeId:'',popTipOd:'',mwdMonelId:'',mwdMonelOd:'',driftAzimuth:'',magDec:'',distBitGamma:'',distBitElec:'',imoDeg:'',atBitDist:'',
        bitMfg:'',insertToothPdc:'',bitModel:'',motorMfg:'',motorSize:'',deg:'',bitReason:'',motorReason:'',mwdReason:'',otherReason:'',surfData:'',btmData:'',electricalData:'',circulatingData:'',drillingData:'',mlobesStage:'',summaryList:runSummaryList,toolList:runToolList,
    }
}

// SURVEYS
export function getSurveysActivityTmp(){
    return {
        reportType:'SURVEYS',date:'',bitDepth:'',surveyDepth:'',inc:'',azi:'',dipa:"",magf:"",grav:"",batv:"",temp:"",lithology:"",comments:"",  }
}
export const toolsSurveysActivityLength = 12
const surveysActivityList = new Array(toolsSurveysActivityLength).fill(null).map(() => {
    return getSurveysActivityTmp()
})
export function getInitSURVEYS(){
    return {
        company:'',wellNumber:'',magDec:'',bitToSensor:'',bitType:'',activityList:surveysActivityList,
    }
}

// OFFSETS
export const offsetLen = 20;

// TOOLSONSITE
export const getToolsOnsiteToolTmp = getToolListTmp
export const toolsOnsiteToolLength = 1
const toolsOnsiteToolList = new Array(toolsOnsiteToolLength).fill(null).map(() => {
    return getToolsOnsiteToolTmp()
})
export function getInitTOOLSONSITE(){
    return {
        reportType:'TOOLSONSITE',toolList:toolsOnsiteToolList 
    }
}
// RSS_TOOLSONSITE
export const getRssToolsOnsiteToolTmp = getToolListTmp
export const rssToolsOnsiteToolLength = 1
const rssToolsOnsiteToolList = new Array(rssToolsOnsiteToolLength).fill(null).map(() => {
    return getRssToolsOnsiteToolTmp()
})
export function getInitRSS_TOOLSONSITE(){
    return {
        reportType:'RSS_TOOLSONSITE',toolList:rssToolsOnsiteToolList 
    }
}
// PROJECTTOOLSONSITE
export const getProjectToolsOnSiteToolTmp = getToolListTmp
export const projectToolsOnSiteToolLength = 1
const projectToolsOnSiteToolList = new Array(projectToolsOnSiteToolLength).fill(null).map(() => {
    return getProjectToolsOnSiteToolTmp()
})
export function getInitPROJECTTOOLSONSITE(){
    return {
        reportType:'PROJECTTOOLSONSITE',toolList:projectToolsOnSiteToolList 
    }
}
// TOOLS
export function getToolsBhaTmp(){
    return {
        name:'',serialNum:'',outerDiameter:'',length:'',cumLength:'',
    }
}
export const toolsBhaListLength = 2
const toolsBhaList = new Array(toolsBhaListLength).fill(null).map(() => {
    return getToolsBhaTmp()
})

export function getToolsDeviationTmp(){
    return {
        md:'',wellAngle:'',azimuth:'',doglegDegree:'',movement:'',
    }
}
export const toolsDeviationLength = 2
const toolsDeviationList = new Array(toolsDeviationLength).fill(null).map(() => {
    return getToolsDeviationTmp()
})

export function getToolsDeviceTmp(){
    return {
        position:'',nameSerialNum:'',downTime:'',
    }
}
export const toolsDeviceLength = 2
const toolsDeviceList = new Array(toolsDeviceLength).fill(null).map(() => {
    return getToolsDeviceTmp()
})

export function getToolsDrillTmp(){
    return {
        wob:'',speed:'',displacement:'',pumpPressure:'',
    }
}
export const toolsDrillLength = 2
const toolsDrillList = new Array(toolsDrillLength).fill(null).map(() => {
    return getToolsDrillTmp()
})

export function getToolsActivityTmp(){
    return {
        startEndTime:'',content:'',
    }
}
export const toolsActivityLength = 2
const toolsActivityList = new Array(toolsActivityLength).fill(null).map(() => {
    return getToolsActivityTmp()
})
export function getInitTOOLS(){
    return {
        reportType:'TOOLS',date:'',reportDate:'',wellNumber:'',client:'',drillingCrew:'',workPaDate:'',workDaDate:'',workCumstTime:'',workCurstTime:'',workCumdwTime:'',workCurdwTime:'',workEntryDepth:'',workCumFootage:'',bitType:'',bitWaterEye:'',bitMan:'',bitIadc:'',bitTfa:'',runEntryDate:'',runCurDepth:'',runDrillDepth:'',runCumstTime:'',runCurstTime:'',runCumclTime:'',runCurclTime:'',runCumdrTime:'',runCurdrTime:'',runAvgRop:'',runCumFootage:'',runCurFootage:'',daSensorDist:'',gammaDist:'',nearBitDist:'',mudDen:'',mudAlk:'',mudPlsVis:'',mudYeVal:'',mudFunVis:'',mudSsFor:'',mudSandCon:'',mudSolidPha:'',mudWaterLoss:'',mudCake:'',mudDiTemp:'',mudSys:'',mwdSpeed:'',mwdNoiseRatio:'',mwdSptInt:'',activityList:toolsActivityList,drillList:toolsDrillList,deviceList:toolsDeviceList,deviationList:toolsDeviationList,bhaList:toolsBhaList
    }
}