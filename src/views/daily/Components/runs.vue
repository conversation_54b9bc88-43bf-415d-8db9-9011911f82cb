<template>
    <div class="content" v-loading="showLoading">
        <div
            style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
        >
            <span
                class="report-title"
                style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <el-select
                v-model="selectedRunId"
                @change="onSelectRun"
                placeholder="请选择趟次"
                style="margin-left:10px"
            >
                <el-option
                    v-for="(item, key) in currentRunList"
                    :key="`run${key}`"
                    :label="item.run"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted&&!globalDisableEdit">
                <el-popover
                    v-model="showAddPopover"
                    trigger="click"
                    placement="right-start"
                >
                    <div class="add-popover">
                        <div class="add-popover-title">
                            新增
                        </div>
                        <div class="add-popover-form">
                            <div class="form-name">趟次：</div>
                            <el-input
                                clearable
                                placeholder="请输入趟次"
                                v-model.trim="runAdd"
                            ></el-input>
                        </div>
                        <div class="add-popover-btn">
                            <el-button
                                @click="onConfirmAddRun"
                                size="small"
                                type="primary"
                            >
                                确 定
                            </el-button>
                            <el-button
                                @click="showAddPopover = false"
                                size="small"
                            >
                                取 消
                            </el-button>
                        </div>
                    </div>

                    <el-button
                        slot="reference"
                        style="margin-left:10px;"
                        type="primary"
                        size="small"
                    >
                        <span style="font-size:14px">
                            <i class="el-icon-plus"></i>
                            新增趟次
                        </span>
                    </el-button>
                </el-popover>
                <el-button
                    @click="onClickDeleteRunReport"
                    type="danger"
                    icon="el-icon-delete"
                    style="margin-left:10px"
                    >删除趟次</el-button
                >

                <el-button
                    style="margin-left:10px"
                    icon="el-icon-refresh"
                    type="warning"
                    @click="onRefreshData()"
                >
                    手动汇总
                </el-button>
                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left:10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left:10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>

        <div v-if="showReport" class="mwd-container" ref="mwd-container">
            <div class="content-container2" style="min-width:1000px;">
                <div class="box-border">
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            趟次RUN #
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                disabled
                                v-model="report.run"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            井眼尺寸HOLE SIZE mm
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.holeSize"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            排量FLOW RATE L/S
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.flowRate"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            泥浆类型MUD TYPE
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudType"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            泥浆密度MUD WEIGHT g/cm³
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudWeight"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            泥浆粘度MUD VISCOSITY s
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mudViscosity"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            含砂/固相SAND/SOLIDS
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.sandSolids"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            钻头厂家BIT MFG.
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitMfg"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            入井时间DATE IN
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-date-picker
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.dateIn"
                                type="datetime"
                                placeholder=""
                                format="yyyy-MM-dd HH:mm"
                            ></el-date-picker>
                        </div>

                        <div class="title center" style="flex:230px 0 0">
                            出井时间DATE OUT
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-date-picker
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.dateOut"
                                type="datetime"
                                placeholder=""
                                format="yyyy-MM-dd HH:mm"
                            ></el-date-picker>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            钻头类型INSERT/TOOTH/PDC
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.insertToothPdc"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            入井深度DEPTH IN m
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.depthIn"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            出井深度DEPTH OUT m
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.depthOut"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            钻头型号BIT MODEL
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitModel"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            脉宽PULSE WIDTH sec
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.pulseWidth"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            设计方位DRIFT/AZIMUTH deg
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.driftAzimuth"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            螺杆厂家MOTOR MFG.
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.motorMfg"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            限流环内径ORIFICE ID"
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.orificeId"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            磁偏角MAG. DEC. deg
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.magDec"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            螺杆尺寸MOTOR SIZE inch
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.motorSize"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            蘑菇头外径POP TIP OD"
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.popTipOd"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            伽马零长DIST.BIT-GAMMA m
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.distBitGamma"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            定子/转子M.LOBES/STAGE
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mlobesStage"
                            ></el-input>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            无磁内径MWD MONEL IDmm
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdMonelId"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            测斜零长DIST. BIT-ELEC.m
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.distBitElec"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:230px 0 0">
                            度数SET @ deg.
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.deg"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            无磁外径MWD MONEL ODmm
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdMonelOd"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            探管内角差IMO deg
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.imoDeg"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex: 230px 0 0">脉冲信号强度Psi</div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.ss"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex: 230px 0 0">最高温度℃</div>
                        <div class="text center" style="flex: 1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.maxBht"
                            ></InputNumber>
                        </div>
                        <div class="title center" style="flex:230px 0 0">
                            近钻头零长At Bit DIST .m
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.atBitDist"
                            ></InputNumber>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top:none">
                    <div class="line">
                        <div class="title center" style="flex:1 0 0">
                            工具串TARTAN MWD TOOLS
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1 0 0">
                            名称COMPONENT
                        </div>
                        <div class="title center" style="flex:1 0 0">
                            序列号SERIAL #
                        </div>
                        <div class="title center" style="flex:1 0 0">
                            起始时间START CIRC.hrs
                        </div>
                        <div class="title center" style="flex:1 0 0">
                            总循环时间TOTAL CIRC. hrs
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.toolList"
                        :key="`toolList` + key"
                    >
                        <i
                            v-if="
                                report.toolList.length > runToolListLength &&
                                    isEdit
                            "
                            style="font-size:15px;"
                            class="el-icon-circle-close hover-icon delete"
                            @click="onClickDelete('toolList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size:15px;"
                            v-if="isEdit"
                            @click="onClickAdd('toolList', key)"
                        ></i>
                        <div class="text center" style="flex:1 0 0">
                            <el-input
                                v-if="!isEdit"
                                class="mwd-input"
                                disabled
                                v-model="item.invName"
                            ></el-input>
                            <el-select
                                v-else
                                clearable
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.invCode"
                                @change="onInvNameChange(item, key)"
                            >
                                <el-option
                                    v-for="tool in nameCodeList"
                                    :key="tool.invCode"
                                    :label="tool.invName"
                                    :value="tool.invCode"
                                >
                                    <span>{{ tool.invName }}</span>
                                    <span style="color:#8492a6;font-size:13px">
                                        {{ tool.invCode }}
                                    </span>
                                </el-option>
                            </el-select>
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <el-select
                                clearable
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.serialNumber"
                                @focus="onSerialNumberFocus(item, key)"
                                @change="onSerialNumberChange(item, key)"
                            >
                                <el-option
                                    v-for="tool in toolListFilterByInvName(
                                        item.invName
                                    )"
                                    :key="tool.stockId"
                                    :label="tool.serialNumber"
                                    :value="tool.serialNumber"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.start"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1 0 0">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.total"
                            ></InputNumber>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top:none">
                    <div class="line">
                        <div class="title center" style="flex:4">
                            起钻原因REASON TO PULL OUT OF HOLE
                        </div>
                    </div>
                    <div class="line">
                        <div class="center title" style="flex:1">钻头BIT</div>
                        <div class="center title" style="flex:1">螺杆MOTOR</div>
                        <div class="center title" style="flex:1">仪器MWD</div>
                        <div class="center title" style="flex:1">其他OTHER</div>
                    </div>
                    <div class="line">
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.bitReason"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.motorReason"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.mwdReason"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex:1">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.otherReason"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:5">
                            仪器运转数据MWD OPERATING DATA
                        </div>
                    </div>
                    <div class="line">
                        <div class="center title" style="flex:1">
                            地面信号SURFpsi
                        </div>
                        <div class="center title" style="flex:1">
                            井底信号BTM psi
                        </div>
                        <div class="center title" style="flex:1">
                            通电ELECTRICAL hrs
                        </div>
                        <div class="center title" style="flex:1">
                            循环CIRCULATING hrs
                        </div>
                        <div class="center title" style="flex:1">
                            纯钻DRILLING hrs
                        </div>
                    </div>
                    <div class="line">
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.surfData"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.btmData"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.electricalData"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.circulatingData"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.drillingData"
                            ></InputNumber>
                        </div>
                    </div>
                </div>

                <!-- <div class="box-border" style="border-top:none">
                    <div class="line">
                        <div class="title center" style="flex:1;height:36px">
                            概述SUMMARY
                        </div>
                        <div class="title center">
                            <el-date-picker
                                class="mwd-input"
                                v-model="timespan"
                                value-format="yyyy-MM-dd"
                                @change="onTimespanChange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                            <div class="title center" style="flex:1">
                                概述SUMMARY
                                <a
                                    href="javascript:void(0)"
                                    style="margin-left:20px;color:blue"
                                    >导入仪器日报工况</a
                                >
                            </div>
                        </div>
                    </div>
                    <div class="line">
                        <div class="title center" style="flex:1">日期DATE</div>
                        <div class="title center" style="flex:1">起始时间TIME</div>
                        <div class="title center" style="flex:1">结束时间TIME</div>
                        <div class="title center" style="flex:1">井深DEPTH</div>
                        <div class="title center" style="flex:6">
                            工况DESCRIPTION
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.summaryList"
                        :key="key"
                    >
                        <i
                            v-if="
                                report.summaryList.length >
                                    runSummaryListLength && isEdit
                            "
                            style="font-size:15px;"
                            class="el-icon-circle-close hover-icon delete"
                            @click="onClickDelete('summaryList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size:15px;"
                            v-if="isEdit"
                            @click="onClickAdd('summaryList', key)"
                        ></i>
                        <div class="text center" style="flex:1">
                            <el-date-picker
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div class="text center" style="flex:1">
                            <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                value-format="HH:mm"
                                :picker-options="{
                                start: '00:00',
                                step: '00:05',
                                end: '23:50',
                                }"
                                v-model="item.startTime"
                            ></el-time-select>
                        </div>
                        <div class="text center" style="flex:1">
                            <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                value-format="HH:mm"
                                :picker-options="{
                                start: '00:00',
                                step: '00:05',
                                end: '23:50',
                                }"
                                v-model="item.endTime"
                            ></el-time-select>
                        </div>
                        <div class="text center" style="flex:1">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.depth"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex:6">
                            <el-input
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.description"
                            ></el-input>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</template>

<script>
import {
    runSummaryListLength,
    runToolListLength,
    getInitRUN,
    getInitBHA,
    getRunSummaryListTmp as summaryListTmp,
    getRunToolListTmp as toolListTmp,
} from "./data";
import {
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
    getBaseData,
    dailyReportRefresh,
    apiAddDailyReport,
    apiDeleteDailyReport,
    apiGetDailyReportBaseinfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RUN";
export default {
    name: "RUN",
    data() {
        return {
            REPORT_TYPE,
            report: {},
            runSummaryListLength,
            runToolListLength,
            showReport: false,
            timespan: "",
            selectedRunId: null,
            isEdit: false,
            currentRunList: [],
            runAdd: "",
            showAddPopover: false,
            showLoading: true
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        selectedRun: String | Number,
        job: String,
        toolList: Array,
        jobId: Number | String,
        jobStatus: Number,
        globalDisableEdit:Boolean
    },
    computed: {
        nameCodeList() {
            let invCodeList = [
                ...new Set(this.toolList.map((item) => item.invCode)),
            ];
            return invCodeList.map((item) => {
                let tmp = this.toolList.find(
                    (option) => option.invCode === item
                );
                return { invCode: tmp.invCode, invName: tmp.invName };
            });
        },
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    created() {
        this.report = this.getInitRUN();
    },
    methods: {
        summaryListTmp,
        toolListTmp,
        getInitRUN,
        getInitBHA,
        addHorizonDrag() {
            // const main = document.querySelector(".mwd-container");
            // let isMove = false;
            // let initX = 0,initY = 0;
            // let moveX = 0,moveY = 0;
            // let disX = 0, disY = 0;
            // let initScrollLeft = 0,initScrollTop = 0;
            // main?.addEventListener(
            //     "mousedown",
            //     function(e) {
            //         initScrollLeft = main.scrollLeft;
            //         initScrollTop = main.scrollTop;
            //         isMove = true;
            //         initX = e.pageX;
            //         initY = e.pageY;
            //     },
            //     true
            // );
            // main?.addEventListener("mousemove", function(e) {
            //     if (isMove) {
            //         moveX = e.pageX;
            //         moveY = e.pageY;
            //         disX = -moveX + initX;
            //         disY = -moveY + initY;
            //         main.scrollLeft = disX + initScrollLeft;
            //         main.scrollTop = disY + initScrollTop;
            //     }
            // });
            // main.addEventListener("mouseup", function() {
            //     isMove = false;
            // });
        },
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onRefreshData() {
            dailyReportRefresh({ jobId: this.report.jobId, reportType: "RUN" })
                .then((res) => {
                    this.loadReport(this.report.id, "RUN");
                })
                .catch((err) => {});
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },
        checkDuplicate(arr) {
            let nonEmptyArr = arr
                .map((item) => item.serialNumber)
                .reduce((acc, cur) => {
                    if (cur) {
                        return acc.concat(cur);
                    }
                    return acc;
                }, []);
            let nonEmptySet = new Set(nonEmptyArr);
            return nonEmptyArr.length !== nonEmptySet.size;
        },
        onClickDeleteRunReport() {
            let form = new FormData();
            form.append("reportId", String(this.selectedRunId));
            form.append("reportType", REPORT_TYPE);
            this.$confirm("确认要删除该趟次吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiDeleteDailyReport(form).then(() => {
                    this.loadReport();
                });
            });
        },
        onConfirmAddRun() {
            if (this.currentRunList.some((item) => item.run == this.runAdd)) {
                this.$message.error("趟次不能重复！");
                return;
            }
            const initReportRun = this.getInitRUN();
            let postReportRun = Object.assign(initReportRun, {
                run: this.runAdd,
                jobId: this.jobId,
                reportType: "RUN",
            });

            const initReportBHA = this.getInitBHA();
            let postReportBHA = Object.assign(initReportBHA, {
                run: this.runAdd,
                jobId: this.jobId,
                reportType: "BHA",
            });
            Promise.all([
                apiAddDailyReport(postReportRun),
                apiAddDailyReport(postReportBHA),
            ]).then(() => {
                this.loadReport();
                this.$emit("updateRunList");
                this.showAddPopover = false;
            });
        },
        updateReport() {
            if (this.checkDuplicate(this.report.toolList)) {
                this.$message.error("工具列表中有重复的序列号！");
                return;
            }
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        onSelectRun() {
            let map = this.currentRunList.find((item) => {
                return item.id == this.selectedRunId;
            });
            if (map) {
                this.$emit("updateCommonRun", map.run);
            }
            this.loadReport();
        },
        // TODO: prop -> selectedDate
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentRunList = baseInfo.data?.data || [];
            const idx = this.currentRunList.findIndex((item) => {
                return item.run === this.selectedRun;
            });
            let reportId;
            if (idx != -1) {
                reportId = this.currentRunList[idx].id;
                this.selectedRunId = reportId;
                this.getReportInfo(reportId);
            } else {
                reportId = this.currentRunList[0]?.id;
                if (reportId) {
                    this.selectedRunId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedRunId = null;
                    this.hideReport();
                }
            }
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            }).then((res) => {
                this.report = dealReport(res.data.data, REPORT_TYPE);
                this.$nextTick(() => {
                    this.showReport = true;
                    this.$nextTick(() => {
                        this.addHorizonDrag();
                    });
                });
            }).finally(()=>{
                this.showLoading = false;
            });
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
        toolListFilterByInvName(invName) {
            if (!invName) {
                return this.toolList;
            }
            return this.toolList.filter((item) => {
                return item.invName === invName;
            });
        },
        onSerialNumberFocus(item, key) {},
        filterSerialNumbers(invCode) {
            if (!invCode) {
                return [];
            }
            return this.toolList.filter(
                (option) =>
                    option.invCode === invCode &&
                    option.serialNumber &&
                    option.serialNumber !== "无"
            );
        },
        onSerialNumberChange(item, key) {
            if (item.serialNumber) {
                let map = this.toolList.find(
                    (tool) => tool.serialNumber == item.serialNumber
                );
                this.report.toolList[key].serialNumber = map.serialNumber;
                this.report.toolList[key].stockId = map.stockId;
            } else {
                this.report.toolList[key].stockId = null;
            }
        },
        onInvNameChange(item, key) {
            let tmp = this.toolListTmp();
            let invCode = item.invCode;
            let invName = this.nameCodeList.find(
                (map) => map.invCode === invCode
            ).invName;
            tmp.invName = invName;
            tmp.invCode = invCode;
            this.report.toolList[key] = Object.assign(
                this.report.toolList[key],
                tmp
            );
        },
        onTimespanChange() {
            if (this.timespan && this.timespan[0]) {
                getBaseData({
                    jobId: this.jobId,
                    startDate: this.timespan[0],
                    endDate: this.timespan[1],
                }).then((res) => {
                    let data = res.data.data || [];
                    this.report.summaryList = data.map((item) => {
                        return {
                            date: new Date(item.endDate).Format("yyyy-MM-dd"),
                            time: new Date(item.endDate).Format("hh:mm"),
                            depth: item.depthTo,
                            description: item.activity,
                        };
                    });
                });
            }
        },
    },
};
</script>
<style lang="scss">
.mwd-input.el-date-editor.el-input__inner {
    width: 280px !important;
}
.mwd-input .el-range-separator {
    width: 20px;
}
</style>
