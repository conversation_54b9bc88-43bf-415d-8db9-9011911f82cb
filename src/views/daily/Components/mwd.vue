<template>
    <div class="content" style="position: relative" v-loading="showLoading">
        <div
            style="
                z-index: 999;
                height: 50px;
                display: flex;
                align-items: center;
                min-width: 700px;
                background: white;
                border-bottom: 1px dashed #eee;
            "
        >
            <span
                class="report-title"
                style="
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 600;
                    color: #333333;
                "
            >
                {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
            </span>
            <el-select
                style="margin-left: 10px"
                v-model="selectedDateId"
                @change="onSelectDate"
            >
                <el-option
                    v-for="(item, key) in currentDateList"
                    :key="`date${key}`"
                    :label="item.date"
                    :value="item.id"
                ></el-option>
            </el-select>
            <template v-if="!isJobCompleted && !globalDisableEdit">
                <el-popover
                    v-model="showAddPopover"
                    trigger="click"
                    placement="right-start"
                >
                    <div class="add-popover">
                        <div class="add-popover-title">新增</div>

                        <div class="calender" style="width: 400px">
                            <el-calendar v-model="orginDateAdd"> </el-calendar>
                        </div>

                        <div class="add-popover-btn">
                            <el-button
                                @click="onConfirmAddDate"
                                size="small"
                                type="primary"
                            >
                                确 定
                            </el-button>
                            <el-button
                                @click="showAddPopover = false"
                                size="small"
                            >
                                取 消
                            </el-button>
                        </div>
                    </div>

                    <el-button
                        slot="reference"
                        style="margin-left: 10px"
                        type="primary"
                        size="small"
                    >
                        <span style="font-size: 14px">
                            <i class="el-icon-plus"></i>
                            新增日报
                        </span>
                    </el-button>
                </el-popover>
                <el-button
                    @click="onClickDeleteDailyReport"
                    type="danger"
                    icon="el-icon-delete"
                    style="margin-left: 10px"
                    >删除日报</el-button
                >

                <el-button
                    v-if="!isEdit"
                    type="primary"
                    @click="onEditReport"
                    style="margin-left: 10px"
                    >编辑</el-button
                >
                <el-button
                    v-if="isEdit"
                    style="margin-left: 10px"
                    type="primary"
                    @click="onSaveReport"
                    >保存</el-button
                >
                <el-button v-if="isEdit" style="" @click="onCancelEditReport"
                    >取消</el-button
                >
            </template>
        </div>
        <div class="mwd-container" v-if="showReport">
            <div class="content-container2">
                <div style="margin-bottom: 10px">
                    <span>
                        工程师：<el-input
                            :disabled="!isEdit"
                            v-model="report.operator"
                            style="width: 200px"
                        ></el-input>
                    </span>
                    <span style="margin-left: 10px">
                        仪器箱Kit#：<el-input
                            disabled
                            v-model="report.kit"
                            style="width: 200px"
                        ></el-input>
                    </span>
                </div>
                <div class="box-border">
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            基本信息GENERAL INFO
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            泥浆数据MUD DATA
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            仪器基础数据MWD DATA
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            工单号Job #
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            {{ job }}
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            泥浆类型Type
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.mudType"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            探管内角差IMO °
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.imo"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            开始日期Start Date
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <el-date-picker
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.startDate"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            泥浆密度Mud Wt g/cm³
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.mudDen"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            钻具角差DAO °
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.dao"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            施工天数Day #
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.constructionDays"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            粘度Vis s
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.viscosity"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            磁偏角Mag Dec °
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.magEc"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            监工Comp. Man
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.superintendent"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            固相含量Solids %
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.solidContent"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            伽马零长Gamma Dist m
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.gammaDist"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">DD</div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.dd1"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            含砂量Sands %
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.sandContent"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            近钻伽马零长At bit Dist m
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.atBitDist"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">DD</div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.dd2"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            含油量Oil %
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.oilContent"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            限流环Orfice
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.orfice"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">MWD</div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.mwd1"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            酸碱度PH
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.ph"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            蘑菇头PoPpit
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.poppit"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">MWD</div>
                        <div style="flex: 1 0 0" class="text">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.mwd2"
                            ></el-input>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            塑性粘度PV mPa.s
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.pv"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            脉宽Plsw sec
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.plsw"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            趟次Run #
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.run"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            动切力YP Pa
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.vp"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            磁场强度Nmag nT
                        </div>
                        <div style="flex: 1" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.nmag"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            开始深度Start Depth
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.startDepth"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            失水FL mL
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.fl"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            磁倾角Ndip °
                        </div>
                        <div style="flex: 1" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.ndip"
                            ></InputNumber>
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            结束深度End Depth
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.endDepth"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            温度Temp ℃
                        </div>
                        <div style="flex: 1 0 0" class="text">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="report.temp"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="title center"></div>
                        <div style="flex: 1" class="text">
                            <el-input class="mwd-input" disabled></el-input>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            DAILY SUMMARY每日汇总 - HOURS时间
                        </div>
                    </div>
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">名称</div>
                        <div style="flex: 1 0 0" class="title center">
                            序列号
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            Start开始
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            Today今天
                        </div>
                        <div style="flex: 1 0 0" class="title center">
                            Total总计
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.toolList"
                        :key="key"
                    >
                        <div style="flex: 1 0 0" class="text center">
                            <el-input
                                v-if="!isEdit"
                                class="mwd-input"
                                disabled
                                v-model="item.invName"
                            ></el-input>
                            <el-select
                                v-else
                                clearable
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.invCode"
                                @change="onInvNameChange(item, key)"
                            >
                                <el-option
                                    v-for="tool in nameCodeList"
                                    :key="tool.invCode"
                                    :label="tool.invName"
                                    :value="tool.invCode"
                                >
                                    <span>{{ tool.invName }}</span>
                                    <span
                                        style="color: #8492a6; font-size: 13px"
                                    >
                                        {{ tool.invCode }}
                                    </span>
                                </el-option>
                            </el-select>
                        </div>
                        <div style="flex: 1 0 0" class="text center">
                            <el-select
                                clearable
                                placeholder=""
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="item.serialNumber"
                                @change="onSerialNumberChange(item, key)"
                            >
                                <el-option
                                    v-for="tool in filterSerialNumbers(
                                        item.invCode
                                    )"
                                    :key="tool.stockId"
                                    :label="tool.serialNumber"
                                    :value="tool.serialNumber"
                                ></el-option>
                            </el-select>
                        </div>
                        <div style="flex: 1 0 0" class="text center">
                            <InputNumber
                                class="mwd-input"
                                :disabled="!isEdit"
                                @change="onTodayChanged(item, key)"
                                v-model="item.start"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 0 0" class="text center">
                            <InputNumber
                                class="mwd-input"
                                @change="onTodayChanged(item, key)"
                                :disabled="!isEdit"
                                v-model="item.today"
                            ></InputNumber>
                        </div>
                        <div style="flex: 1 1 0" class="text center not-input">
                            <InputNumber
                                class="mwd-input"
                                disabled
                                v-model="item.total"
                            ></InputNumber>
                        </div>
                        <i
                            v-if="
                                report.toolList.length > mwdToolLength && isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size: 15px"
                            @click="onClickDelete('toolList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size: 15px"
                            v-if="isEdit"
                            @click="onClickAdd('toolList', key)"
                        ></i>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div style="flex: 1 0 0" class="title center">
                            24小时计划Planned Changes for next 24h
                        </div>
                    </div>
                    <div class="line">
                        <div
                            style="flex: 1 0 0; height: auto"
                            class="text center"
                        >
                            <el-input
                                type="textarea"
                                class="mwd-input"
                                :disabled="!isEdit"
                                v-model="report.plan"
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="box-border" style="border-top: none">
                    <div class="line">
                        <div class="title center px10" style="flex: 1">
                            开始时间Start Time
                        </div>
                        <div class="title center px10" style="flex: 1">
                            结束时间End Time
                        </div>
                        <div class="title center px10" style="flex: 1">
                            起始井深mFrom
                        </div>
                        <div class="title center px12" style="flex: 1">
                            结束井深mTo
                        </div>
                        <div class="title center px12" style="flex: 1">
                            Activity工况
                        </div>
                        <div class="title center px12" style="flex: 1">
                            SPP立压MPA
                        </div>
                        <div class="title center px12" style="flex: 1">
                            转速RPM
                        </div>
                        <div class="title center px12" style="flex: 1">
                            SS信号强度Psi
                        </div>
                        <div class="title center px10" style="flex: 1">
                            扭矩Torque KN.M
                        </div>
                        <div class="title center px12" style="flex: 1">
                            排量FR L/S
                        </div>
                        <div class="title center px12" style="flex: 1">
                            备注Note
                        </div>
                    </div>
                    <div
                        class="line hover-line"
                        v-for="(item, key) in report.activityList"
                        :key="`activityList${key}`"
                    >
                        <i
                            v-if="
                                report.activityList.length >
                                    mwdActivityLength && isEdit
                            "
                            class="el-icon-circle-close hover-icon delete"
                            style="font-size: 15px"
                            @click="onClickDelete('activityList', key)"
                        ></i>
                        <i
                            class="el-icon-circle-plus hover-icon plus"
                            style="font-size: 15px"
                            v-if="isEdit"
                            @click="onClickAdd('activityList', key)"
                        ></i>
                        <div class="text center" style="flex: 1">
                            <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.startTime"
                                :picker-options="{
                                    start: '00:00',
                                    step: '00:05',
                                    end: '24:00',
                                }"
                            ></el-time-select>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-time-select
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.endTime"
                                value-format="HH:mm"
                                :picker-options="{
                                    minTime: item.startTime,
                                    start: '00:00',
                                    step: '00:05',
                                    end: '24:00',
                                }"
                            ></el-time-select>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.depthFrom"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.depthTo"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-autocomplete
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.activity"
                                placeholder=""
                                :fetch-suggestions="querySearchWorkCondition"
                                value-key="name"
                            >
                            </el-autocomplete>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.spp"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.rpm"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.ss"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <!-- <InputNumber
                            :disabled="!isEdit"
                            class="mwd-input"
                            v-model="item.torque"
                        ></InputNumber> -->
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.torque"
                            ></el-input>
                        </div>
                        <div class="text center" style="flex: 1">
                            <InputNumber
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.fr"
                            ></InputNumber>
                        </div>
                        <div class="text center" style="flex: 1">
                            <el-input
                                :disabled="!isEdit"
                                class="mwd-input"
                                v-model="item.note"
                            ></el-input>
                        </div>
                    </div>
                </div>
                <div class="daily-summary">
                    <h3>工作汇报</h3>
                </div>
                <div class="box-border">
                    <div style="display: flex">
                        <div
                            v-for="(item, key) in report.workSummaryList"
                            :key="key"
                            style="flex: 1 0 0"
                        >
                            <div class="line">
                                <div style="flex: 1 0 0" class="title center">
                                    {{ item.time }}
                                </div>
                            </div>
                            <!-- <div style="text-align:center">{{ item.time }}</div> -->
                            <div class="line">
                                <div
                                    style="flex: 1 0 0; height: auto"
                                    class="text"
                                >
                                    <el-input
                                        :disabled="!isEdit"
                                        spellcheck="false"
                                        class="mwd-input"
                                        type="textarea"
                                        :autosize="{ minRows: 50, maxRows: 50 }"
                                        style="width: 100%"
                                        @change="onInputChange"
                                        v-model="item.content"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getInitMWD,
    mwdActivityLength,
    mwdToolLength,
    getMwdActivityTmp as activityListTmp,
    getMwdToolTmp as toolListTmp,
} from "./data.js";
import {
    apiAddDailyReport,
    apiDeleteDailyReport,
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
    apiUpdateDailyReport,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "MWD";
export default {
    name: "mwd",
    data() {
        return {
            REPORT_TYPE,
            report: {},
            activeCollapse: [1, 2, 3, 4, 5],
            mwdActivityLength,
            mwdToolLength,
            showReport: false,
            selectedDateId: null,
            currentDateList: [],
            showAddPopover: false,
            orginDateAdd: "",
            isEdit: false,
            showLoading: true,
        };
    },
    props: {
        username: String,
        electronStore: Object,
        reportType: String,
        date: String,
        selectedDate: String,
        job: String,
        jobId: Number,
        selectedJob: String,
        workConditionList: Array,
        toolList: Array,
        jobStatus: Number,
        globalDisableEdit: Boolean,
    },
    computed: {
        nameCodeList() {
            let invCodeList = [
                ...new Set(this.toolList.map((item) => item.invCode)),
            ];
            return invCodeList.map((item) => {
                let tmp = this.toolList.find(
                    (option) => option.invCode === item
                );
                return { invCode: tmp.invCode, invName: tmp.invName };
            });
        },
        isJobCompleted() {
            return this.jobStatus === 1;
        },
    },
    watch: {
        selectedDateId(newVal) {
            let map = this.currentDateList.find((item) => {
                return item.id == newVal;
            });
            if (map) {
                this.$emit("updateCommonDate", map.date);
            }
        },
    },
    created() {},
    methods: {
        getInitMWD,
        toolListTmp,
        activityListTmp,
        onClickAdd(item, key) {
            this.report[item].splice(key + 1, 0, this[`${item}Tmp`]());
        },
        onClickDelete(item, key) {
            this.report[item].splice(key, 1);
        },
        checkDuplicate(arr) {
            let nonEmptyArr = arr
                .map((item) => item.serialNumber)
                .reduce((acc, cur) => {
                    if (cur) {
                        return acc.concat(cur);
                    }
                    return acc;
                }, []);
            let nonEmptySet = new Set(nonEmptyArr);
            return nonEmptyArr.length !== nonEmptySet.size;
        },
        updateReport() {
            if (this.checkDuplicate(this.report.toolList)) {
                this.$message.error("工具列表中有重复的序列号！");
                return;
            }
            apiUpdateDailyReport({
                reportType: REPORT_TYPE,
                ...this.report,
            }).then(() => {
                this.isEdit = false;
                this.loadReport();
            });
        },
        // TODO: prop -> selectedDate
        async loadReport() {
            this.showLoading = true;
            const baseInfo = await apiGetDailyReportBaseinfo({
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            this.currentDateList = baseInfo.data?.data || [];
            const idx = this.currentDateList.findIndex((item) => {
                return item.date === this.selectedDate;
            });
            let reportId;
            if (idx != -1) {
                reportId = this.currentDateList[idx].id;
                this.selectedDateId = reportId;
                this.getReportInfo(reportId);
            } else {
                reportId = this.currentDateList[0]?.id;
                if (reportId) {
                    this.selectedDateId = reportId;
                    this.getReportInfo(reportId);
                } else {
                    this.selectedDateId = null;
                    this.hideReport();
                }
            }
        },
        onSelectDate() {
            this.loadReport();
        },
        onClickDeleteDailyReport() {
            let form = new FormData();
            form.append("reportId", String(this.selectedDateId));
            form.append("reportType", REPORT_TYPE);
            this.$confirm("确认要删除该日报吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiDeleteDailyReport(form).then(() => {
                    this.loadReport();
                });
            });
        },
        onConfirmAddDate() {
            const date = this.orginDateAdd || new Date();
            const dateAdd = date.Format("yyyy-MM-dd");
            if (this.currentDateList.some((item) => item.date === dateAdd)) {
                this.$message.error("报告日期不能重复！");
                return;
            }
            const initReport = this.getInitMWD();
            let postReport = Object.assign(initReport, {
                date: dateAdd,
                jobId: this.jobId,
                reportType: REPORT_TYPE,
            });
            apiAddDailyReport(postReport).then(() => {
                this.loadReport();
                this.showAddPopover = false;
            });
        },
        getReportInfo(reportId) {
            apiGetDailyReportInfo({
                reportType: REPORT_TYPE,
                reportId,
            })
                .then((res) => {
                    this.report = dealReport(res.data.data, REPORT_TYPE);
                    this.$nextTick(() => {
                        this.showReport = true;
                    });
                })
                .finally(() => {
                    this.showLoading = false;
                });
        },
        onEditReport() {
            this.isEdit = true;
        },
        onCancelEditReport() {
            this.isEdit = false;
            this.loadReport();
        },
        onSaveReport() {
            this.updateReport();
        },
        hideReport() {
            this.showLoading = false;
            this.showReport = false;
        },
        querySearchWorkCondition(queryString, cb) {
            let reg = new RegExp(queryString.toUpperCase().replace(/\s/g, ""));
            cb(this.workConditionList.filter((item) => reg.test(item.name)));
        },
        toolListFilterByInvName(invName) {
            if (!invName) {
                // return this.toolList;
                return [];
            }
            let arr = this.toolList.filter((item) => {
                return item.invName === invName;
            });

            return arr;
        },

        onSerialNumberChange(item, key) {
            if (item.serialNumber) {
                let map = this.toolList.find(
                    (tool) => tool.serialNumber == item.serialNumber
                );
                this.report.toolList[key].serialNumber = map.serialNumber;
                this.report.toolList[key].stockId = map.stockId;
            } else {
                this.report.toolList[key].stockId = null;
            }
        },
        filterSerialNumbers(invCode) {
            if (!invCode) {
                return [];
            }
            return this.toolList.filter(
                (option) =>
                    option.invCode === invCode &&
                    option.serialNumber &&
                    option.serialNumber !== "无"
            );
        },
        onInvNameChange(item, key) {
            let tmp = this.toolListTmp();
            let invCode = item.invCode;
            let invName = this.nameCodeList.find(
                (map) => map.invCode === invCode
            ).invName;
            tmp.invName = invName;
            tmp.invCode = invCode;
            this.report.toolList[key] = Object.assign(
                this.report.toolList[key],
                tmp
            );
        },
        onTodayChanged(item, key) {
            this.$nextTick(() => {
                this.report.toolList[key].total =
                    Number(item.start || 0) + Number(item.today || 0);
            });
        },
    },
};
</script>
