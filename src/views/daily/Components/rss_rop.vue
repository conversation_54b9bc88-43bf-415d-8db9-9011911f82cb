<template>
  <ImageViewer class="content rigout-report" v-loading="showLoading" style="position:relative">
    <div
      style="z-index:999;height:50px;display:flex;align-items:center;min-width:700px;background:white;border-bottom:1px dashed #eee"
    >
      <span
        class="report-title"
        style="font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 600;color: #333333;"
      >
        {{ $REPORT_TYPE_MAP[REPORT_TYPE] }}
      </span>
    </div>
    <div v-if="showReport" class="mwd-container">
      <div class="content-container2">
        <div class="basic-info">
          <div class="info-container">
            <div class="info-title" style="flex:70px 0 0">
              Engineer：
            </div>
            <el-input
              :disabled="!isEdit"
              v-model="report.operator"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">Job Number：</div>
            <el-input
              disabled
              v-model="job"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
        </div>
        <div class="box-border">
          <div class="line">
            <div class="title center" style="flex:60px 0 0">#</div>
            <div class="title center" style="flex:1">
              项目Action
            </div>
            <div class="title center" style="flex:200px 0 0">
              日期Date
            </div>
            <div class="title center" style="flex:200px 0 0">
              Initial
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              1
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              完井后完成所有日报，保存所有数据文件，伽马数据库，及其他本井所有电子文档。
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[0].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[0].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              2
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              下载数据，保存至文件夹memory，并作备份（RUN summary核对JOB DETAILS；Run DETAILS;VSS memory 加载一下）。
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[1].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[1].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              3
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              工作机数据库Database备份（内存处理后备份一个数据库+自动备份的最近一个数据库）
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[2].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[2].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[2].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              4
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              Final Log（4图2Las；500MD的LAS步长改成0.1524）；LQC--AE las、gamma QC、memdiagnostics depth based QC、memdiagnostics time based QC（3图1las，注意循环划眼都加上）（三图1las）
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[3].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[3].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[3].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              5
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              仪器出井后，进行整体检查，有任何问题或者表面有任何冲蚀，都需要记录和汇报
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[4].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[4].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[4].initial"
                class="mwd-input"
              ></el-input>
              <!-- <el-date-picker class="mwd-input basic-info-input"
                               v-model="report.actionList[4].initial"
                        type="date" placeholder="" :disabled="!isEdit" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker> -->
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              6
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              打包所有线缆和其他工具，标记出有损坏和会影响后续工作的地方上报公司；确保滤子、磁铁、传感器、吊绳、等所有工具装箱。
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[5].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[5].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[5].initial"
                class="mwd-input"
              ></el-input>
              <!-- <el-date-picker class="mwd-input basic-info-input"
                               v-model="report.actionList[5].initial"
                        type="date" placeholder="" :disabled="!isEdit" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker> -->
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              7
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              存在仪器失效，完成失效报告。
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[6].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[6].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[6].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              8
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              收拾完毕后收拾好所有垃圾，不要遗留任何垃圾在现场，避免给公司造成不良影响。
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[7].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[7].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[7].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <div class="line">
            <div class="title center auto" style="flex:60px 0 0;height:auto;">
              9
            </div>
            <div class="text left auto" style="flex:1;height:auto">
              根据检查表再次检查，确保带走所有东西;固定好仪器箱和所有接头.
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <!-- <el-input
                            :disabled="!isEdit"
                            v-model="report.actionList[8].date"
                            class="mwd-input"
                        ></el-input> -->
              <el-date-picker
                class="mwd-input basic-info-input"
                v-model="report.actionList[8].date"
                type="date"
                placeholder=""
                :disabled="!isEdit"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </div>
            <div class="text center auto" style="flex:200px 0 0">
              <el-input
                :disabled="!isEdit"
                v-model="report.actionList[8].initial"
                class="mwd-input"
              ></el-input>
            </div>
          </div>
          <!-- <div class="line">
            <div
              class="text left"
              style="flex:100% 0 0;width:100%;min-height:120px;height:auto"
            >
              <div v-html="report.screenShoot" class="rich-text-cls"></div>
            </div>
          </div> -->
        </div>

        <div class="basic-info">
          <div class="info-container">
            <div class="info-title">MWD Engineer：</div>
            <el-input
              :disabled="!isEdit"
              v-model="report.mwdOEngineer"
              class="mwd-input basic-info-input"
            ></el-input>
          </div>
          <div class="info-container">
            <div class="info-title">Signature & Date:</div>
            <!-- <el-input
                        :disabled="!isEdit"
                        v-model="report.signatureDate"
                        class="mwd-input basic-info-input"
                    ></el-input> -->
            <el-date-picker
              class="mwd-input basic-info-input"
              v-model="report.signatureDate"
              type="date"
              placeholder=""
              :disabled="!isEdit"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </div>
      </div>
    </div>
  </ImageViewer>
</template>

<script>
import { getInitRSS_ROP_REPORT } from "./data";
import {
    apiGetDailyReportBaseinfo,
    apiGetDailyReportInfo,
} from "@/api/mwd";
import dealReport from "./init";
const REPORT_TYPE = "RSS_ROPROCEDURES";
export default {
  name: "RSS_ROPROCEDURES",
  created() {
    this.report = getInitRSS_ROP_REPORT();
  },
  props: {
    username: String,
    electronStore: Object,
    reportType: String,
    date: String,
    run: String,
    job: String,
    jobId: Number,
    jobStatus: Number,
    globalDisableEdit:Boolean
  },
  data() {
    return {
      REPORT_TYPE,
      getInitRSS_ROP_REPORT,
      report: {},
      showReport: false,
      isEdit: false,
      showLoading: true,
    };
  },
  computed: {
    isJobCompleted() {
      return this.jobStatus === 1;
    },
  },
  methods: {
    async loadReport() {
        this.showLoading = true;
        const baseInfo = await apiGetDailyReportBaseinfo({
            jobId: this.jobId,
            reportType: REPORT_TYPE,
        });
        const baseInfoList = baseInfo.data?.data || [];
        if (baseInfoList.length === 0) {
            this.$message.error("暂无相关数据");
            this.showLoading = false;
            return;
        }
        const reportId = baseInfoList[0].id;
        apiGetDailyReportInfo({
            reportType: REPORT_TYPE,
            reportId,
        }).then((res) => {
            this.report = dealReport(res.data.data, REPORT_TYPE);
            this.$nextTick(() => {
                this.showReport = true;
            });
        }).finally(()=>{
            this.showLoading = false;
        });
    },
  },
};
</script>
<style lang="scss">
.rich-text-cls{
  p{
    margin:5px 0;
  }
  line-height: 1;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.auto {
  height: auto !important;
}
.upload-btn {
  width: 100px;
  height: 100px;
  border: 1px dashed grey;
  border-radius: 5px;
  text-align: center;
  line-height: 100px;
}
</style>
<style lang="scss" scoped>
.img-container {
  position: relative;
  height: 100%;
  &:hover {
    img {
      filter: brightness(0.2);
    }
    .img-icon-container {
      opacity: 1;
    }
  }
  .img-icon-container {
    width: 100%;
    height: 20px;
    position: absolute;
    top: 50%;
    display: flex;
    justify-content: center;
    opacity: 0;
    .img-icon {
      margin-left: 20px;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
  }
}
.basic-info {
  display: flex;
  justify-content: flex-start;
  // line-height: 3.6;
  margin-left: 10px;
  height: 40px;
  .info-container {
    display: flex;
    align-items: center;
    .info-title {
      flex: 150px 0 0;
      color: #3c4d73;
    }
    .basic-info-input {
      border-bottom: 1px solid #ccd3d9;
      min-width: 200px;
      max-width: 260px;
    }
  }
  div {
    flex: 400px 0 0;
    text-align: right;
  }
  
}
</style>
