<template>
    <el-dialog :visible.sync="isCompleteFileDialogVisble" title="完井资料" top="100px">
      <el-button :loading="isFileUploading" type="primary" @click="onUploadCompleteFile" style="margin-top: -60px;">上传</el-button>
      <el-table
        :data="completeFileList"
        stripe
      >
        <el-table-column label="文件名" prop="massFileName">
            <template slot-scope="scope">
                <a :href="`${scope.row.massFilePath}`">
                    <span :title="scope.row.massFileName" style="width: 300px;color:blue; overflow: hidden;white-space: nowrap; text-overflow: ellipsis;display: inline-block;">
                        {{ scope.row.massFileName }}
                    </span>
                </a>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" width="300px"  prop="createTime">
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template slot-scope="scope">
              <el-button type="text" @click="onDeleteCompleteFile(scope.row.massId)">
                  删除
              </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :hide-on-single-page="true"
          style="margin-top:20px"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          @current-change="onCurrentChange"
          :current-page="currentPage"
      ></el-pagination>
      <el-upload
            class="ghost-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="onUploadCompleteWellFile"
            >
            <div ref="ghostFileUploader" style="width:0"></div>
        </el-upload>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiUploadCompleteWellFile, apiGetCompleteFileList, apiDeleteCompleteWellFile } from '@/api/mwd'
@Component({})
export default class extends Vue {
  private completeFileList:any[] = []
  private isFileUploading = false
  private isCompleteFileDialogVisble = false
  private currentPage = 1
  private total = 0
  private pageSize = 8
  private jobId = -1
  private openDialog(jobId){
    this.jobId = jobId;
    this.isCompleteFileDialogVisble = true;
    this.getCompleteFileList(true);
  }
  private onClickCompleteFile(){
    this.isCompleteFileDialogVisble = true;
    this.getCompleteFileList(true);
  }
  private onDeleteCompleteFile(massId){
    this.$confirm("确认删除该文件?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(()=>{
      const form = new FormData();
      form.append('type', 'MASSID');
      form.append('idList', [massId].toString())
      apiDeleteCompleteWellFile(form).then(()=>{
        this.getCompleteFileList(true)
        this.$message.success('操作成功！')
      })
    })
  }
  private onUploadCompleteWellFile(file){
      this.isFileUploading  = true
      const form = new FormData();
      form.append('jobId', String(this.jobId));
      form.append('file', file);
      apiUploadCompleteWellFile(form, (progressEvent)=>{
        let percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        console.log(`上传进度: ${percentCompleted}%`);
      }).then(()=>{
          this.getCompleteFileList();
          this.$message.success('操作成功！')
      }).finally(()=>{
        this.isFileUploading = false
      })
  }
  private getCompleteFileList(bool=false){
    if(bool){
      this.currentPage = 1;
    }
    apiGetCompleteFileList({current:this.currentPage, size: this.pageSize},{jobId: this.jobId}).then(res=>{
      this.completeFileList = res.data?.data?.records || [];
      this.total = res.data?.data?.total;
    })
  }
  private onCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getCompleteFileList();
  }
  private onUploadCompleteFile(){
    (this.$refs.ghostFileUploader as any).click();
  }
}

</script>