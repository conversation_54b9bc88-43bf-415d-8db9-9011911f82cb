export const priorityList = ['普通', '高级', '紧急'];
export const templateList = ['近钻头维修', '近钻头组装', '螺杆维修'];
export const taskStatus = ['未开始', '已开工', '已完成', '拒收', '终止', '归档'];
export const projectAttr = ['返修单', '钻具通知书', '组装单']

export enum EnumProduceStatus {
    "未开始" = 0,
    "已开始" = 1,
    "已结束" = 2,
    "拒收" = 3,
    "终止" = 4,
    "归档" = 5
}
export enum EnumPriority {
    "普通" = 0,
    "高级" = 1,
    "紧急" = 2,
}

export const RECEIPT_TYPE_MAP = {
    MATERIAL_REQUEST: '领料单',
    PURCHASE: '请购单',
    ENTER_WAREHOUSE: '入库单',
    ASSOCIATION: '外协单'
}

export const ADD_TYPE_MAP = {
    SINGLE: '新增',
    BATCH: '批量新增',

}