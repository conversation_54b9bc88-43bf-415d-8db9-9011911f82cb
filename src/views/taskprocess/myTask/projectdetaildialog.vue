<template>
    <el-dialog title="详情" :visible.sync="isDialogVisible">
        <el-form :model="form" label-width="120px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划编号：">
                        {{ form.produceCode }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item required label="项目名称：">
                        {{ form.produceName }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="项目属性：">
                        {{ form.produceType }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="优先级：">
                        {{ EnumPriority[form.priority] }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="责任人：">
                        {{ form.directorName }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="相关单据：">
                        {{ form.receiptCode }}
                    </el-form-item>
                </el-col>
                <!-- <el-col style="margin-left:-4%" :span="12">
                    <el-button type="primary" size="small">相关单据</el-button>
                </el-col> -->
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划开始时间：">
                        {{ form.planStartDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划结束时间：">
                        {{ form.planEndDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际开始时间：">
                        {{ form.actualStartDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际结束时间：">
                        {{ form.actualEndDate }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>

<script lang="ts">
import { EnumProduceStatus, EnumPriority } from "../enum";
import { Component, Vue } from "vue-property-decorator";
import { apiGetProductionTaskInfo } from "@/api/process";
@Component({})
export default class extends Vue {
    private EnumProduceStatus = EnumProduceStatus;
    private EnumPriority = EnumPriority;
    private isDialogVisible = false;
    private form: any = {};
    private async showDialog(scope: any) {
        await apiGetProductionTaskInfo({ produceId: scope.row.ancestors }).then(
            (res) => {
                this.form = res.data.data;
                this.isDialogVisible = true;
            }
        );
    }
}
</script>
