<template>
    <el-dialog top="5vh" :title="`详情`" :visible.sync="isDialogVisible">
        <el-form :model="form" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="任务名称：">
                        {{ form.produceName }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="任务编码：">
                        {{ form.produceCode }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划开始：">
                        {{ form.planStartDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划结束：">
                        {{ form.planEndDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际开始：">
                        {{ form.actualStartDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际结束：">
                        {{ form.actualEndDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="完成率：">
                        {{ form.progress + "%" }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="任务级别：">
                        {{ EnumPriority[form.priority] }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="任务状态：">
                        {{ EnumProduceStatus[form.produceStatus] }}
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="任务描述：">
                        {{ form.produceDesc }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-form :model="logForm">
            <el-row>
                <el-col :span="24" style="padding:0 40px 0 20px">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane label="日志" name="first">
                            <div
                                v-if="commentList && commentList.length"
                                class="comments-container"
                            >
                                <div
                                    class="comment-content"
                                    v-for="comment in commentList"
                                    :key="comment.id"
                                >
                                    <div class="right">
                                        <div class="comment">
                                            {{ comment.comment }}
                                        </div>
                                        <div class="date">
                                            {{ comment.userName }}
                                            {{
                                                new Date(
                                                    comment.createTime
                                                ).Format("yyyy-MM-dd hh:mm:ss")
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                暂无数据
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="相关附件" name="second">
                            <div
                                v-if="relatedFileList && relatedFileList.length"
                                class="file-list"
                            >
                                <div
                                    class="file-item"
                                    v-for="file in relatedFileList"
                                    :key="file.fileId"
                                >
                                    <span
                                        @click="onDownload(file.fileId)"
                                        class="file-name"
                                    >
                                        <i class="el-icon-document"></i>
                                        {{ file.fileName }}
                                    </span>
                                </div>
                            </div>
                            <div v-else>
                                暂无数据
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="相关单据" name="third">
                            <el-table
                                v-if="receiptList.length"
                                :data="receiptList"
                            >
                                <el-table-column
                                    prop="receiptType"
                                    label="单据类型"
                                >
                                    <template slot-scope="scope">{{
                                        RECEIPT_TYPE_MAP[scope.row.receiptType]
                                    }}</template>
                                </el-table-column>
                                <el-table-column
                                    prop="fileInfoList"
                                    label="文件列表"
                                >
                                    <template slot-scope="scope">
                                        <div
                                            class="file-list"
                                            style="width:100%"
                                        >
                                            <div
                                                class="file-item"
                                                v-for="file in scope.row
                                                    .fileInfoList"
                                                :key="file.fileId"
                                            >
                                                <span
                                                    @click="
                                                        onDownload(file.fileId)
                                                    "
                                                    class="file-name"
                                                >
                                                    <i
                                                        class="el-icon-document"
                                                    ></i>
                                                    {{ file.fileName }}
                                                </span>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div v-else>暂无数据</div>
                        </el-tab-pane>
                        <el-tab-pane label="人员分配" name="fourth">
                            <el-table
                                v-if="
                                    allocatedUserList &&
                                        allocatedUserList.length
                                "
                                :data="allocatedUserList"
                            >
                                <el-table-column prop="name" label="人员">
                                </el-table-column>
                                <el-table-column prop="hours" label="工时">
                                </el-table-column>
                            </el-table>
                            <div v-else>暂无数据</div>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import {
    EnumPriority,
    EnumProduceStatus,
    RECEIPT_TYPE_MAP,
    ADD_TYPE_MAP,
} from "../enum";
import {
    apiGetCommentList,
    apiGetFileList,
    apiGetReceiptList,
    apiGetProduceUser,
} from "@/api/process";
@Component({})
export default class extends Vue {
    private allocatedUserList: any[] = [];
    private ADD_TYPE_MAP = ADD_TYPE_MAP;
    private receiptType = "MATERIAL_REQUEST";
    private EnumPriority = EnumPriority;
    private EnumProduceStatus = EnumProduceStatus;
    private RECEIPT_TYPE_MAP = RECEIPT_TYPE_MAP;
    private isDialogVisible = false;
    private commentList: any[] = [];
    private form: any = { comment: "" };
    private logForm: any = { comment: "" };
    private activeTab = "first";
    private relatedFileList: any[] = [];
    private receiptList: any[] = [];
    private async showDialog(scope: any) {
        this.form = { ...scope.row };
        this.produceId = scope.row.produceId;
        await this.getCommentList();
        await this.getRelatedFileList();
        await this.getReceiptList();
        await this.getAllocatedUserList();

        this.isDialogVisible = true;
    }
    private async getAllocatedUserList() {
        apiGetProduceUser({ produceId: this.produceId }).then((res) => {
            this.allocatedUserList = res.data.data || [];
        });
    }
    private async getCommentList() {
        await apiGetCommentList({ produceId: this.produceId }).then((res) => {
            this.commentList = res.data.data || [];
        });
    }
    private async getRelatedFileList() {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        form.append("businessType", "OTHER");
        await apiGetFileList(form).then((res) => {
            this.relatedFileList = res.data.data || [];
        });
    }
    private async getReceiptList() {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        await apiGetReceiptList(form).then((res) => {
            this.receiptList = res.data.data || [];
        });
    }

    private onDownload(fileId: string) {
        const downloadUrl = "/api/file/download?fileId=" + fileId;
        window.open(downloadUrl);
    }
}
</script>
<style lang="scss">
.dropdown-upload .el-upload:focus {
    color: white !important;
}
</style>
<style lang="scss" scoped>
.dropdown-active {
    color: #3370ff;
}
.file-list {
    width: 50%;
    cursor: pointer;
    margin-top: 10px;
    .file-item {
        height: 26px;
        line-height: 26px;
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        .file-name:hover {
            color: #3370ff;
        }
        .file-delete-icon {
            display: none;
        }
        &:hover {
            .file-delete-icon {
                line-height: 26px;
                margin-right: 10px;
                display: inline !important;
            }
            background: #fafafa;
        }
    }
}
.comments-container {
    margin-top: 16px;
    width: 100%;
    word-break: normal;
    max-height: 200px;
    overflow: auto;
    &::-webkit-scrollbar {
        width: 5px;
        height: 8px;
        z-index: 999;
        position: fixed;
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background: rgb(204, 204, 204);
        width: 6px;
    }
    &::-webkit-scrollbar-track {
        background: rgb(226, 226, 226);
    }
    .comment-content {
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        margin-bottom: 16px;
        &:hover {
            .right {
                .username {
                    .delete {
                        opacity: 1;
                        color: rgba(3, 14, 44, 0.65);
                        text-decoration: underline;
                    }
                }
            }
        }
        .left {
            margin-right: 16px;
            .avatar {
                width: 40px !important;
                height: 40px !important;
                // background-color: #cccccc !important;
                background-color: rgba(128, 128, 128, 0.3) !important;
                color: rgba(128, 128, 128, 1) !important;
            }
        }
        .right {
            width: 97%;
            .username {
                color: rgba(3, 14, 44, 0.85);
                font-size: 14px;
                margin-bottom: 5px;
                .delete {
                    margin-left: 5px;
                    cursor: pointer;
                    opacity: 0;
                    color: rgba(3, 14, 44, 0.85);
                }
            }
            .comment {
                margin-bottom: 5px;
                font-size: 14px;
                font-weight: 500;
                color: rgba(3, 14, 44, 0.85);
                user-select: text;
            }
            .date {
                font-weight: 400;
                // color: rgba(3, 14, 44, 0.45);
                font-size: 14px;
                text-align: right;
            }
        }
    }
}
</style>
