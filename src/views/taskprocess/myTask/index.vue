<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">我的任务</div>
            <div style="position:relative;">
                <el-form :model="form" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="任务状态:">
                                <el-select
                                    v-model="form.produceStatus"
                                    style="width:90%"
                                    @change="getMyTaskList(1)"
                                    clearable
                                >
                                    <el-option
                                        v-for="i in 5"
                                        :key="i"
                                        :label="EnumProduceStatus[i - 1]"
                                        :value="i - 1"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="任务级别:">
                                <el-select
                                    v-model="form.priority"
                                    style="width:90%"
                                    @change="getMyTaskList(1)"
                                    clearable
                                >
                                    <el-option
                                        v-for="i in 3"
                                        :label="EnumPriority[i - 1]"
                                        :key="i"
                                        :value="i - 1"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="6">
                            <el-button type="primary" @click="onSearch">
                                查询
                            </el-button>
                        </el-col> -->
                    </el-row>
                </el-form>
                <div class="simple-line" style="margin-top:0"></div>
                <el-table
                    :data="tableData"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    stripe
                >
                    <el-table-column prop="produceCode" label="任务编号">
                    </el-table-column>
                    <el-table-column prop="produceName" label="任务名称">
                    </el-table-column>
                    <el-table-column prop="planStartDate" label="计划开始时间">
                    </el-table-column>
                    <el-table-column prop="planEndDate" label="计划结束时间">
                    </el-table-column>
                    <el-table-column label="计划状态"
                        ><template slot-scope="scope">
                            {{
                                EnumProduceStatus[
                                    scope.row.projectProduceStatus
                                ]
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column label="任务状态">
                        <template slot-scope="scope">
                            {{ EnumProduceStatus[scope.row.produceStatus] }}
                        </template>
                    </el-table-column>
                    <el-table-column label="任务级别"
                        ><template slot-scope="scope">
                            {{ EnumPriority[scope.row.priority] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="date" label="操作" width="400">
                        <template slot-scope="scope">
                            <el-popover
                                placement="top"
                                :ref="`popover-1-${scope.$index}`"
                            >
                                <p>确认接收？</p>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        size="mini"
                                        type="text"
                                        @click="
                                            $refs[
                                                `popover-1-${scope.$index}`
                                            ].doClose()
                                        "
                                    >
                                        取消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="onUpdateStatus(scope, 1)"
                                    >
                                        确定
                                    </el-button>
                                </div>
                                <el-button
                                    :disabled="scope.row.produceStatus != 0"
                                    slot="reference"
                                    type="text"
                                    style="margin-right:10px"
                                >
                                    接收任务
                                </el-button>
                            </el-popover>
                            <router-link
                                :to="
                                    `/taskprocess/process?produceId=${scope.row.produceId}&ancestor=${scope.row.ancestors}`
                                "
                            >
                                <el-button type="text">
                                    任务分解
                                </el-button>
                            </router-link>

                            <el-popover
                                :key="`reject-popover-${scope.$index}`"
                                placement="top"
                                :ref="`popover-3-${scope.$index}`"
                            >
                                <p>
                                    确认
                                    {{
                                        scope.row.produceStatus != 3
                                            ? "拒收"
                                            : "撤销拒收"
                                    }}
                                    ？
                                </p>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        size="mini"
                                        type="text"
                                        @click="
                                            $refs[
                                                `popover-3-${scope.$index}`
                                            ].doClose()
                                        "
                                    >
                                        取消
                                    </el-button>
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="
                                            onUpdateStatus(
                                                scope,
                                                scope.row.produceStatus != 3
                                                    ? 3
                                                    : 999
                                            )
                                        "
                                    >
                                        确定
                                    </el-button>
                                </div>
                                <el-button
                                    :key="`reject-${scope.$index}`"
                                    slot="reference"
                                    type="text"
                                    style="margin:0 10px"
                                    :disabled="!!scope.row.progress"
                                >
                                    {{
                                        scope.row.produceStatus != 3
                                            ? "拒收"
                                            : "撤销拒收"
                                    }}
                                </el-button>
                            </el-popover>
                            <el-button type="text" @click="onDesc(scope)">
                                计划说明
                            </el-button>
                            <el-button type="text" @click="onDetail(scope)">
                                任务详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="10"
                    @current-change="onCurrentChange"
                    :current-page="currentPage"
                ></el-pagination>
            </div>
        </div>
        <project-detail-dialog
            :userTreeList="userTreeList"
            :isDisableEdit="isDisableEdit"
            @updateSuccess="updateSuccess"
            ref="projectDetailDialog"
        ></project-detail-dialog>
        <task-detail-dialog
            :userTreeList="userTreeList"
            :isDisableEdit="isDisableEdit"
            ref="taskDetailDialog"
            @updateSuccess="updateSuccess"
        ></task-detail-dialog>
        <task-detail ref="taskDetail"></task-detail>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { EnumPriority, EnumProduceStatus } from "../enum";
// import TableDialog from "./dialog.vue";
import { Popover as ElPopover } from "element-ui";
import {
    apiGetMyTask,
    apiUpdateMyTaskStatus,
} from "@/api/process";
import { UserModule } from "@/store/modules/user";
import ProjectDetailDialog from "./projectdetaildialog.vue";
import TaskDetailDialog from "./taskdetaildialog.vue";
import { validateDisableTaskEdit } from "../validate";
import TaskDetail from "./taskdetail.vue";
import { ConstantModule } from "@/store/modules/constant";
@Component({
    components: { ProjectDetailDialog, TaskDetailDialog, TaskDetail },
})
export default class extends Vue {
    private EnumPriority = EnumPriority;
    private EnumProduceStatus = EnumProduceStatus;
    private userTreeList: any[] = [];
    private tableData: any = [];
    private form: any = {};
    private total = 1;
    private currentPage = 1;
    private isDisableEdit = true;

    private onDetail(scope: any) {
        if (validateDisableTaskEdit(scope.row.projectProduceStatus)) {
            (this.$refs.taskDetail as any).showDialog(scope);
        } else {
            (this.$refs.taskDetailDialog as any).showDialog(scope);
        }
    }
    private onDesc(scope: any) {
        this.isDisableEdit =
            scope.row.produceStatus == 4 || scope.row.produceStatus == 5;
        (this.$refs.projectDetailDialog as any).showDialog(scope);
    }
    private async mounted() {
        this.getMyTaskList();
        this.userTreeList = await ConstantModule.getUserTreeList();
    }
    private updateSuccess() {
        this.getMyTaskList();
    }
    private async getMyTaskList(current = this.currentPage || 1) {
        console.log(UserModule.userId);
        await apiGetMyTask(
            { current, size: 10 },
            {
                director: UserModule.userId,
                produceStatus: this.form.produceStatus,
                priority: this.form.priority,
            }
        ).then((res) => {
            const data = res.data.data;
            this.total = data.total;
            this.tableData = data.records;
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getMyTaskList();
    }
    private onUpdateStatus(scope: any, status: number) {
        apiUpdateMyTaskStatus({
            produceId: scope.row.produceId,
            produceStatus: status == 999 ? 0 : status,
        }).then(() => {
            this.getMyTaskList();
            (this.$refs[
                `popover-${status == 999 ? 3 : status}-${scope.$index}`
            ] as ElPopover).doClose();
        });
    }
    private onDefuse(scope: any) {
        this.$router.push(
            `/taskprocess/process?produceId=${scope.row.produceId}&ancestor=${scope.row.ancestors}`
        );
    }
}
</script>
