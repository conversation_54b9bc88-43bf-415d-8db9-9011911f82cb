<template>
    <el-dialog title="详情" :visible.sync="isDialogVisible">
        <el-form :model="form" label-width="100px">
            <el-row :gutter="20" v-if="editType !== 'ADD'">
                <el-col :span="12">
                    <el-form-item label="计划编号">
                        <el-input
                            disabled
                            style="width:90%"
                            v-model="form.produceCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item required label="项目名称">
                        <el-input
                            style="width:90%"
                            v-model="form.produceName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="项目属性">
                        <el-input
                            style="width:90%"
                            v-model="form.produceType"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="优先级">
                        <el-select style="width:90%" v-model="form.priority">
                            <el-option
                                v-for="item in 3"
                                :key="item"
                                :label="EnumPriority[item - 1]"
                                :value="item - 1"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="责任人">
                        <el-cascader
                            :show-all-levels="false"
                            style="width:90%"
                            v-model="form.director"
                            :options="userTreeList"
                            :props="{
                                value: 'id',
                                label: 'name',
                                emitPath: false,
                            }"
                        ></el-cascader>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="相关单据">
                        <el-input
                            style="width:90%"
                            v-model="form.receiptCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <!-- <el-col style="margin-left:-4%" :span="12">
                    <el-button type="primary" size="small">相关单据</el-button>
                </el-col> -->
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="计划开始时间">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.planStartDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划结束时间">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.planEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际开始时间">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.actualStartDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际结束时间">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.actualEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12" v-if="editType == 'ADD'">
                    <el-form-item label="生产模板">
                        <el-select style="width:90%" v-model="form.templateId">
                            <el-option
                                v-for="item in templateList"
                                :key="item.templateId"
                                :label="item.templateName"
                                :value="item.templateId"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button
                type="primary"
                :disabled="isDisableEdit"
                @click="onConfirm"
                >确认</el-button
            >
            <el-button @click="onCancel">取消</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { EnumProduceStatus, EnumPriority } from "../enum";
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiAddProductionTask, apiUpdateProductionTask } from "@/api/process";
import { validateItemList } from "./validateItem";
import { ConstantModule } from "@/store/modules/constant";
@Component({})
export default class extends Vue {
    private EnumProduceStatus = EnumProduceStatus;
    private EnumPriority = EnumPriority;
    private isDialogVisible = false;
    private form: any = {};
    private activeTab = "first";
    private editType = "ADD";
    private userTreeList: any[] = [];
    private isDisableEdit = true;
    @Prop({ default: () => [] }) private templateList!: any[];
    private async showDialog(editType: string, scope: any) {
        this.editType = editType;
        const isEdit = editType == "EDIT";
        this.form = isEdit ? { ...scope.row } : {};
        this.isDisableEdit =
            isEdit &&
            (scope.row.produceStatus == 4 || scope.row.produceStatus == 5);
        this.userTreeList = await ConstantModule.getUserTreeList();
        this.isDialogVisible = true;
    }
    private onConfirm() {
        if (
            !validateItemList.every(
                (item) => this.form[item] || this.form[item] == 0
            )
        ) {
            this.$message.error("有必填项未填写");
            return;
        }
        if (this.editType === "ADD") {
            this.form.parentId = -1;
            apiAddProductionTask(this.form).then(() => {
                this.$emit("getProductionTaskList");
                this.isDialogVisible = false;
            });
        } else {
            apiUpdateProductionTask(this.form).then(() => {
                this.$emit("getProductionTaskList");
                this.isDialogVisible = false;
            });
        }
    }

    private onCancel() {
        this.isDialogVisible = false;
    }
}
</script>
