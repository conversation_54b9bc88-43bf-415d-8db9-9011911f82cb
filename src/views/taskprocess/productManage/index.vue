<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">计划管理</div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="100px">
                    <el-col :span="6">
                        <el-form-item
                            label-width="80px"
                            label="计划编号"
                            size="normal"
                        >
                            <el-autocomplete
                                style="width:100%"
                                clearable
                                v-model="form.produceCode"
                                :fetch-suggestions="querySearchProduceCode"
                                placeholder="请输入内容"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="6">
                        <el-form-item label="生产属性" size="normal">
                            <el-select v-model="produceType" style="width:100%">
                                <el-option
                                    v-for="item in projectAttr"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="6">
                        <el-form-item label="优先级" size="normal">
                            <el-select
                                clearable
                                v-model="form.priority"
                                style="width:100%"
                            >
                                <el-option
                                    v-for="item in 3"
                                    :key="item"
                                    :label="EnumPriority[item - 1]"
                                    :value="item - 1"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="" size="normal" label-width="0">
                            <el-button
                                icon="el-icon-search"
                                type="primary"
                                @click="onSearch"
                            >
                                查询
                            </el-button>
                            <el-button
                                icon="el-icon-plus"
                                type="primary"
                                @click="onAdd"
                            >
                                新增
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <div class="simple-line" style="margin-top:0"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    prop="produceCode"
                    width="200"
                    label="计划编号"
                >
                </el-table-column>
                <el-table-column prop="produceName" label="计划名称">
                </el-table-column>
                <el-table-column prop="priority" label="优先级">
                    <template slot-scope="scope">
                        {{ EnumPriority[scope.row.priority] }}
                    </template>
                </el-table-column>
                <el-table-column prop="produceType" label="生产属性">
                </el-table-column>
                <el-table-column
                    prop="directorName"
                    label="负责人"
                ></el-table-column>
                <el-table-column label="进度">
                    <template slot-scope="scope">
                        <el-progress
                            :text-inside="true"
                            :stroke-width="16"
                            :percentage="Number(scope.row.progress || 0)"
                        ></el-progress>
                    </template>
                </el-table-column>
                <el-table-column prop="produceStatus" label="生产状态">
                    <template slot-scope="scope">
                        {{ EnumProduceStatus[scope.row.produceStatus] }}
                    </template>
                </el-table-column>
                <el-table-column prop="receiptCode" label="单据编号">
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-popover
                            placement="top"
                            :ref="`popover-5-${scope.$index}`"
                        >
                            <p>确认归档？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-5-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onUpdateStatus(scope, 5)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button
                                :disabled="scope.row.produceStatus == 5"
                                slot="reference"
                                type="text"
                                style="margin-right:10px"
                            >
                                归档
                            </el-button>
                        </el-popover>
                        <el-popover
                            placement="top"
                            :ref="`popover-4-${scope.$index}`"
                        >
                            <p>确认终止？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-4-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onUpdateStatus(scope, 4)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button
                                :disabled="scope.row.produceStatus == 4"
                                slot="reference"
                                type="text"
                                style="margin-right:10px"
                            >
                                终止
                            </el-button>
                        </el-popover>
                        <el-button
                            type="text"
                            style="margin-right:10px"
                            @click="onDetail(scope)"
                        >
                            详情
                        </el-button>
                        <el-popover
                            placement="top"
                            :ref="`popover-${scope.$index}`"
                        >
                            <p>确认删除？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onDelete(scope)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button
                                :disabled="scope.row.produceStatus == 5"
                                slot="reference"
                                type="text"
                            >
                                删除
                            </el-button>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top:20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <table-dialog
            :templateList="templateList"
            @getProductionTaskList="getProductionTaskList"
            :priorityList="priorityList"
            ref="tableDialog"
        ></table-dialog>
        <table-detail
            :templateList="templateList"
            :priorityList="priorityList"
            ref="tableDetail"
        ></table-detail>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import TableDialog from "./dialog.vue";
import TableDetail from "./detail.vue";
import { Popover as ElPopover } from "element-ui";
import {
    EnumProduceStatus,
    EnumPriority,
    priorityList,
    projectAttr,
} from "../enum";
import {
    apiGetProductionTaskListByCondition,
    apiGetTaskTemplateList,
    apiDeleteProductionTask,
    apiGetProductionTaskFuzzyList,
    apiUpdateMyTaskStatus,
} from "@/api/process";
import { validateDisableTaskEdit } from "../validate";
@Component({ components: { TableDialog, TableDetail } })
export default class extends Vue {
    private form: any = {};
    private EnumProduceStatus = EnumProduceStatus;
    private EnumPriority = EnumPriority;
    private tableData = [];
    private priorityList = priorityList;
    private projectAttr = projectAttr;
    private templateList: any[] = [];
    private priority: number | null = null;
    private produceType = "";
    private produceCode = "";
    private total = 1;
    private currentPage = 1;
    private mounted() {
        this.getProductionTaskList();
        this.getTemplateList();
    }
    private onDetail(scope: any) {
        if (validateDisableTaskEdit(scope.row.produceStatus)) {
            (this.$refs.tableDetail as any).showDialog(scope);
        } else {
            (this.$refs.tableDialog as any).showDialog("EDIT", scope);
        }
    }
    private onAdd() {
        (this.$refs.tableDialog as any).showDialog("ADD");
    }
    private async getProductionTaskList(
        condition: any = this.form,
        cs: any = { current: this.currentPage, size: 10 }
    ) {
        await apiGetProductionTaskListByCondition(condition, cs).then((res) => {
            this.total = res.data.data.total;
            this.tableData = res.data.data.records;
        });
    }
    private getTemplateList() {
        apiGetTaskTemplateList({}).then((res) => {
            this.templateList = res.data.data;
        });
    }
    private onDelete(scope: any) {
        apiDeleteProductionTask({ produceId: scope.row.produceId }).then(() => {
            (this.$refs[`popover-${scope.$index}`] as ElPopover).doClose();
            this.getProductionTaskList();
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getProductionTaskList();
    }
    private onSearch() {
        this.currentPage = 1;
        this.getProductionTaskList(this.form);
    }
    private querySearchProduceCode(queryString: string, cb: any) {
        apiGetProductionTaskFuzzyList({ produceCode: queryString }).then(
            (res) => {
                cb(
                    res.data.data.map((item: any) => {
                        return { value: item.produceCode };
                    })
                );
            }
        );
    }
    private onUpdateStatus(scope: any, status: number) {
        apiUpdateMyTaskStatus({
            produceId: scope.row.produceId,
            produceStatus: status,
        }).then(() => {
            this.getProductionTaskList();
            (this.$refs[
                `popover-${status}-${scope.$index}`
            ] as ElPopover).doClose();
        });
    }
}
</script>
