<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">进度管理</div>
            <div
                class="process-container"
                style="display:flex;height:calc(100% - 40px)"
            >
                <div
                    class="left"
                    style="flex:160px 0 0;border-right:1px solid #dee0e3;height:100%"
                >
                    <div
                        class="list"
                        v-for="item in productionTaskList"
                        :key="item.produceId"
                    >
                        <div
                            class="list-item"
                            :class="{ active: item.produceId === produceId }"
                            @click="onClickTask(item.produceId)"
                        >
                            <span :title="item.produceName">
                                {{ item.produceName }}
                            </span>
                        </div>
                    </div>
                </div>

                <div style="flex:1 0 0;padding-left:20px" v-if="produceId > 0">
                    <el-button
                        icon="el-icon-plus"
                        @click="onAdd(produceId)"
                        style="margin-bottom:10px"
                        type="primary"
                    >
                        添加子任务
                    </el-button>
                    <el-table
                        :data="tableData"
                        :header-cell-style="commmonTableHeaderCellStyle"
                        stripe
                        :tree-props="{
                            children: 'childList',
                        }"
                        row-key="produceId"
                        :expand-row-keys="defaultExpandRowKeys"
                        :row-class-name="tableRowClassName"
                        ref="table"
                    >
                        <el-table-column
                            prop="produceName"
                            label="任务名称"
                        ></el-table-column>
                        <el-table-column
                            prop="planStartDate"
                            label="计划开始日期"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="planEndDate"
                            label="计划结束日期"
                        >
                        </el-table-column>
                        <el-table-column prop="directorName" label="责任人">
                        </el-table-column>
                        <el-table-column
                            prop="progress"
                            label="进度"
                            width="200"
                        >
                            <template slot-scope="scope">
                                <el-progress
                                    :text-inside="true"
                                    :stroke-width="16"
                                    :percentage="
                                        Number(scope.row.progress || 0)
                                    "
                                ></el-progress>
                            </template>
                        </el-table-column>
                        <el-table-column prop="priority" label="优先级">
                            <template slot-scope="scope">
                                {{ EnumPriority[scope.row.priority] }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="320">
                            <template slot-scope="scope">
                                <el-button type="text" @click="onDetail(scope)"
                                    >详情</el-button
                                >
                                <el-button
                                    :disabled="isDisableEdit"
                                    type="text"
                                    @click="onAddBatch(scope)"
                                    >批量添加子节点</el-button
                                >
                                <el-button
                                    :disabled="isDisableEdit"
                                    type="text"
                                    style="margin-right:10px"
                                    @click="onAdd(scope)"
                                    >添加子节点</el-button
                                >
                                <el-popover
                                    placement="top"
                                    :ref="`popover-${scope.$index}`"
                                >
                                    <p>确认删除？</p>
                                    <div style="text-align: right; margin: 0">
                                        <el-button
                                            size="mini"
                                            type="text"
                                            @click="
                                                $refs[
                                                    `popover-${scope.$index}`
                                                ].doClose()
                                            "
                                        >
                                            取消
                                        </el-button>
                                        <el-button
                                            type="primary"
                                            size="mini"
                                            @click="onDelete(scope)"
                                        >
                                            确定
                                        </el-button>
                                    </div>
                                    <el-button
                                        :disabled="isDisableEdit"
                                        slot="reference"
                                        type="text"
                                    >
                                        删除节点
                                    </el-button>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <table-detail ref="tableDetail"></table-detail>
        <table-dialog @addNodeCB="addNodeCB" ref="tableDialog"></table-dialog>
        <tree-table-dialog
            @batchAddNodeCB="batchAddNodeCB"
            ref="treeTableDialog"
        ></tree-table-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import TableDialog from "./dialog.vue";
import TableDetail from "./detail.vue";
import TreeTableDialog from "./InlineEditTreeTable.vue";
import {
    apiGetProductionTaskListByCondition,
    apiGetTaskTemplateList,
    apiGetProductionTaskInfo,
    apiDeleteProductionTask,
} from "@/api/process";
import { EnumPriority } from "../enum";
import { validateDisableTaskEdit } from "../validate";
@Component({ components: { TableDialog, TreeTableDialog, TableDetail } })
export default class extends Vue {
    private isDisableEdit = true;
    private productionTaskList: any[] = [];
    private defaultExpandRowKeys: any[] = [];
    private tableData = [];
    private EnumPriority = EnumPriority;
    private produceId: number = -1;
    private ancestors: number = -1;
    private async mounted() {
        this.getProductionTaskList();
        let produceId = this.$route.query.produceId as string;
        let ancestor = this.$route.query.ancestor;
        if (produceId) {
            this.produceId = Number(ancestor);
            await this.getTaskInfo(this.produceId);
            this.defaultExpandRowKeys = this.getDefaultExpandRowKeys(
                this.tableData,
                Number(produceId)
            );
            this.$router.replace("/taskprocess/process");
        }
    }
    private getDefaultExpandRowKeys(data: any[], produceId: number) {
        // 递归获取目标节点的produceId路径
        this.defaultExpandRowKeys = [];
        let temp: number[][] = [];
        // 是否找到目标节点
        let flag = false;
        function getExpandRowKeys(childList: any[]) {
            let temp_inner: number[] = [];
            for (let i = 0, len = childList.length; i < len; i++) {
                // 如果到达找到目标节点，开始break每层的循环
                if (flag) {
                    break;
                }
                temp_inner.push(childList[i].produceId);
                // 找到目标id
                if (childList[i].produceId == produceId) {
                    flag = true;
                    break;
                }
                if (childList[i].childList && !flag) {
                    getExpandRowKeys(childList[i].childList);
                }
            }
            if (flag) {
                temp.push(temp_inner);
            }
        }
        getExpandRowKeys(data);
        let produceIdPath: string[] = [];
        temp.forEach((item, idx) => {
            produceIdPath.push(String(item[item.length - 1]));
        });
        return produceIdPath.reverse();
    }
    private async getProductionTaskList(
        condition: any = {},
        cs: any = { current: 1, size: 100000 }
    ) {
        await apiGetProductionTaskListByCondition(condition, cs).then((res) => {
            this.productionTaskList = res.data.data.records;
        });
    }
    private tableRowClassName({ row }: any) {
        if (
            row.produceId ==
            this.defaultExpandRowKeys[this.defaultExpandRowKeys.length - 1]
        ) {
            return "hightlight-row";
        }
        return "";
    }

    private onClickTask(produceId: number = this.produceId) {
        this.produceId = produceId;
        this.getTaskInfo();
    }
    private async getTaskInfo(produceId: number = this.produceId) {
        await apiGetProductionTaskInfo({ produceId }).then((res) => {
            const data = res.data.data;
            this.tableData = data.childList;
            this.ancestors = data.produceId;
            this.isDisableEdit = validateDisableTaskEdit(data.produceStatus);
        });
    }

    private addNodeCB(scope: any) {
        this.getTaskInfo();
    }
    private batchAddNodeCB(scope: any) {
        this.getTaskInfo();
    }
    // private async getProductionTaskInfo(produceId: number) {
    //     await apiGetProductionTaskInfo({ produceId }).then((res) => {
    //         const data = res.data.data;
    //         this.tableData = data.childList;
    //         this.ancestors = data.produceId;
    //     });
    // }
    private onDetail(scope: any) {
        if (this.isDisableEdit) {
            (this.$refs.tableDetail as any).showDialog(
                scope.row.produceId,
                scope.row
            );
        } else {
            (this.$refs.tableDialog as any).showDialog(
                scope.row.produceId,
                scope.row,
                "EDIT"
            );
        }
    }
    private onAdd(scope: any) {
        let produceId: any;
        if (typeof scope === "number") {
            produceId = scope;
        } else {
            produceId = scope.row.produceId;
        }
        (this.$refs.tableDialog as any).showDialog(produceId, {}, "ADD");
    }
    private onAddBatch(scope: any) {
        (this.$refs.treeTableDialog as any).showDialog(scope.row.produceId);
    }
    private onDelete(scope: any) {
        apiDeleteProductionTask({ produceId: scope.row.produceId }).then(() => {
            this.getTaskInfo();
        });
    }
}
</script>
<style lang="scss">
.el-table .hightlight-row {
    background: oldlace;
    td {
        background: inherit !important;
    }
}
</style>
<style lang="scss" scoped>
.list {
    cursor: pointer;
    font-size: 14px;
    padding-right: 20px;
    .list-item {
        box-sizing: border-box;
        padding: 5px 2px;
        line-height: 20px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        span {
            width: 140px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .list-item:hover {
        background-color: #eff0f1;
    }

    .active {
        background-color: #ebf3ff;
        color: #37f;
    }
}
</style>
