<template>
    <el-dialog
        top="5vh"
        :title="editType === 'ADD' ? `添加` : `详情`"
        :visible.sync="isDialogVisible"
    >
        <el-form :model="form" label-width="80px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item required label="任务名称">
                        <el-input
                            style="width:90%"
                            v-model="form.produceName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="任务编码">
                        <el-input
                            disabled
                            style="width:90%"
                            v-model="form.produceCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划开始">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.planStartDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计划结束">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.planEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际开始">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.actualStartDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际结束">
                        <el-date-picker
                            style="width:90%"
                            v-model="form.actualEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="责任人">
                        <el-cascader
                            :show-all-levels="false"
                            style="width:90%"
                            v-model="form.director"
                            :options="userTreeList"
                            :props="{
                                value: 'id',
                                label: 'name',
                                emitPath: false,
                            }"
                        ></el-cascader>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="完成率">
                        <el-input
                            :disabled="!!form.leaf || editType === 'ADD'"
                            style="width:90%"
                            v-model="form.progress"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item required label="任务级别">
                        <el-select style="width:90%" v-model="form.priority">
                            <el-option
                                v-for="item in 3"
                                :key="item"
                                :label="EnumPriority[item - 1]"
                                :value="item - 1"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="任务状态">
                        <el-input
                            style="width:90%"
                            disabled
                            v-model="EnumProduceStatus[form.produceStatus]"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="任务描述">
                        <el-input
                            style="width:96%"
                            type="textarea"
                            v-model="form.produceDesc"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-form v-if="editType === 'EDIT'" :model="logForm">
            <el-row>
                <el-col :span="24" style="padding:0 40px 0 20px">
                    <el-tabs v-model="activeTab">
                        <el-tab-pane label="日志" name="first">
                            <el-form-item label-width="0">
                                <el-input
                                    style="width:88%;margin-right:10px"
                                    type="textarea"
                                    v-model="logForm.comment"
                                ></el-input>
                                <el-button type="primary" @click="onSubmitLog">
                                    提交
                                </el-button>
                            </el-form-item>
                            <div class="comments-container">
                                <div
                                    class="comment-content"
                                    v-for="comment in commentList"
                                    :key="comment.id"
                                >
                                    <div class="right">
                                        <div class="comment">
                                            {{ comment.comment }}
                                        </div>
                                        <div class="date">
                                            {{ comment.userName }}
                                            {{
                                                new Date(
                                                    comment.createTime
                                                ).Format("yyyy-MM-dd hh:mm:ss")
                                            }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="相关附件" name="second">
                            <el-upload
                                :show-file-list="false"
                                action="string"
                                :before-upload="validateRelatedFile"
                            >
                                <el-button type="primary">上传附件</el-button>
                            </el-upload>
                            <div class="file-list">
                                <div
                                    class="file-item"
                                    v-for="file in relatedFileList"
                                    :key="file.fileId"
                                >
                                    <span
                                        @click="onDownload(file.fileId)"
                                        class="file-name"
                                    >
                                        <i class="el-icon-document"></i>
                                        {{ file.fileName }}
                                    </span>
                                    <i
                                        @click="
                                            onDeleteRelatedFile(file.fileId)
                                        "
                                        class="el-icon-close file-delete-icon"
                                    ></i>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="相关单据" name="third">
                            <el-dropdown split-button type="primary">
                                <el-upload
                                    :show-file-list="false"
                                    action="string"
                                    :before-upload="validateReceiptFile"
                                    class="dropdown-upload"
                                >
                                    上传{{ RECEIPT_TYPE_MAP[receiptType] }}单据
                                </el-upload>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item
                                        v-for="(item, key) in RECEIPT_TYPE_MAP"
                                        :key="key"
                                        :class="{
                                            'dropdown-active':
                                                receiptType == key,
                                        }"
                                        @click.native="receiptType = key"
                                    >
                                        {{ RECEIPT_TYPE_MAP[key] }}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <el-table
                                v-if="receiptList.length"
                                :data="receiptList"
                            >
                                <el-table-column
                                    prop="receiptType"
                                    label="单据类型"
                                >
                                    <template slot-scope="scope">{{
                                        RECEIPT_TYPE_MAP[scope.row.receiptType]
                                    }}</template>
                                </el-table-column>
                                <el-table-column
                                    prop="fileInfoList"
                                    label="文件列表"
                                >
                                    <template slot-scope="scope">
                                        <div
                                            class="file-list"
                                            style="width:100%"
                                        >
                                            <div
                                                class="file-item"
                                                v-for="file in scope.row
                                                    .fileInfoList"
                                                :key="file.fileId"
                                            >
                                                <span
                                                    @click="
                                                        onDownload(file.fileId)
                                                    "
                                                    class="file-name"
                                                >
                                                    <i
                                                        class="el-icon-document"
                                                    ></i>
                                                    {{ file.fileName }}
                                                </span>
                                                <i
                                                    @click="
                                                        onDeleteReceiptFile(
                                                            file.fileId
                                                        )
                                                    "
                                                    class="el-icon-close file-delete-icon"
                                                ></i>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                        <el-tab-pane label="人员分配" name="fourth">
                            <el-button @click="onAddRow" type="primary">
                                添加人员
                            </el-button>
                            <el-table :data="allocatedUserList">
                                <el-table-column prop="name" label="人员">
                                    <template slot-scope="scope">
                                        <span
                                            v-if="
                                                editObject.editRowIndex !==
                                                    scope.$index ||
                                                    Object.keys(preObject)
                                                        .length
                                            "
                                        >
                                            {{ scope.row.name }}
                                        </span>
                                        <el-cascader
                                            v-else
                                            :show-all-levels="false"
                                            style="width:90%"
                                            v-model="scope.row.userId"
                                            :options="userTreeList"
                                            :props="{
                                                value: 'id',
                                                label: 'name',
                                                emitPath: false,
                                            }"
                                        ></el-cascader>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="hours" label="工时">
                                    <template slot-scope="scope">
                                        <span
                                            v-if="
                                                editObject.editRowIndex !==
                                                    scope.$index
                                            "
                                        >
                                            {{ scope.row.hours }}
                                        </span>
                                        <el-input
                                            v-else
                                            v-model="scope.row.hours"
                                        ></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="180">
                                    <template slot-scope="scope">
                                        <span
                                            v-if="
                                                editObject.editRowIndex !==
                                                    scope.$index
                                            "
                                        >
                                            <el-button
                                                type="text"
                                                @click="onEditRow(scope)"
                                            >
                                                编辑
                                            </el-button>
                                            <el-button
                                                type="text"
                                                @click="onDeleteRow(scope)"
                                            >
                                                删除
                                            </el-button>
                                        </span>
                                        <span v-else>
                                            <el-button
                                                type="text"
                                                @click="onSaveRow(scope)"
                                            >
                                                保存
                                            </el-button>
                                            <el-button
                                                type="text"
                                                @click="onCancelRow(scope)"
                                            >
                                                取消
                                            </el-button>
                                        </span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </el-col>
            </el-row>
        </el-form>
        <template slot="footer">
            <el-button type="primary" @click="onConfirm">
                确认
            </el-button>
            <el-button @click="onCancel">取消</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import {
    EnumPriority,
    EnumProduceStatus,
    RECEIPT_TYPE_MAP,
    ADD_TYPE_MAP,
} from "../enum";
import {
    apiAddProductionTask,
    apiUpdateProductionTask,
    apiAddComment,
    apiGetCommentList,
    apiDeleteFile,
    apiGetFileList,
    apiAddFile,
    apiGetReceiptList,
    apiGetTaskTemplateList,
    apiGetProduceUser,
    apiDeleteProduceUser,
    apiAddProduceUser,
    apiUpdateProduceUser,
} from "@/api/process";
import { Popover as ElPopover } from "element-ui";
import { UserModule } from "@/store/modules/user";
import { validateItemList } from "./validateItem";
import { ConstantModule } from "@/store/modules/constant";
@Component({})
export default class extends Vue {
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject = {};
    private allocatedUserId = "";
    private allocatedUserList: any[] = [];
    private userTreeList: any[] = [];
    private templateList: any[] = [];
    private ADD_TYPE_MAP = ADD_TYPE_MAP;
    private addType = "SINGLE"; // SINGLE | BATCH
    private receiptType = "MATERIAL_REQUEST";
    private EnumPriority = EnumPriority;
    private EnumProduceStatus = EnumProduceStatus;
    private RECEIPT_TYPE_MAP = RECEIPT_TYPE_MAP;
    private isDialogVisible = false;
    private commentList: any[] = [];
    private form: any = { comment: "" };
    private logForm: any = { comment: "" };
    private editType = "ADD";
    private activeTab = "first";
    private relatedFileList: any[] = [];
    private receiptList: any[] = [];
    private async showDialog(produceId: number, row: any, editType: string) {
        this.logForm.comment = "";
        this.editType = editType;
        this.form = { ...row };
        this.produceId = produceId;
        this.userTreeList = await ConstantModule.getUserTreeList();
        if (editType === "EDIT") {
            await this.getCommentList();
            await this.getRelatedFileList();
            await this.getReceiptList();
            await this.getAllocatedUserList();
        } else {
            await this.getTemplateList();
        }
        this.isDialogVisible = true;
    }
    private async getAllocatedUserList() {
        apiGetProduceUser({ produceId: this.produceId }).then((res) => {
            this.allocatedUserList = res.data.data || [];
        });
    }
    private onDeleteAllocatedUser(id: number) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiDeleteProduceUser({ id }).then(() => {
                this.getAllocatedUserList();
            });
        });
    }
    private onAllocateUser() {
        if (this.allocatedUserId) {
            apiAddProduceUser({
                produceId: this.produceId,
                userId: this.allocatedUserId,
            }).then(() => {
                (this.$refs[`popover`] as ElPopover).doClose();
                this.getAllocatedUserList();
            });
        }
    }
    private async getCommentList() {
        await apiGetCommentList({ produceId: this.produceId }).then((res) => {
            this.commentList = res.data.data || [];
        });
    }
    private async getTemplateList() {
        apiGetTaskTemplateList({}).then((res) => {
            this.templateList = res.data.data || [];
        });
    }
    private async getRelatedFileList() {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        form.append("businessType", "OTHER");
        await apiGetFileList(form).then((res) => {
            this.relatedFileList = res.data.data || [];
        });
    }
    private async getReceiptList() {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        await apiGetReceiptList(form).then((res) => {
            this.receiptList = res.data.data || [];
        });
    }
    private validateRelatedFile(file: any) {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        form.append("businessType", "OTHER");
        form.append("receiptType", "MATERIAL_REQUEST");
        form.append("file", file);
        apiAddFile(form).then(() => {
            this.getRelatedFileList();
        });
    }
    private validateReceiptFile(file: any) {
        let form = new FormData();
        form.append("produceId", `${this.produceId}`);
        form.append("businessType", "RECEIPT");
        form.append("file", file);
        form.append("receiptType", this.receiptType);
        apiAddFile(form).then(() => {
            this.getReceiptList();
        });
    }
    private onDeleteRelatedFile(fileId: string) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                let form = new FormData();
                form.append("fileId", fileId);
                apiDeleteFile(form).then(() => {
                    this.getRelatedFileList();
                });
            })
            .catch(() => {});
    }
    private onDeleteReceiptFile(fileId: string) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                let form = new FormData();
                form.append("fileId", fileId);
                apiDeleteFile(form).then(() => {
                    this.getReceiptList();
                });
            })
            .catch(() => {});
    }
    private onConfirm() {
        if (
            !validateItemList.every(
                (item) => this.form[item] || this.form[item] === 0
            )
        ) {
            this.$message.error("有必填项未填写");
            return;
        }
        if (this.editType === "EDIT") {
            apiUpdateProductionTask(this.form).then(() => {
                this.isDialogVisible = false;
                this.$emit("addNodeCB");
            });
        } else {
            this.form.parentId = this.produceId;
            apiAddProductionTask(this.form).then(() => {
                this.isDialogVisible = false;
                this.$emit("addNodeCB");
            });
        }
    }
    private onSubmitLog() {
        this.logForm.produceId = this.produceId;
        if (!this.logForm.comment) {
            return;
        }
        this.logForm.userId = UserModule.userId;
        apiAddComment(this.logForm).then(() => {
            this.getCommentList();
            this.logForm.comment = "";
        });
    }
    private onCancel() {
        this.isDialogVisible = false;
    }
    private onDownload(fileId: string) {
        const downloadUrl = "/api/file/download?fileId=" + fileId;
        window.open(downloadUrl);
    }
    private onToggleAddType(addType: string) {
        this.addType = addType;
    }
    private onConfirmAdd() {
        console.log((this.$refs.inlineEditTreeTable as any).childList);
    }

    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.allocatedUserList.push({
            userId: "",
            name: "",
            hours: "",
        });
        this.editObject.editRowIndex = this.allocatedUserList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiDeleteProduceUser({ id: scope.row.id }).then(() => {
                this.getAllocatedUserList();
            });
        });
    }

    private onSaveRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            apiUpdateProduceUser({
                id: scope.row.id,
                hours: scope.row.hours,
            }).then(() => {
                this.getAllocatedUserList();
                this.editObject.editRowIndex = -1;
            });
        } else {
            apiAddProduceUser({
                produceId: this.produceId,
                userId: scope.row.userId,
                hours: scope.row.hours,
            }).then(async () => {
                await this.getAllocatedUserList();
                this.editObject.editRowIndex = -1;
            });
        }
    }

    private onCancelRow(scope: any) {
        this.getAllocatedUserList();
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss">
.dropdown-upload .el-upload:focus {
    color: white !important;
}
</style>
<style lang="scss" scoped>
.dropdown-active {
    color: #3370ff;
}
.file-list {
    width: 50%;
    cursor: pointer;
    margin-top: 10px;
    .file-item {
        height: 26px;
        line-height: 26px;
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        .file-name:hover {
            color: #3370ff;
        }
        .file-delete-icon {
            display: none;
        }
        &:hover {
            .file-delete-icon {
                line-height: 26px;
                margin-right: 10px;
                display: inline !important;
            }
            background: #fafafa;
        }
    }
}
.comments-container {
    margin-top: 16px;
    width: 100%;
    word-break: normal;
    max-height: 200px;
    overflow: auto;
    &::-webkit-scrollbar {
        width: 5px;
        height: 8px;
        z-index: 999;
        position: fixed;
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background: rgb(204, 204, 204);
        width: 6px;
    }
    &::-webkit-scrollbar-track {
        background: rgb(226, 226, 226);
    }
    .comment-content {
        display: flex;
        -webkit-box-pack: start;
        justify-content: flex-start;
        margin-bottom: 16px;
        &:hover {
            .right {
                .username {
                    .delete {
                        opacity: 1;
                        color: rgba(3, 14, 44, 0.65);
                        text-decoration: underline;
                    }
                }
            }
        }
        .left {
            margin-right: 16px;
            .avatar {
                width: 40px !important;
                height: 40px !important;
                // background-color: #cccccc !important;
                background-color: rgba(128, 128, 128, 0.3) !important;
                color: rgba(128, 128, 128, 1) !important;
            }
        }
        .right {
            width: 97%;
            .username {
                color: rgba(3, 14, 44, 0.85);
                font-size: 14px;
                margin-bottom: 5px;
                .delete {
                    margin-left: 5px;
                    cursor: pointer;
                    opacity: 0;
                    color: rgba(3, 14, 44, 0.85);
                }
            }
            .comment {
                margin-bottom: 5px;
                font-size: 14px;
                font-weight: 500;
                color: rgba(3, 14, 44, 0.85);
                user-select: text;
            }
            .date {
                font-weight: 400;
                // color: rgba(3, 14, 44, 0.45);
                font-size: 14px;
                text-align: right;
            }
        }
    }
}
</style>
