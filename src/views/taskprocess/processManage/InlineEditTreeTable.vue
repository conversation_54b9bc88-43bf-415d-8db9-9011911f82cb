<template>
    <el-dialog
        width="80%"
        top="5vh"
        :title="`添加`"
        :visible.sync="isDialogVisible"
    >
        <div>
            <el-select
                @change="onSelectTemplateType"
                placeholder="请选择模板类型"
                v-model="selectedTemplateType"
            >
                <el-option label="任务模板" value="TASK"></el-option>
                <el-option label="工序模板" value="PROCEDURE"></el-option>
            </el-select>
            <el-select
                v-if="selectedTemplateType === 'TASK'"
                @change="onSelectTemplate"
                placeholder="请选择模板"
                v-model="templateId"
            >
                <el-option
                    v-for="item in templateList"
                    :key="item.templateId"
                    :label="item.templateName"
                    :value="item.templateId"
                ></el-option>
            </el-select>
            <el-select
                v-if="selectedTemplateType === 'PROCEDURE'"
                @change="onSelectTemplate"
                placeholder="请选择模板"
                v-model="templateId"
            >
                <el-option
                    v-for="item in templateList"
                    :key="item.processFlowId"
                    :label="item.processFlowName"
                    :value="item.processFlowId"
                ></el-option>
            </el-select>
            <div style="min-height:200px;margin-top:20px">
                <template v-if="templateId > 0">
                    <el-button
                        @click="onAddRow"
                        type="primary"
                        style="margin-top:-10px"
                    >
                        添加主节点
                    </el-button>
                    <el-table
                        :data="childList"
                        :header-cell-style="commmonTableHeaderCellStyle"
                        style="margin-top:10px"
                        row-key="customId"
                        :tree-props="{
                            children: 'childList',
                        }"
                        ref="table"
                        @row-click="onRowClick"
                    >
                        <el-table-column label="编号" width="80">
                            <template slot-scope="scope">
                                <span>
                                    {{ scope.$index + 1 }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column label="名称" prop="produceName">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    {{ scope.row.produceName }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.produceName"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="描述" prop="produceDesc">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    {{ scope.row.produceDesc }}
                                </span>
                                <el-input
                                    v-else
                                    v-model="scope.row.produceDesc"
                                ></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="计划开始时间"
                            prop="planStartDate"
                        >
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    {{ scope.row.planStartDate }}
                                </span>
                                <el-date-picker
                                    v-else
                                    style="width:100%"
                                    v-model="scope.row.planStartDate"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                ></el-date-picker>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="计划结束时间"
                            prop="planEndDate"
                        >
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    {{ scope.row.planEndDate }}
                                </span>
                                <el-date-picker
                                    v-else
                                    style="width:100%"
                                    v-model="scope.row.planEndDate"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                ></el-date-picker>
                            </template>
                        </el-table-column>

                        <el-table-column label="责任人" prop="director">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    {{ scope.row.directorName }}
                                </span>
                                <el-cascader
                                    v-else
                                    @change="onChange(scope)"
                                    :show-all-levels="false"
                                    style="width:90%"
                                    v-model="scope.row.director"
                                    :options="userTreeList"
                                    :props="{
                                        value: 'id',
                                        label: 'name',
                                        emitPath: false,
                                    }"
                                ></el-cascader>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="180">
                            <template slot-scope="scope">
                                <span
                                    v-if="
                                        editObject.editRowIndex !== scope.$index
                                    "
                                >
                                    <el-button
                                        type="text"
                                        @click="onAddChild(scope)"
                                    >
                                        添加子节点
                                    </el-button>
                                    <el-button
                                        type="text"
                                        @click="onEditRow(scope)"
                                    >
                                        编辑
                                    </el-button>
                                    <el-button
                                        type="text"
                                        @click="onDeleteRow(scope)"
                                    >
                                        删除
                                    </el-button>
                                </span>
                                <span v-else>
                                    <el-button
                                        type="text"
                                        @click="onSaveRow(scope)"
                                    >
                                        保存
                                    </el-button>
                                    <el-button
                                        type="text"
                                        @click="onCancelRow(scope)"
                                    >
                                        取消
                                    </el-button>
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
        </div>
        <template slot="footer">
            <el-button type="primary" @click="onConfirm">
                确认
            </el-button>

            <el-button @click="onCancel">取消</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Table as ElTable } from "element-ui";
import {
    apiGetTaskTemplateInfo,
    apiBatchAddProductionTask,
    apiGetTaskTemplateList,
    apiGetProcessList,
    apiGetProcessInfo,
} from "@/api/process";
import { ADD_TYPE_MAP } from "../enum";
import { ConstantModule } from "@/store/modules/constant";
@Component({})
export default class extends Vue {
    private selectedTemplateType = "";
    private userTreeList: any[] = [];
    private ADD_TYPE_MAP = ADD_TYPE_MAP;
    private isDialogVisible = false;
    private templateId = "";
    private originForm: any = {};
    private childList: any[] = [];
    private customId = -1;
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject = {};
    private templateList: any[] = [];
    // TODO: computed
    private getCustomId() {
        this.customId--;
        return this.customId;
    }
    private onChange(scope: any) {
        scope.row.directorName = this.findNameById(scope.row.director);
    }
    private findNameById(id: string) {
        let flag = false;
        let ret = "";
        function recursiveGetName(children: any[]) {
            for (let i = 0, len = children.length; i < len; i++) {
                if (flag) {
                    break;
                }
                if (children[i].id == id) {
                    flag = true;
                    ret = children[i].name;
                }
                if (children[i].children && !flag) {
                    recursiveGetName(children[i].children);
                }
            }
        }
        recursiveGetName(this.userTreeList);
        return ret;
    }
    private async showDialog(produceId: number) {
        this.produceId = produceId;
        this.userTreeList = await ConstantModule.getUserTreeList();
        this.isDialogVisible = true;
    }
    private async getTaskTemplateList() {
        await apiGetTaskTemplateList({}).then((res) => {
            this.templateList = res.data.data || [];
        });
    }
    private async getProcedureTemplateList() {
        await apiGetProcessList({}, { current: 1, size: 100000 }).then(
            (res) => {
                this.templateList = res.data.data.records || [];
            }
        );
    }
    private async onSelectTemplateType() {
        this.templateId = "";
        this.childList = [];
        if (this.selectedTemplateType === "TASK") {
            this.getTaskTemplateList();
        } else {
            this.getProcedureTemplateList();
        }
    }
    private async onSelectTemplate() {
        if (this.selectedTemplateType === "TASK") {
            this.getTaskTemplateChildrenList();
        } else {
            this.getProcedureTemplateChildrenList();
        }
    }
    private onRowClick(row: any) {
        // TODO: 优化展开事件
        // (this.$refs[`table`] as ElTable).toggleRowExpansion(row);
    }
    private async getProcedureTemplateChildrenList() {
        await apiGetProcessInfo({ processFlowId: this.templateId }).then(
            (res) => {
                this.originForm = res.data.data;
                let childList = res.data.data.flowDetailList || [];
                // 递归赋ID, 并赋值name和desc
                this.childList = this.setCustomId(childList, "flow");
                console.log(this.childList);
                // this.refreshTreeTable();
            }
        );
    }
    private async getTaskTemplateChildrenList() {
        await apiGetTaskTemplateInfo({ templateId: this.templateId }).then(
            (res) => {
                this.originForm = res.data.data;
                let childList = res.data.data.childList || [];
                // 递归赋ID, 并赋值name和desc
                this.childList = this.setCustomId(childList, "template");
                console.log(this.childList);
                // this.refreshTreeTable();
            }
        );
    }
    private setCustomId(data: any[], type: string) {
        let _this = this;
        function recursiveSetCustomId(childList: any[]) {
            for (let i = 0, len = childList.length; i < len; i++) {
                _this.$set(
                    childList[i],
                    "produceName",
                    childList[i][`${type}Name`]
                );
                _this.$set(
                    childList[i],
                    "produceDesc",
                    childList[i][`${type}Desc`]
                );
                // childList[i].produceName = childList[i].templateName;
                // childList[i].produceDesc = childList[i].templateDesc;
                // delete childList[i].templateDesc;
                // delete childList[i].templateName;
                childList[i].customId = _this.getCustomId();
                if (childList[i].childList) {
                    recursiveSetCustomId(childList[i].childList);
                }
            }
        }
        recursiveSetCustomId(data);
        return data;
    }
    private onConfirm() {
        let childList = this.childList.map((item) => {
            item.parentId = this.produceId;
            return item;
        });
        apiBatchAddProductionTask(childList).then(() => {
            this.$emit("batchAddNodeCB");
        });

        this.isDialogVisible = false;
    }
    private onCancel() {
        this.isDialogVisible = false;
    }
    private onAddRow() {
        
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        const customId = this.getCustomId();
        this.childList.push({
            customId: customId,
            produceDesc: ``,
            produceName: ``,
        });

        this.editObject.editRowIndex =
            this.recursiveCountChildLength(this.childList) - 1;
        // this.refreshTreeTable();
    }
    private recursiveCountChildLength(data: any): number {
        // 递归获取原数据所有子节点的个数
        let childrenLen = 0;
        function recursiveGetChildrenLen(childList: any[]) {
            let len = childList.length;
            childrenLen += len;
            for (let i = 0; i < len; i++) {
                if (childList[i].childList) {
                    recursiveGetChildrenLen(childList[i].childList);
                }
            }
        }
        recursiveGetChildrenLen(data);
        return childrenLen;
    }
    onAddChild(scope: any) {
        
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        if (!scope.row.childList) {
            this.$set(scope.row, "childList", []);
        }
        const customId = this.getCustomId();
        scope.row.childList.push({
            customId: customId,
            produceDesc: ``,
            produceName: ``,
        });
        // 要编辑的节点是当前节点的index，并偏移该节点下所有子节点个数的数量
        this.editObject.editRowIndex =
            scope.$index + this.recursiveCountChildLength(scope.row.childList);
        // this.refreshTreeTable();
        this.$nextTick(() => {
            (this.$refs[`table`] as ElTable).toggleRowExpansion(
                scope.row,
                true
            );
        });
    }
    // private refreshTreeTable() {
    //     // HACK: 刷新节点
    //     this.childList.push({ customId: 0 });
    //     this.childList.pop();
    // }
    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }
    private recursiveFindRow(
        targetData: any[],
        targetCustomId: number
    ): [any[], number] {
        // 得益于引用类型，直接返回目标节点的父节点和目标节点在父节点的位置
        let flag = true;
        let retList: any[] = [],
            retIndex = -1;
        function findRow(childList: any) {
            for (let i = 0, len = childList.length; i < len; i++) {
                if (childList[i].customId == targetCustomId) {
                    retList = childList;
                    retIndex = i;
                    flag = false;
                    break;
                }
                if (flag && childList[i].childList) {
                    findRow(childList[i].childList);
                }
            }
        }
        findRow(targetData);
        return [retList, retIndex];
    }
    private onDeleteRow(scope: any) {
        let editRowIndex = this.editObject.editRowIndex;
        if (editRowIndex >= 0 && scope.$index !== editRowIndex) {
            this.$message.error("请先保存");
            return;
        }
        // 递归寻找要删除的节点在源数据的位置，并删除
        let [childList, idx] = this.recursiveFindRow(
            this.childList,
            scope.row.customId
        );
        childList.splice(idx, 1);
        // this.refreshTreeTable();
    }

    private onSaveRow(scope: any) {
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            let [childList, idx] = this.recursiveFindRow(
                this.childList,
                scope.row.customId
            );
            childList.splice(idx, 1, this.preObject);
        } else {
            this.onDeleteRow(scope);
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
    private onDialogClose() {
        this.editObject.editRowIndex = -1;
    }
}
</script>
