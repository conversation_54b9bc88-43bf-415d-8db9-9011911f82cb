<template>
    <el-dialog
        @close="onDialogClose"
        title="详情"
        :visible.sync="isDialogVisible"
    >
        <el-button @click="onAddRow" type="primary" style="margin-top:-10px">
            添加主节点
        </el-button>
        <el-table
            :data="childList"
            :header-cell-style="commmonTableHeaderCellStyle"
            style="margin-top:10px"
            row-key="customId"
            :tree-props="{
                children: 'childList',
            }"
            ref="table"
            @row-click="onRowClick"
        >
            <el-table-column label="编号">
                <template slot-scope="scope">
                    <span>
                        {{ getRowIndexPath(scope.row.customId).join(".") }}
                        <!-- {{ scope.$index + 1 }} -->
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="名称" prop="templateName">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.templateName }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.templateName"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="描述" prop="templateDesc">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.templateDesc }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.templateDesc"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="280">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <!-- <el-button type="text" @click="onAddChildNode(scope)">
                            添加子节点
                        </el-button> -->
                        <el-dropdown trigger="click">
                            <span class="el-dropdown-link">
                                <el-button
                                    slot="reference"
                                    type="text"
                                    style="margin-right:10px"
                                >
                                    添加节点
                                </el-button>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                    v-if="showAddChildBtn(scope)"
                                    @click.native="onAddChildNode(scope)"
                                >
                                    添加子节点
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="showInsertNextBtn(scope)"
                                    @click.native="onInsertNextNode(scope)"
                                >
                                    向下插入节点
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-if="showInsertBeforeBtn(scope)"
                                    @click.native="onInsertBeforeNode(scope)"
                                >
                                    向上插入节点
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <template slot="footer">
            <el-button type="primary" @click="onConfirm">确认</el-button>
            <el-button @click="onCancel">取消</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
interface ITableData {
    name?: String;
    describe?: String;
}
import { Table as ElTable } from "element-ui";
import { apiGetTaskTemplateInfo, apiUpdateTaskTemplate } from "@/api/process";
@Component({})
export default class extends Vue {
    private isDialogVisible = false;
    private originForm: any = {};
    private childList: any[] = [
        // {
        //     templateId: 1,
        //     templateDesc: "1desc",
        //     templateName: "1name",
        //     childList: [
        //         {
        //             templateId: 2,
        //             templateDesc: "2desc",
        //             templateName: "2name",
        //             childList: [],
        //         },
        //     ],
        // },
        // {
        //     templateId: 3,
        //     templateDesc: "3desc",
        //     templateName: "3name",
        //     childList: [
        //         {
        //             templateId: 4,
        //             templateDesc: "4desc",
        //             templateName: "4name",
        //             childList: [],
        //         },
        //     ],
        // },
    ];
    private customId = -1;
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject = {};
    private showAddChildBtn(scope: any) {
        // 有child就不显示添加子节点
        return !scope.row.childList?.length;
    }
    private showInsertNextBtn(scope: any) {
        return true;
    }
    private showInsertBeforeBtn(scope: any) {
        // 只在第一节点显示向上插入
        let indexPath = this.getRowIndexPath(scope.row.customId);
        return indexPath[indexPath.length - 1] === 1;
    }
    // TODO: computed
    private getCustomId() {
        return --this.customId;
    }
    private getRowIndexPath(customId: number) {
        // 递归获取目标节点的(index+1)路径
        let temp: number[][] = [];
        // 是否找到目标节点
        let flag = false;
        function getExpandRowKeys(childList: any[]) {
            let temp_inner: number[] = [];
            for (let i = 0, len = childList.length; i < len; i++) {
                // 如果到达找到目标节点，开始break每层的循环
                if (flag) {
                    break;
                }
                temp_inner.push(i + 1);
                // 找到目标id
                if (childList[i].customId == customId) {
                    flag = true;
                    break;
                }
                if (childList[i].childList && !flag) {
                    getExpandRowKeys(childList[i].childList);
                }
            }
            if (flag) {
                temp.push(temp_inner);
            }
        }
        getExpandRowKeys(this.childList);
        let produceIdPath: number[] = [];
        temp.forEach((item) => {
            produceIdPath.push(item[item.length - 1]);
        });
        return produceIdPath.reverse();
    }
    private async showDialog(templateId: number) {
        this.templateId = templateId;
        await this.getTemplateChildrenList();
        this.isDialogVisible = true;
    }
    private onRowClick(row: any) {
        // TODO: 优化展开事件
        // (this.$refs[`table`] as ElTable).toggleRowExpansion(row);
    }
    private async getTemplateChildrenList() {
        await apiGetTaskTemplateInfo({ templateId: this.templateId }).then(
            (res) => {
                this.originForm = res.data.data;
                let childList = res.data.data.childList || [];
                // 递归赋ID
                this.childList = this.setCustomId(childList);
                // this.refreshTreeTable();
            }
        );
    }
    private setCustomId(data: any[]) {
        let _this = this;
        function recursiveSetCustomId(childList: any[]) {
            for (let i = 0, len = childList.length; i < len; i++) {
                childList[i].customId = _this.getCustomId();
                if (childList[i].childList) {
                    recursiveSetCustomId(childList[i].childList);
                }
            }
        }
        recursiveSetCustomId(data);
        return data;
    }
    private onConfirm() {
        this.originForm.childList = this.childList;
        apiUpdateTaskTemplate(this.originForm);
        this.isDialogVisible = false;
    }
    private onCancel() {
        this.isDialogVisible = false;
    }
    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        const customId = this.getCustomId();
        this.childList.push({
            customId: customId,
            templateDesc: ``,
            templateName: ``,
        });

        this.editObject.editRowIndex =
            this.recursiveCountChildLength(this.childList) - 1;
        // this.refreshTreeTable();
    }
    private recursiveCountChildLength(data: any): number {
        // 递归获取原数据所有子节点的个数
        let childrenLen = 0;
        function recursiveGetChildrenLen(childList: any[]) {
            let len = childList.length;
            childrenLen += len;
            for (let i = 0; i < len; i++) {
                if (childList[i].childList) {
                    recursiveGetChildrenLen(childList[i].childList);
                }
            }
        }
        recursiveGetChildrenLen(data);
        return childrenLen;
    }
    onAddChildNode(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        if (!scope.row.childList) {
            this.$set(scope.row, "childList", []);
        }
        const customId = this.getCustomId();
        scope.row.childList.push({
            customId: customId,
            templateDesc: ``,
            templateName: ``,
        });
        // 要编辑的节点是当前节点的index，并偏移该节点下所有子节点个数的数量
        this.editObject.editRowIndex =
            scope.$index + this.recursiveCountChildLength(scope.row.childList);
        // this.refreshTreeTable();
        this.$nextTick(() => {
            (this.$refs[`table`] as ElTable).toggleRowExpansion(
                scope.row,
                true
            );
        });
    }
    onInsertBeforeNode(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        let [childList, idx] = this.recursiveFindRow(
            this.childList,
            scope.row.customId
        );
        const customId = this.getCustomId();

        // 当前业务，idx都是0
        childList.splice(idx, 0, {
            customId: customId,
            templateDesc: ``,
            templateName: ``,
        });
        // 向上插入后, 相当于新节点顶替当前目标节点位置, 所以新节点的index就是目标节点的index
        this.editObject.editRowIndex = scope.$index;
    }
    onInsertNextNode(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        let [childList, idx] = this.recursiveFindRow(
            this.childList,
            scope.row.customId
        );
        const customId = this.getCustomId();
        // 向后插入，偏移+1
        childList.splice(idx + 1, 0, {
            customId: customId,
            templateDesc: ``,
            templateName: ``,
        });
        // 向后插入节点，新节点位置在目标节点之后，偏移+1
        this.editObject.editRowIndex = scope.$index + 1;
        // this.refreshTreeTable();
    }
    // private refreshTreeTable() {
    //     // HACK: 刷新节点
    //     this.childList.push({ customId: 0 });
    //     this.childList.pop();
    // }
    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }
    private recursiveFindRow(
        targetData: any[],
        targetCustomId: number
    ): [any[], number] {
        // 得益于引用类型，直接返回目标节点的父节点和目标节点在父节点的位置
        let flag = true;
        let retList: any[] = [],
            retIndex = -1;
        function findRow(childList: any) {
            for (let i = 0, len = childList.length; i < len; i++) {
                if (childList[i].customId == targetCustomId) {
                    retList = childList;
                    retIndex = i;
                    flag = false;
                    break;
                }
                if (flag && childList[i].childList) {
                    findRow(childList[i].childList);
                }
            }
        }
        findRow(targetData);
        return [retList, retIndex];
    }
    private onDeleteRow(scope: any) {
        let editRowIndex = this.editObject.editRowIndex;
        if (editRowIndex >= 0 && scope.$index !== editRowIndex) {
            this.$message.error("请先保存");
            return;
        }
        // 递归寻找要删除的节点在源数据的位置，并删除
        let [childList, idx] = this.recursiveFindRow(
            this.childList,
            scope.row.customId
        );
        childList.splice(idx, 1);
        // this.refreshTreeTable();
    }

    private onSaveRow(scope: any) {
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            let [childList, idx] = this.recursiveFindRow(
                this.childList,
                scope.row.customId
            );
            childList.splice(idx, 1, this.preObject);
        } else {
            this.onDeleteRow(scope);
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
    private onDialogClose() {
        this.editObject.editRowIndex = -1;
    }
}
</script>
