<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">生产模板</div>
            模板名称：
            <el-input
                clearable
                v-model="templateName"
                style="width:200px;margin-right:10px"
            ></el-input>
            <el-button
                icon="el-icon-search"
                @click="onSearch"
                type="primary"
                style="margin-right:10px"
            >
                查询
            </el-button>
            <el-button
                icon="el-icon-plus"
                @click="onAdd"
                type="primary"
                style="margin:10px 0"
            >
                添加模板
            </el-button>
            <div class="simple-line" style="margin-top:10px"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column label="模板编号">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column prop="templateName" label="模板名称">
                </el-table-column>
                <el-table-column prop="templateDesc" label="模板描述">
                </el-table-column>
                <el-table-column prop="name" label="任务节点数">
                </el-table-column>
                <el-table-column prop="date" label="创建人"> </el-table-column>
                <el-table-column prop="date" label="创建日期">
                </el-table-column>
                <el-table-column prop="date" label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" @click="onEdit(scope)">
                            编辑
                        </el-button>
                        <el-button
                            style="margin-right:10px"
                            type="text"
                            @click="onDetail(scope)"
                        >
                            详情
                        </el-button>
                        <el-popover
                            placement="top"
                            :ref="`popover-${scope.$index}`"
                        >
                            <p>确认删除？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onDelete(scope)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button slot="reference" type="text">
                                删除
                            </el-button>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            :title="dialogType === 'ADD' ? '新增' : '编辑'"
            :visible.sync="isDialogVisible"
        >
            <el-form :model="form" label-width="80px">
                <el-row :gutter="20">
                    <!-- <el-col :span="12">
                        <el-form-item label="模板编号">
                            <el-input v-model="form.templateId"></el-input>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12">
                        <el-form-item label="模板名称">
                            <el-input v-model="form.templateName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="模板描述">
                            <el-input
                                type="textarea"
                                v-model="form.templateDesc"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template slot="footer">
                <el-button type="primary" @click="onConfirm">确认</el-button>
                <el-button @click="onCancel">取消</el-button>
            </template>
        </el-dialog>
        <table-dialog ref="tableDialog"></table-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import TableDialog from "./dialog.vue";
import { Popover as ElPopover } from "element-ui";
import {
    apiGetTaskTemplateList,
    apiUpdateTaskTemplate,
    apiAddTaskTemplate,
    apiDeleteTaskTemplate,
} from "@/api/process";
@Component({ components: { TableDialog } })
export default class extends Vue {
    private tableData = [];
    private childrenList = [];
    private isDialogVisible = false;
    private dialogType = "ADD";
    private form = {};
    private templateName = "";
    // private querySearchTemplateName(queryString: string, cb: any) {
    //     apiGetProcessFuzzyList({ partNumber: queryString }).then((res) => {
    //         cb(
    //             res.data.data.map((item: any) => {
    //                 return { value: item.partNumber };
    //             })
    //         );
    //     });
    // }
    private onSearch() {
        this.getAllTemplate();
    }
    private onEdit(scope: any) {
        this.form = { ...scope.row };
        this.dialogType = "EDIT";
        this.isDialogVisible = true;
    }
    private onAdd() {
        this.dialogType = "ADD";
        this.form = { parentId: -1, templateName: "", templateDesc: "" };
        this.isDialogVisible = true;
    }
    private onConfirm() {
        if (this.dialogType === "ADD") {
            apiAddTaskTemplate(this.form).then(() => {
                this.getAllTemplate();
                this.isDialogVisible = false;
            });
        } else {
            apiUpdateTaskTemplate(this.form).then(() => {
                this.getAllTemplate();
                this.isDialogVisible = false;
            });
        }
    }
    private onCancel() {
        this.isDialogVisible = false;
    }
    private onDetail(scope: any) {
        (this.$refs.tableDialog as any).showDialog(scope.row.templateId);
    }
    private onDelete(scope: any) {
        apiDeleteTaskTemplate({ templateId: scope.row.templateId }).then(() => {
            (this.$refs[`popover-${scope.$index}`] as ElPopover).doClose();
            this.getAllTemplate();
        });
    }
    private mounted() {
        this.getAllTemplate();
    }
    private async getAllTemplate() {
        await apiGetTaskTemplateList({
            templateName: this.templateName || undefined,
        }).then((res) => {
            this.tableData = res.data.data || [];
        });
    }
}
</script>
