<template>
    <el-dialog
        :close-on-click-modal="false"
        title="详情"
        width="80%"
        :visible.sync="isDialogVisible"
    >
        <el-form :model="formData" label-width="90px">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="工单号">
                        <el-input
                            disabled
                            style="width:100%"
                            v-model="formData.processFlowCode"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="零件号">
                        <el-input
                            style="width:100%"
                            v-model="formData.partNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="名称">
                        <el-input
                            style="width:100%"
                            v-model="formData.processFlowName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="材质">
                        <el-input v-model="formData.materialQuality"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="炉号">
                        <el-input v-model="formData.furnaceNumber"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="签发日期">
                        <el-date-picker
                            disabled
                            style="width:100%"
                            v-model="formData.createTime"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="应完成日期">
                        <el-date-picker
                            style="width:100%"
                            v-model="formData.planEndDate"
                            type="date"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="需求数量">
                        <el-input v-model="formData.quantity"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-button
            style="margin-bottom:10px"
            type="primary"
            @click="onAddProcessFlowRow"
        >
            新增工序
        </el-button>
        <el-table
            ref="expandTable"
            :header-cell-style="commmonTableHeaderCellStyle"
            class="expand-table"
            :data="formData.flowDetailList"
        >
            <el-table-column type="expand">
                <template slot-scope="expandScope">
                    <div style="padding:10px 40px">
                        <el-button
                            type="primary"
                            @click="onAddAllocateRow(expandScope)"
                        >
                            新增
                        </el-button>
                        <el-table
                            style="margin-top:10px;"
                            :data="expandScope.row.operateList"
                            border
                        >
                            <el-table-column
                                prop="deviceNumber"
                                label="设备编号"
                            >
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        {{ scope.row.deviceNumber }}
                                    </span>
                                    <el-input
                                        v-else
                                        v-model="scope.row.deviceNumber"
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="startTime" label="开始时间">
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        {{ scope.row.startTime }}
                                    </span>
                                    <el-date-picker
                                        v-else
                                        v-model="scope.row.startTime"
                                        type="datetime"
                                        placeholder="选择日期时间"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    >
                                    </el-date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column prop="endTime" label="结束时间">
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        {{ scope.row.endTime }}
                                    </span>
                                    <el-date-picker
                                        v-else
                                        v-model="scope.row.endTime"
                                        type="datetime"
                                        placeholder="选择日期时间"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                    >
                                    </el-date-picker>
                                </template>
                            </el-table-column>
                            <el-table-column prop="operateName" label="操作员">
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        {{ scope.row.operateName }}
                                    </span>
                                    <el-cascader
                                        v-else
                                        @change="onOperateChange(scope)"
                                        :show-all-levels="false"
                                        style="width:90%"
                                        v-model="scope.row.operateId"
                                        :options="userTreeList"
                                        :props="{
                                            value: 'id',
                                            label: 'name',
                                            emitPath: false,
                                        }"
                                    ></el-cascader>
                                </template>
                            </el-table-column>
                            <el-table-column prop="quantity" label="数量">
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        {{ scope.row.quantity }}
                                    </span>
                                    <el-input
                                        v-else
                                        v-model="scope.row.quantity"
                                    ></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <span
                                        v-if="
                                            expandScope.$index !==
                                                expandScopeEditRowIndex ||
                                                allocateEditObject.editRowIndex !==
                                                    scope.$index
                                        "
                                    >
                                        <el-button
                                            type="text"
                                            @click="
                                                onEditAllocateRow(
                                                    scope,
                                                    expandScope
                                                )
                                            "
                                        >
                                            编辑
                                        </el-button>
                                        <el-button
                                            type="text"
                                            @click="
                                                onDeleteAllocateRow(
                                                    scope,
                                                    expandScope
                                                )
                                            "
                                        >
                                            删除
                                        </el-button>
                                    </span>
                                    <span v-else>
                                        <el-button
                                            type="text"
                                            @click="onSaveAllocateRow(scope)"
                                        >
                                            保存
                                        </el-button>
                                        <el-button
                                            type="text"
                                            @click="
                                                onCancelAllocateRow(
                                                    scope,
                                                    expandScope
                                                )
                                            "
                                        >
                                            取消
                                        </el-button>
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-table-column>

            <el-table-column prop="sort" label="工序号"> </el-table-column>
            <el-table-column prop="flowName" label="工序名称">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.flowName }}
                    </span>
                    <el-input v-else v-model="scope.row.flowName"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="flowDesc" label="工序描述">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.flowDesc }}
                    </span>
                    <el-input v-else v-model="scope.row.flowDesc"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.deviceName }}
                    </span>
                    <el-input v-else v-model="scope.row.deviceName"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="qualifiedQuantity" label="合格数量">
                <!-- <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.qualifiedQuantity }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.qualifiedQuantity"
                    ></el-input>
                </template> -->
            </el-table-column>
            <el-table-column prop="repairQuantity" label="返修数量">
                <!-- <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.repairQuantity }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.repairQuantity"
                    ></el-input>
                </template> -->
            </el-table-column>
            <el-table-column prop="abandonQuantity" label="报废数量">
                <!-- <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.abandonQuantity }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.abandonQuantity"
                    ></el-input>
                </template> -->
            </el-table-column>
            <el-table-column prop="" label="完成日期">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.endDate }}
                    </span>
                    <el-date-picker
                        v-else
                        v-model="scope.row.endDate"
                        placeholder=""
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </template>
            </el-table-column>
            <el-table-column prop="standardHours" label="标准工时">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.standardHours }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.standardHours"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="prepareHours" label="准备工时">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        {{ scope.row.prepareHours }}
                    </span>
                    <el-input
                        v-else
                        v-model="scope.row.prepareHours"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <span
                        v-if="
                            procesFlowEditObject.editRowIndex !== scope.$index
                        "
                    >
                        <el-button
                            type="text"
                            @click="onEditProcessFlowRow(scope)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            type="text"
                            @click="onDeleteProcessFlowRow(scope)"
                        >
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button
                            type="text"
                            @click="onSaveProcessFlowRow(scope)"
                        >
                            保存
                        </el-button>
                        <el-button
                            type="text"
                            @click="onCancelProcessFlowRow(scope)"
                        >
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>

        <span slot="footer">
            <el-button type="primary" @click="onConfirm">确认</el-button>
            <el-button @click="onCancel">取消</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiUpdateProcess, apiGetProcessInfo } from "@/api/process";
import { Table as ElTable } from "element-ui";
@Component({})
export default class extends Vue {
    private formData: any = {};
    private expandScopeEditRowIndex = -1;
    private isProcessDialogVisible = false;
    private isDialogVisible = false;
    private form: any = {};
    private procesFlowEditObject = { editRow: false, editRowIndex: -1 };
    private processFlowPreObject: any = {};
    private allocatePreObject: any = {};
    private allocateEditObject = { editRow: false, editRowIndex: -1 };
    @Prop({ default: () => {} }) userTreeList!: any[];

    private async showDialog(scope: any) {
        this.processFlowId = scope.row.processFlowId;
        this.procesFlowEditObject = { editRow: false, editRowIndex: -1 };
        this.allocateEditObject = { editRow: false, editRowIndex: -1 };
        await this.getProcessInfo();
        this.isDialogVisible = true;
    }
    private async getProcessInfo() {
        await apiGetProcessInfo({ processFlowId: this.processFlowId }).then(
            (res) => {
                // this.flowDetailList = res.data.data.flowDetailList;
                this.formData = res.data.data;
            }
        );
    }
    private onConfirm() {
        apiUpdateProcess(this.formData).then(() => {
            this.$emit("getProcessList");
            this.isDialogVisible = false;
        });
        // console.log(this.formData);
    }

    private onCancel() {
        this.isDialogVisible = false;
    }
    // 工序信息业务
    private onCheckUserChange(scope: any) {
        scope.row.checkUserName = this.findNameById(scope.row.checkUserId);
    }
    private findNameById(id: string) {
        let flag = false;
        let ret = "";
        function recursiveGetName(children: any[]) {
            for (let i = 0, len = children.length; i < len; i++) {
                if (flag) {
                    break;
                }
                if (children[i].id == id) {
                    flag = true;
                    ret = children[i].name;
                }
                if (children[i].children && !flag) {
                    recursiveGetName(children[i].children);
                }
            }
        }
        recursiveGetName(this.userTreeList);
        return ret;
    }
    private onAddProcessFlowRow() {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        if (!this.formData.flowDetailList) {
            this.$set(this.formData, "flowDetailList", []);
        }
        this.formData.flowDetailList.push({
            processFlowId: this.processFlowId,
            deviceName: undefined,
            flowName: undefined,
            flowDesc: undefined,
            standardHours: undefined,
            prepareHours: undefined,
            checkUserId: undefined,
        });
        this.processFlowPreObject = {};
        this.procesFlowEditObject.editRow = true;
        this.procesFlowEditObject.editRowIndex =
            this.formData.flowDetailList.length - 1;
    }

    private onEditProcessFlowRow(scope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.processFlowPreObject = { ...scope.row };
        this.procesFlowEditObject.editRowIndex = scope.$index;
    }

    private onDeleteProcessFlowRow(scope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.formData.flowDetailList.splice(scope.$index, 1);
    }

    private onSaveProcessFlowRow(scope: any) {
        this.procesFlowEditObject.editRowIndex = -1;
    }
    private async onCancelProcessFlowRow(scope: any) {
        if (Object.keys(this.processFlowPreObject).length) {
            this.formData.flowDetailList[scope.$index] = {
                ...this.processFlowPreObject,
            };
        } else {
            this.formData.flowDetailList.pop();
        }
        this.procesFlowEditObject.editRowIndex = -1;
    }

    // 人员分配业务
    private onOperateChange(scope: any) {
        scope.row.operateName = this.findNameById(scope.row.operateId);
    }
    private onAddAllocateRow(expandScope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.allocatePreObject = {};
        if (!expandScope.row.operateList) {
            this.$set(expandScope.row, "operateList", []);
            this.$nextTick(() => {
                (this.$refs.expandTable as ElTable).toggleRowExpansion(
                    expandScope.row,
                    true
                );
            });
        }
        expandScope.row.operateList.push({
            deviceNumber: "",
            quantity: "",
            startTime: "",
            endTime: "",
            operateId: "",
        });
        this.allocateEditObject.editRow = true;
        this.expandScopeEditRowIndex = expandScope.$index;
        this.allocateEditObject.editRowIndex =
            expandScope.row.operateList.length - 1;
    }

    private onEditAllocateRow(scope: any, expandScope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.allocatePreObject = { ...scope.row };
        this.allocateEditObject.editRowIndex = scope.$index;
        this.expandScopeEditRowIndex = expandScope.$index;
    }

    private onDeleteAllocateRow(scope: any, expandScope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        expandScope.row.operateList.splice(scope.$index, 1);
    }

    private onSaveAllocateRow() {
        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
    }

    private onCancelAllocateRow(scope: any, expandScope: any) {
        if (Object.keys(this.allocatePreObject).length) {
            scope.row = {
                ...this.allocatePreObject,
            };
        } else {
            this.formData.flowDetailList[expandScope.$index].operateList.pop();
        }

        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
        this.allocatePreObject = {};
    }
}
</script>
<style lang="scss">
.expand-table {
    .el-table__cell.el-table__expanded-cell {
        padding: 0;
        // background: #f5f7fa;
    }
    .el-table__expanded-cell:hover {
        // background-color: #f5f7fa !important;
    }
}
</style>
