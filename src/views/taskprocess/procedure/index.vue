<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div class="app-card-title">工序管理</div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="6">
                        <el-form-item label="签发日期" size="normal">
                            <el-date-picker
                                clearable
                                style="width:100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="零件号" size="normal">
                            <el-autocomplete
                                clearable
                                v-model="form.partNumber"
                                :fetch-suggestions="querySearchPartnumber"
                                placeholder="请输入内容"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="名称" size="normal">
                            <el-autocomplete
                                clearable
                                v-model="form.processFlowName"
                                :fetch-suggestions="querySearchProcessFlowName"
                                placeholder="请输入内容"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="" size="normal" label-width="0">
                            <el-button
                                icon="el-icon-search"
                                type="primary"
                                @click="onSearch"
                            >
                                查询
                            </el-button>
                            <el-button
                                icon="el-icon-plus"
                                type="primary"
                                @click="onAdd"
                            >
                                新增
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <div class="simple-line" style="margin-top:0"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    prop="processFlowCode"
                    width="200"
                    label="工单号"
                >
                </el-table-column>
                <el-table-column prop="partNumber" label="零件号">
                </el-table-column>
                <el-table-column prop="processFlowName" label="名称">
                </el-table-column>
                <el-table-column prop="materialQuality" label="材质">
                </el-table-column>
                <el-table-column
                    prop="furnaceNumber"
                    label="炉号"
                ></el-table-column>
                <el-table-column prop="createTime" label="签发日期">
                </el-table-column>
                <el-table-column prop="planEndDate" label="应完成日期">
                </el-table-column>
                <el-table-column prop="quantity" width="100" label="需求数量">
                </el-table-column>
                <el-table-column label="操作" width="220">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            style="margin-right:10px"
                            @click="onDetail(scope)"
                        >
                            详情
                        </el-button>
                        <el-popover
                            placement="top"
                            :ref="`popover-${scope.$index}`"
                        >
                            <p>确认删除？</p>
                            <div style="text-align: right; margin: 0">
                                <el-button
                                    size="mini"
                                    type="text"
                                    @click="
                                        $refs[
                                            `popover-${scope.$index}`
                                        ].doClose()
                                    "
                                >
                                    取消
                                </el-button>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="onDelete(scope)"
                                >
                                    确定
                                </el-button>
                            </div>
                            <el-button
                                style="margin-right:10px"
                                slot="reference"
                                type="text"
                            >
                                删除
                            </el-button>
                        </el-popover>
                        <router-link
                            target="_blank"
                            :to="`/processprint?id=${scope.row.processFlowId}`"
                        >
                            <el-button type="text" style="margin-right:10px">
                                打印
                            </el-button>
                        </router-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <update-batch-dialog
            :deviceList="deviceList"
            :userTreeList="userTreeList"
            ref="updateBatchDialog"
        >
        </update-batch-dialog>
        <add-batch-dialog
            :deviceList="deviceList"
            :userTreeList="userTreeList"
            @getProcessList="getProcessList"
            ref="addBatchDialog"
        ></add-batch-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import UpdateBatchDialog from "./updateBatchDialog.vue";
import AddBatchDialog from "./addBatchDialog.vue";
import { Popover as ElPopover } from "element-ui";
import {
    apiGetProcessList,
    apiDeleteProcess,
    apiGetProcessFuzzyList,
} from "@/api/process";
import { apiGetDictList } from "@/api/dict";
import { ConstantModule } from "@/store/modules/constant";
@Component({ components: { UpdateBatchDialog, AddBatchDialog } })
export default class extends Vue {
    private tableData = [];
    private userTreeList: any[] = [];
    private processFlowName: string = "";
    private partNumber = "";
    private confirmDate = ["", ""];
    private deviceList: any[] = [];
    private form: any = {};
    private async mounted() {
        this.getProcessList();
        this.userTreeList = await ConstantModule.getUserTreeList();
    }
    private async getDeviceList() {
        await apiGetDictList({ businessType: 15 }).then((res) => {
            this.deviceList = res.data.data || [];
        });
    }
    private async onDetail(scope: any) {
        if (!this.deviceList.length) {
            await this.getDeviceList();
        }
        (this.$refs.updateBatchDialog as any).showDialog(scope);
    }
    private async onAdd() {
        if (!this.deviceList.length) {
            await this.getDeviceList();
        }
        (this.$refs.addBatchDialog as any).showDialog("ADD");
    }
    private async getProcessList(
        condition: any = this.form,
        cs: any = { current: 1, size: 10 }
    ) {
        await apiGetProcessList(condition, cs).then((res) => {
            this.tableData = res.data.data.records;
        });
    }
    private onDelete(scope: any) {
        apiDeleteProcess({ processFlowId: scope.row.processFlowId }).then(
            () => {
                (this.$refs[`popover-${scope.$index}`] as ElPopover).doClose();
                this.getProcessList();
            }
        );
    }
    private onSearch() {
        if (this.confirmDate) {
            [this.form.startDate, this.form.endDate] = this.confirmDate;
        } else {
            [this.form.startDate, this.form.endDate] = ["", ""];
        }
        this.getProcessList(this.form);
    }
    private onPrint(scope: any) {}
    private querySearchPartnumber(queryString: string, cb: any) {
        apiGetProcessFuzzyList({ partNumber: queryString }).then((res) => {
            cb(
                res.data.data.map((item: any) => {
                    return { value: item.partNumber };
                })
            );
        });
    }
    private querySearchProcessFlowName(queryString: string, cb: any) {
        apiGetProcessFuzzyList({ processFlowName: queryString }).then((res) => {
            cb(
                res.data.data.map((item: any) => {
                    return { value: item.processFlowName };
                })
            );
        });
    }
}
</script>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
</style>
