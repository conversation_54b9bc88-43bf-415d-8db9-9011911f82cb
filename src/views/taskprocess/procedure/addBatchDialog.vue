<template>
    <el-dialog
        top="5vh"
        :close-on-click-modal="false"
        title="详情"
        width="80%"
        :visible.sync="isDialogVisible"
    >
        <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="info">
                <el-form :model="formData" label-width="90px">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item required label="工单号">
                                <el-input
                                    style="width:100%"
                                    v-model="formData.processFlowCode"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item required label="零件号">
                                <el-input
                                    style="width:100%"
                                    v-model="formData.partNumber"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item required label="名称">
                                <el-input
                                    style="width:100%"
                                    v-model="formData.processFlowName"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item required label="工单类型">
                                <el-select
                                    style="width:100%"
                                    v-model="formData.workOrderType"
                                >
                                    <el-option
                                        v-for="(item, key) in workOrderTypeMap"
                                        :key="key"
                                        :label="item"
                                        :value="key"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item required label="项目类型">
                                <el-select
                                    style="width:100%"
                                    v-model="formData.projectType"
                                >
                                    <el-option
                                        v-for="(item, key) in projectTypeMap"
                                        :key="key"
                                        :label="item"
                                        :value="key"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="材质">
                                <el-input
                                    v-model="formData.materialQuality"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="炉号">
                                <el-input
                                    v-model="formData.furnaceNumber"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="签发日期">
                                <el-date-picker
                                    disabled
                                    style="width:100%"
                                    v-model="formData.createTime"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="应完成日期">
                                <el-date-picker
                                    style="width:100%"
                                    v-model="formData.planEndDate"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                ></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item required label="需求数量">
                                <el-input
                                    v-model="formData.quantity"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="工序信息" name="procedure">
                <el-button
                    style="margin-bottom:10px"
                    type="primary"
                    @click="onAddProcessFlowRow"
                >
                    新增工序
                </el-button>
                <el-table
                    ref="expandTable"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    class="expand-table"
                    :data="formData.flowDetailList"
                >
                    <el-table-column prop="sort" label="工序号">
                        <template slot-scope="scope">
                            <span>
                                {{
                                    scope.row.flowId && scope.row.flowId > 0
                                        ? scope.row.sort
                                        : ""
                                }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="flowName" label="工序名称">
                        <template slot="header"
                            ><span style="color:red;">*</span>工序名称</template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.flowName }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.flowName"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="flowDesc" label="工序描述">
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.flowDesc }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.flowDesc"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="deviceName" label="设备名称">
                        <template slot="header"
                            ><span style="color:red;">*</span>设备名称</template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.deviceName }}
                            </span>
                            <el-select
                                style="width:100%"
                                v-else
                                v-model="scope.row.deviceName"
                            >
                                <el-option
                                    v-for="item in deviceList"
                                    :label="item.name"
                                    :value="item.name"
                                    :key="item.id"
                                ></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="processRequiredQuantity"
                        label="需求数量"
                    >
                        <template slot="header"
                            ><span style="color:red;">*</span>需求数量</template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.processRequiredQuantity }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.processRequiredQuantity"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="" label="完成日期">
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.endDate }}
                            </span>
                            <el-date-picker
                                v-else
                                style="width:100%"
                                v-model="scope.row.endDate"
                                placeholder=""
                                value-format="yyyy-MM-dd"
                            >
                            </el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="standardHours" label="标准工时(min)">
                        <template slot="header"
                            ><span style="color:red;">*</span
                            >标准工时(min)</template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.standardHours }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.standardHours"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="prepareHours" label="准备工时(min)">
                        <template slot="header"
                            ><span style="color:red;">*</span
                            >准备工时(min)</template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                {{ scope.row.prepareHours }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.prepareHours"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="160">
                        <template slot-scope="scope">
                            <span
                                v-if="
                                    procesFlowEditObject.editRowIndex !==
                                        scope.$index
                                "
                            >
                                <el-button
                                    type="text"
                                    @click="onEditProcessFlowRow(scope)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    type="text"
                                    @click="onDeleteProcessFlowRow(scope)"
                                >
                                    删除
                                </el-button>
                            </span>
                            <span v-else>
                                <el-button
                                    type="text"
                                    @click="onSaveProcessFlowRow(scope)"
                                >
                                    保存
                                </el-button>
                                <el-button
                                    type="text"
                                    @click="onCancelProcessFlowRow(scope)"
                                >
                                    取消
                                </el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="操作员信息" name="operators">
                <div>
                    <el-select
                        style="margin-right:10px"
                        v-model="selectedFlowId"
                    >
                        <el-option
                            v-for="item in flowList"
                            :key="item.flowId"
                            :value="item.flowId"
                            :label="item.flowName"
                        ></el-option>
                    </el-select>
                    <el-button type="primary" @click="onAddAllocateRow">
                        新增
                    </el-button>
                    <el-table style="margin-top:10px;" :data="operatorList">
                        <el-table-column type="expand">
                            <template slot-scope="expandScope">
                                <el-form
                                    :model="expandScope.row"
                                    label-width="120px"
                                    style="padding:10px"
                                >
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="开始时间"
                                            >
                                                <el-date-picker
                                                    clearable
                                                    style="width:100%"
                                                    value-format="yyyy-MM-dd HH:mm:ss"
                                                    v-model="
                                                        expandScope.row
                                                            .startTime
                                                    "
                                                    type="datetime"
                                                >
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="结束时间"
                                            >
                                                <el-date-picker
                                                    clearable
                                                    style="width:100%"
                                                    value-format="yyyy-MM-dd HH:mm:ss"
                                                    v-model="
                                                        expandScope.row.endTime
                                                    "
                                                    type="datetime"
                                                >
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="设备编号"
                                            >
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .deviceNumber
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item label="首次调试">
                                                <el-radio-group
                                                    v-model="
                                                        expandScope.row
                                                            .firstTest
                                                    "
                                                >
                                                    <el-radio :label="1"
                                                        >是</el-radio
                                                    >
                                                    <el-radio :label="0"
                                                        >否</el-radio
                                                    >
                                                </el-radio-group>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item label="总件数">
                                                {{ getTotal(expandScope.row) }}
                                            </el-form-item>
                                        </el-col>

                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="合格数量"
                                            >
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .qualifiedQuantity
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="返修数量"
                                            >
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .repairQuantity
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="报废数量"
                                            >
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .abandonQuantity
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item label="效率">
                                                {{
                                                    getEfficient(
                                                        expandScope.row
                                                    )
                                                }}
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="操作人"
                                            >
                                                <el-cascader
                                                    @change="
                                                        onOperateChange(
                                                            expandScope
                                                        )
                                                    "
                                                    :show-all-levels="false"
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .operateId
                                                    "
                                                    :options="userTreeList"
                                                    :props="{
                                                        value: 'id',
                                                        label: 'name',
                                                        emitPath: false,
                                                    }"
                                                ></el-cascader>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item
                                                required
                                                label="总耗时(min)"
                                            >
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .totalHours
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item label="班次">
                                                <el-select
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row.workType
                                                    "
                                                >
                                                    <el-option
                                                        value="白"
                                                        label="白"
                                                    >
                                                    </el-option>
                                                    <el-option
                                                        value="夜"
                                                        label="夜"
                                                    >
                                                    </el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="6">
                                            <el-form-item label="报废原因">
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .abandonReason
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>

                                        <el-col :span="6">
                                            <el-form-item label="质检员">
                                                <el-cascader
                                                    :show-all-levels="false"
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row
                                                            .checkUserId
                                                    "
                                                    :options="userTreeList"
                                                    :props="{
                                                        value: 'id',
                                                        label: 'name',
                                                        emitPath: false,
                                                    }"
                                                ></el-cascader>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="6">
                                            <el-form-item label="备注">
                                                <el-input
                                                    style="width:100%"
                                                    v-model="
                                                        expandScope.row.comment
                                                    "
                                                ></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </template>
                        </el-table-column>

                        <el-table-column prop="flowName" label="工序名称">
                        </el-table-column>
                        <el-table-column prop="deviceNumber" label="设备编号">
                        </el-table-column>
                        <el-table-column prop="startTime" label="开始时间">
                        </el-table-column>
                        <el-table-column prop="endTime" label="结束时间">
                        </el-table-column>
                        <el-table-column prop="operateName" label="操作员">
                        </el-table-column>
                        <el-table-column prop="quantity" label="数量">
                            <template slot-scope="scope">
                                {{ getTotal(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="onDeleteAllocateRow(scope)"
                                >
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-tab-pane>
        </el-tabs>

        <span slot="footer">
            <el-button type="primary" @click="onConfirm">确认</el-button>
            <el-button @click="onCancel">取消</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiAddProcess } from "@/api/process";
import { Table as ElTable } from "element-ui";
import { projectTypeMap, workOrderTypeMap } from "../../machine-ledger/enum";
import {
    validateBaseInfoItemList,
    validateProcedureInfoItemList,
    validateOperatorInfoItemList,
} from "./validateItem";
@Component({})
export default class extends Vue {
    private projectTypeMap = projectTypeMap;
    private workOrderTypeMap = workOrderTypeMap;
    private uid = 0;
    private activeTab = "info";
    private formData: any = {};
    private expandScopeEditRowIndex = -1;
    private isProcessDialogVisible = false;
    private isDialogVisible = false;
    private form: any = {};
    private procesFlowEditObject = { editRow: false, editRowIndex: -1 };
    private processFlowPreObject: any = {};
    private allocatePreObject: any = {};
    private allocateEditObject = { editRow: false, editRowIndex: -1 };
    private selectedFlowId = "";
    @Prop({ default: () => [] }) userTreeList!: any[];
    @Prop({ default: () => [] }) deviceList!: any[];
    get operatorList(): any[] {
        if (!this.formData.flowDetailList) return [];
        return this.formData.flowDetailList.reduce(
            (acc: any, cur: any) => [...acc, ...(cur.operateList || [])],
            []
        );
    }
    get flowList(): any[] {
        if (!this.formData.flowDetailList) return [];
        return this.formData.flowDetailList.map((item: any) => {
            return {
                flowId: item.flowId,
                flowName: item.flowName,
            };
        });
    }
    private getId() {
        this.uid--;
        return this.uid;
    }

    private async showDialog() {
        this.selectedFlowId = "";
        this.activeTab = "info";
        this.procesFlowEditObject = { editRow: false, editRowIndex: -1 };
        this.allocateEditObject = { editRow: false, editRowIndex: -1 };
        await this.getProcessInfo();
        this.isDialogVisible = true;
    }
    private async getProcessInfo() {
        this.formData = {};
    }
    private validItem(targetList: any[], validateList: any[]): boolean {
        return targetList.every((item1) =>
            validateList.every((item2) => item1[item2] || item1[item2] == 0)
        );
    }
    private onConfirm() {
        const itemBaseInfoValid = this.validItem(
            [this.formData],
            validateBaseInfoItemList
        );
        const itemProcedureInfoValid = this.validItem(
            this.formData.flowDetailList || [],
            validateProcedureInfoItemList
        );
        const itemOperatorInfoValid = this.validItem(
            this.formData.detailList || [],
            validateOperatorInfoItemList
        );
        if (!itemBaseInfoValid) {
            this.$message.error("基本信息内有必填项未填写");
            return;
        }
        if (!itemProcedureInfoValid) {
            this.$message.error("工序信息内有必填项未填写");
            return;
        }
        if (!itemOperatorInfoValid) {
            this.$message.error("操作员信息内有必填项未填写");
            return;
        }
        apiAddProcess(this.formData).then(() => {
            this.$emit("getProcessList");
            this.isDialogVisible = false;
        });
    }

    private onCancel() {
        this.isDialogVisible = false;
    }
    // 工序信息业务
    private getTotal(row: any) {
        return (
            Number(row.qualifiedQuantity || 0) +
            Number(row.repairQuantity || 0) +
            Number(row.abandonQuantity || 0)
        );
    }
    private getHoursByFlowId(flowId: number) {
        const tmp: any = this.formData.flowDetailList.find(
            (item: any) => item.flowId == flowId
        );
        return [tmp.standardHours, tmp.prepareHours];
    }
    // (工序标准工时*总件数+准备工时)/总耗时
    private getEfficient(row: any) {
        const [standardHours, prepareHours] = this.getHoursByFlowId(row.flowId);
        const efficent =
            ((Number(standardHours || 0) *
                (Number(row.qualifiedQuantity || 0) +
                    Number(row.repairQuantity || 0) +
                    Number(row.abandonQuantity || 0)) +
                Number(prepareHours || 0)) /
                Number(row.totalHours || 0)) *
            100;

        return isNaN(efficent) ? 0 : efficent.toFixed(2) + "%";
    }
    private onCheckUserChange(scope: any) {
        scope.row.checkUserName = this.findNameById(scope.row.checkUserId);
    }
    private findNameById(id: string) {
        let flag = false;
        let ret = "";
        function recursiveGetName(children: any[]) {
            for (let i = 0, len = children.length; i < len; i++) {
                if (flag) {
                    break;
                }
                if (children[i].id == id) {
                    flag = true;
                    ret = children[i].name;
                }
                if (children[i].children && !flag) {
                    recursiveGetName(children[i].children);
                }
            }
        }
        recursiveGetName(this.userTreeList);
        return ret;
    }
    private onAddProcessFlowRow() {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        if (!this.formData.flowDetailList) {
            this.$set(this.formData, "flowDetailList", []);
        }
        this.formData.flowDetailList.push({
            deviceName: undefined,
            flowName: undefined,
            flowId: this.getId(),
            flowDesc: undefined,
            standardHours: undefined,
            prepareHours: undefined,
            checkUserId: undefined,
        });
        this.processFlowPreObject = {};
        this.procesFlowEditObject.editRow = true;
        this.procesFlowEditObject.editRowIndex =
            this.formData.flowDetailList.length - 1;
    }

    private onEditProcessFlowRow(scope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.processFlowPreObject = { ...scope.row };
        this.procesFlowEditObject.editRowIndex = scope.$index;
    }

    private onDeleteProcessFlowRow(scope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.formData.flowDetailList.splice(scope.$index, 1);
    }

    private onSaveProcessFlowRow(scope: any) {
        if (
            !validateProcedureInfoItemList.every(
                (item) => scope.row[item] || scope.row[item] == 0
            )
        ) {
            this.$message.error("有必填项未填写");
            return;
        }
        this.procesFlowEditObject.editRowIndex = -1;
    }
    private async onCancelProcessFlowRow(scope: any) {
        if (Object.keys(this.processFlowPreObject).length) {
            this.formData.flowDetailList[scope.$index] = {
                ...this.processFlowPreObject,
            };
        } else {
            this.formData.flowDetailList.pop();
        }
        this.procesFlowEditObject.editRowIndex = -1;
    }

    // 人员分配业务
    private onOperateChange(scope: any) {
        scope.row.operateName = this.findNameById(scope.row.operateId);
    }
    private onAddAllocateRow(expandScope: any) {
        if (!this.selectedFlowId) {
            this.$message.error("请先选择工序");
            return;
        }
        let flowDetailList = this.formData.flowDetailList;
        let idx = flowDetailList.findIndex(
            (item: any) => item.flowId === this.selectedFlowId
        );
        if (!flowDetailList[idx].operateList) {
            this.$set(flowDetailList[idx], "operateList", []);
        }
        let flowName = flowDetailList[idx].flowName;
        flowDetailList[idx].operateList.push({
            uid: this.getId(),
            flowId: this.selectedFlowId,
            flowName,
            deviceNumber: "",
            quantity: "",
            startTime: "",
            endTime: "",
            operateId: "",
            workOrderType: "",
            projectType: "",
            firstTest: "",
            qualifiedQuantity: "",
            repairQuantity: "",
            abandonQuantity: "",
            totalHours: "",
            workType: "",
            abandonReason: "",
            checkUserId: "",
            comment: "",
        });
    }

    private onEditAllocateRow(scope: any, expandScope: any) {
        if (
            this.procesFlowEditObject.editRowIndex >= 0 ||
            this.allocateEditObject.editRowIndex >= 0
        ) {
            this.$message.error("请先保存");
            return;
        }
        this.allocatePreObject = { ...scope.row };
        this.allocateEditObject.editRowIndex = scope.$index;
        this.expandScopeEditRowIndex = expandScope.$index;
    }

    private onDeleteAllocateRow(scope: any) {
        let { flowId, uid } = scope.row;
        let flowDetailList = this.formData.flowDetailList;
        let flowIdx = flowDetailList.findIndex(
            (item: any) => item.flowId === flowId
        );
        let operateIdx = flowDetailList[flowIdx].operateList.findIndex(
            (item: any) => item.uid === uid
        );
        flowDetailList[flowIdx].operateList.splice(operateIdx, 1);
    }

    private onSaveAllocateRow() {
        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
    }

    private onCancelAllocateRow(scope: any, expandScope: any) {
        if (Object.keys(this.allocatePreObject).length) {
            scope.row = {
                ...this.allocatePreObject,
            };
        } else {
            this.formData.flowDetailList[expandScope.$index].operateList.pop();
        }

        this.allocateEditObject.editRowIndex = -1;
        this.expandScopeEditRowIndex = -1;
        this.allocatePreObject = {};
    }
}
</script>
<style lang="scss">
.expand-table {
    .el-table__cell.el-table__expanded-cell {
        padding: 0;
        // background: #f5f7fa;
    }
    .el-table__expanded-cell:hover {
        // background-color: #f5f7fa !important;
    }
}
</style>
