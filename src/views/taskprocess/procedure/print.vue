<template>
    <div style="padding:10px 20px;width:700px">
        <el-form :model="form" label-width="100px">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="工单号：">
                        {{ form.processFlowCode }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="零件号：">
                        {{ form.partNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="名称：">
                        {{ form.processFlowName }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="材质：">
                        {{ form.materialQuality }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="炉号：">
                        {{ form.furnaceNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="签发日期：">
                        {{ form.createTime }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="数量：">
                        {{ form.quantity }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="应完日期：">
                        {{ form.planEndDate }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-table border :data="tableData" style="width:100%">
            <el-table-column prop="sort" label="工序"> </el-table-column>
            <el-table-column prop="flowName" label="工序名称">
            </el-table-column>
            <el-table-column prop="flowDesc" label="工序描述">
            </el-table-column>
            <el-table-column prop="deviceName" label="设备名称">
            </el-table-column>
            <el-table-column prop="processRequiredQuantity" label="需求数量">
            </el-table-column>
            <el-table-column prop="standardHours" label="标准工时">
            </el-table-column>
            <el-table-column prop="prepareHours" label="准备工时">
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetProcessInfo } from "@/api/process";
@Component({})
export default class extends Vue {
    private form: any = {};
    private tableData: any[] = [];
    private processFlowId: any = "";
    private mounted() {
        this.processFlowId = this.$route.query.id;
        this.getProcessInfo();
    }
    private getProcessInfo() {
        apiGetProcessInfo({ processFlowId: this.processFlowId }).then((res) => {
            // this.flowDetailList = res.data.data.flowDetailList;
            let data = res.data.data;
            this.form = data;
            this.tableData = data.flowDetailList || [];
        });
    }
}
</script>
