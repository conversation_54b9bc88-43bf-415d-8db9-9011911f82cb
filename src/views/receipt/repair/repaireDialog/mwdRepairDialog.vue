<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="提示"
        width="80%"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            :rules="detailFormRules"
            label-width="100px"
        >
            <el-row>
                <el-col :span="8">
                    <el-form-item label="井号: " prop="wellNum">
                        <el-select
                            style="width:80%"
                            v-model="detailForm.wellId"
                            placeholder="请选择"
                            :disabled="operType == 'EDIT' ? true : false"
                            @change="onSelectWellId"
                        >
                            <el-option
                                v-for="item in wellList"
                                :key="item.id"
                                :label="item.wellNumber"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号: " prop="jobNum">
                        <el-select
                            style="width:80%"
                            v-model="detailForm.jobNumber"
                            @change="onChangeJobNum"
                            :disabled="operType === 'EDIT'"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in jobList"
                                :key="item.id"
                                :label="item.jobNumber"
                                :value="item.jobNumber"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称: ">
                        <el-input
                            style="width:80%"
                            v-model="detailForm.customerName"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="返回日期: " prop="repairDate">
                        <el-date-picker
                            style="width:80%"
                            v-model="detailForm.repairDate"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            type="date"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <!-- <div>{{ kitNumber }}</div> -->
                    <el-form-item label="kit箱: " prop="kitNumber">
                        <el-input
                            style="width:80%"
                            v-model="detailForm.kitNumber"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人: ">
                        <el-input
                            style="width:80%"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.repairDetailList"
            style="margin-bottom:20px"
        >
            <el-table-column prop="invCode" label="存货编码"></el-table-column>
            <el-table-column>
                <template slot="header"
                    >仪器名称<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">{{
                        scope.row.invName
                    }}</span>
                    <!-- <el-autocomplete
                        v-else
                        v-model="scope.row.invName"
                        :fetch-suggestions="querySearchOne"
                        :trigger-on-focus="false"
                        class="inline-input"
                        placeholder="填写名称"
                        value-key="invName"
                        @select="onSelectInvName"
                    ></el-autocomplete> -->
                    <el-select
                        v-else
                        v-model="scope.row.invName"
                        @change="onSelectInvName"
                        placeholder="填写名称"
                        style="width:100%"
                    >
                        <el-option
                            v-for="item in nameCodeList"
                            :key="item.invCode"
                            :label="item.invName"
                            :value="item.invName"
                        >
                            <span>{{ item.invName }}</span>
                            <span style="color:#8492a6;font-size:13px">{{
                                item.invCode
                            }}</span>
                        </el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="序列号">
                <template slot="header">序列号列表</template>
                <template slot-scope="scope">
                    <div v-if="editObject.editRowIndex !== scope.$index">
                        <span>{{ scope.row.serialNumber }}</span>
                    </div>
                    <el-select
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .serialNumber
                        "
                        placeholder="序列表"
                        style="width:100%"
                        @change="onSelectSerialNum(scope)"
                    >
                        <el-option
                            v-for="item in filterSerialNumbers(scope)"
                            :key="item.serialNumber"
                            :label="item.serialNumber"
                            :value="item.serialNumber"
                        >
                        </el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="circulateBht">
                <template slot="header">
                    循环温度<span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateBht }}
                    </span>
                    <el-input-number
                        v-else
                        size="small"
                        controls-position="right"
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .circulateBht
                        "
                    ></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht">
                <template slot="header"
                    >最高温度<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index"
                        >{{ scope.row.maxBht }}
                    </span>
                    <el-input-number
                        v-else
                        size="small"
                        controls-position="right"
                        v-model="
                            detailForm.repairDetailList[scope.$index].maxBht
                        "
                    ></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="入井时长" prop="hour">
                <template slot="header"
                    >入井时长<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.hour }}
                    </span>
                    <el-input-number
                        size="small"
                        controls-position="right"
                        v-else
                        v-model="detailForm.repairDetailList[scope.$index].hour"
                    ></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
                <template slot="header"
                    >循环时间<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateHrs }}
                    </span>
                    <el-input-number
                        size="small"
                        controls-position="right"
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .circulateHrs
                        "
                    ></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="RunCount" prop="runCount">
                <template slot="header">
                    RunCount<span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.runCount }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].runCount
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.returnReason }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .returnReason
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.note }}
                    </span>
                    <el-input
                        v-else
                        v-model="detailForm.repairDetailList[scope.$index].note"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="float:right"
            type="primary"
            @click="onAddRow"
            >添加元素
        </el-button>
        <span slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false">取 消</el-button>
            <el-button
                type="primary"
                @click="onHandleSave"
                :disabled="
                    !!detailForm.isConfirm &&
                        detailForm.isConfirm == 1 &&
                        dataError
                "
                >确 定</el-button
            >
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Form as ElForm } from "element-ui/types/element-ui";
import {
    apiAddRepairOrder,
    apiGetRepairDetail,
    apiUpdateRepairOrder,
} from "@/api/repair";
import { apiJobInfo, apiJobListByWell, getTooData } from "@/api/mwd";
import { apiGetKitBoxInfo2 } from "@/api/kitBox";

interface IForm {
    jobNumber?: string;
    wellNumber?: string;
    wellId: string | number;
    kitId?: number | string;
    kitNumber?: string;
    repairDate?: string;
    customerName?: string;
    contactUser?: string;
    warehouseReceivingDate?: string;
    repairDetailList: IRepairDetail[];
    toolType?: string;
    isConfirm?: number;
}

interface IRepairDetail {
    repairDeviceId?: number;
    modelTypeId?: number;
    modelType?: string;
    serialNumber?: number | string;
    circulateBht?: number;
    maxBht?: number;
    hour?: number;
    circulateHrs?: number;
    runCount?: number;
    returnReason?: string;
    note?: string;
    invCode?: number;
    invName?: string;
    partNumber?: string;
    stockId?: number | null;
    serialNumberList?: string[];
    repairType?: string;
    quantity?: number;
}
@Component({})
export default class extends Vue {
    detailFormRules = {
        wellNumber: [{ required: true, message: "*", trigger: "blur" }],
        jobNumber: [{ required: true, message: "*", trigger: "blur" }],
        kitId: [{ required: true, message: "*", trigger: "blur" }],
        repairDate: [{ required: true, message: "*", trigger: "blur" }],
    };
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private operType = "ADD";
    private kitNumber = "";
    private detailDialogVisible = false;
    private dataError = false;
    private detailForm: IForm = {
        jobNumber: "",
        wellId: "",
        wellNumber: "",
        contactUser: "",
        kitId: undefined,
        kitNumber: "",
        repairDate: "",
        customerName: "",
        warehouseReceivingDate: "",
        repairDetailList: [],
        toolType: "MWD",
    };
    private options: any[] = [];
    @Prop({ default: [] }) private wellList!: [];
    private jobList: any[] = [];
    get nameCodeList() {
        let invCodeList = [
            ...new Set(this.options.map((item) => item.invCode)),
        ];
        return invCodeList.map((item) => {
            let tmp = this.options.find((option) => option.invCode === item);
            return { invCode: tmp.invCode, invName: tmp.invName };
        });
    }
    getItemByKey(arr: any[], field: string, val: string | number) {
        return arr[arr.findIndex((itm) => itm[field] == val)];
    }
    async getJobList() {
        return apiJobListByWell({ wellId: this.detailForm.wellId }).then(
            (res) => {
                return res.data.data || [];
            }
        );
    }
    getKitItemList() {
        apiGetKitBoxInfo2({ wellNumber: this.detailForm.wellNumber }).then(
            (res) => {
                const { data } = res.data;
                let arr: any = [];
                const kitBoxItemList = data ? data.kitBoxItemList || [] : [];
                const len = kitBoxItemList.length;
                for (let i = 0; i < len; i++) {
                    const invCode = kitBoxItemList[i].invCode;
                    const invName = kitBoxItemList[i].invName;
                    const partNumber = kitBoxItemList[i].partNumber;
                    const itemSpecificList = kitBoxItemList[i].itemSpecificList;
                    const itemListLen = itemSpecificList.length;
                    for (let j = 0; j < itemListLen; j++) {
                        let stockId = itemSpecificList[j].stockId;
                        let serialNumber = itemSpecificList[j].serialNumber;
                        arr.push({
                            stockId,
                            serialNumber,
                            invCode,
                            invName,
                            partNumber,
                        });
                    }
                    if (!itemListLen) {
                        arr.push({ stockId: null, invCode, invName });
                    }
                }
                this.options = arr;
            }
        );
    }
    filterSerialNumbers(scope: any) {
        if (scope.row.invCode == "") {
            return [];
        }
        return this.options.filter(
            (option) =>
                option.invCode === scope.row.invCode &&
                option.serialNumber &&
                option.serialNumber !== "无"
        );
    }
    async onSelectWellId() {
        this.detailForm.wellNumber = this.getItemByKey(
            this.wellList,
            "id",
            this.detailForm.wellId
        ).wellNumber;
        this.detailForm.repairDetailList = [];
        this.editObject = { editRow: false, editRowIndex: -1 };
        let jobList = await this.getJobList();
        this.jobList = jobList.filter((job: any) => job.jobStatus != 1);
        if (this.jobList.length == 0) {
            this.$message.error("该井不存在正在作业的job，请前往创建");
            this.detailForm.jobNumber = "";
            this.kitNumber = "";
            this.detailForm.kitNumber = "";
            this.detailForm.kitId = "";
            return;
        }
        this.detailForm.jobNumber = this.jobList[0].jobNumber;
        apiJobInfo({ jobId: this.jobList[0].id }).then((res1) => {
            const info = res1.data.data;
            this.kitNumber = info.kitNumber;
            this.detailForm.kitNumber = info.kitNumber;
            this.detailForm.kitId = info.kitBoxId;
            this.getKitItemList();
        });
    }
    onChangeJobNum() {
        this.detailForm.repairDetailList = [];
        this.editObject = { editRow: false, editRowIndex: -1 };

        let index = this.jobList.findIndex(
            (d) => d.jobNumber == this.detailForm.jobNumber
        );
        if (index == -1) return;
        apiJobInfo({ jobId: this.jobList[index].id }).then((res1) => {
            const info = res1.data.data;
            this.kitNumber = info.kitNumber;
            this.detailForm.kitNumber = info.kitNumber;
            this.detailForm.kitId = info.kitBoxId;
        });
    }
    onHandleSave() {
        // console.log(this.detailForm);
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        if (this.detailForm.repairDetailList.length == 0) {
            this.$message.error("请先添加返修工具");
            return;
        }
        (this.$refs["detailForm"] as ElForm).validate((valid) => {
            if (!valid) return;
            this.saveEditData();
        });
    }

    saveEditData() {
        //save data
        if (
            this.detailForm.repairDetailList == null ||
            this.detailForm.repairDetailList.length == 0
        ) {
            this.$message.error("请添加维修条目");
            return;
        }
        // for (let i = 0; i < this.detailForm.repairDetailList.length; i++) {
        //     let tmp = this.detailForm.repairDetailList[i];
        //     if (tmp.serialNumber.indexOf("auto_") > -1) {
        //         this.detailForm.repairDetailList[i].serialNumber = "";
        //     }
        // }
        if (this.operType === "EDIT") {
            //update
            apiUpdateRepairOrder(this.detailForm).then((res) => {
                this.$message.success("更新成功");
                this.$emit("getFilterRepairList");
                this.detailDialogVisible = false;
            });
        } else {
            //add
            let params = {
                jobNumber: this.detailForm.jobNumber,
                wellId: this.detailForm.wellId,
                wellNumber: this.detailForm.wellNumber,
                kitId: this.detailForm.kitId,
                kitNumber: this.detailForm.kitNumber,
                repairDate: this.detailForm.repairDate,
                customerName: this.detailForm.customerName,
                contactUser: this.detailForm.contactUser,
                repairDetailList: this.detailForm.repairDetailList,
            };
            apiAddRepairOrder(params).then(() => {
                this.detailDialogVisible = false;
                this.$emit("getFilterRepairList");
                this.$message.success("添加成功");
            });
        }
    }

    onSelectInvName(item: any) {
        console.log(this.options);
        //this.$set( this.detailForm.repairDetailList[this.editObject.editRowIndex],"invCode",item.invCode);
        let index = this.options.findIndex((it) => it.invName == item);
        if (index < 0) {
            this.$message.error("获取数据异常，请联系管理员");
            this.dataError = true;
            return;
        }

        this.detailForm.repairDetailList[
            this.editObject.editRowIndex
        ].invCode = this.options[index].invCode;
        this.detailForm.repairDetailList[
            this.editObject.editRowIndex
        ].invName = this.options[index].invName;
        this.detailForm.repairDetailList[
            this.editObject.editRowIndex
        ].partNumber = this.options[index].partNumber;
        this.detailForm.repairDetailList[
            this.editObject.editRowIndex
        ].serialNumber = "";
    }

    async onSelectSerialNum(scope: any) {
        let serialNumber = scope.row.serialNumber;
        let itemMap = this.options.find(
            (option) => option.serialNumber === serialNumber
        );
        scope.row.stockId = itemMap.stockId;

        let form = new FormData();
        form.append("jobId", String(this.detailForm.wellId));
        form.append("stockId", itemMap.stockId);

        getTooData(form).then((res) => {
            const info = res.data.data;
            if (!info) {
                return;
            }
            scope.row.circulateBht = info.circulateBht;
            scope.row.maxBht = info.maxBht;
            scope.row.hour = info.hour;
            scope.row.circulateHrs = info.circulateHrs;
            scope.row.runCount = info.runCount;
            scope.row.returnReason = info.returnReason;
            scope.row.note = info.note;
        });
    }

    private showDialog(type: string, repairId: number) {
        this.editObject.editRowIndex = -1;
        this.operType = type;
        this.options = [];
        if (type === "ADD") {
            this.detailDialogVisible = true;
            this.detailForm.jobNumber = "";
            this.detailForm.wellNumber = "";
            this.detailForm.wellId = "";
            this.detailForm.kitId = undefined;
            this.detailForm.repairDate = "";
            this.detailForm.customerName = "";
            this.detailForm.contactUser = "";
            // this.detailForm.toolType="MWD";
            this.detailForm.warehouseReceivingDate = "";
            this.detailForm.repairDetailList = [];
            this.detailForm.isConfirm = 0;
        } else {
            apiGetRepairDetail({ id: repairId }).then(async (res) => {
                this.detailForm = res.data.data;
                if (this.detailForm.wellNumber) {
                    this.detailForm.wellId = this.getItemByKey(
                        this.wellList,
                        "wellNumber",
                        this.detailForm.wellNumber
                    ).id;
                    this.jobList = await this.getJobList();
                    this.detailDialogVisible = true;
                }
                this.getKitItemList();
                // for (
                //     let i = 0;
                //     i < this.detailForm.repairDetailList.length;
                //     i++
                // ) {
                //     let tmp = this.detailForm.repairDetailList[i];
                //     if (
                //         tmp.serialNumber == "" &&
                //         tmp.stockId != "" &&
                //         tmp.stockId != null
                //     ) {
                //         this.detailForm.repairDetailList[i].serialNumber =
                //             "auto_" + tmp.stockId;
                //     }
                // }
            });
        }
    }

    private onAddRow() {
        //console.log(this.detailForm)
        if (
            this.detailForm.kitNumber == null ||
            this.detailForm.kitNumber == undefined ||
            this.detailForm.kitNumber == ""
        ) {
            this.$message.error("并没有组装kit箱");
            return;
        }
        
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.detailForm.repairDetailList.push({
            stockId: null,
            repairDeviceId: undefined,
            modelTypeId: undefined,
            modelType: "",
            serialNumber: undefined,
            circulateBht: undefined,
            maxBht: undefined,
            hour: undefined,
            circulateHrs: undefined,
            runCount: undefined,
            repairType: "UNDER_REPAIR",
            returnReason: undefined,
            quantity: 1,
            note: "",
        });
        this.editObject.editRow = true;
        this.editObject.editRowIndex =
            this.detailForm.repairDetailList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.repairDetailList.splice(scope.$index, 1);
    }

    private onSaveRow(scope: any) {
        for (let i = 0; i < this.detailForm.repairDetailList.length; i++) {
            let tmp = this.detailForm.repairDetailList[i];
            if (
                tmp.stockId &&
                i != scope.$index &&
                tmp.stockId === scope.row.stockId
            ) {
                this.$message.error("有重复项，请删除");
                return;
            }
        }
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.detailForm.repairDetailList[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.detailForm.repairDetailList.pop();
        }
        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
