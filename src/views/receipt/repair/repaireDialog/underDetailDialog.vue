<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="提示"
        width="98%"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="井号：" prop="wellNum">
                        {{ detailForm.wellNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号" prop="jobNum">
                        {{ detailForm.jobNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称：">
                        {{ detailForm.customerName }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返回日期：" prop="repairDate">
                        {{ detailForm.repairDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人：">
                        {{ detailForm.contactUser }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返修场地：">
                        {{ detailForm.serviceCenter }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="repairDetailList" style="margin-bottom:20px">
            <el-table-column label="存货编码" prop="invCode"> </el-table-column>
            <el-table-column label="名称" prop="invName"> </el-table-column>
            <el-table-column label="序列号" prop="serialNumber">
            </el-table-column>
            <el-table-column label="循环温度" prop="circulateBht" width="100">
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht" width="100"> </el-table-column>
            <el-table-column label="入井时长" prop="hour" width="100"> </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs" width="100">
            </el-table-column>
            <el-table-column label="泥浆类型" prop="mudType"> </el-table-column>
            <el-table-column label="弯度" prop="angle"> </el-table-column>
            <el-table-column label="耐温" prop="endureTemperature">
            </el-table-column>
            <el-table-column label="扣型" prop="claspType"> </el-table-column>
            <el-table-column label="扶正器尺寸/类型" prop="stbSize" width="140">
            </el-table-column>
            <el-table-column label="最大外径" prop="odMax"> </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
            </el-table-column>
            <el-table-column label="备注" prop="note"> </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiGetUnderRepairOrderInfo } from "@/api/repair";
@Component({})
export default class extends Vue {
    private detailDialogVisible = false;
    private detailForm: any = {};
    private repairDetailList: any[] = [];
    private showDialog(repairId: number) {
        apiGetUnderRepairOrderInfo({ id: repairId }).then((res) => {
            this.detailForm = res.data.data;
            this.repairDetailList = this.detailForm.repairDetailList || [];
            this.detailDialogVisible = true;
        });
    }
}
</script>
