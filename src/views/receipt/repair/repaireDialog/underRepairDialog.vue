<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="提示"
        width="98%"
    >
        <el-form
            ref="detailForm"
            :model="detailForm"
            :rules="detailFormRules"
            label-width="100px"
        >
            <el-row>
                <el-col :span="8">
                    <el-form-item label="井号" prop="wellNum">
                        <el-select
                            style="width:80%"
                            v-model="detailForm.wellNumber"
                            placeholder="请选择"
                            @change="onSelectWellNumber"
                        >
                            <el-option
                                v-for="item in wellList"
                                :key="item.id"
                                :label="item.wellNumber"
                                :value="item.wellNumber"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号" prop="jobNum">
                        <el-select
                            style="width:80%"
                            v-model="detailForm.jobNumber"
                            placeholder="请选择"
                            @change="onSelectJobNumber"
                        >
                            <el-option
                                v-for="item in jobList"
                                :key="item.id"
                                :label="item.jobNumber"
                                :value="item.jobNumber"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称">
                        <el-input
                            style="width:80%"
                            v-model="detailForm.customerName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返回日期" prop="repairDate">
                        <el-date-picker
                            style="width:80%"
                            v-model="detailForm.repairDate"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            type="date"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人">
                        <el-input
                            style="width:80%"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="返修场地">
                        <el-select
                            style="width:80%"
                            v-model="detailForm.serviceCenter"
                            placeholder=""
                        >
                            <el-option label="本厂" value="本厂"></el-option>
                            <el-option label="B5" value="B5"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.repairDetailList"
            style="margin-bottom:20px"
        >
            <el-table-column prop="invCode" label="存货编码"> </el-table-column>
            <el-table-column>
                <template slot="header">
                    名称<span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.invName }}
                    </span>
                    <el-select
                        v-else
                        @change="onSelectInvName(scope)"
                        v-model="
                            detailForm.repairDetailList[scope.$index].invCode
                        "
                    >
                        <el-option
                            v-for="item in underWellNameCodeList"
                            :key="item.invCode"
                            :label="item.invName"
                            :value="item.invCode"
                        >
                            <span>{{ item.invName }}</span>
                            <span style="color:#8492a6;font-size:13px">
                                {{ item.invCode }}
                            </span>
                        </el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="序列号">
                <template slot="header"
                    >序列号<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.serialNumber }}
                    </span>
                    <el-select
                        @change="onSelectSerialNumber(scope)"
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .serialNumber
                        "
                    >
                        <el-option
                            v-for="item in toolList.filter(tool=>tool.invCode===scope.row.invCode)"
                            :key="item.stockId"
                            :label="item.serialNumber"
                            :value="item.serialNumber"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="circulateBht">
                <template slot="header">
                    循环温度
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateBht }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .circulateBht
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht">
                <template slot="header">
                    最高温度
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.maxBht }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].maxBht
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="入井时长" prop="hour">
                <template slot="header">
                    入井时长<span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.hour }}
                    </span>
                    <el-input
                        v-else
                        v-model="detailForm.repairDetailList[scope.$index].hour"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
                <template slot="header">
                    循环时间
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.circulateHrs }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .circulateHrs
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="泥浆类型" prop="mudType">
                <template slot="header">
                    泥浆类型
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.mudType }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].mudType
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="弯度" prop="mudType">
                <template slot="header">
                    弯度
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.angle }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].angle
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="耐温" prop="endureTemperature">
                <template slot="header"
                    >耐温<span style="color:red;">*</span></template
                >
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.endureTemperature }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index]
                                .endureTemperature
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="扣型" prop="claspType">
                <template slot="header">
                    扣型
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.claspType }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].claspType
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="扶正器尺寸/类型" prop="stbSize" width="140">
                <template slot="header">
                    扶正器尺寸/类型
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.stbSize }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].stbSize
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="最大外径" prop="odMax">
                <template slot="header">
                    最大外径
                    <span style="color:red;">*</span>
                </template>
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.odMax }}
                    </span>
                    <el-input
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].odMax
                        "
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ getRepairType(scope.row.repairType) }}
                    </span>
                    <el-select
                        v-else
                        v-model="
                            detailForm.repairDetailList[scope.$index].repairType
                        "
                        placeholder=""
                    >
                        <el-option
                            v-for="it in repairTypes"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        {{ scope.row.note }}
                    </span>
                    <el-input
                        v-else
                        v-model="detailForm.repairDetailList[scope.$index].note"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <span v-if="editObject.editRowIndex !== scope.$index">
                        <el-button type="text" @click="onEditRow(scope)">
                            编辑
                        </el-button>
                        <el-button type="text" @click="onDeleteRow(scope)">
                            删除
                        </el-button>
                    </span>
                    <span v-else>
                        <el-button type="text" @click="onSaveRow(scope)">
                            保存
                        </el-button>
                        <el-button type="text" @click="onCancelRow(scope)">
                            取消
                        </el-button>
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            icon="el-icon-plus"
            style="float:right"
            type="primary"
            @click="onAddRow"
            >添加元素
        </el-button>
        <span slot="footer" class="dialog-footer">
            <el-button @click="detailDialogVisible = false">取 消</el-button>
            <el-button
                type="primary"
                @click="onHandleSave"
                :disabled="!!detailForm.isConfirm && detailForm.isConfirm == 1"
                >确 定</el-button
            >
        </span>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Form as ElForm } from "element-ui/types/element-ui";
import {
    apiAddUnderRepairOrder,
    apiGetUnderRepairOrderInfo,
    apiUpdateUnderRepairOrder,
} from "@/api/repair";
import { apiJobListByWell, getTooData } from "@/api/mwd";
import { apiGetDownholeToolList } from "@/api/underwell";

interface IForm {
    jobNumber?: string;
    wellNumber?: string;
    kitNumber?: string;
    repairDate?: string;
    customerName?: string;
    contactUser?: string;
    warehouseReceivingDate?: string;
    repairDetailList: IRepairDetail[];
    destination?: number;
}

interface IRepairDetail {
    repairDeviceId?: number;
    modelTypeId?: number;
    invName?: string;
    serialNumber?: number | string;
    circulateBht?: number;
    maxBht?: number;
    hour?: number;
    circulateHrs?: number;
    runCount?: number;
    mudType?: string;
    angle?: string;
    endureTemperature?: string;
    connection?: string;
    stbSize?: string;
    odMax?: string;
    returnReason?: string;
    note?: string;
}

function getIdFromItem(
    list: any[],
    itemKey: string,
    itemValue: any,
    idKey: string
) {
    let idx = list.findIndex((item: any) => item[itemKey] == itemValue);
    return idx === -1 ? null : list[idx][idKey];
}

@Component({})
export default class extends Vue {
    detailFormRules = {
        wellNumber: [{ required: true, message: "*", trigger: "blur" }],
        jobNumber: [{ required: true, message: "*", trigger: "blur" }],
        kitId: [{ required: true, message: "*", trigger: "blur" }],
        repairDate: [{ required: true, message: "*", trigger: "blur" }],
    };
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private operType = "ADD";
    private jobList = [];
    private toolList: any[] = [];
    private kitBoxItemList: any[] = [];
    private detailDialogVisible = false;
    private repairTypes = [
        { label: "返厂维修", value: "UNDER_REPAIR" },
        { label: "直接入库", value: "READY" },
        { label: "待定报废", value: "TO_BE_SCRAPPED" },
        { label: "报废", value: "SCRAP" },
    ];
    // TODO: 添加所有数据
    private detailForm: any = {
        jobNumber: "",
        wellNumber: "",
        contactUser: "",
        repairDate: "",
        kitNumber: "",
        customerName: "",
        toolType: "UNDER_WELL",
        warehouseReceivingDate: "",
        repairDetailList: [],
        destination: 1,
    };
    private wellId: number | null = null;
    private jobId: number | null = null;

    @Prop({ default: [] }) private wellList!: [];

    get getKitBoxInfoList() {
        return this.kitBoxItemList.map((item) => {
            return { invCode: item.invCode, invName: item.invName };
        });
    }
    get underWellNameCodeList() {
        const invCodeList = new Set(this.toolList.map((item) => item.invCode));
        let ret: any[] = [];
        invCodeList.forEach((item) => {
            let tmp = this.toolList.find((tool) => tool.invCode === item);
            ret.push({ invCode: tmp.invCode, invName: tmp.invName });
        });
        return ret;
    }

    getRepairType(repairType: any) {
        let index = this.repairTypes.findIndex((re) => re.value == repairType);
        return index == -1 ? "" : this.repairTypes[index].label;
    }
    // 根据井号查工单号列表
    onSelectWellNumber() {
        this.editObject.editRowIndex = -1;
        this.detailForm.repairDetailList = [];
        this.wellId = getIdFromItem(
            this.wellList,
            "wellNumber",
            this.detailForm.wellNumber,
            "id"
        );
        apiJobListByWell({ wellId: this.wellId }).then((res) => {
            let jobList = res.data.data || [];
            this.jobList = jobList.filter((job: any) => job.jobStatus != 1);
        });
        this.getToolList();
    }
    private getToolList() {
        apiGetDownholeToolList({ wellNumber: this.detailForm.wellNumber }).then(
            (res) => {
                this.toolList = res.data.data || [];
            }
        );
    }
    // 根据工单号查kit箱
    onSelectJobNumber() {
        this.jobId = getIdFromItem(
            this.jobList,
            "jobNumber",
            this.detailForm.jobNumber,
            "id"
        );
    }

    onHandleSave() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        (this.$refs["detailForm"] as ElForm).validate((valid) => {
            if (!valid) return;
            this.saveEditData();
        });
    }

    saveEditData() {
        //save data
        if (this.operType === "EDIT") {
            //update
            apiUpdateUnderRepairOrder(this.detailForm)
                .then((res) => {
                    let { data, message, code } = res.data;
                    if (code == 200) {
                        this.$message.success("更新成功");
                        this.$emit("getFilterRepairList");
                        this.detailDialogVisible = false;
                    } else {
                        this.$message.error("更新失败");
                    }
                })
                .catch((err) => {});
        } else {
            //add
            apiAddUnderRepairOrder(this.detailForm)
                .then((res) => {
                    let { data, message, code } = res.data;
                    if (code == 200) {
                        this.detailDialogVisible = false;
                        this.$emit("getFilterRepairList");
                        this.$message.success("更新成功");
                    } else {
                        this.$message.error("添加异常");
                    }
                })
                .catch((err) => {});
        }
    }

    private getItemSpecificList(scope: any) {
        if (scope.row.invName) {
            let idx = this.kitBoxItemList.findIndex(
                (item) => item.invName == scope.row.invName
            );
            console.log(idx);
            return this.kitBoxItemList[idx].itemSpecificList || [];
        } else {
            let ret: any = [];
            this.kitBoxItemList.forEach((item) => {
                if (item.itemSpecificList) {
                    ret = [...ret, ...item.itemSpecificList];
                }
            });
            return ret;
        }
    }
    private getItemFromKey(key: string, value: any) {
        return this.toolList.find((item) => item[key] == value);
    }
    onSelectInvName(scope: any) {
        const item = this.getItemFromKey("invCode", scope.row.invCode);
        let stockId = item.stockId;
        scope.row.invName = item.invName;
        scope.row.serialNumber = '';
        let form = new FormData();
        form.append("stockId", stockId);
        form.append("jobId", String(this.jobId));
        getTooData(form).then((res) => {
            let data = res.data.data || {};
            scope.row.note = data.note;
            scope.row.returnReason = data.returnReason;
            scope.row.odMax = data.odMax;
            scope.row.stbSize = data.stbSize;
            scope.row.connection = data.connection;
            scope.row.endureTemperature = data.endureTemperature;
            scope.row.angle = data.angle;
            scope.row.mudType = data.mudType;
            scope.row.runCount = data.runCount;
            scope.row.circulateHrs = data.circulateHrs;
            scope.row.hour = data.hour;
            scope.row.maxBht = data.maxBht;
            scope.row.circulateBht = data.circulateBht;
            scope.row.claspType = data.claspType;
        });
    }
    onSelectSerialNumber(scope: any) {
        // let itemSpecificList = this.getItemSpecificList(scope);
        const item = this.getItemFromKey(
            "serialNumber",
            scope.row.serialNumber
        );
        let stockId = item.stockId;
        scope.row.invName = item.invName;
        scope.row.stockId = stockId;
        let form = new FormData();
        form.append("stockId", stockId);
        form.append("jobId", String(this.jobId));
        getTooData(form).then((res) => {
            let data = res.data.data || {};
            scope.row.note = data.note;
            scope.row.returnReason = data.returnReason;
            scope.row.odMax = data.odMax;
            scope.row.stbSize = data.stbSize;
            scope.row.connection = data.connection;
            scope.row.endureTemperature = data.endureTemperature;
            scope.row.angle = data.angle;
            scope.row.mudType = data.mudType;
            scope.row.runCount = data.runCount;
            scope.row.circulateHrs = data.circulateHrs;
            scope.row.hour = data.hour;
            scope.row.maxBht = data.maxBht;
            scope.row.circulateBht = data.circulateBht;
        });
    }

    private showDialog(type: string, repairId: number) {
        this.editObject.editRowIndex = -1;
        this.operType = type;
        if (type === "ADD") {
            this.detailDialogVisible = true;
            this.detailForm.jobNumber = "";
            this.detailForm.wellNumber = "";
            this.detailForm.kitNumber = undefined;
            this.detailForm.repairDate = "";
            this.detailForm.customerName = "";
            this.detailForm.contactUser = "";
            this.detailForm.warehouseReceivingDate = "";
            this.detailForm.repairDetailList = [];
        } else {
            apiGetUnderRepairOrderInfo({ id: repairId }).then((res) => {
                this.detailForm = res.data.data;
                if (this.detailForm.wellNumber) {
                    this.wellId = getIdFromItem(
                        this.wellList,
                        "wellNumber",
                        this.detailForm.wellNumber,
                        "id"
                    );
                    apiJobListByWell({ wellId: this.wellId }).then((res) => {
                        let jobList = res.data.data || [];
                        this.jobList = jobList.filter(
                            (job: any) => job.jobStatus != 1
                        );
                        this.onSelectJobNumber();
                    });
                }
                this.getToolList();
                this.detailDialogVisible = true;
            });
        }
    }

    private onAddRow() {
        this.preObject = {};
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.repairDetailList.push({
            repairDeviceId: "",
            modelTypeId: "",
            invName: "",
            serialNumber: "",
            circulateBht: "",
            maxBht: "",
            hour: "",
            circulateHrs: "",
            runCount: "",
            mudType: "",
            angle: "",
            endureTemperature: "",
            connection: "",
            stbSize: "",
            odMax: "",
            returnReason: "",
            note: "",
        });
        this.editObject.editRow = true;
        this.editObject.editRowIndex =
            this.detailForm.repairDetailList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.repairDetailList.splice(scope.$index, 1);
    }

    private onSaveRow(scope: any) {
        this.editObject.editRowIndex = -1;
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.detailForm.repairDetailList[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.detailForm.repairDetailList.pop();
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }
}
</script>
