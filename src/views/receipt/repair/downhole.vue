<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">钻具通知单</div>
            </div>
            <div class="block">
                <el-row :gutter="0">
                    <el-col :span="5">
                        <span style="font-size: 16px">井号: </span>
                        <el-select
                            v-model="filterForm.wellNumber"
                            clearable
                            label="1"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in wellList"
                                :key="item.wellNumber"
                                :label="item.wellNumber"
                                :value="item.wellNumber"
                            >
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="9">
                        <span style="font-size: 16px">日期: </span>
                        <el-date-picker
                            v-model="filterForm.dateRange"
                            end-placeholder="结束日期"
                            range-separator="至"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            start-placeholder="开始日期"
                            type="daterange"
                        >
                        </el-date-picker>
                    </el-col>
                    <!-- <el-col :span="6">
                        <span style="font-size: 16px">返修类型: </span>
                        <el-select
                            v-model="filterForm.repairType" diabled
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in repairTypes"
                                :key="item.repairType"
                                :label="item.typeName"
                                :value="item.repairType"
                            >
                            </el-option>
                        </el-select>
                    </el-col> -->
                    <el-col :span="4">
                        <el-button
                            icon="el-icon-search"
                            type="primary"
                            @click="onSearch"
                            >查 询</el-button
                        >
                        <el-button
                            icon="el-icon-edit"
                            type="primary"
                            @click="onAddRepairOrder"
                            >添加</el-button
                        >
                    </el-col>
                </el-row>
            </div>
            <div class="simple-line"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    width="220"
                    label="返修单号"
                    prop="repairCode"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                ></el-table-column>
                <el-table-column
                    label="kit箱"
                    prop="kitNumber"
                ></el-table-column>
                <el-table-column
                    label="返修日期"
                    prop="repairDate"
                ></el-table-column>
                <el-table-column
                    label="客户名称"
                    prop="customerName"
                ></el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                ></el-table-column>
                <el-table-column
                    label="库房接收日期"
                    prop="warehouseReceivingDate"
                ></el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            icon="el-icon-document"
                            size="medium"
                            circle
                            style="padding:6px 6px"
                            type="primary"
                            @click="onDetail(scope.row)"
                        >
                        </el-button>
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="库房确认"
                            placement="top"
                        >
                            <el-button
                                icon="el-icon-check"
                                size="medium"
                                circle
                                style="padding:6px 6px"
                                :disabled="
                                    scope.row.warehouseReceivingDate != null &&
                                        scope.row.warehouseReceivingDate !== ''
                                "
                                type="success"
                                @click="onConfirm(scope.row)"
                            >
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="danger"
                            icon="el-icon-delete"
                            style="padding:6px 6px"
                            circle
                            :disabled="
                                scope.row.warehouseReceivingDate != null &&
                                    scope.row.warehouseReceivingDate !== ''
                            "
                            @click="onDelete(scope.row)"
                        ></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <MwdRepairDialog @getFilterRepairList="getFilterRepairList" :wellList="wellList" ref="MwdRepairDialog" />
        <UnderRepairDialog @getFilterRepairList="getFilterRepairList" :wellList="wellList" ref="UnderRepairDialog" />
        <UnderDetailDialog ref="UnderDetailDialog"></UnderDetailDialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import {
    apiGetRepairUnderList,
    apiGetRepairMWDList,
    apiConfirmRepairOrder,
    apiDeleteRepairOrder,
    apiConfirmUnderRepairOrder,
    apiDeleteUnderRepairOrder,
} from "@/api/repair";
import { apiJobListByWell } from "@/api/mwd";
import MwdRepairDialog from "./repaireDialog/mwdRepairDialog.vue";
import UnderRepairDialog from "./repaireDialog/underRepairDialog.vue";
import UnderDetailDialog from "./repaireDialog/underDetailDialog.vue";
import { ConstantModule } from "@/store/modules/constant";
@Component({
    components: { MwdRepairDialog, UnderRepairDialog, UnderDetailDialog },
})
export default class extends Vue {
    filterForm = {
        wellNumber: "",
        dateRange: [],
        repairType: 2,
    };
    private detailDialogVisible = false;

    //private repairItems:any=[];
    private wellList: any = [];
    private repairTypes: any = [];
    private tableData: any = [];
    private jobList = [];
    mounted() {
        this.getWellList();
        this.getRepairTypes();
        this.getFilterRepairList();
    }
    /**
     * 获取井列表
     * */
    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }
    /**
     * 获取返修单类型
     * */
    getRepairTypes() {
        this.repairTypes = [
            //{ repairType: 1, typeName: "仪器返修" },
            { repairType: 2, typeName: "工具返修" },
        ];
    }
    /**
     * 获取返修单列表
     * */
    getFilterRepairList() {
        //this.filterTableData=[];
        let params: any = {};
        if (this.filterForm.wellNumber != "")
            params.wellNumber = this.filterForm.wellNumber;
        if (this.filterForm.dateRange.length > 0) {
            params.startDate = this.filterForm.dateRange[0];
            params.endDate = this.filterForm.dateRange[1];
        }
        if (this.filterForm.repairType == 1) {
            //mwd repair list
            apiGetRepairMWDList(params)
                .then((res) => {
                    let { data, message, code } = res.data;
                    this.tableData = data;
                })
                .catch((err) => {});
        } else {
            //tool repair list
            apiGetRepairUnderList(params)
                .then((res) => {
                    let { data, message, code } = res.data;
                    this.tableData = data;
                })
                .catch((err) => {});
        }
    }
    onSearch() {
        this.getFilterRepairList();
    }
    onAddRepairOrder() {
        if (this.filterForm.repairType === 1) {
            (this.$refs.MwdRepairDialog as any).showDialog("ADD");
        } else {
            (this.$refs.UnderRepairDialog as any).showDialog("ADD");
        }
    }
    onDetail(row: any) {
        if (this.filterForm.repairType === 1) {
            (this.$refs.MwdRepairDialog as any).showDialog(
                "EDIT",
                row.repairId
            );
        } else {
            if (row.warehouseReceivingDate) {
                (this.$refs.UnderDetailDialog as any).showDialog(row.repairId);
            } else {
                (this.$refs.UnderRepairDialog as any).showDialog(
                    "EDIT",
                    row.repairId
                );
            }
        }
    }

    onConfirm(row: any) {
        this.$confirm("确认收到货物？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            let form = new FormData();
            form.append("repairId", row.repairId);
            if (this.filterForm.repairType === 1) {
                apiConfirmRepairOrder(form)
                    .then((res) => {
                        this.getFilterRepairList();
                    })
                    .catch((err) => {});
            } else {
                apiConfirmUnderRepairOrder(form)
                    .then((res) => {
                        this.getFilterRepairList();
                    })
                    .catch((err) => {});
            }
        });
    }
    onDelete(row: any) {
        this.$confirm("确认删除吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            if (this.filterForm.repairType === 1) {
                apiDeleteRepairOrder({ repairId: row.repairId })
                    .then((res) => {
                        this.getFilterRepairList();
                    })
                    .catch((err) => {});
            } else {
                apiDeleteUnderRepairOrder({ repairId: row.repairId })
                    .then((res) => {
                        this.getFilterRepairList();
                    })
                    .catch((err) => {});
            }
        });
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
