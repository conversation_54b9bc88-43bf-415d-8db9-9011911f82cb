<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">kit箱入库</div>
            </div>
            <el-row :gutter="25">
                <el-col>
                    <span style="font-size: 16px">kit箱：</span>
                    <el-select
                        v-model="kitBoxId"
                        @change="onSelectKitItems"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in kits"
                            :key="item.kitBoxId"
                            :label="item.kitNumber"
                            :value="item.kitBoxId"
                        >
                        </el-option>
                    </el-select>
                    <el-button
                        type="primary"
                        style="float:right;"
                        :disabled="this.kitBoxId === ''"
                        @click="onConfirm"
                    >
                        kit箱返厂
                    </el-button>
                </el-col>
            </el-row>
            <div style="color:red;">
                <el-alert
                    style="margin:20px 0"
                    title="kit箱返厂"
                    type="warning"
                    description="kit箱与井脱离关系"
                    show-icon
                >
                </el-alert>
                <el-alert
                    style="margin:20px 0"
                    title="kit箱入库前"
                    type="warning"
                    description=' kit箱入库前，请一一验收kit箱内部工具，工具集工有4种状态：kit箱中待命、待定报废、维修中、落井，再进行 "kit箱返厂" 操作'
                    show-icon
                >
                </el-alert>
                <el-alert
                    style="margin:20px 0"
                    title="kit箱中待命"
                    type="warning"
                    description="仍然属于kit箱，其它三种状态，该工具不属于kit箱"
                    show-icon
                >
                </el-alert>
            </div>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="tableData"
            >
                <el-table-column label="编号" prop="stockId">
                    <template slot-scope="scope">
                        {{
                            scope.row.stockId !== "undefined"
                                ? scope.row.stockId
                                : "kit箱待命"
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="库存编号"
                    prop="invCode"
                ></el-table-column>
                <el-table-column
                    label="工具名称"
                    prop="invName"
                ></el-table-column>
                <el-table-column
                    label="PartNumber"
                    prop="partNumber"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                ></el-table-column>
                <el-table-column label="数量" prop="quantity"></el-table-column>
                <el-table-column label="状态" prop="toolType">
                    <template slot-scope="scope">
                        {{
                            scope.row.stockId == "undefined"
                                ? "kit箱待命"
                                : scope.row.toolType &&
                                  scope.row.toolType == 7008
                                ? "kit箱待命"
                                : "上井服务"
                        }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <!--kit箱中待命-->
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="kit箱中待命"
                            placement="top-start"
                        >
                            <svg-icon
                                v-if="
                                    scope.row.stockId > 0 &&
                                        scope.row.toolType &&
                                        scope.row.toolType !== 7008
                                "
                                class="ready-icon"
                                name="ready"
                                style="cursor:pointer;margin-right:20px;"
                                width="25px"
                                height="25px"
                                @click="onUpdateStatus(scope, '7008')"
                            />
                        </el-tooltip>
                        <!--待定报废-->
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="待定报废"
                            placement="top-start"
                        >
                            <svg-icon
                                class="scrap-icon"
                                name="scrap"
                                style="cursor:pointer;margin-right:20px;"
                                width="25px"
                                height="25px"
                                @click="onUpdateStatus(scope, '7003')"
                            />
                        </el-tooltip>
                        <!--维修中-->
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="维修中"
                            placement="top-start"
                        >
                            <svg-icon
                                class="repair-icon"
                                name="repair"
                                style="cursor:pointer;margin-right:20px;"
                                width="25px"
                                height="25px"
                                @click="onUpdateStatus(scope, '7001')"
                            />
                        </el-tooltip>
                        <!--落井-->
                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="落井"
                            placement="top-start"
                        >
                            <svg-icon
                                class="well-icon"
                                name="well"
                                style="cursor:pointer;margin-right:20px;"
                                width="25px"
                                height="25px"
                                @click="onUpdateStatus(scope, '7006')"
                            />
                        </el-tooltip>
                        <!-- <span v-if="scope.row.toolType === 7008">kit箱中待命</span> @click.stop="click"
                        <el-select v-else v-model="scope.row.newStatus" placeholder="">
                            <el-option v-for="t in toolTypes" :label="t.label" :value="t.value"></el-option>
                        </el-select> -->
                    </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="160">
                    <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" content="确认零件状态" placement="right">
                            <el-button
                                v-if="scope.row.toolType !== 7008"
                                type="success"
                                style="padding:6px 6px"
                                size="medium"
                                icon="el-icon-check"
                                @click="onConfirmStockStatus(scope)"
                                circle
                                plain>
                            </el-button>
                        </el-tooltip>
                    </template>
                </el-table-column> -->
            </el-table>
        </div>
    </div>
</template>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import {
    apiConfirmStatus,
    apiGetKitBoxInfo,
    apiGetKitBoxList,
    apiRemoveKitBox,
} from "@/api/kitBox";

@Component({})
export default class extends Vue {
    private kitBoxId: any = "";
    private kits: any[] = [];
    private tableData: any[] = [];
    private saveObj = { rowIndex: -1, status: undefined };
    toolTypes = [
        { label: "维修保养", value: 7001 },
        { label: "待定报废", value: 7003 },
        { label: "kit箱中待命", value: 7008 },
        { label: "落井", value: 7005 },
    ];

    mounted() {
        this.initKits();
    }

    private onConfirm() {
        /**kit入库是7004 待命状态；
         * kit箱内的工具入库处于在kit箱中待命 7008
         */
        if (this.tableData.findIndex((x) => x.toolType !== 7008) != -1) {
            this.$message.error("请确认所有工具状态");
        } else {
            this.$confirm("确认kit返厂？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiConfirmStatus({ kitBoxId: this.kitBoxId, status: 7004 })
                    .then(() => {
                        this.initKits();
                        this.onSelectKitItems();
                        this.$message.success("确认入库成功");
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            });
        }
    }

    private onConfirmStockStatus(scope: any) {
        apiRemoveKitBox({
            kitBoxId: this.kitBoxId,
            stockId: scope.row.stockId,
            status: scope.row.newStatus,
        })
            .then(() => {
                this.onSelectKitItems();
                this.$message.success("入库零件状态成功");
            })
            .catch((err) => {
                console.log(err);
            });
    }

    private onUpdateStatus(scope: any, newStatus: any) {
        if (scope.row.stockId === "undefined" && scope.row.quantity > 1) {
            let quantity = scope.row.quantity;
            this.$prompt(
                `共有${quantity}个仪器，请填写要更改状态的数量`,
                "提示",
                {
                    inputValue: quantity,
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    closeOnClickModal: false,
                    inputValidator(inp: any) {
                        inp = parseInt(inp);
                        if (isNaN(inp) || inp < 1 || inp > quantity) {
                            return `请输入介于1-${quantity}的数字`;
                        }
                        return true;
                    },
                }
            ).then(({ value }: any) => {
                value = parseInt(value);
                if (isNaN(value)) {
                    this.$message.error("请填写正确的数字");
                    return;
                }
                if (value < 1 || value > quantity) {
                    this.$message.error(`数字必须介于1-${quantity}之间`);
                    return;
                }
                apiRemoveKitBox({
                    kitBoxId: this.kitBoxId,
                    stockId:
                        scope.row.stockId == "undefined"
                            ? null
                            : scope.row.stockId,
                    invCode: scope.row.invCode,
                    status: newStatus,
                    quantity: value,
                }).then((res) => {
                    this.onSelectKitItems();
                });
            });
        } else {
            this.$confirm("确认该状态吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                apiRemoveKitBox({
                    kitBoxId: this.kitBoxId,
                    stockId:
                        scope.row.stockId == "undefined"
                            ? null
                            : scope.row.stockId,
                    invCode: scope.row.invCode,
                    status: newStatus,
                }).then((res) => {
                    this.onSelectKitItems();
                });
            });
        }
    }

    initKits() {
        apiGetKitBoxList({})
            .then((res) => {
                let { data, message, code } = res.data;
                let kits: any[] = [];
                for (let i = 0; i < data.length; i++) {
                    /**7002 上井服务 */
                    if (data[i].status == 7002) kits.push(data[i]);
                }
                this.kits = kits;
            })
            .catch((err) => {
                console.log(err);
            });
    }

    onSelectKitItems() {
        apiGetKitBoxInfo({ kitBoxId: this.kitBoxId })
            .then((res) => {
                let { data, message, code } = res.data;
                this.tableData = [];
                for (let i = 0; i < data.kitBoxItemList.length; i++) {
                    let invCode = data.kitBoxItemList[i].invCode;
                    let invName = data.kitBoxItemList[i].invName;
                    let partNumber = data.kitBoxItemList[i].partNumber;
                    let quantity = Number(
                        data.kitBoxItemList[i].actualQuantity || 0
                    );
                    const itemSpecificList =
                        data.kitBoxItemList[i].itemSpecificList;
                    const itemLen = itemSpecificList.length;
                    if (itemLen == 0)
                        this.tableData.push({
                            stockId: "undefined",
                            serialNumber: "",
                            invCode: invCode,
                            invName: invName,
                            partNumber: partNumber,
                            toolType: 7008,
                            newStatus: 7001,
                            quantity: quantity,
                        });
                    else {
                        for (let j = 0; j < itemLen; j++) {
                            let stockId = itemSpecificList[j].stockId;
                            let sn = itemSpecificList[j].serialNumber;
                            let status = itemSpecificList[j].status;
                            this.tableData.push({
                                stockId: stockId,
                                serialNumber: sn,
                                invCode: invCode,
                                invName: invName,
                                partNumber: partNumber,
                                toolType: status == 7002 ? 7001 : status,
                                newStatus: 7001,
                                quantity: 1,
                            });
                        }
                        if (quantity > itemLen) {
                            this.tableData.push({
                                stockId: "undefined",
                                serialNumber: "",
                                invCode: invCode,
                                invName: invName,
                                partNumber: partNumber,
                                toolType: 7008,
                                newStatus: 7001,
                                quantity: quantity - itemLen,
                            });
                        }
                    }
                }
            })
            .catch((err) => {
                console.log(err);
            });
    }
}
</script>
