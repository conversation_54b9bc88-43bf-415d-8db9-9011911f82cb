<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <el-button type="primary" @click="onAddRequisition">
                添加调拨单
            </el-button>
            <div class="simple-line"></div>
            <el-table
                :data="requisitionList"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column prop="transferCode" label="调拨单号">
                </el-table-column>
                <el-table-column prop="wellNumber" label="井号">
                </el-table-column>
                <el-table-column prop="toolType" label="调拨类型">
                    <template slot-scope="{}">
                        井下工具
                    </template>
                </el-table-column>
                <el-table-column prop="transferDate" label="返回日期">
                </el-table-column>
                <el-table-column prop="destination" label="目的地">
                </el-table-column>
                <el-table-column
                    prop="warehouseConfirmTime"
                    label="库房确认时间"
                ></el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            @click="onDetail(scope)"
                            type="primary"
                            style="padding:6px 6px"
                            size="medium"
                            circle
                            icon="el-icon-document"
                        >
                        </el-button>
                        <el-tooltip
                            content="库房确认"
                            placement="top"
                            effect="dark"
                        >
                            <el-button
                                circle
                                type="success"
                                style="padding:6px 6px"
                                :disabled="scope.row.isConfirm === 1"
                                size="medium"
                                @click="onWarehouseConfirm(scope)"
                                icon="el-icon-check"
                            >
                            </el-button>
                        </el-tooltip>

                        <el-button
                            :disabled="scope.row.isConfirm === 1"
                            @click="onDeleteTransfer(scope)"
                            type="danger"
                            circle
                            style="padding:6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                        >
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog
            custom-class="requisition-dialog"
            :close-on-click-modal="false"
            title="调拨单"
            :visible.sync="showRequistionDialog"
        >
            <el-form ref="detailForm" :model="detailForm" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item required label="井号" prop="wellNumber">
                            <el-select
                                style="width:80%"
                                :disabled="
                                    !!detailForm.transferId &&
                                        detailForm.transferId > 0
                                "
                                v-model="detailForm.wellNumber"
                                placeholder="请选择"
                                @change="onToolistChange"
                            >
                                <el-option
                                    v-for="item in wellList"
                                    :key="item.wellNumber"
                                    :label="item.wellNumber"
                                    :value="item.wellNumber"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item required label="工具类型" prop="toolType">
                            井下工具
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            required
                            label="返回日期"
                            prop="transferDate"
                        >
                            <el-date-picker
                                style="width:80%"
                                v-model="detailForm.transferDate"
                                type="date"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            required
                            label="目标井"
                            prop="destination"
                        >
                            <el-select
                                style="width:80%"
                                v-model="detailForm.destination"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in wellList"
                                    :key="item.wellNumber"
                                    :label="item.wellNumber"
                                    :value="item.wellNumber"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                style="margin-bottom:20px"
                :data="detailForm.transferDetailList"
            >
                <el-table-column label="编号">
                    <template slot-scope="scope">
                        <div class="cell-content">
                            {{ scope.$index + 1 }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="存货编码" prop="invCode">
                </el-table-column>
                <el-table-column>
                    <template slot="header">
                        产品名称<span style="color:red;">*</span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex != scope.$index">
                            {{ scope.row.invName }}
                        </span>
                        <el-select
                            v-else
                            v-model="scope.row.invCode"
                            @change="onSelectInvName(scope)"
                        >
                            <el-option
                                v-for="item in nameCodeList"
                                :key="item.invCode"
                                :label="item.invName"
                                :value="item.invCode"
                            >
                                <span>{{ item.invName }}</span>
                                <span style="color:#8492a6;font-size:13px">
                                    {{ item.invCode }}
                                </span>
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="序列号">
                    <template slot="header">
                        序列号<span style="color:red;">*</span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex != scope.$index">
                            {{ scope.row.serialNumber }}
                        </span>
                        <el-select
                            v-else
                            v-model="scope.row.stockId"
                            @change="onSerialNumberChange(scope)"
                        >
                            <el-option
                                v-for="item in filterSerialNumbers(scope)"
                                :key="item.stockId"
                                :label="item.serialNumber"
                                :value="item.stockId"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="数量">
                    <template slot="header"
                        >数量<span style="color:red;">*</span></template
                    >
                    <template slot-scope="scope">
                        <!-- v-if="editObject.editRowIndex != scope.$index" -->
                        <span>
                            {{ scope.row.quantity }}
                        </span>
                        <!-- <el-input v-else v-model="detailForm.transferDetailList[scope.$index].quantity"></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column prop="note">
                    <template slot="header">
                        备注<span style="color:red;">*</span>
                    </template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex != scope.$index">
                            {{ scope.row.note }}
                        </span>
                        <el-input
                            v-else
                            v-model="
                                detailForm.transferDetailList[scope.$index].note
                            "
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex != scope.$index">
                            <el-button type="text" @click="onEditRow(scope)">
                                编辑
                            </el-button>
                            <el-button type="text" @click="onDeleteRow(scope)">
                                删除
                            </el-button>
                        </span>
                        <span v-else>
                            <el-button type="text" @click="onSaveRow(scope)">
                                保存
                            </el-button>
                            <el-button type="text" @click="onCancelRow(scope)">
                                取消
                            </el-button>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-button
                icon="el-icon-plus"
                type="primary"
                style="float:right"
                @click="onAddRow"
            >
                添加元素
            </el-button>
            <span slot="footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="onConfirm"
                    :disabled="
                        !!detailForm.isConfirm && detailForm.isConfirm == 1
                    "
                    >确认</el-button
                >
            </span>
        </el-dialog>
        <DetailDialog ref="DetailDialog"></DetailDialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { Form as ElForm } from "element-ui";
import {
    apiAddTransfer,
    apiConfirmTransfer,
    apiDeleteTransfer,
    apiGetTransferInfo,
    apiGetTransferList,
    apiUpdateTransfer,
} from "@/api/requisition";
import { apiGetUnderholeToolsByWell } from "@/api/transfer";
import { apiGetKitBoxInfo2 } from "@/api/kitBox";
import DetailDialog from "./detail.vue";
import { ConstantModule } from "@/store/modules/constant";
interface IForm {
    transferId?: string;
    wellNumber?: string;
    transferDate?: string;
    destination?: string;
    toolType?: string;
    transferDetailList: ITransferDetail[];
}

interface ITransferDetail {
    note?: string;
    quantity?: string;
    transferDesc?: string;
    serialNumber?: string;
    stockId?: 0;
    invName?: string;
}

@Component({ components: { DetailDialog } })
export default class extends Vue {
    private operType = "ADD";
    private detailForm: IForm = { transferDetailList: [] };
    private showRequistionDialog = false;
    private requisitionList = [];
    private clickIndex = [-1, -1];
    private tableData: ITransferDetail[] = [];
    private editObject = { editRow: false, editRowIndex: -1 };
    private preObject: any = {};
    private wellList: any = [];
    private options: any[] = [];
    private loading = false;
    private toolTypes = [
        { label: "井下工具", value: "UNDER_WELL" },
        { label: "MWD仪器", value: "MWD" },
    ];
    private toolType = "UNDER_WELL";
    // <el-option label="井下工具" value="UNDER_WELL" />
    //                     <el-option label="MWD仪器" value="MWD" /> -->
    get nameCodeList() {
        let invCodeList = [
            ...new Set(this.options.map((item) => item.invCode)),
        ];
        return invCodeList.map((item) => {
            let tmp = this.options.find((option) => option.invCode === item);
            return { invCode: tmp.invCode, invName: tmp.invName };
        });
    }
    private mounted() {
        this.$nextTick(() => {
            this.getTransferList();
            this.getWellList();
        });
    }

    private getToolType(v: any) {
        let index = this.toolTypes.findIndex((r) => r.value == v);
        return index == -1 ? "" : this.toolTypes[index].label;
    }

    private onClearToolItems() {
        this.editObject.editRowIndex = -1;
        this.detailForm.transferDetailList = [];
    }
    private onToolistChange() {
        this.getToolList();
        this.onClearToolItems();
    }
    private getToolList() {
        if (!this.detailForm.wellNumber) {
            return;
        }
        this.getUnderToolList();
    }
    filterSerialNumbers(scope: any) {
        if (!scope.row.invCode) {
            return [];
        }
        return this.options.filter(
            (option) =>
                option.invCode === scope.row.invCode &&
                option.serialNumber &&
                option.serialNumber !== "无"
        );
    }
    /* 
        工具列表数据结构(TODO: 井下工具数量先默认是1)
        invName, invCode, quantity, serialNumber
    */

    // 获取MWD工具列表
    private getMwdToolList() {
        apiGetKitBoxInfo2({ wellNumber: this.detailForm.wellNumber }).then(
            (res) => {
                const { data } = res.data;
                let arr: any = [];
                const kitBoxItemList = data ? data.kitBoxItemList || [] : [];
                const len = kitBoxItemList.length;
                for (let i = 0; i < len; i++) {
                    const invCode = kitBoxItemList[i].invCode;
                    const invName = kitBoxItemList[i].invName;
                    const partNumber = kitBoxItemList[i].partNumber;
                    const itemSpecificList = kitBoxItemList[i].itemSpecificList;
                    const itemListLen = itemSpecificList.length;
                    for (let j = 0; j < itemListLen; j++) {
                        let stockId = itemSpecificList[j].stockId;
                        let serialNumber = itemSpecificList[j].serialNumber;
                        arr.push({
                            stockId,
                            serialNumber,
                            invCode,
                            invName,
                            partNumber,
                        });
                    }
                    if (!itemListLen) {
                        arr.push({ stockId: null, invCode, invName });
                    }
                }
                this.options = arr;
            }
        );
    }
    // 获取井下工具列表
    private getUnderToolList() {
        apiGetUnderholeToolsByWell({
            wellNumber: this.detailForm.wellNumber,
        }).then((res) => {
            let list = res.data.data || [];
            this.options = list;
        });
    }
    private getTransferList() {
        apiGetTransferList({}).then((res) => {
            this.requisitionList = res.data.data;
        });
    }
    private onSelectInvName(scope: any) {
        const map = this.nameCodeList.find(
            (item) => item.invCode === scope.row.invCode
        );
        scope.row.invName = map.invName;
        scope.row.serialNumber = "";
    }
    private onAddRequisition() {
        this.detailForm = {
            transferId: undefined,
            wellNumber: undefined,
            transferDate: undefined,
            destination: undefined,
            transferDetailList: [],
            stockId: null,
            isConfirm: 0,
            toolType: this.toolType,
        };
        this.editObject.editRowIndex = -1;
        this.showRequistionDialog = true;
        this.operType = "ADD";
        this.$nextTick(() => {
            (this.$refs.detailForm as ElForm).clearValidate();
        });
    }

    private onDeleteTransfer(scope: any) {
        if (scope.row.isConfirm && scope.row.isConfirm == 1) {
            this.$message.error("库房确认后不允许修改");
            return;
        }
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeleteTransfer({ transferId: scope.row.transferId })
                    .then((res) => {
                        this.getTransferList();
                    })
                    .catch((err) => {});
            })
            .catch(() => {});
    }

    /**
     * 获取井列表
     * */
    async getWellList() {
        this.wellList = await ConstantModule.getWellList();
    }

    private onAddRow() {
        if (!this.detailForm.wellNumber) {
            this.$message.error("请先选择井号");
            return;
        }
        
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.detailForm.transferDetailList.push({
            transferDesc: undefined,
            quantity: 1,
            note: undefined,
            serialNumber: "",
            stockId: undefined,
            invName: "",
        });
        this.editObject.editRow = true;
        this.editObject.editRowIndex =
            this.detailForm.transferDetailList.length - 1;
    }

    private onEditRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDeleteRow(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.detailForm.transferDetailList.splice(scope.$index, 1);
    }

    private onSaveRow(scope: any) {
        if (!scope.row.serialNumber) {
            scope.row.stockId = null;
        }
        this.editObject.editRowIndex = -1;
    }

    private onSerialNumberChange(scope: any) {
        const itemMap = this.options.find(
            (item) => item.stockId == scope.row.stockId
        );

        scope.row.serialNumber = itemMap.serialNumber;
        console.log(scope, itemMap);
    }

    private onCancelRow(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.detailForm.transferDetailList[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.detailForm.transferDetailList.pop();
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }

    private onDetail(scope: any) {
        if (scope.row.warehouseConfirmTime) {
            (this.$refs.DetailDialog as any).showDialog(scope);
        } else {
            this.operType = "EDIT";
            this.editObject.editRowIndex = -1;
            apiGetTransferInfo({ transferId: scope.row.transferId }).then(
                (res) => {
                    this.detailForm = res.data.data;
                    this.getToolList();
                    this.showRequistionDialog = true;
                }
            );
        }
    }

    private onConfirm() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        if (this.detailForm.transferDetailList.length == 0) {
            this.$message.error("请添加调拨工具");
            return;
        }
        if (this.detailForm.destination == this.detailForm.wellNumber) {
            this.$message.error("填报调拨井有误");
            return;
        }
        // let index=this.detailForm.transferDetailList.findIndex(d=>d.stockId<=0)
        // if(index>-1){
        //     this.$message.error("序列号必须全部来源于库存");
        //     return;
        // }
        console.log(this.detailForm);
        (this.$refs.detailForm as ElForm).validate((valid) => {
            if (valid) {
                if (this.operType === "ADD") {
                    apiAddTransfer(this.detailForm).then((res) => {
                        this.showRequistionDialog = false;
                        this.getTransferList();
                    });
                } else {
                    apiUpdateTransfer(this.detailForm).then((res) => {
                        this.showRequistionDialog = false;
                        this.getTransferList();
                    });
                }
            }
        });
    }

    private onCancel() {
        this.showRequistionDialog = false;
    }

    private onWarehouseConfirm(scope: any) {
        this.$confirm("确认？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiConfirmTransfer({ transferId: scope.row.transferId })
                .then((res) => {
                    this.getTransferList();
                })
                .catch((err) => {});
        });
    }
}
</script>
<style lang="scss">
.requisition-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
}
</style>
<style lang="scss" scoped>
.editable {
    width: 50%;

    .add-button {
        width: 100%;
        margin-bottom: 20px;
        height: 30px !important;
        font-size: 16px;
    }

    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;

        &.add {
            bottom: 5px;
        }

        &.delete {
            top: 5px;
        }

        &:hover {
        }
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
}
</style>
