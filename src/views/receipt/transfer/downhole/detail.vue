<template>
    <el-dialog
        custom-class="requisition-dialog"
        title="调拨单"
        :visible.sync="isDialogVisible"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="井号：" prop="wellNumber">
                        {{ detailForm.wellNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="工具类型：" prop="toolType">
                        井下工具
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="返回日期：" prop="transferDate">
                        {{ detailForm.transferDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="目标井：" prop="destination">
                        {{ detailForm.destination }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table style="margin-bottom:20px" :data="transferDetailList">
            <el-table-column label="编号">
                <template slot-scope="scope">
                    <div class="cell-content">
                        {{ scope.$index + 1 }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="存货编码" prop="invCode"> </el-table-column>
            <el-table-column label="产品名称" prop="invName"> </el-table-column>
            <el-table-column label="序列号" prop="serialNumber">
            </el-table-column>
            <el-table-column label="数量" prop="quantity"> </el-table-column>
            <el-table-column prop="note" label="备注"> </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetTransferInfo } from "@/api/requisition";
@Component({})
export default class extends Vue {
    private transferDetailList: any[] = [];
    private detailForm: any = {};
    private isDialogVisible = false;
    private showDialog(scope: any) {
        apiGetTransferInfo({ transferId: scope.row.transferId }).then((res) => {
            this.detailForm = res.data.data;
            this.transferDetailList = this.detailForm.transferDetailList;
            this.isDialogVisible = true;
        });
    }
}
</script>
