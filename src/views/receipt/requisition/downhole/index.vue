<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">井下工具需求单</div>
            </div>
            <el-row
                style="font-family:sans-serif; font-size:16px; font-weight:bold; margin-top:10px;"
            >
                <el-col :span="6">
                    <div>
                        井号:
                        <span>
                            <el-select
                                v-model="form.wellNumber"
                                clearable
                                placeholder="请选择井号"
                                @change="onSearch"
                            >
                                <el-option
                                    v-for="item in wellList"
                                    :key="item.id"
                                    :label="item.wellNumber"
                                    :value="item.wellNumber"
                                >
                                </el-option>
                            </el-select>
                        </span>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div class="block">
                        日期:
                        <el-date-picker
                            v-model="form.date"
                            align="right"
                            type="date"
                            clearable
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期"
                            @change="onSearch"
                        >
                        </el-date-picker>
                    </div>
                </el-col>
                <el-col :span="6">
                    <div>
                        目的地:
                        <span>
                            <el-select
                                v-model="form.shipTo"
                                clearable
                                placeholder="请选择目的地"
                                @change="onSearch"
                            >
                                <el-option
                                    v-for="item in wellCityList"
                                    :key="item.shipTo"
                                    :label="item.shipTo"
                                    :value="item.shipTo"
                                >
                                </el-option>
                            </el-select>
                        </span>
                    </div>
                </el-col>
                <el-col :span="5">
                    <el-button type="primary" @click="onAddRequest">
                        添加需求单
                    </el-button>
                </el-col>
            </el-row>
            <div class="simple-line"></div>
            <el-table
                :header-cell-style="commmonTableHeaderCellStyle"
                :data="requestList"
            >
                <el-table-column label="编号">
                    <template slot-scope="scope">
                        {{ scope.row.materialRequestCode }}
                    </template>
                </el-table-column>
                <el-table-column label="井号">
                    <template slot-scope="scope">
                        {{ scope.row.wellNumber }}
                    </template>
                </el-table-column>
                <el-table-column label="目的地">
                    <template slot-scope="scope">
                        {{ scope.row.shipTo }}
                    </template>
                </el-table-column>
                <el-table-column label="日期">
                    <template slot-scope="scope">
                        {{ scope.row.date }}
                    </template>
                </el-table-column>
                <el-table-column label="工具类型">
                    <template slot-scope="scope">
                        {{ scope.row.toolType }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            style="padding:6px 6px"
                            size="medium"
                            circle
                            icon="el-icon-document"
                            @click="onDetail(scope)"
                        ></el-button>

                        <el-tooltip
                            class="item"
                            effect="dark"
                            content="库房确认"
                            placement="top"
                        >
                            <el-button
                                icon="el-icon-check"
                                size="medium"
                                circle
                                style="padding:6px 6px"
                                :disabled="scope.row.isConfirm === 1"
                                type="success"
                                @click="onConfirm(scope)"
                            >
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="danger"
                            circle
                            style="padding:6px 6px"
                            size="medium"
                            icon="el-icon-delete"
                            @click="onDeleteRequest(scope)"
                            :disabled="scope.row.isConfirm === 1"
                        >
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-dialog
                width="90%"
                :visible.sync="showDialog"
                :close-on-click-modal="false"
                :title="dialogTitle"
            >
                <div></div>
                <el-row
                    style="font-family:sans-serif; font-size: 16px; font-weight: bold"
                >
                    <el-col :span="6">
                        <div>
                            井号:
                            <span>
                                <el-select
                                    v-model="wellNumber"
                                    clearable
                                    :disabled="materialRequestId > 0"
                                    placeholder="请选择井号"
                                >
                                    <el-option
                                        v-for="item in wellList"
                                        :key="item.id"
                                        :label="item.wellNumber"
                                        :value="item.wellNumber"
                                    >
                                    </el-option>
                                </el-select>
                            </span>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="block">
                            日期:
                            <el-date-picker
                                v-model="date"
                                align="right"
                                type="date"
                                value-format="yyyy-MM-dd"
                                :disabled="materialRequestId > 0"
                                placeholder="选择日期"
                            >
                            </el-date-picker>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="demo-input-suffix">
                            目的地:
                            <el-input
                                v-model="shipTo"
                                clearable
                                style="width:200px"
                                prefix-icon="el-icon-s-promotion"
                                placeholder="请填写目的地"
                                >目的地:
                            </el-input>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div>
                            工具类型: 井下工具
                        </div>
                    </el-col>
                </el-row>
                <div class="simple-line"></div>
                <el-table
                    :data="materialRequestDetailList"
                    :header-cell-style="commmonTableHeaderCellStyle"
                    stripe
                    class="kit_class"
                >
                    <el-table-column label="编号">
                        <template slot-scope="scope">
                            {{ scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="存货编码" prop="invCode">
                    </el-table-column>
                    <el-table-column>
                        <template slot="header">产品名称</template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.invName }}
                            </span>
                            <el-autocomplete
                                v-else
                                v-model="scope.row.invName"
                                :fetch-suggestions="querySearch"
                                class="inline-input"
                                placeholder="填写产品名称"
                                value-key="invName"
                                @select="onEditInvName"
                            >
                                <template slot-scope="{ item }">
                                    <div style="line-height:normal">
                                        {{ item.invName }}
                                    </div>
                                    <span style="color:#b4b4b4;font-size:12px;">
                                        {{ item.invCode || "无" }}
                                    </span>
                                </template>
                            </el-autocomplete>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="materialRequestId > 0">
                        <template slot="header">序列号</template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.serialNumberList.join(",") }}
                            </span>
                            <el-select
                                v-else
                                @focus="onFocusSerialNumber(scope)"
                                multiple
                                collapse-tags
                                v-model="scope.row.stockIdList"
                            >
                                <el-option
                                    v-for="item in serialNumberList.filter(
                                        (serialNumber) =>
                                            serialNumber.status == 7004
                                    )"
                                    :label="item.serialNumber"
                                    :value="item.stockId"
                                    :key="item.stockId"
                                >
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="circulateBht">
                        <template slot="header">
                            循环温度
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.circulateBht }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.circulateBht"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="最高温度" prop="maxBht">
                        <template slot="header">
                            最高温度
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.maxBht }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.maxBht"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="入井时长" prop="hour">
                        <template slot="header">
                            入井时长<span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.hour }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.hour"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="循环时间" prop="circulateHrs">
                        <template slot="header">
                            循环时间
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.circulateHrs }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.circulateHrs"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="泥浆类型" prop="mudType">
                        <template slot="header">
                            泥浆类型
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.mudType }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.mudType"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="弯度" prop="mudType">
                        <template slot="header">
                            弯度
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.angle }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.angle"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="耐温" prop="endureTemperature">
                        <template slot="header"
                            >耐温<span style="color:red;">*</span></template
                        >
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.endureTemperature }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.endureTemperature"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="扣型" prop="claspType">
                        <template slot="header">
                            扣型
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.claspType }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.claspType"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="扶正器尺寸/类型"
                        prop="stbSize"
                        width="140"
                    >
                        <template slot="header">
                            扶正器尺寸/类型
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.stbSize }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.stbSize"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="最大外径" prop="odMax">
                        <template slot="header">
                            最大外径
                            <span style="color:red;">*</span>
                        </template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.odMax }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.odMax"
                            ></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column>
                        <template slot="header">数量</template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.quantity }}
                            </span>
                            <el-input
                                v-else
                                v-model="scope.row.quantity"
                            ></el-input>
                        </template>
                    </el-table-column>

                    <el-table-column>
                        <template slot="header">备注</template>
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                {{ scope.row.note }}
                            </span>
                            <el-input v-else v-model="scope.row.note">
                            </el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <span
                                v-if="editObject.editRowIndex !== scope.$index"
                            >
                                <el-button type="text" @click="onEdit(scope)">
                                    编辑
                                </el-button>
                                <el-button type="text" @click="onDelete(scope)">
                                    删除
                                </el-button>
                            </span>
                            <span v-else>
                                <el-button type="text" @click="onSave(scope)">
                                    保存
                                </el-button>
                                <el-button type="text" @click="onCancel(scope)">
                                    取消
                                </el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-button
                    icon="el-icon-plus"
                    style=" margin:20px;"
                    type="primary"
                    @click="onAddRow"
                    :disabled="this.isConfirm === 1"
                >
                    添加元素
                </el-button>
                <el-button
                    style="margin-right: 20px;"
                    type="primary"
                    @click="onSaveList"
                    :disabled="this.isConfirm === 1"
                >
                    保存需求单
                </el-button>
            </el-dialog>
            <DetailDialog ref="DetailDialog"></DetailDialog>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import {
    apiAddRequest,
    apiConfirmRequest,
    apiDeleteRequest,
    apiDetailedRequestList,
    apiGetRequestList,
    apiUpdateRequest,
} from "@/api/request";

import { apiWellShipTo } from "@/api/wellInfo";
import { apiGetDeviceFuzzyList, apiGetInvNameFuzzyList } from "@/api/warehouse";
import DetailDialog from "./detail.vue";
import { ConstantModule } from "@/store/modules/constant";

interface IRequestList {
    materialDetailId?: number;
    materialRequestId?: number;
    serialNumber?: string;
    serialNumberList?: string[];
    invName?: string;
    quantity?: number;
    isKit?: number;
    note?: string;
    createTime?: string;
    updateTime?: string;
    invCode?: number;
    stockId?: number;
    stockIdList?: number[];
}

@Component({ components: { DetailDialog } })
export default class extends Vue {
    private materialRequestId = -1;
    private requestList = [];
    private requestListTwo = [];
    private wellList: any[] = [];
    private wellCityList: any = [];
    private wellNumber: string = "";
    private date: any = "";
    private shipTo: string = "";
    private note: string = "";
    private warehouseConfirmDate: string = "";
    private materialRequestCode: string = "";
    private toolType: string = "UNDER_WELL";
    private isConfirm?: number = 0;
    dialogTitle = "需求单详情";
    showDialog = false;
    private requestDetailList = [];
    private options: any[] = [];
    private materialRequestDetailList: IRequestList[] = [];
    private preObject: any = {};
    private form: any = {
        wellNumber: "",
        date: "",
        shipTo: "",
    };
    private toolList = [
        {
            value: "MWD",
            label: "MWD",
        },
        {
            value: "UNDER_WELL",
            label: "井下",
        },
    ];
    private editObject = { editRow: false, editRowIndex: -1 };
    private serialNumberList: any[] = [];
    async mounted() {
        apiGetRequestList({}).then((response) => {
            this.requestListTwo = response.data.data;
            this.onSelectWellNumber();
        });
        this.wellList = await ConstantModule.getWellList();
        apiWellShipTo({}).then((response) => {
            let cityList: any = [];

            cityList = response.data.data;
            cityList.forEach((item: any) => {
                let cityObject: any = {};
                cityObject.shipTo = item;
                cityObject.label = item;
                this.wellCityList.push(cityObject);
            });
        });
    }
    private onFocusSerialNumber(scope: any) {
        if (scope.row.invCode) {
            apiGetDeviceFuzzyList({
                invCode: scope.row.invCode,
                size: 30,
            }).then((res) => {
                let list = res.data.data.currentStockList || [];
                this.serialNumberList = list.filter(
                    (item) => item.serialNumber && item.serialNumber != "无"
                );
            });
        }
    }
    private onSelectWellNumber() {
        let wellNumber = this.wellNumber;
        this.requestList = this.requestListTwo.filter((value) => {
            return (value as any).wellNumber.match(wellNumber);
        });
    }

    private onSearch() {
        this.getSearchedList();
    }

    private getSearchedList() {
        let detail: any = {};
        console.log(this.form.deviceNumber);
        if (this.form.wellNumber != "")
            detail.wellNumber = this.form.wellNumber;
        if (this.form.date != "") detail.date = this.form.date;
        if (this.form.shipTo != "") detail.shipTo = this.form.shipTo;
        apiGetRequestList(detail)
            .then((res) => {
                let { code, message, data } = res.data;
                this.requestList = res.data.data;
            })
            .catch((err) => {
                console.log(err);
            });
    }

    private onEditInvName(params: any) {
        this.materialRequestDetailList[this.editObject.editRowIndex].invCode =
            params.invCode;
        this.materialRequestDetailList[
            this.editObject.editRowIndex
        ].serialNumberList = [];
        this.materialRequestDetailList[
            this.editObject.editRowIndex
        ].stockIdList = [];
        this.materialRequestDetailList[
            this.editObject.editRowIndex
        ].serialNumber = "";
    }

    private querySearch(queryString: string, callback: any) {
        apiGetInvNameFuzzyList({
            invName: queryString,
        }).then((res) => {
            this.options = res.data.data || [];
            this.materialRequestDetailList[
                this.editObject.editRowIndex
            ].stockId = undefined;
            callback(this.options);
        });
    }

    private onAddRequest() {
        this.materialRequestId = -1;
        this.isConfirm = 0;
        this.dialogTitle = "添加需求单";
        this.editObject.editRowIndex = -1;
        this.showDialog = true;
        this.requestDetailList = [];
        this.materialRequestDetailList = [];
        this.wellNumber = "";
        this.date = "";
        this.shipTo = "";
        this.note = "";
        // this.$nextTick(() => {
        //     (this.$refs.form as ElForm).clearValidate();
        // });
    }

    private onDeleteRequest(scope: any) {
        this.$confirm("确认删除此需求单吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiDeleteRequest({ materialRequestId: scope.row.materialRequestId })
                .then(() => {
                    this.getRequestId();
                })
                .catch((error) => {
                    console.log(error);
                });
        });
    }

    private onConfirm(scope: any) {
        this.$confirm("最终确认该需求单吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            apiConfirmRequest({
                materialRequestId: scope.row.materialRequestId,
            })
                .then(() => {
                    this.getRequestId();
                })
                .catch((err) => {
                    console.log(err);
                });
        });
    }
    private muateDetailList(detailList: IRequestList[]): IRequestList[] {
        const invCodeList = [
            ...new Set(detailList.map((item) => item.invCode)),
        ];
        let ret: any = [];
        for (let i = 0, len1 = invCodeList.length; i < len1; i++) {
            let tmp: any = { serialNumberList: [], stockIdList: [] };
            let quantity = 0;
            for (let j = 0, len2 = detailList.length; j < len2; j++) {
                if (detailList[j].invCode === invCodeList[i]) {
                    if (!tmp.invCode) {
                        tmp.invCode = detailList[j].invCode;
                        tmp.invName = detailList[j].invName;
                        tmp.note = detailList[j].note;
                        tmp.isKit = detailList[j].isKit;
                    }
                    if (detailList[j].serialNumber) {
                        quantity++;
                        tmp.serialNumberList.push(detailList[j].serialNumber);
                        tmp.stockIdList.push(detailList[j].stockId);
                    } else {
                        quantity += Number(detailList[j].quantity || 0);
                    }
                }
            }
            tmp.quantity = quantity;
            ret.push(tmp);
        }
        return ret;
    }
    private onDetail(scope: any) {
        this.editObject.editRowIndex = -1;
        if (scope.row.isConfirm == 1) {
            (this.$refs.DetailDialog as any).showDialog(scope);
        } else {
            let formData = new FormData();
            formData.append("materialRequestId", scope.row.materialRequestId);
            apiDetailedRequestList(formData).then((res) => {
                this.requestDetailList = res.data.data;
                this.materialRequestDetailList = this.muateDetailList(
                    res.data.data.materialRequestDetailList
                );
                this.materialRequestId = (this
                    .requestDetailList as any).materialRequestId;
                this.materialRequestCode = (this
                    .requestDetailList as any).materialRequestCode;
                this.wellNumber = (this.requestDetailList as any).wellNumber;
                this.date = (this.requestDetailList as any).date;
                this.shipTo = (this.requestDetailList as any).shipTo;
                this.warehouseConfirmDate = (this
                    .requestDetailList as any).warehouseConfirmDate;
                this.isConfirm = (this.requestDetailList as any).isConfirm;
                this.showDialog = true;
            });
        }
    }

    private getRequestId() {
        apiGetRequestList({}).then((response) => {
            this.requestList = response.data.data;
        });
    }

    private onEdit(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }

    private onDelete(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.materialRequestDetailList.splice(scope.$index, 1);
    }

    private onSave(scope: any) {
        console.log(scope);
        // 必须有产品名和数量
        if (!scope.row.invName) {
            this.$message.error("请填写产品名称");
            return;
        }
        if (!scope.row.quantity || scope.row.quantity == 0) {
            this.$message.error("请填写正确的数量");
            return;
        }

        const invCodeItemList = this.materialRequestDetailList.filter(
            (item) => item.invCode === scope.row.invCode
        );
        if (invCodeItemList.length > 1) {
            this.$message.error("有重复的存货编码");
            return;
        }
        scope.row.serialNumberList = [];
        scope.row.stockIdList.forEach((stockId: any) => {
            let tmp = this.serialNumberList.find(
                (item) => item.stockId == stockId
            );
            scope.row.serialNumberList.push(tmp.serialNumber);
        });
        this.editObject.editRowIndex = -1;
    }

    private onCancel(scope: any) {
        if (Object.keys(this.preObject).length) {
            this.materialRequestDetailList[scope.$index] = {
                ...this.preObject,
            };
        } else {
            this.materialRequestDetailList.pop();
        }

        this.editObject.editRowIndex = -1;
        this.preObject = {};
    }

    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = {};
        this.materialRequestDetailList.push({
            serialNumberList: [] as any[],
            stockIdList: [] as any[],
            invName: "",
            invCode: undefined,
            circulateBht: undefined,
            maxBht: undefined,
            hour: undefined,
            circulateHrs: undefined,
            mudType: undefined,
            angle: undefined,
            endureTemperature: undefined,
            claspType: undefined,
            stbSize: undefined,
            odMax: undefined,
            quantity: 0,
            isKit: 0,
            note: "",
        } as any);
        this.editObject.editRow = true;
        this.editObject.editRowIndex =
            this.materialRequestDetailList.length - 1;
    }
    private inverseMutateDetailList(detailList: any[]): any[] {
        let ret: any[] = [];
        for (let i = 0, len1 = detailList.length; i < len1; i++) {
            let detail = detailList[i];
            let stockIdList = detail.stockIdList || [];
            let len2 = stockIdList.length;

            for (let j = 0; j < len2; j++) {
                ret.push({
                    invName: detail.invName,
                    invCode: detail.invCode,
                    stockId: detail.stockIdList[j],
                    serialNumber: detail.serialNumberList[j],
                    quantity: 1,
                    note: detail.note,
                    isKit: detail.isKit,
                });
            }
            if (len2 < detail.quantity) {
                ret.push({
                    invName: detail.invName,
                    invCode: detail.invCode,
                    stockId: null,
                    serialNumber: null,
                    quantity: detail.quantity - len2,
                    note: detail.note,
                    isKit: detail.isKit,
                });
            }
        }
        return ret;
    }
    onSaveList() {
        if (this.materialRequestDetailList.length == 0) {
            this.$message.error("请添加清单");
            return;
        }
        let materialRequestDetailList = this.inverseMutateDetailList(
            this.materialRequestDetailList
        );
        if (this.materialRequestId == -1) {
            let params = {
                wellNumber: this.wellNumber,
                date: this.date,
                shipTo: this.shipTo,
                warehouseConfirmDate: this.warehouseConfirmDate,
                isConfirm: this.isConfirm,
                toolType: this.toolType,
                materialRequestDetailList,
            };
            apiAddRequest(params)
                .then((resOne) => {
                    let { code, message, data } = resOne.data;
                    this.getRequestId();
                    if (code == 200) {
                        this.$message.success("保存成功");
                        this.showDialog = false;
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        } else {
            let params = {
                materialRequestId: this.materialRequestId,
                masterRequestCode: this.masterRequestCode,
                wellNumber: this.wellNumber,
                date: this.date,
                shipTo: this.shipTo,
                warehouseConfirmDate: this.warehouseConfirmDate,
                isConfirm: this.isConfirm,
                toolType: this.toolType,
                materialRequestDetailList,
            };
            apiUpdateRequest(params)
                .then((resTwo) => {
                    let { code, message, data } = resTwo.data;
                    this.getRequestId();
                    if (code == 200) {
                        this.$message.success("保存成功");
                        this.showDialog = false;
                    }
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    }
}
</script>

<style lang="scss">
.kit_class {
    .el-switch {
        height: auto !important;
    }
}
</style>
