<template>
    <el-dialog
        width="1500px"
        :visible.sync="isDialogVisible"
        :close-on-click-modal="false"
        title="需求单"
    >
        <div></div>
        <el-row
            style="font-family:sans-serif; font-size: 16px; font-weight: bold"
        >
            <el-col :span="6">
                <div>
                    井号:
                    {{ form.wellNumber }}
                </div>
            </el-col>
            <el-col :span="6">
                <div class="block">
                    日期:
                    {{ form.date }}
                </div>
            </el-col>
            <el-col :span="6">
                <div class="demo-input-suffix">目的地:{{ form.shipTo }}</div>
            </el-col>
            <el-col :span="6">
                <div>工具类型:{{ form.toolType }}</div>
            </el-col>
        </el-row>
        <div class="simple-line"></div>
        <el-table
            :data="materialRequestDetailList"
            :header-cell-style="commmonTableHeaderCellStyle"
            stripe
            class="kit_class"
        >
            <el-table-column label="编号">
                <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                </template>
            </el-table-column>
            <el-table-column label="存货编码" prop="invCode"> </el-table-column>
            <el-table-column label="产品名称" prop="invName"> </el-table-column>
            <el-table-column prop="serialNumber" label="序列号">
            </el-table-column>
            <el-table-column label="数量" prop="quantity"> </el-table-column>
            <el-table-column label="是否属于Kit箱" prop="isKit">
                <template slot-scope="scope">
                    {{ scope.row.isKit === 1 ? "是" : "否" }}
                </template>
            </el-table-column>
            <el-table-column label="备注" prop="note"> </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiDetailedRequestList } from "@/api/request";
@Component({})
export default class extends Vue {
    private form: any = {};
    private materialRequestDetailList: any[] = [];
    private isDialogVisible = false;
    private showDialog(scope: any) {
        let formData = new FormData();
        formData.append("materialRequestId", scope.row.materialRequestId);
        apiDetailedRequestList(formData).then((res) => {
            this.form = res.data.data;
            this.materialRequestDetailList = this.form.materialRequestDetailList;
            this.isDialogVisible = true;
        });
    }
}
</script>
