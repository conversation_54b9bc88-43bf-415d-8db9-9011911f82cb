<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">交接单</div>
            </div>
            <el-button type="primary" @click="onHandleAddReceipt">添加交接单</el-button>
            <div class="simple-line"></div>
            <el-table :header-cell-style="commmonTableHeaderCellStyle" :data="receipts">
                <el-table-column label="编号">
                    <template slot-scope="scope">
                        {{ scope.row.receiptCode }}
                    </template>
                </el-table-column>
                <el-table-column label="交接人">
                    <template slot-scope="scope">
                        {{ scope.row.deliveryPerson }}
                    </template>
                </el-table-column>
                <el-table-column label="交接日期">
                    <template slot-scope="scope">
                        {{ scope.row.receiptTime }}
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="primary" size="small" icon="el-icon-document" circle
                                   @click="onDetail(scope)"></el-button>
                        <el-button type="success" size="small" icon="el-icon-check" circle
                                   @click="onConfirm(scope)"></el-button>
                        <el-button type="danger" size="small" icon="el-icon-delete" circle
                                   @click="onDeleteReceipt(scope)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog width="800px" :close-on-click-modal="false" :visible.sync="showDialog" :title="dialogTitle">
            <el-form :model="form" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="receiptCode" label="交接编号：">
                            <el-input
                                style="width:90%" disabled
                                v-model="form.receiptCode"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="deliveryPerson" label="交接人：">
                            <el-input
                                style="width:90%"
                                v-model="form.deliveryPerson"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item prop="receiptPerson" label="接收人:">
                            <el-input
                                style="width:90%"
                                v-model="form.receiptPerson"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item prop="receiptTime" label="接收日期:">
                            <el-date-picker v-model="form.receiptTime" type="date"
                                            value-format="yyyy-MM-dd"
                                            placeholder="选择日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="editList" stripe>
                <el-table-column label="编号">
                    <template slot-scope="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header">序列号</template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.serialNumber }}
                        </span>
                        <el-select v-else
                                   v-model="scope.row.serialNumber"
                                   filterable
                                   remote
                                   reserve-keyword
                                   placeholder="请输入关键词"
                                   @change="onGetStockName(scope)"
                                   :remote-method="onSearchSerialNumber"
                                   :loading="loading">
                            <el-option
                                v-for="item in options"
                                :key="item.stockId"
                                :label="item.serialNumber || '无'"
                                :value="item.serialNumber"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column>
                    <template slot="header">产品名称</template>
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            {{ scope.row.stockName }}
                        </span>
                        <el-input v-else v-model="scope.row.stockName"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <span v-if="editObject.editRowIndex !== scope.$index">
                            <el-button type="text" @click="onEdit(scope)">
                                编辑
                            </el-button>
                            <el-button type="text" @click="onDelete(scope)">
                                删除
                            </el-button>
                        </span>
                        <span v-else>
                            <el-button type="text" @click="onSave(scope)">
                                保存
                            </el-button>
                            <el-button type="text" @click="onCancel(scope)">
                                取消
                            </el-button>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <el-button icon="el-icon-plus" style="margin-right:20px;" type="primary" @click="onAddRow">
                添加元素
            </el-button>
            <el-button style="margin: 20px;" type="primary" @click="onSaveList">
                保存交接单
            </el-button>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetDeviceFuzzyList } from "@/api/warehouse";
import { apiAddDeliveryReceipt, apiGetDeliveryReceiptInfo, apiListDeliveryReceipt } from "@/api/receipt";

interface IDeliveryReceiptDetail {
  deliveryReceiptId: number;
  stockId: number;
  serialNumber: string;
  stockName: string;
}

@Component({})
export default class extends Vue {
    private receipts = [];
    showDialog = false;
    dialogTitle = "添加交接单";
    form = { receiptCode: "", deliveryPerson: "", receiptPerson: "", receiptTime: "" };
    private editObject = { editRow: false, editRowIndex: -1 };
    private editList: IDeliveryReceiptDetail[] = [];
    private preObject: IDeliveryReceiptDetail | null = null; 
    private loading = false;
    private options = [];
    
    mounted() {
        this.$nextTick(() => {
            this.initData();
        });
    }
    
    private initData() {
        //获取交接单历史
        apiListDeliveryReceipt({ receiptTimeS: "", receiptTimeE: "", current: 1, pageSize: 10000 }).then(res => {
            let { code, message, data } = res.data;
            //console.log(data)
            this.receipts = data.deliveryReceipts;
        }).catch(err => {
        });
    }
    
    private initReceiptInfo(id: any) {
        apiGetDeliveryReceiptInfo({ deliveryReceiptId: id }).then(res => {
            let { code, data, message } = res.data;
            this.editList = data.deliveryReceiptDetails;
        }).catch(err => {
        });
    }
    
    private onHandleAddReceipt() {
        this.dialogTitle = "添加交接单";
        this.editObject.editRowIndex = -1;
        this.showDialog = true;
        this.form = { receiptCode: "", deliveryPerson: "", receiptPerson: "", receiptTime: "" };
        this.editList = [];
        
    }
    
    private onSearchSerialNumber(query: any) {
        if (query !== "") {
            apiGetDeviceFuzzyList({ serialNumber: query, size: 20 }).then((res) => {
                let list = res.data.data.currentStockList || [];
                this.options = list;
                // this.kitTemplateItemList[index].invName = list[0] ? list[0].invName : ''
            });
        }
    }
    
    private onGetStockName(scope: any) {
        // scope.$index
        let seal = scope.row.serialNumber;
        let index = (this.options as any[]).findIndex((res: any) => res.serialNumber == seal);
        if (index < 0) {
            this.$message.error("请将序列号录入ERP中");
            return;
        }
        (this.editList as any[])[scope.$index].stockId = (this.options as any[])[index].stockId;
        (this.editList as any[])[scope.$index].stockName = (this.options as any[])[index].invName;
        
        
    }
    
    private onAddRow() {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        (this.editList as any[]).push({ deliveryReceiptId: 0, stockId: 0, serialNumber: "", stockName: "" });
        this.editObject.editRow = true;
        this.editObject.editRowIndex = (this.editList as any[]).length - 1;
    }
    
    private onDetail(scope: any) {
        
        Object.assign(this.form, scope.row);
        this.showDialog = true;
        this.dialogTitle = "编辑交接单";
        this.initReceiptInfo(scope.row.id);
        
    }
    
    private onConfirm(scope: any) {
    
    }
    
    private onDeleteReceipt(scope: any) {
    
    }
    
    private onSaveList() {
        if ((this.editList as any[]).length == 0) {
            this.$message.error("请添加交接单条目");
            return;
        }
        let params = { deliveryReceipt: {}, deliveryReceiptDetails: [] };
        params.deliveryReceipt = {
            receiptCode: this.form.receiptCode,
            receiptPerson: this.form.receiptPerson,
            receiptTime: this.form.receiptTime,
            deliveryPerson: this.form.deliveryPerson,
        };
        for (let i = 0; i < (this.editList as any[]).length; i++) {
            if ((this.editList as any[])[i].stockId > 0) {
                (params.deliveryReceiptDetails as any[]).push({
                    stockId: (this.editList as any[])[i].stockId,
                    stockName: (this.editList as any[])[i].stockName,
                    serialNumber: (this.editList as any[])[i].serialNumber,
                });
            } else {
                this.$message.error("请将第" + (i + 1) + "条数据序列号录入ERP中");
                return;
            }
        }
        apiAddDeliveryReceipt(params).then(res => {
            let { data, message, code } = res.data;
            if (code == 200) {
                this.$message.success("保存成功");
                this.showDialog = false;
            }
        }).catch(err => {
        });
    }
    
    private onEdit(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        this.preObject = { ...scope.row };
        this.editObject.editRowIndex = scope.$index;
    }
    
    private onDelete(scope: any) {
        if (this.editObject.editRowIndex >= 0) {
            this.$message.error("请先保存");
            return;
        }
        (this.editList as any[]).splice(scope.$index, 1);
    }
    
    private onSave(scope: any) {
        if (scope.row.stockName === "" || scope.row.stockName == undefined) {
            this.$message.error("请填写产品名称");
            return;
        }
        
        let err = false;
        for (let i = 0; i < (this.editList as any[]).length; i++) {
            if (i == scope.$index) continue;
            if ((this.editList as any[])[i].serialNumber == scope.row.serialNumber) {
                err = true;
                break;
            }
        }
        if (err) {
            this.$message.error("有重复项");
            return;
        }
        this.editObject.editRowIndex = -1;
    }
    
    private onCancel(scope: any) {
        if (this.preObject !== null) { 
            (this.editList as any[])[scope.$index] = { ...this.preObject };
        } else {
            (this.editList as any[]).pop();
        }
        
        this.editObject.editRowIndex = -1;
        this.preObject = null; 
    }
    
}
</script>
