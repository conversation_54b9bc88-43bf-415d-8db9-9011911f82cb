<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%;">
            <div style="display: flex; justify-content: space-between;">
                <div class="app-card-title">失效报告管理</div>
            </div>
            <el-row :gutter="0">
                <el-col :span="5">
                    <span style="font-size: 16px">井号: </span>
                    <el-select
                        v-model="filterForm.wellNumber"
                        clearable
                        label="1"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in wellList"
                            :key="item.wellNumber"
                            :label="item.wellNumber"
                            :value="item.wellNumber"
                        >
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="9">
                    <span style="font-size: 16px">日期: </span>
                    <el-date-picker
                        v-model="filterForm.dateRange"
                        end-placeholder="结束日期"
                        format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </el-col>
                <el-col :span="1">
                    <el-button
                        v-model="onSearch"
                        icon="el-icon-search"
                        type="primary"
                        @click="onSearch"
                        >查 询</el-button
                    >
                </el-col>
            </el-row>
            <div class="simple-line"></div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
            >
                <el-table-column
                    fixed
                    label="失效单号"
                    prop="failureReportCode"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                ></el-table-column>
                <el-table-column label="井队" prop="wellTeam"></el-table-column>
                <el-table-column label="kit箱" prop="kitNumber"></el-table-column>
                <el-table-column
                    label="失效日期"
                    prop="failureDate"
                ></el-table-column>
                <el-table-column
                    label="报告日期"
                    prop="reportDate"
                ></el-table-column>
                <el-table-column
                    label="客户名称"
                    prop="customerName"
                ></el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                ></el-table-column>
                <el-table-column
                    label="库房接收日期"
                    prop="warehouseReceivingDate"
                ></el-table-column>
                <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button
                            icon="el-icon-document"
                            size="medium" circle
                            style="padding:6px 6px"
                            type="primary"
                            @click="onDetail(scope)"
                            >
                        </el-button>
                        <el-tooltip content="库房接收" placement="top" effect="dark">
                            <el-button
                                icon="el-icon-check"
                                size="medium"
                                style="padding:6px 6px" :disabled="scope.row.isConfirm==1"
                                type="success" circle
                                @click="onConfirm(scope.row)"
                                >
                            </el-button>
                        </el-tooltip>
                        
                        
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import {
    apiConfirmFailureReportOrder,
    apiGetFailureReportList,
} from "@/api/failure";
import { ConstantModule } from "@/store/modules/constant";


@Component({})
export default class extends Vue {
    filterForm = {
        wellNumber: "",
        dateRange: [],
    };
    private tableData: any[] = [];
    private wellList: any[] = [];

    mounted() {
        this.getWellList();
        this.getFilterFailureReportList()
    }

    async getWellList() {
        this.wellList = await ConstantModule.getWellList()
    }

    getFilterFailureReportList() {
        //this.filterTableData=[];
        let params:any = {};
        if (this.filterForm.wellNumber != "")
            params.wellNumber = this.filterForm.wellNumber;
        if (this.filterForm.dateRange.length > 0) {
            params.startDate = this.filterForm.dateRange[0];
            params.endDate = this.filterForm.dateRange[1];
        }
        apiGetFailureReportList(params)
            .then((res) => {
                let { data } = res.data;
                this.tableData = data;
            })
            .catch((err) => {});
    }

    onSearch() {
        this.getFilterFailureReportList();
        // this.filterTableData = this.tableData.filter((item: any) => {
        //     let flag1 = this.serialNumber
        //         ? this.serialNumber == item.serialNumber
        //         : true;
        //     let flag2 = this.deviceType
        //         ? this.deviceType == item.deviceType
        //         : true;
        //     let flag3 = this.status ? this.status == item.status : true;
        //     return flag1 && flag2 && flag3;
        // });
    }

    onDetail(scope:any) {
          this.$router.push({ path: '/daily/index',query:{wellNumber:scope.row.wellNumber,jobNumber:scope.row.jobNumber} })
      
    }

    onConfirm(row: any) {
        this.$confirm("确认收到货物？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            let form = new FormData();
            form.append("failureReportId", row.failureReportId);
            apiConfirmFailureReportOrder(form)
                .then((res) => {
                    this.getFilterFailureReportList();
                })
                .catch((err) => {});
        });
    }
}
</script>
