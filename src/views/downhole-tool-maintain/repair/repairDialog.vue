<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="返修单"
        width="80%"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="作业号: ">
                        <span>{{ detailForm.jobNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="井号: ">
                        <span>{{ detailForm.wellNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="kit箱: ">
                        <span>{{ detailForm.kitNumber }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="返修日期: ">
                        <span>{{ detailForm.returnDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="客户名称: ">
                        <span>{{ detailForm.customerName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人: ">
                        <span>{{ detailForm.contactUser }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式: ">
                        <span>{{ detailForm.contactNumber }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table
            :data="detailForm.repairDetailList"
            style="margin-bottom: 20px"
            :header-cell-style="commmonTableHeaderCellStyle"
        >
            <el-table-column label="仪器名称" prop="invName">
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber">
            </el-table-column>
            <el-table-column label="入井时间" prop="inWellHour">
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
            </el-table-column>
            <el-table-column label="循环温度" prop="circulateBht">
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht"> </el-table-column>

            <el-table-column label="趟次" prop="run"> </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
            </el-table-column>
            <el-table-column label="备注" prop="note"> </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="{ row }">
                    <el-button type="text">
                        <router-link
                            :to="`/downhole-tool-maintain/maintain?repairCode=${detailForm.repairCode}&repairDetailId=${row.repairDetailId}`"
                        >
                            创建台账
                        </router-link>
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetRepairInfo } from "@/api/repair";
interface IForm {
    jobNumber?: string;
    wellNumber?: string;
    kitNumber?: string;
    returnDate?: string;
    customerName?: string;
    contactUser?: string;
    repairDetailList: IRepairDetail[];
    toolType: string;
}

interface IRepairDetail {
    serialNumber?: number | string;
    inWellHour?: number;
    circulateBht?: number;
    maxBht?: number;
    circulateHrs?: number;
    run?: number;
    returnReason?: string;
    note?: string;
}
@Component({})
export default class extends Vue {
    private detailDialogVisible = false;
    private detailForm: IForm = this.getInitForm();
    private getInitForm(): IForm {
        return {
            jobNumber: "",
            wellNumber: "",
            contactUser: "",
            kitNumber: "",
            returnDate: "",
            customerName: "",
            repairDetailList: [],
            toolType: "UNDER_WELL",
        };
    }
    private getInitDetailItem(): IRepairDetail {
        return {
            serialNumber: undefined,
            inWellHour: undefined,
            circulateBht: undefined,
            maxBht: undefined,
            circulateHrs: undefined,
            run: undefined,
            returnReason: undefined,
            note: undefined,
        };
    }

    private async showDialog(type: string, repairId: number) {
        await apiGetRepairInfo({ repairId: repairId }).then((res) => {
            this.detailForm = res.data.data;
        });
        this.detailDialogVisible = true;
    }
}
</script>
<style lang="scss">
.repair-dialog {
    .el-dialog__footer {
        padding-top: 40px !important;
    }
    .el-table__expanded-cell {
        background: white !important;
    }
}
</style>
