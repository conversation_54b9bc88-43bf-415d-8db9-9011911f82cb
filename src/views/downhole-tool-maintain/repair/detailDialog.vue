<template>
    <el-dialog
        :visible.sync="detailDialogVisible"
        custom-class="repair-dialog"
        :close-on-click-modal="false"
        title="详情"
        width="80%"
    >
        <el-form ref="detailForm" :model="detailForm" label-width="100px">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="井号: " prop="wellNum">
                        {{ detailForm.wellNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="作业号: " prop="jobNum">
                        {{ detailForm.jobNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="客户名称: ">
                        {{ detailForm.customerName }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="返回日期: " prop="repairDate">
                        {{ detailForm.repairDate }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <!-- <div>{{ kitNumber }}</div> -->
                    <el-form-item label="kit箱: " prop="kitNumber">
                        {{ detailForm.kitNumber }}
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="现场联系人: ">
                        {{ detailForm.contactUser }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="repairDetailList" style="margin-bottom:20px">
            <el-table-column prop="invName">
                <template slot="header">
                    仪器种类<span style="color:red;">*</span>
                </template>
            </el-table-column>
            <el-table-column label="序列号" prop="serialNumber">
            </el-table-column>
            <el-table-column prop="circulateBht">
                <template slot="header">
                    循环温度<span style="color:red;">*</span>
                </template>
            </el-table-column>
            <el-table-column label="最高温度" prop="maxBht">
                <template slot="header"
                    >最高温度<span style="color:red;">*</span></template
                >
            </el-table-column>
            <el-table-column label="入井时长" prop="hour">
                <template slot="header"
                    >入井时长<span style="color:red;">*</span></template
                >
            </el-table-column>
            <el-table-column label="循环时间" prop="circulateHrs">
                <template slot="header"
                    >循环时间<span style="color:red;">*</span></template
                >
            </el-table-column>
            <el-table-column label="RunCount" prop="runCount">
                <template slot="header">
                    RunCount<span style="color:red;">*</span>
                </template>
            </el-table-column>
            <el-table-column label="返回原因" prop="returnReason">
            </el-table-column>
            <el-table-column label="备注" prop="note"> </el-table-column>
        </el-table>
    </el-dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { apiGetRepairDetail } from "@/api/repair";
@Component({})
export default class extends Vue {
    private repairDetailList: any = [];
    private detailDialogVisible = false;
    private detailForm: any = {};
    private showDialog(repairId: number) {
        apiGetRepairDetail({ id: repairId }).then(async (res) => {
            this.detailForm = res.data.data;
            this.repairDetailList = this.detailForm.repairDetailList || [];
            this.detailDialogVisible = true;
        });
    }
}
</script>
<style lang="scss"></style>
