<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div style="display: flex; justify-content: space-between">
                <div class="app-card-title">返修单</div>
            </div>
            <el-table
                :data="tableData"
                :header-cell-style="commmonTableHeaderCellStyle"
                stripe
                style="margin-top: 10px"
            >
                <el-table-column
                    width="240"
                    label="返修单号"
                    prop="repairCode"
                ></el-table-column>
                <el-table-column
                    label="作业号"
                    prop="jobNumber"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                ></el-table-column>
                <!-- <el-table-column
                    label="kit箱"
                    prop="kitNumber"
                ></el-table-column> -->
                <el-table-column
                    label="返修日期"
                    prop="returnDate"
                ></el-table-column>
                <el-table-column
                    label="客户名称"
                    prop="customerName"
                ></el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                ></el-table-column>
                <el-table-column
                    label="联系方式"
                    prop="contactNumber"
                ></el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="createTime"
                ></el-table-column>
                <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button type="primary" @click="onDetail(scope.row)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <RepairDialog @getRepairList="getRepairList" ref="RepairDialog" />
        <DetailDialog ref="DetailDialog" />
    </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { apiGetRepairList, apiDeleteRepair } from "@/api/repair";
import RepairDialog from "./repairDialog.vue";
import DetailDialog from "./detailDialog.vue";
@Component({
    components: { RepairDialog, DetailDialog },
})
export default class extends Vue {
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private tableData: any = [];
    mounted() {
        this.getRepairList();
    }
    getRepairList() {
        apiGetRepairList(
            { toolType: "UNDER_WELL" },
            {
                current: this.currentPage,
                size: this.pageSize,
            }
        )
            .then((res) => {
                const data = res.data.data || {};
                this.tableData = data.records || [];
                this.total = data.total;
            })
            .catch((err) => {});
    }

    onAddRepairOrder() {
        (this.$refs.RepairDialog as any).showDialog("ADD");
    }
    onDetail(row: any) {
        (this.$refs.RepairDialog as any).showDialog("DETAIL", row.repairId);
    }

    onDelete(row: any) {
        this.$confirm("确认删除吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(() => {
            const form = new FormData();
            form.append("repairId", row.repairId);
            apiDeleteRepair(form)
                .then(() => {
                    this.$message.success("操作成功！");
                    this.currentPage = 1;
                    this.getRepairList();
                })
                .catch((err) => {});
        });
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getRepairList();
    }
}
</script>
<style>
.el-date-editor .el-range-separator {
    width: 25px;
}
</style>
