<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">部件列表</div>
            <el-form :model="searchForm" label-width="50px">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="品名">
                            <el-autocomplete
                                style="width: calc(100%)"
                                v-model="searchForm.invName"
                                :fetch-suggestions="remoteMethodInvName"
                                placeholder="请输入内容"
                                clearable
                                @select="getCurrentStockList(true)"
                                @change="getCurrentStockList(true)"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="序列号" label-width="60px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="getCurrentStockList(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" >
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                >
                    <template slot-scope="{row}">
                        {{ getSerialNumber(row) }}
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="versionNumber"
                    label="固件版本号"
                    align="center"
                ></el-table-column> -->
                <el-table-column
                    prop="maxBht"
                    label="最高温度(℃)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="totalCirculateHrs"
                    label="总循环时长(h)"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="totalInWellHrs"
                    label="总入井时长(h)"
                    align="center"
                ></el-table-column>
                <!-- <el-table-column
                    prop="reviseTotalHours"
                    label="修正总时长(h)"
                    align="center"
                ></el-table-column> -->
                <el-table-column
                    prop="serveTotalCount"
                    label="总服役次数"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="parentInvName"
                    label="母件品名"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="parentSerialNumber"
                    label="母件序列号"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="100px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="onDetail(scope.row)"
                            type="text"
                            >详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
            <el-dialog width="80%" title="服役详情" :visible.sync="isDialogVisible">
                <el-table :data="detailTableData" :header-cell-style="commmonTableHeaderCellStyle" >
                    <el-table-column
                        prop="outPitToolsNumber"
                        label="出厂操作单号"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.commonId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.outPitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.outPitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="wellNumber" label="井号"></el-table-column>
                    <el-table-column prop="jobNumber" label="作业号"></el-table-column>
                    <el-table-column
                        prop="circulateHrs"
                        label="循环时间(h)"
                    ></el-table-column>
                    <el-table-column
                        prop="inWellHour"
                        label="入井时间(h)"
                    ></el-table-column>
                    <el-table-column
                        prop="maxBht"
                        label="最高温度(℃)"
                    ></el-table-column>
                    <el-table-column
                        prop="run"
                        label="入井趟次"
                    ></el-table-column>
                    <el-table-column
                        prop="parentInvName"
                        label="母件品名"
                    ></el-table-column>
                    <el-table-column
                        prop="parentSerialNumber"
                        label="母件序列号"
                    ></el-table-column>
                    <el-table-column
                        prop="pitToolsNumber"
                        label="入厂操作单号"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.endMwdId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.pitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.pitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top: 20px"
                    background
                    layout="prev, pager, next"
                    :total="detailTotal"
                    :page-size="10"
                    @current-change="onCurrentDetailChange"
                    :current-page="currentDetailPage"
                ></el-pagination>
            </el-dialog>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiGetServiceHistoryDetailList, apiGetServiceHistoryList } from "@/api/tool-track";
import { apiGetInvNameFuzzyList } from "@/api/tools";
import { getRubberTypeStr } from '@/utils/constant.downhole'
@Component({})
export default class extends Vue {
    private searchForm: any = {};
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private detailForm: any = {};
    private isDialogVisible = false;
    private currentDetailPage = 1;
    private detailTotal = 0;
    private detailTableData: any[] = [];
    private mounted() {
        const serialNumber = decodeURIComponent(this.$route.query.serialNumber as string || "")
        if(serialNumber){
            this.searchForm.serialNumber = serialNumber;
        }
        this.getCurrentStockList();
    }
    
    private getSerialNumber(row) {
        if(row.invName === '万向轴壳体'){
            const { serialNumber, angle, curve } = row;
            const angleStr = angle||angle==0 ? angle+'°' : '';
            const curveStr = curve === 0 ? '可调' : curve === 1 ? '单弯' : '';
            return `${serialNumber||""} ${angleStr || curveStr ? `(${curveStr}${angleStr})` : ''}`
        }else if(row.invName === '定子'){
            const { serialNumber, rubberType } = row;
            const rubberStr = getRubberTypeStr(rubberType)
            return `${serialNumber||""} ${rubberStr ? `(${rubberStr})` : ''}`
        }else{
            return row.serialNumber
        }
        
    }
    private remoteMethodInvName(queryString: any, cb) {
        apiGetInvNameFuzzyList(
            { invName: queryString, toolType: "UNDER_WELL" }
        ).then((res) => {
            const data = res.data?.data || [];
            cb(data.map((item) => ({value: item.invName})));
        });
    }
    
    private async onDetail(row){
        this.detailForm = row;
        this.currentDetailPage = 1;
        await this.getCurrentDetailStockList();
        this.isDialogVisible = true;
    }
    private getCurrentDetailStockList() {
        const form = { toolType: "UNDER_WELL", invName:this.detailForm.invName, serialNumber: this.detailForm.serialNumber }
        return apiGetServiceHistoryList(form, {
            current: this.currentDetailPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.detailTableData = data.records || [];
            this.detailTotal = data.total;
        });
    }

    private onCurrentDetailChange(currentPage: number) {
        this.currentDetailPage = currentPage;
        this.getCurrentDetailStockList();
    }
    private getCurrentStockList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const invName = this.searchForm.invName || undefined;
        const serialNumber = this.searchForm.serialNumber || undefined;
        apiGetServiceHistoryDetailList({invName,serialNumber,toolType: "UNDER_WELL"}, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
    get p_PittoolsInfoView(){
        return this.$checkBtnPermission('sys:pittools:info');
    }
}
</script>
<style lang="scss" scoped></style>
