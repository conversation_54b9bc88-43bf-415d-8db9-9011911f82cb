<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">工单查询</div>
            <el-form :model="searchForm" label-width="80px">
                <el-row :gutter="20">
                    <el-col :span="20">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="内部单号">
                                    <el-input
                                        v-model="searchForm.pitToolsNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="工单类型">
                                    <el-select
                                        v-model="searchForm.workOrderType"
                                        @change="onSearchWorkOrder"
                                        placeholder=""
                                        clearable
                                        style="width:100%"
                                    >
                                        <el-option
                                            v-for="item in MaintainTypeList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="仪器分类">
                                    <el-select
                                        v-model="searchForm.deviceType"
                                        @change="onSearchWorkOrder"
                                        clearable
                                        style="width:100%"
                                    >
                                        <el-option
                                            v-for="item in deviceTypeList"
                                            :key="item.id"
                                            :value="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="仪器名称">
                                    <el-input
                                        v-model="searchForm.invName"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="序列号">
                                    <el-input
                                        v-model="searchForm.serialNumber"
                                        @change="onSearchWorkOrder"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="井号">
                                    <el-autocomplete
                                        style="width: calc(100%)"
                                        v-model="searchForm.wellNumber"
                                        :fetch-suggestions="querySearchAsync"
                                        placeholder="请输入内容"
                                        @select="handleSelect"
                                        clearable
                                        @change="onSearchWorkOrder"
                                        @clear="onSearchWorkOrder"
                                    ></el-autocomplete>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="维修状态">
                                    <el-select
                                        v-model="searchForm.finish"
                                        @change="onSearchWorkOrder"
                                        clearable
                                        style="width:100%"
                                    >
                                        <el-option label="完成" :value="1"></el-option>
                                        <el-option label="未完成" :value="0"></el-option>
                                        <el-option label="滞留" :value="-1"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-col>
                    
                    <!-- <el-col :span="4">
                        <el-form-item label="作业号">
                            <el-input
                                v-model="searchForm.jobNumber"
                                @change="onSearchWorkOrder"
                                style="width: calc(100% - 100px)"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="接收日期">
                            <el-date-picker
                                placement="bottom-start"
                                clearable
                                style="width: calc(100% - 100px)"
                                @change="onSearchWorkOrder"
                                value-format="yyyy-MM-dd"
                                v-model="searchForm.daterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="4">
                        <el-button
                            type="primary"
                            @click="onAddWorkOrder"
                            v-if="$checkBtnPermission(`sys:pittools:add`)"
                            >新增</el-button
                        >
                        <el-button
                            type="primary"
                            @click="onClickExportWorkOrder"
                            v-if="$checkBtnPermission(`sys:pittools:workorder:export`)"
                            >导出</el-button
                        >
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData">
                <el-table-column
                    label="内部单号"
                    prop="pitToolsNumber"
                    align="center"
                    fixed="left"
                    min-width="140px"
                ></el-table-column>
                <el-table-column
                    label="工单类型"
                    prop="workOrderType"
                    align="center"
                    fixed="left"
                    min-width="100px"
                >
                    <template slot-scope="scope">
                        {{ MaintainTypeEnumMap[scope.row.workOrderType] || "" }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="仪器类型"
                    prop="deviceTypeStr"
                    align="center"
                    width="120px"
                ></el-table-column>
                <el-table-column
                    label="仪器名称"
                    prop="invName"
                    align="center"
                    width="120px"
                ></el-table-column>
                <el-table-column
                    label="序列号"
                    prop="serialNumber"
                    align="center"
                    width="120px"
                ></el-table-column>
                <el-table-column
                    label="维修状态"
                    prop="finish"
                    width="120px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <CustomTag tagType="FINISH" :tagValue="scope.row.finish" />
                    </template>
                </el-table-column>
                
                <el-table-column
                    label="维修人"
                    prop="laborHours"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="维修耗时"
                    prop="laborHours"
                    width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="井号"
                    prop="wellNumber"
                    min-width="120"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="所有者"
                    prop="owner"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="现场联系人"
                    prop="contactUser"
                    align="center"
                    width="100"
                ></el-table-column>
                <el-table-column
                    label="联系方式"
                    prop="contactNumber"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    label="返回原因"
                    prop="returnReason"
                    align="center"
                    min-width="160"
                ></el-table-column>
                <el-table-column
                    label="入井时间"
                    prop="inWellHour"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="循环时间"
                    prop="circulateHrs"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="入井趟次"
                    prop="run"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最高温度"
                    prop="maxBht"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="完成日期"
                    prop="endDate"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后编辑人"
                    prop="lastModifiedByStr"
                    min-width="100"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="最后修改时间"
                    prop="updateTime"
                    min-width="170"
                    align="center"
                >
                    <!-- <template slot-scope="scope">
                        {{ formatDate(scope.row.updateTime) }}
                    </template> -->
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="100px"
                    v-if="p_pitToolsInfo||p_pitToolsDelete"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-if="p_pitToolsInfo"
                            type="text"
                            @click="onDetail(scope.row.pitToolsId)"
                        >
                            查看
                        </el-button>
                        <el-button
                            v-if="p_pitToolsDelete"
                            type="text"
                            @click="onDelete(scope.row.pitToolsId)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="pageSize"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <el-dialog :visible.sync="isExportDialogVisible" width="300px" top="40vh" title="请选择导出的模板">
            <div style="width:100%; margin-top:-10px">
                <el-select v-model="pitToolsExportType" style="width:100%;">
                    <el-option label="组装类型模板" :value="1"></el-option>
                    <el-option label="拆卸类型模板" :value="2"></el-option>
                    <el-option label="螺杆水力使用统计模板" :value="3"></el-option>
                </el-select>
            </div>
            <template #footer>
                <el-button @click="isExportDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onExportWorkOrder">导 出</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import { apiDeletePitToolsWorkOrder, apiGetPitToolsWorkOrderList, apiExportPittoolsWorkOrder } from "@/api/downhole-maintain";
import getDict from "@/utils/getDict";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { MaintainTypeList, MaintainTypeEnumMap, MaintainTypeEnum } from "@/utils/constant.downhole";
@Component({})
export default class extends Vue {
    private searchForm: any = {};
    private tableData: any[] = [];
    private deviceTypeList: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private MaintainTypeList = MaintainTypeList;
    private MaintainTypeEnumMap = MaintainTypeEnumMap;
    private isExportDialogVisible = false;
    private pitToolsExportType = 1;
    get p_pitToolsInfo(){
        return this.$checkBtnPermission(`sys:pittools:info`)
    }
    get p_pitToolsDelete(){
        return this.$checkBtnPermission(`sys:pittools:workorder:delete`)
    }
    private async mounted() {
        this.getPitToolsWorkOrderList();
        [this.deviceTypeList] = await getDict([18]);
    }
    private onDelete(pitToolsId: number){
        this.$confirm("确认删除？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        })
            .then(() => {
                apiDeletePitToolsWorkOrder({ pitToolsId}).then(() => {
                    this.getPitToolsWorkOrderList();
                });
            })
            .catch(() => {});
    }
    private getPitToolsWorkOrderList() {
        apiGetPitToolsWorkOrderList(this.searchForm, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }
    private onDetail(pitToolsId: number) {
        this.$router.push(
            `/downhole-tool-maintain/maintain?pitToolsId=${pitToolsId}`
        );
    }
    private onAddWorkOrder() {
        this.$router.push("/downhole-tool-maintain/maintain");
    }
    private onSearchWorkOrder() {
        const daterange = this.searchForm.daterange;
        if (daterange?.length) {
            this.searchForm.startTime = daterange[0];
            this.searchForm.endTime = daterange[1];
        } else {
            this.searchForm.startTime = undefined;
            this.searchForm.endTime = undefined;
        }
        Object.keys(this.searchForm).forEach((key) => {
            let value = this.searchForm[key];
            if (value === "" || value === null) {
                this.searchForm[key] = undefined;
            }
        });
        this.currentPage = 1;
        this.getPitToolsWorkOrderList();
    }
    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getPitToolsWorkOrderList();
    }
    private handleSelect(){
        this.onSearchWorkOrder()
    }
    private querySearchAsync(qs, cb){
        const form = new FormData();
        form.append("wellNumberKey", qs || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item.wellNumber})))
        })
    }
    private onClickExportWorkOrder(){
        // this.isExportDialogVisible = true;
        this.$confirm("确认导出当前条件下的工单?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
        }).then(()=>{
            this.onExportWorkOrder();
        })
    }
    private onExportWorkOrder(){
        const loading = this.$loading({
            lock: true,
            text: "处理中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
        });
        apiExportPittoolsWorkOrder({...this.searchForm}).then((res) => {
            if(!res.data){
                this.$message.error("暂无数据")
                return
            }
            const blob = new Blob([res.data], {
                type: "application/word;charset=UTF-8",
            });
            const workOrderType = this.searchForm.workOrderType;
            let fileNameType;
            switch (workOrderType) {
                case MaintainTypeEnum.ASSEMBLE:
                    fileNameType = "组装";
                    break;
                case MaintainTypeEnum.DISASSEMBLE:
                    fileNameType = "拆卸";
                    break;
                default:
                    fileNameType = "使用统计";
                    break;
            }
            const fileName = `井下${fileNameType}工单.xlsx`;
            const link = document.createElement("a");
            link.style.display = "none";
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            link.remove();
            window.URL.revokeObjectURL(link.href);
            this.isExportDialogVisible = false;
        }).finally(()=>{
            loading.close();
        })
    }
}
</script>
<style lang="scss" scoped></style>
