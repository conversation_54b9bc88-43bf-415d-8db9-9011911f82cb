<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">仪器列表</div>
            <el-form :model="searchForm" label-width="70px">
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-form-item label="仪器类型">
                            <el-select
                                clearable
                                style="width: 100%"
                                v-model="searchForm.deviceType"
                                placeholder=""
                                @change="getCurrentStockList(true)"
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="序列号" label-width="60px">
                            <el-input style="width: calc(100%)" clearable v-model="searchForm.serialNumber" @change="getCurrentStockList(true)">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="使用状态">
                            <el-select
                                clearable
                                style="width: 100%"
                                v-model="searchForm.useStatus"
                                placeholder=""
                                @change="getCurrentStockList(true)"
                            >
                                <el-option
                                    v-for="item in useStatusList"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="tableData" :header-cell-style="commmonTableHeaderCellStyle" >
                <el-table-column prop="deviceType" label="仪器类型">
                    <template slot-scope="scope">
                        <span>{{ getDeviceTypeStr(scope.row.deviceType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="invName" label="品名"></el-table-column>
                <el-table-column
                    prop="serialNumber"
                    label="序列号"
                    align="center"
                >
                    <template slot-scope="{row}">
                        <span v-if="row.deviceType===DEVICE_TYPE_SCREW">
                             <el-popover
                                placement="bottom"
                                width="500"
                                trigger="hover"
                                popper-class="thin-col"
                            >  
                                <div style="font-size: 16px;margin-left: 8px">螺杆信息</div>
                                <el-form label-width="100px">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="耐温：">
                                                <span>{{ row.pitToolsDevice.endureTemperature }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="压差(MPa)：">
                                                <span>{{ row.pitToolsDevice.pressureSub }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="扭矩(KN·m)：" label-width="120px">
                                                <span>{{ row.pitToolsDevice.torque }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="弯度类型：">
                                                <span>{{ getCurveTypeStr(row.pitToolsDevice.curve) }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="角度：">
                                                <span>{{ row.pitToolsDevice.angle }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="泥浆类型：">
                                                <span>{{ row.pitToolsDevice.mudType }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="最大外径：">
                                                <span>{{ row.pitToolsDevice.odMax }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="扣型：">
                                                <span>{{ row.pitToolsDevice.claspType }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                                <div style="font-size: 16px;margin-top:16px;margin-left: 8px">非核心部件信息</div>
                                <el-form label-width="100px">
                                    <el-row>
                                        <el-col :span="8">
                                            <el-form-item label="扶正器：">
                                                <span>{{ row.pitToolsDevice.stbDescribe }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="串轴承：">
                                                <span>{{ row.pitToolsDevice.bearing }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="水帽：">
                                                <span>{{ row.pitToolsDevice.waterCap }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="万向轴总成：" label-width="100px">
                                                <span>{{ row.pitToolsDevice.cardanShaft }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="上TC静圈：">
                                                <span>{{ row.pitToolsDevice.upTcStaticCircle }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="上TC动圈：">
                                                <span>{{ row.pitToolsDevice.upTcDynamicCircle }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="下TC静圈：">
                                                <span>{{ row.pitToolsDevice.downTcStaticCircle }}</span>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="下TC动圈：">
                                                <span>{{ row.pitToolsDevice.downTcDynamicCircle }}</span>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                                <span slot="reference">{{ row.serialNumber }}</span>
                            </el-popover>
                        </span>
                        <span v-else>
                            {{ row.serialNumber }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="maxBht"
                    label="最高温度(℃)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="totalCirculateHrs"
                    label="总循环时长(h)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="totalInWellHrs"
                    label="总入井时长(h)"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="serveTotalCount"
                    label="总服役次数"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="useStatus"
                    label="使用状态"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>{{ getUseStatusStr(scope.row.useStatus) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="parentInvName"
                    label="最后操作工单"
                    align="center"
                >
                    <template slot-scope="{row}">
                            <router-link 
                                tag="a" 
                                style="color: blue" 
                                target="_blank" 
                                :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.mwdId}`" 
                                v-if="p_PittoolsInfoView"
                            >
                                {{ row.lastMwdNumber }}
                            </router-link>
                        <span v-else>{{ row.lastMwdNumber }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="100px"
                    align="center"
                    v-if="p_DeviceDetail"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="onDetail(scope.row)"
                            type="text"
                            >详情</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :hide-on-single-page="true"
                style="margin-top: 20px"
                background
                layout="prev, pager, next"
                :total="total"
                :page-size="10"
                @current-change="onCurrentChange"
                :current-page="currentPage"
            ></el-pagination>
        </div>
        <DeviceDetailDialog ref="detailDialog" />
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import DeviceDetailDialog from "./detail.vue"
import { apiGetDeviceStatisticList } from "@/api/mwd";
import getDict from "@/utils/getDict";
import { DEVICE_TYPE_SCREW, getCurveTypeStr, getMudTypeStr, getUseStatusStr, useStatusList } from "@/utils/constant.downhole";

@Component({ components: { DeviceDetailDialog }})
export default class extends Vue {
    private searchForm: any = {};
    private DEVICE_TYPE_SCREW = DEVICE_TYPE_SCREW;
    private getUseStatusStr = getUseStatusStr;
    private useStatusList = useStatusList;
    private tableData: any[] = [];
    private currentPage = 1;
    private pageSize = 10;
    private total = 0;
    private detailForm: any = {};
    private isDialogVisible = false;
    private currentDetailPage = 1;
    private detailTotal = 0;
    private detailTableData: any[] = [];
    private deviceTypeList:any [] = [];
    private getCurveTypeStr = getCurveTypeStr;
    private getMudTypeStr = getMudTypeStr;
    get p_DeviceDetail(){
        return this.$checkBtnPermission('sys:pittools:devicelist:detail');
    }
    get p_PittoolsInfoView(){
        return this.$checkBtnPermission('sys:pittools:info');
    }
    private async mounted() {
        [this.deviceTypeList] = await getDict([18]);
        this.getCurrentStockList();
    }
    
    private getDeviceTypeStr(deviceType) {
        return (
            this.deviceTypeList.find((item) => item.id == deviceType)?.name ||
            ""
        );
    }
    
    private async onDetail(row){
        (this.$refs.detailDialog as any).showDialog(row)
    }
    private getCurrentStockList(flag = false) {
        if (flag) {
            this.currentPage = 1;
        }
        const invName = this.searchForm.invName || undefined;
        const serialNumber = this.searchForm.serialNumber || undefined;
        const deviceType = this.searchForm.deviceType || undefined;
        const useStatus = this.searchForm.useStatus || undefined;
        apiGetDeviceStatisticList({useStatus,invName,serialNumber,deviceType, toolType: "UNDER_WELL"}, {
            current: this.currentPage,
            size: this.pageSize,
        }).then((res) => {
            const data = res.data.data || {};
            this.tableData = data.records || [];
            this.total = data.total;
        });
    }

    private onCurrentChange(currentPage: number) {
        this.currentPage = currentPage;
        this.getCurrentStockList();
    }
}
</script>
<style lang="scss" scoped>
.thin-col {
    .el-row{
        display: flex;
        flex-wrap: wrap;
    }
    .el-form-item{
        margin-bottom: 0px;
    }
    
    .el-form :deep() .el-form-item__content {
        line-height: 24px;
        margin-top: 6px;
    }
}
</style>
