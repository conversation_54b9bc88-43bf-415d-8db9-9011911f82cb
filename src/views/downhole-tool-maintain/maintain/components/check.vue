<template>
    <div>
        <div class="tool-card-title">维修检查</div>
        <div class="step-container">
            <div
                class="step"
                :class="activeStep === item.component ? 'active' : ''"
                @click="onClickStep(item)"
                v-for="item in steps"
                :key="item.name"
            >
                <span v-html="item.name" style="white-space: pre-line"></span>
            </div>
        </div>
        <div class="component-container">
            <component
                :is="activeStep"
                :isConfirm="isConfirm"
                :checkData="checkData"
                :isEdit="isEdit"
            ></component>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import FaultDiagnosis from "./checks/faultdiagnosis.vue";
import DrillAssemble from "./checks/drill.vue";
import DrillStander from "./checks/drillstander.vue";
import InsulationAssemble from "./checks/insulaiton.vue";
import InsulationStander from './checks/insulationstander.vue'
import SpiralAssemble from "./checks/spiral.vue";
import SpiralStander from "./checks/spiralstander.vue";
import HydraAssemble from "./checks/hydra.vue";
@Component({
    components: {
        FaultDiagnosis,
        DrillAssemble,
        DrillStander,
        InsulationAssemble,
        InsulationStander,
        SpiralAssemble,
        SpiralStander,
        HydraAssemble,
    },
})
export default class extends Vue {
    private activeStep = "";
    get steps() {
        return [
            // {
            //     deviceType: "17002",
            //     name: "近钻头装配",
            //     component: "DrillAssemble",
            // },
            // {
            //     deviceType: "17002",
            //     name: "标识、包装标准",
            //     component: "DrillStander",
            // },
            // {
            //     deviceType: "17004",
            //     name: "绝缘短节装配",
            //     component: "InsulationAssemble",
            // },
            // {
            //     deviceType: "17004",
            //     name: "标识、包装标准",
            //     component: "InsulationStander",
            // },
            // {
            //     deviceType: "17003",
            //     name: "螺杆装配",
            //     component: "SpiralAssemble",
            // },
            // {
            //     deviceType: "17003",
            //     name: "标识、包装标准",
            //     component: "SpiralStander",
            // },
            // {
            //     deviceType: "17001",
            //     name: "旋扣扭矩KN·M",
            //     component: "HydraAssemble",
            // },
            { deviceType: "", name: "故障诊断", component: "FaultDiagnosis" },
        ].filter(
            (item) => !item.deviceType || item.deviceType == this.deviceType
        );
    }
    @Prop({ default: () => false }) isConfirm!: Boolean;
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => {} }) originForm!: any;
    get checkData(){
        return this.originForm?.pitToolsRepairCheckData?.checkData || {};
    }
    get deviceType(){
        return this.originForm?.workOrderPitToolsDetailList[0]?.deviceType
    }
    @Watch("deviceType")
    onDeviceTypeChange(nv: string) {
        switch (nv) {
            case "17002":
                this.activeStep = "DrillAssemble";
                break;
            case "17004":
                this.activeStep = "InsulationAssemble";
                break;
            case "17003":
                this.activeStep = "SpiralAssemble";
                break;
            case "17001":
                this.activeStep = "HydraAssemble";
                break;
            default:
                this.activeStep = "FaultDiagnosis";
                break;
        }
    }
    private onClickStep(item: any) {
        this.activeStep = item.component;
    }
}
</script>
<style lang="scss" scoped>
.step-container {
    display: flex;
    .step {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 50px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        border: 1px solid #ccc;
        user-select: none;
        cursor: pointer;
        &.active {
            background: rgba(67, 86, 152, 0.8);
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
