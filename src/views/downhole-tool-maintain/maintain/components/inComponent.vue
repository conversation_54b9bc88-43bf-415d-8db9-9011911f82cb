<template>
    <div style="margin-bottom: 20px">
        <div class="tool-card-title">入厂部件清单</div>
        <el-table :data="tableData" border :cell-style="tableCellStyle">
            <el-table-column prop="invName" label="核心部件名称"> </el-table-column>
            <el-table-column prop="serialNumber" label="序列号">
                <template slot-scope="scope">
                    <span style="margin-right:10px">{{ scope.row.serialNumber }}</span>
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onViewHistory(scope.row.serialNumber,scope.row.invName)"
                        v-if="scope.row.serialNumber"
                        >历史追溯</el-button
                    >
                </template>
            </el-table-column>
            <!-- <el-table-column prop="reviseTotalHours" label="修正总入井时间">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.reviseTotalHours"
                        v-if="isEdit"
                    ></el-input>
                    <span v-else>{{ scope.row.reviseTotalHours }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="totalHours" label="总时间">
            </el-table-column> -->
            <el-table-column prop="repairType" label="维修类型">
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.repairType"
                        v-if="isEdit"
                        @change="isChange=true"
                    >
                        <el-option
                            v-for="item in repairTypeList"
                            :value="item.value"
                            :label="item.label"
                            :key="item.value"
                        ></el-option>
                    </el-select>
                    <span v-else>
                        {{ getRepairType(scope.row.repairType) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="failureReason" label="故障原因">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.failureReason"
                        v-if="isEdit"
                        @change="isChange=true"
                    ></el-input>
                    <span v-else>{{ scope.row.failureReason }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="repairAction" label="方案&执行措施">
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.repairAction"
                        v-if="isEdit"
                        @change="isChange=true"
                    ></el-input>
                    <span v-else>{{ scope.row.repairAction }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="replacedSerialNumber" label="出厂序列号">
                <template slot-scope="scope">
                    <span style="margin-right:10px">{{ scope.row.replacedSerialNumber }}</span>
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onViewHistory(scope.row.replacedSerialNumber,scope.row.invName)"
                        v-if="scope.row.replacedSerialNumber"
                        >历史追溯</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <HistoryTrack ref="historyTrack" />
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import HistoryTrack from "./HistoryTrack.vue";
@Component({ name: "InComponents", components: { HistoryTrack } })
export default class extends Vue {
    @Prop({ default: () => {} }) originForm!: any;
    private repairTypeList = [
        { value: "MAINTAIN", label: "常规保养" },
        { value: "REPAIR", label: "失效维修" },
    ];
    @Prop({ default: () => false }) isConfirm!: Boolean;
    @Prop({ default: () => false }) isFinished!: Boolean;
    @Prop({ default: () => false }) isEdit!: Boolean;
    private isChange = false;
    get inComponentList() {
        return this.originForm?.pitToolsDeviceAssembleRecord?.inComponentList || [];
    }
    get outComponentList() {
        return this.originForm?.pitToolsDeviceAssembleRecord?.outComponentList || [];
    }
    get tableData() {
        return this.inComponentList
            .map((inItem, index) => {
                let tmp = inItem;
                let outItem = this.outComponentList[index];
                let replacedSerialNumber = outItem?.serialNumber || "";
                tmp.replacedSerialNumber = replacedSerialNumber;
                return tmp;
            })
            .filter(
                (inItem, index) =>
                    inItem.serialNumber ||
                    this.outComponentList[index].serialNumber
            );
    }
    private getRepairType(code: string) {
        return this.repairTypeList.find((item) => item.value === code)?.label;
    }
    
    private onViewHistory(serialNumber: string, invName: string) {
        (this.$refs.historyTrack as any).showDialog({ serialNumber, invName });
    }
    private tableCellStyle({ row,column }){
        if(row.serialNumber != row.replacedSerialNumber&&column.property==='replacedSerialNumber'){
            if(row.replacedSerialNumber ){
                return {background: 'rgba(0,255,0,0.5)' }
            }else{
                return { background: 'rgba(255,0,0,0.5)' }
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.hover-icon {
    display: none;
    position: absolute;
    right: 5px;
}
.hover-icon.add {
    bottom: 5px;
}
.hover-icon.delete {
    top: 5px;
}

.el-table__row:hover .hover-icon {
    display: block;
}
.el-table__row:hover .tail-input {
    width: calc(100% - 20px) !important;
}
</style>
