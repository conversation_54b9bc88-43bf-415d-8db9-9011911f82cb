<template>
    <div class="tool-card flex-row">
        <div class="tool-card-title">
            创建工单
            <el-button
                type="primary"
                style="float: right"
                @click="onAddWorkOrder"
                v-if="!pitToolsId && p_PitToolsAdd"
            >
                确认创建
            </el-button>
            <el-button
                type="primary"
                style="float: right;margin-right:10px"
                @click="onRelate"
            >
                关联钻具通知单
            </el-button>
        </div>
        <el-form
            :model="form"
            :rules="topFormRules"
            label-width="90px"
            ref="topForm"
        >
            <el-row style="margin-left: -18px">
                <el-col :span="6">
                    <el-form-item label="工单号" prop="pitToolsNumber">
                        <el-input
                            clearable
                            style="width: 100%"
                            v-model="form.pitToolsNumber"
                        >
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="工单类型" required>
                        <el-select
                            v-model="form.workOrderType"
                            placeholder=""
                            style="width:100%"
                        >
                            <el-option v-for="item in MaintainTypeList" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="tool-card-title">仪器清单</div>
            <el-form
                :model="workOrderPitToolsDetailList[0]"
                label-width="90px"
                style="padding-top: 10px; margin-left: -18px"
                :rules="bottomFormRules"
                :validate-on-rule-change="false"
                ref="bottomForm"
            >
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item
                            label="维修仪器"
                            size="normal"
                            prop="serialNumber"
                        >
                            <el-autocomplete 
                                v-model="workOrderPitToolsDetailList[0].serialNumber" 
                                :fetch-suggestions="querySearchValidSN"
                                style="width:100%"
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item
                            prop="deviceType"
                            label="仪器类型"
                            size="normal"
                        >
                            <el-select
                                style="width: 100%"
                                v-model="workOrderPitToolsDetailList[0].deviceType"
                                @change="onDeviceTypeChange($event)"
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item
                            prop="invName"
                            label="仪器名称"
                            size="normal"
                        >
                            <el-input v-model="workOrderPitToolsDetailList[0].invName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                        <el-form-item label="是否失效">
                            <el-select
                                v-model="form.isBroke"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="item in brokeList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                        <el-form-item label="井号">
                            <el-select
                                v-model="form.wellNumber"
                                filterable
                                remote
                                placeholder=""
                                :remote-method="remoteWellNumberMethod"
                                clearable
                                style="width:100%"
                            >
                                <el-option
                                v-for="item in fuzzyWellNumberList"
                                :key="item.value"
                                :label="item.value"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                        <el-form-item label="作业编号">
                            <el-input v-model="form.jobNumber"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.ASSEMBLE">
                        <el-form-item label="使用地点" size="normal">
                            <el-input
                                v-model="form.location"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="所有者" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].owner"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现场联系人" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].contactUser"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系方式" size="normal">
                            <el-input
                                v-model="
                                    workOrderPitToolsDetailList[0].contactNumber
                                "
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="6">
                        <el-form-item label="开始维修日期" label-width="110px">
                            <el-date-picker
                                placement="bottom-start"
                                style="width: 100%"
                                v-model="form.startDate"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                        <el-form-item label="入井趟次" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].run"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="入井时间" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].inWellHour"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="循环时间" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].circulateHrs"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="最高温度" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].maxBht"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="循环温度" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].circulateBht"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6" v-if="form.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                        <el-form-item label="返修原因" size="normal">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                v-model="workOrderPitToolsDetailList[0].returnReason"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="备注" size="normal">
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 3 }"
                                v-model="workOrderPitToolsDetailList[0].notes"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tool-card-title" v-if="workOrderPitToolsDetailList[0].deviceType===DEVICE_TYPE_SCREW">螺杆信息</div>
            <el-form
                :model="workOrderPitToolsDetailList[0]"
                label-width="90px"
                style="padding-top: 10px; margin-left: -18px"
                v-if="workOrderPitToolsDetailList[0].deviceType===DEVICE_TYPE_SCREW"
            >
                <el-row>
                    <el-col :span="6">
                        <el-form-item
                            label="泥浆类型"
                            size="normal"
                        >
                            <el-autocomplete
                                style="width:100%"
                                v-model="workOrderPitToolsDetailList[0].mudType"
                                :fetch-suggestions="querySearch"
                                placeholder=""
                            ></el-autocomplete>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item
                            label="弯度类型"
                            size="normal"
                        >
                            
                            <el-select
                                style="width:100%"
                                v-model="workOrderPitToolsDetailList[0].curve"
                                clearable
                            >
                                <el-option
                                    v-for="item in curveTypeList"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="弯度" size="normal">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderPitToolsDetailList[0].angle"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="耐温" size="normal">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderPitToolsDetailList[0].endureTemperature"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="扣型" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].claspType"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="扶正器" size="normal">
                            <el-input
                                v-model="workOrderPitToolsDetailList[0].stbDescribe"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="最大外径" size="normal">
                            <InputNumber 
                                style="width:100%" 
                                align="left"
                                v-model="workOrderPitToolsDetailList[0].odMax"
                            ></InputNumber>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-form>
        <el-dialog
            :title="`返修单-${repairCode}`"
            :visible.sync="isRepairDialogVisible"
        >
            <el-form ref="repairForm" :model="repairForm" label-width="100px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="作业号: ">
                            <span>{{ repairForm.jobNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="井号: ">
                            <span>{{ repairForm.wellNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="kit箱: ">
                            <span>{{ repairForm.kitNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="返修日期: ">
                            <span>{{ repairForm.returnDate }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="客户名称: ">
                            <span>{{ repairForm.customerName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="现场联系人: ">
                            <span>{{ repairForm.contactUser }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="联系方式: ">
                            <span>{{ repairForm.contactNumber }}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                :data="repairForm.repairDetailList"
                style="margin-bottom: 20px"
                :header-cell-style="commmonTableHeaderCellStyle"
            >
                <el-table-column width="55">
                    <template slot-scope="{ row }">
                        <el-checkbox
                            style="color: red"
                            @click.native.prevent="onSelect(row.repairDetailId)"
                            :value="
                                row.repairDetailId == selectedRepairDetailId
                            "
                        ></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column label="序列号" prop="serialNumber">
                </el-table-column>
                <el-table-column label="入井时间" prop="inWellHour">
                </el-table-column>
                <el-table-column label="循环时间" prop="circulateHrs">
                </el-table-column>
                <el-table-column label="循环温度" prop="circulateBht">
                </el-table-column>
                <el-table-column label="最高温度" prop="maxBht">
                </el-table-column>

                <el-table-column label="趟次" prop="run"> </el-table-column>
                <el-table-column label="返回原因" prop="returnReason">
                </el-table-column>
                <el-table-column label="备注" prop="note"> </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isRepairDialogVisible = false">
                    取 消
                </el-button>
                <el-button type="primary" @click="onHandleRelate">
                    关 联
                </el-button>
            </span>
        </el-dialog>
        <el-dialog :visible.sync="isRelateDialogVisible" title="关联钻具通知单">
            <el-form :model="relateForm" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="选择日期">
                            <el-date-picker
                                style="width: calc(100%)"
                                placement="bottom-start"
                                clearable
                                @change="onFilterPitTools"
                                value-format="yyyy-MM-dd"
                                v-model="relateForm.daterange"
                                class="fixed-separator-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="选择仪器">
                            <el-cascader
                                v-if="isRelateTreeNodeVisible"
                                style="width: calc(90%)"
                                :show-all-levels="false"
                                :props="pitToolsCascader"
                                v-model="relateForm.cascader"
                            ></el-cascader>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                
            </el-form>
            <template #footer>
                <el-button @click="isRelateDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onConfirmRelate">确认关联</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import {
    apiAddPitToolsWorkOrder,
    apiGetOaBaseInfoList,
    apiGetOaDeviceList,
    apiGetPitToolsWorkOrderInfo,
} from "@/api/downhole-maintain";
import { apiCheckSerialNumber, apiGetFuzzySerialNumberList } from "@/api/tool-mantain";

import { Form as ElForm } from "element-ui";
import { apiGetRepairInfoByCode } from "@/api/repair";
import { apiWellListFuzzy } from "@/api/wellInfo";
import { curveTypeList, DEVICE_TYPE_SCREW, MaintainTypeEnum, MaintainTypeList, mudTypeList, brokeList } from "@/utils/constant.downhole";
import { apiGetDeviceInfo } from "@/api/tools";
@Component({})
export default class extends Vue {
    private MaintainTypeList = MaintainTypeList;
    private MaintainTypeEnum = MaintainTypeEnum;
    private DEVICE_TYPE_SCREW = DEVICE_TYPE_SCREW;
    private isRelateDialogVisible = false;
    private relateDeviceList: any[] = [];
    private relateBaseInfoList: any[] =[];
    private isRelateTreeNodeVisible = true;
    private a = 1;
    private baseInfoList:any [] =[];
    private curveTypeList = curveTypeList;
    private mudTypeList = mudTypeList;
    private relateForm: any = {
        daterange: null,
        cascader: null
    };
    private brokeList = brokeList;
    // get pitToolsCascader(){
    //     console.log(this.a, '??');
    //     return {
    //         lazy: true,
    //         lazyLoad: (node, resolve)=>{
    //             const { level } = node;
    //             if(level===0){
    //                 const nodes = this.relateBaseInfoList.map(item => ({
    //                     value: item.id,
    //                     label: `${item.no}`,
    //                     leaf: false
    //                 }))
    //                 resolve(nodes)
    //             }else{
    //                 const form = new FormData();
    //                 form.append('id',node.value);
    //                 apiGetOaDeviceList(form).then(res=>{
    //                     const data = res.data.data || [];
    //                     const nodes = data.map(item => ({
    //                         value: item.id,
    //                         label: `${item.invName}(${item.serialNumber})`,
    //                         leaf: true
    //                     }))
    //                     this.relateDeviceList = data;
    //                     resolve(nodes);
    //                 })
    //             }
    //         }
    //     }
    // }
    
    private querySearch(queryString, cb) {
        let mudTypeList = this.mudTypeList;
        let results = queryString ? mudTypeList.filter(this.createFilter(queryString)) : mudTypeList;
        // 调用 callback 返回建议列表的数据
        cb(results);
    }
    createFilter(queryString) {
        return (item) => {
            return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        };
    }
    private pitToolsCascader = {
        lazy: true,
        lazyLoad: (node, resolve)=>{
            const { level } = node;
            if(level===0){
                const form = {
                    startDate: this.relateForm.daterange?.[0],
                    endDate: this.relateForm.daterange?.[1],
                }
                apiGetOaBaseInfoList(form).then(res=>{
                    
                    const data = res.data.data || [];
                    const nodes = data.map(item => ({
                        value: item.no,
                        label: `${item.no}`,
                        leaf: false
                    }))
                    resolve(nodes)
                })
            }else{
                const form = new FormData();
                form.append('no',node.value);
                apiGetOaDeviceList(form).then(res=>{
                    const data = res.data.data || [];
                    const nodes = data.map(item => ({
                        value: item.id,
                        label: `${item.invName}(${item.serialNumber})`,
                        leaf: true
                    }))
                    this.relateDeviceList = data;
                    resolve(nodes);
                })
            }
        }
    }
    private repairCode = "";
    private repairForm: any = { repairDetailList: [] };
    private fuzzyWellNumberList: any[] = []
    private isRepairDialogVisible = false;
    private selectedRepairDetailId: number | null = null;
    private form: any = {
        pitToolsNumber: "",
        wellNumber: "",
        moDocNo: "",
        jobNumber: "",
        kitNumber: "",
        startDate: "",
        endDate: "",
        workOrderType: MaintainTypeEnum.ASSEMBLE
    };
    private workOrderPitToolsDetailList: any[] = this.getInitDetailList();
    private topFormRules: any = {
        pitToolsNumber: [
            {
                required: true,
                validator: (_, __, callback) => {
                    if (!this.form.pitToolsNumber) {
                        callback(new Error("请填写工单号！"));
                    } else {
                        callback();
                    }
                },
                trigger: "blur",
            },
        ],
    };
    get bottomFormRules() {
        return {
            deviceType: [
                {
                    required: true,
                    validator: (_, __, callback) => {
                        if (!this.workOrderPitToolsDetailList[0].deviceType) {
                            callback(new Error("请填写仪器类型！"));
                        } else {
                            callback();
                        }
                    },
                    trigger: "blur",
                },
            ],
            invName: [
                {
                    required: true,
                    validator: (_, __, callback) => {
                        if (!this.workOrderPitToolsDetailList[0].invName) {
                            callback(new Error("请填写仪器名称！"));
                        } else {
                            callback();
                        }
                    },
                    trigger: "blur",
                },
            ],
            serialNumber: this.form.workOrderType === MaintainTypeEnum.DISASSEMBLE ? 
                [
                    {
                        required: true,
                        validator: (_, __, callback) => {
                            if (!this.workOrderPitToolsDetailList[0].serialNumber) {
                                callback(new Error("请填写序列号！"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ] : [
                    {
                        required: true,
                        validator: async (_, __, callback) => {
                            const serialNumber = this.workOrderPitToolsDetailList[0].serialNumber
                            if (!serialNumber) {
                               callback(new Error("请填写序列号！"));
                            } else {
                                const data = await apiGetDeviceInfo({serialNumber});
                                const info = data?.data?.data||{};
                                if(info.useStatus === "INUSE"){
                                    callback(new Error("这个序列号正在被使用！"))
                                }else{
                                    callback();
                                }
                            }
                        },
                        trigger: "blur",
                    },
                ],
        }
        
    };
    get p_PitToolsAdd() {
        return this.$checkBtnPermission(`sys:pittools:add`);
    }
    @Prop({ default: () => undefined }) pitToolsId!: number;
    @Prop({ default: () => false }) isFinished!: Boolean;
    @Prop({ default: () => [] }) deviceTypeList!: any[];
    private async mounted() {
        this.repairCode = this.$route.query.repairCode as string;
        this.selectedRepairDetailId = Number(
            this.$route.query.repairDetailId
        );
        if (this.repairCode && this.selectedRepairDetailId) {
            await apiGetRepairInfoByCode({
                repairCode: this.repairCode,
            }).then((res) => {
                this.repairForm = res.data.data;
                this.onHandleRelate();
            });
        }
    }
    private getInitDetailList() {
        return [
            {
                serialNumber: null,
                invName: "",
                deviceType: null,
                owner: null,
                contactUser: null,
                contactNumber: null,
                inWellHour: null,
                circulateHrs: null,
                failure: null,
                failureType: null,
                repairLevel: null,
                repairUser: null,
                checkedUser: null,
                approveUser: null,
                run: null,
                maxBht: null,
                laborHours: null,
                customerName: null,
                repairStartDate: null,
                repairEndDate: null,
                returnReason: null,
                rootReason: null,
                repairAction: null,
                notes: null,
            },
        ];
    }
    private getWorkOrderPitToolsInfo() {
        return apiGetPitToolsWorkOrderInfo({ pitToolsId: this.pitToolsId }).then((res) => {
            const data = res.data.data || {};
            this.form = Object.assign({}, this.form, data);
            this.form.pitToolsNumber = data.pitToolsNumber;
            this.$emit("onGetFinish", data.finish);
            this.workOrderPitToolsDetailList =
                data.workOrderPitToolsDetailList || this.getInitDetailList();
        });
    }
    private onRelateRepair() {
        apiGetRepairInfoByCode({ repairCode: this.repairCode }).then((res) => {
            this.repairForm = res.data.data;
            this.isRepairDialogVisible = true;
        });
    }
    private onSelect(id) {
        this.selectedRepairDetailId =
            this.selectedRepairDetailId == id ? null : id;
    }
    private onHandleRelate() {
        if (this.selectedRepairDetailId) {
            const detailItem = this.repairForm.repairDetailList.find(
                (item) => item.repairDetailId == this.selectedRepairDetailId
            );
            this.form.jobNumber = this.repairForm.jobNumber;
            this.form.wellNumber = this.repairForm.wellNumber;
            this.form.kitNumber = this.repairForm.kitNumber;
            this.workOrderPitToolsDetailList[0].invName = detailItem.invName;
            this.workOrderPitToolsDetailList[0].serialNumber =
                detailItem.serialNumber;
            this.workOrderPitToolsDetailList[0].inWellHour = detailItem.inWellHour;
            this.workOrderPitToolsDetailList[0].circulateHrs =
                detailItem.circulateHrs;
            this.workOrderPitToolsDetailList[0].maxBht = detailItem.maxBht;
            this.workOrderPitToolsDetailList[0].circulateBht =
                detailItem.circulateBht;
            this.workOrderPitToolsDetailList[0].run = detailItem.run;
            this.workOrderPitToolsDetailList[0].returnReason =
                detailItem.returnReason;
            this.workOrderPitToolsDetailList[0].contactNumber =
                this.repairForm.contactNumber;
            this.workOrderPitToolsDetailList[0].contactUser =
                this.repairForm.contactUser;
            this.isRepairDialogVisible = false;
        } else {
            this.$message.error("请选择一个要关联的工具！");
        }
    }
    private async onAddWorkOrder() {
        const valid1 = await (this.$refs.topForm as ElForm)
            .validate()
            .catch(() => {
                return Promise.resolve(false);
            });
        const valid2 = await (this.$refs.bottomForm as ElForm)
            .validate()
            .catch(() => {
                return Promise.resolve(false);
            });
        if (!valid1 || !valid2) {
            return;
        }
        
        let valid = false;
        const serialNumber = this.workOrderPitToolsDetailList[0].serialNumber;
        if(serialNumber){
            const res = await apiCheckSerialNumber({ serialNumber });
            const code = res.data.data;
            valid = code == 1 || code == 0;
        }
        if (!valid) {
            this.$confirm("当前序列号无效，还要继续创建吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                this.onCreateOrder()
            })
        }else{
            this.onCreateOrder()
        }
    }
    private onCreateOrder(){
        this.form.workOrderPitToolsDetailList = this.workOrderPitToolsDetailList;
        apiAddPitToolsWorkOrder(this.form).then((res) => {
            const pitToolsId = res.data.data.pitToolsId;
            this.$message.success("创建成功");
            this.$router.push(`/redirect/downhole-tool-maintain/maintain?pitToolsId=${pitToolsId}`)
        });
    }
    private onDeviceTypeChange(id) {
        let tmp = this.deviceTypeList.find((map) => map.id == id);
        const deviceType = tmp.id == 17003 ? "" : tmp.name;
        this.workOrderPitToolsDetailList[0].invName = deviceType;
    }
    private onRelate(){
        this.isRelateDialogVisible = true;
    }
    private onConfirmRelate(){
        // 可预见的bug, 大整数id不可靠 
        const [selectBaseNo,selectDeviceId] = this.relateForm.cascader || [];
        if(!selectDeviceId){
            this.$message.error('请选择一个仪器！');
            return;
        }
        const baseForm  = {
            no: selectBaseNo
        }
        apiGetOaBaseInfoList(baseForm).then(res=>{
            const data = res.data.data || [];
            const item = data[0];
            if(item){
                this.workOrderPitToolsDetailList[0].contactNumber = item.contactNumber;
                this.workOrderPitToolsDetailList[0].contactUser = item.contactUser;
                this.form.jobNumber = item.jobNumber;
                this.form.workOrderType = item.workOrderType || MaintainTypeEnum.ASSEMBLE;
                this.workOrderPitToolsDetailList[0].owner = item.owner;
            }
        })
        const deviceForm = new FormData();
        deviceForm.append('no',selectBaseNo);
        apiGetOaDeviceList(deviceForm).then((res)=>{
            const data = res.data.data || [];
            const item = data.find(d=>d.id==selectDeviceId);
            if(item){
                this.workOrderPitToolsDetailList[0].invName = item.invName;
                this.workOrderPitToolsDetailList[0].serialNumber = item.serialNumber;
                this.workOrderPitToolsDetailList[0].inWellHour = item.inWellHour;
                this.workOrderPitToolsDetailList[0].circulateHrs = item.circulateHrs;
                this.workOrderPitToolsDetailList[0].maxBht = item.maxBht;
                this.workOrderPitToolsDetailList[0].circulateBht = item.circulateBht;
                this.workOrderPitToolsDetailList[0].returnReason = item.returnReason;
                this.workOrderPitToolsDetailList[0].angle = item.angle;
                this.workOrderPitToolsDetailList[0].claspType = item.claspType;
                this.workOrderPitToolsDetailList[0].endureTemperature = item.endureTemperature;
                this.workOrderPitToolsDetailList[0].mudType = item.mudType;
                this.workOrderPitToolsDetailList[0].odMax = item.odMax;
                this.workOrderPitToolsDetailList[0].stbDescribe = item.stbDescribe;
                this.workOrderPitToolsDetailList[0].notes = item.notes;
                if(item.mudType || item.odMax || item.angle || item.endureTemperature || item.claspType || item.stbDescribe){
                    this.workOrderPitToolsDetailList[0].deviceType=DEVICE_TYPE_SCREW;
                }
            }
            this.isRelateDialogVisible = false;
        })
    }
    
    private remoteWellNumberMethod(query){
        const form = new FormData();
        form.append("wellNumberKey", query || "");
        apiWellListFuzzy(form).then(res=>{
            const data = res.data.data || [];
            this.fuzzyWellNumberList = data.map(item=>({value:item.wellNumber}))
        })
    }
    private onFilterPitTools(){
        // const form = {
        //     startDate: this.relateForm.daterange?.[0],
        //     endDate: this.relateForm.daterange?.[1],
        // }
        // apiGetOaBaseInfoList(form).then(res=>{
        //     const data = res.data.data || [];
        //     this.relateBaseInfoList = data;
        //     this.isRelateTreeNodeVisible = false;
        //     setTimeout(() => {
        //         this.isRelateTreeNodeVisible = true;
        //     }, 0);
        // })
        this.isRelateTreeNodeVisible = false;
        setTimeout(() => {
            this.isRelateTreeNodeVisible = true;
        }, 0);
    }
    
    private querySearchValidSN(query:string, cb:any){
        apiGetFuzzySerialNumberList({serialNumber:query}).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item})))
        })
    }
}
</script>

<style lang="scss" scoped>
.tool-card.flex-row .el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>