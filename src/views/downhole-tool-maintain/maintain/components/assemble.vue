<template>
    <div style="margin-bottom:20px">
        <div class="tool-card-title">拆卸组装</div>
        <el-button @click="onPaste" v-if="isEdit" type="primary" style="margin-bottom:10px">出入厂一致</el-button>
        <el-form label-width="80px" v-if="isScrew">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="扶正器">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.stbDescribe"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="串轴承">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.bearing"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="水帽">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.waterCap"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="万向轴总成" label-width="90px">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.cardanShaft"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="上TC静圈">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.upTcStaticCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="上TC动圈">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.upTcDynamicCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="下TC静圈">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.downTcStaticCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="下TC动圈">
                        <el-input :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.downTcDynamicCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="耐温">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.endureTemperature">
                        </InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="橡胶类型">
                        <el-select disabled v-model="computedForm.rubberType" style="width:100%" placeholder="">
                            <el-option
                                v-for="item in rubberTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="压差(MPa)">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.pressureSub"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="扭矩(KN·m)" label-width="100px">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.torque"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="弯度类型">
                        <el-select disabled v-model="computedForm.curve" style="width:100%">
                            <el-option
                                v-for="item in curveTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="角度">
                        <el-input style="width:100%" align="left" disabled @change="isChange = true" v-model="computedForm.angle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="泥浆类型">
                        <el-select :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.mudType" style="width:100%">
                            <el-option v-for="item in mudTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
                        </el-select>
                        <el-autocomplete
                            v-model="assembleForm.mudType"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入内容"
                            @select="handleSelect"
                        ></el-autocomplete>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="最大外径">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isChange = true" v-model="assembleForm.odMax"></InputNumber>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="tableData" border>
            <el-table-column prop="invName" label="名称" width="200"></el-table-column>
            <el-table-column prop="serialNumber" label="入厂序列号">
                <template slot-scope="scope">
                    <span v-if="scope.row.invName==='定子'" style="margin-right:10px">
                        {{ scope.row.serialNumber }} {{scope.row.rubberType ? `(${getRubberTypeStr(scope.row.rubberType)})` : ''}}
                    </span>
                    <span v-else-if="scope.row.invName==='万向轴壳体'" style="margin-right:10px">
                        {{ getInKetiStr(scope.row) }}
                        
                    </span>
                    <span v-else>
                        {{scope.row.serialNumber}}
                    </span>
                    <el-button
                        class="hover-btn"
                        type="text"
                        @click="onViewHistory(scope.row.serialNumber,scope.row.invName)"
                        v-if="scope.row.serialNumber"
                        >历史追溯</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column prop="" label="出厂序列号">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-autocomplete
                            v-model="scope.row.replacedSerialNumber"
                            :fetch-suggestions="querySearchAsync"
                            @focus="onFocus(scope.row.invName)"
                            value-key="serialNumber"
                            @change="onSNChange"
                            @select="onSNChange"
                            @input="scope.row.valid = true"
                            v-if="isEdit"
                            style="margin-right:10px"
                        ></el-autocomplete>
                        
                        <div v-if="isEdit&&!scope.row.valid" class="sn-error-msg">
                            该部件已组装在其它仪器中
                        </div>
                        <el-select
                            style="width:200px;"
                            v-if="scope.row.invName==='定子'"
                            @change="onTableChange"
                            v-model="scope.row.replacedRubberType"
                            placeholder="橡胶类型"
                            clearable
                        >
                            <el-option
                                v-for="item in rubberTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                        <el-select
                            style="width:80px;margin-right:10px"
                            v-if="scope.row.invName==='万向轴壳体'"
                            @change="onTableChange"
                            v-model="scope.row.replacedCurve"
                            placeholder="弯度类型"
                            clearable
                        >
                            <el-option
                                v-for="item in curveTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                        <InputNumber
                            align="left"
                            style="width:110px;"
                            v-if="scope.row.invName==='万向轴壳体'"
                            @change="onTableChange"
                            v-model="scope.row.replacedAngle"
                            placeholder="弯壳体角度"
                            clearable
                        ></InputNumber>
                    </template>
                    <!-- <el-input v-model="scope.row.replacedSerialNumber"></el-input> -->
                    
                    <span v-else>
                        <span v-if="scope.row.invName==='定子'" style="margin-right:10px">
                            {{ scope.row.replacedSerialNumber }} {{scope.row.replacedRubberType ? `(${getRubberTypeStr(scope.row.replacedRubberType)})` : ''}}
                        </span>
                        <span v-else-if="scope.row.invName==='万向轴壳体'" style="margin-right:10px">
                            {{ getOutKetiStr(scope.row) }}
                        </span>
                        <span v-else style="margin-right:10px">{{ scope.row.replacedSerialNumber }}</span>
                        <el-button
                            class="hover-btn"
                            type="text"
                            @click="onViewHistory(scope.row.replacedSerialNumber,scope.row.invName)"
                            v-if="scope.row.replacedSerialNumber"
                            >历史追溯</el-button
                        >
                    </span>
                </template>
            </el-table-column>
        </el-table>
        <HistoryTrack ref="historyTrack" />
    </div>
</template>
<script lang="ts">
import { apiGetCustomToolList } from "@/api/tools";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { curveTypeList, getCurveTypeStr, getRubberTypeStr, mudTypeList, rubberTypeList } from "@/utils/constant.downhole";
import HistoryTrack from "./HistoryTrack.vue";
import { apiValidatePittoolsAssemble } from "@/api/downhole-maintain";
@Component({ name: "Assemble", components: { HistoryTrack } })
export default class extends Vue {
    @Prop({ default: () => [] }) inComponentList!: any[];
    @Prop({ default: () => [] }) outComponentList!: any[];
    @Prop({default:()=>{}}) originForm !:any;
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => false }) isScrew!: Boolean;
    private currentInvName = "";
    private isChange = false;
    private computedForm:any = {
        angle: null,
        rubberType: null,
        curve: null
    }
    private rubberTypeList = rubberTypeList;
    private getRubberTypeStr = getRubberTypeStr;
    private curveTypeList = curveTypeList;
    private mudTypeList = mudTypeList;
    private mounted() {}
    get tableData() {
        return this.inComponentList.map((inItem, index) => {
            let tmp = {...inItem}
            let outItem = { ...this.outComponentList[index] };
            let replacedSerialNumber = outItem?.serialNumber || "";
            tmp.replacedSerialNumber = replacedSerialNumber;
            let replacedAngle = outItem?.angle;
            tmp.replacedAngle = replacedAngle;
            let replacedCurve = outItem?.curve;
            tmp.replacedCurve = replacedCurve;
            if(tmp.invName==='万向轴壳体'){
                this.computedForm.angle = replacedAngle;
                this.computedForm.curve = replacedCurve;
            }
            let replacedRubberType = outItem?.rubberType || null;
            tmp.replacedRubberType = replacedRubberType;
            if(tmp.invName==='定子'){
                this.computedForm.rubberType = replacedRubberType;
            }
            tmp.valid = true;
            return tmp;
        });
    }
    get assembleForm() {
        return this.originForm.pitToolsDeviceAssembleRecord
    }
    private querySearch(queryString, cb) {
        let mudTypeList = this.mudTypeList;
        let results = queryString ? mudTypeList.filter(this.createFilter(queryString)) : mudTypeList;
        // 调用 callback 返回建议列表的数据
        cb(results);
    }
    createFilter(queryString) {
        return (item) => {
            return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        };
    }
    private onSNChange() {
        this.isChange = true;
        this.validateAssembleSN()
    }
    private getComputedFields(){
        const keti = this.tableData.find(item=>item.invName==='万向轴壳体')
        if(keti){
            this.computedForm.angle = keti.replacedAngle;
            this.computedForm.curve = keti.replacedCurve;
        }
        const dingzi = this.tableData.find(item=>item.invName==='定子')
        if(dingzi){
            this.computedForm.rubberType = dingzi.replacedRubberType
        }
    }
    private getInKetiStr(row){
        const { serialNumber, angle, curve } = row;
        const angleStr = angle||angle==0 ? angle+'°' : '';
        const curveStr = getCurveTypeStr(curve);
        return `${serialNumber||''} ${angleStr || curveStr ? `(${curveStr}${angleStr})` : ''}`
    }
    private getOutKetiStr(row){
        const { replacedSerialNumber, replacedAngle, replacedCurve } = row;
        const angleStr = replacedAngle||replacedAngle==0 ? replacedAngle+'°' : '';
        const curveStr = getCurveTypeStr(replacedCurve);
        return `${replacedSerialNumber} ${angleStr || curveStr ? `(${curveStr}${angleStr})` : ''}`
    }
    private onTableChange(){
        this.isChange = true;
        this.$nextTick(()=>{
            this.getComputedFields()
        })
    }
    private onFocus(invName: string) {
        this.currentInvName = invName;
    }
    private onPaste() {
        this.isChange = true;
        this.tableData.forEach((item,index) => {
            this.tableData[index].replacedSerialNumber = item.serialNumber;
            this.tableData[index].replacedCurve = item.curve;
            this.tableData[index].replacedAngle = item.angle;
            this.getComputedFields();
        })
    }
    private querySearchAsync(serialNumber: string, cb: any) {
        apiGetCustomToolList(
            { invName: this.currentInvName, serialNumber },
            { current: 1, size: 20 }
        ).then((res) => {
            cb(res.data.data?.records);
        });
    }
    private onViewHistory(serialNumber: string, invName: string) {
        (this.$refs.historyTrack as any).showDialog({ serialNumber, invName });
    }
    private async validateAssembleSN(){
        const pitToolsDeviceAssembleRecord = this.originForm.pitToolsDeviceAssembleRecord
        this.tableData.forEach((td, index) => {
            pitToolsDeviceAssembleRecord.outComponentList[index].serialNumber = td?.replacedSerialNumber||"";
            pitToolsDeviceAssembleRecord.outComponentList[index].rubberType = td?.replacedRubberType||null;
            pitToolsDeviceAssembleRecord.outComponentList[index].angle = td?.replacedAngle;
            pitToolsDeviceAssembleRecord.outComponentList[index].curve = td?.replacedCurve;
        });
        const validateInfo = await apiValidatePittoolsAssemble(pitToolsDeviceAssembleRecord);
        const validateData = validateInfo?.data?.data || [];
        validateData.forEach((target)=>{
            const idx = this.tableData.findIndex(item=>item.invName===target.invName&&item.replacedSerialNumber===target.serialNumber);
            this.tableData[idx].valid = false;
        })
        return validateData;
    }
}
</script>
<style lang="scss" scoped>
.hover-btn {
    display: none;
    padding: 0;
    margin-left: 20px;
}
.el-table__row:hover .hover-btn {
    display: inline-block;
}
.sn-error-msg{
    color: red;
    font-size: 12px;
}
</style>
