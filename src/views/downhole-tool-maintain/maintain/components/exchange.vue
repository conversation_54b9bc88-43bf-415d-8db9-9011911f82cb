<template>
    <div>
        <div  style="margin-bottom:10px" v-if="!isFinished&&isEdit">
            <el-button @click="addRow()" type="primary" style="margin-right:10px">首行插入</el-button>
            <span style="float:right">
                <el-input v-model="pickOrderNumber" clearable style="width:200px;margin-right:10px"></el-input>
                <el-button @click="onRelatePickOrder" type="primary">关联领料单</el-button>
            </span>
            
        </div>
        
        <el-table :data="tableData" border>
            <el-table-column prop="serialNumber" label="零件号">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.serialNumber" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.serialNumber}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="chDescription" label="中文描述">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.chDescription" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.chDescription}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="enDescription" label="英文描述">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.enDescription" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.enDescription}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.quantity" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.quantity}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="note" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.note" style="width:100%" class="tail-input" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.note}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                    
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { apiGetPickOrderList } from "@/api/erp";
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "Exchange" })
export default class extends Vue {
    private addRowItem = {serialNumber:'',chDescription:'',enDescription:'',quantity:'',note:''}
    private pickOrderNumber = ""
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>false}) isEdit !:Boolean
    @Prop({default:()=>false}) isFinished !:Boolean
    @Prop({default:()=>[]}) originForm !:any;
    get tableData(){
        return this.originForm.pitToolsRepairCheckData?.checkData?.replaceItemList || []
    }
    private deleteCell(scope:any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope:any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem,
            });
        } else {
            this.tableData.unshift({
                ...this.addRowItem,
            });
        }
    }
    private onRelatePickOrder(){
        if(!this.pickOrderNumber){
            this.$message.error("请填写领料单号！");
            return;
        }
        const form = new FormData();
        form.append("docNo", this.pickOrderNumber)
        apiGetPickOrderList(form).then(res=>{
            let data:any[] = res?.data?.data || [];
            data = data.map(item=>{
                let tmp:any = {...this.addRowItem};
                tmp.serialNumber = item.serialNumber;
                tmp.quantity = item.quantity;
                tmp.note = item.note;
                tmp.chDescription = item.chDescription;
                tmp.enDescription = item.enDescription;
                return tmp;
            })
            data.forEach(item=>{
                this.tableData.push(item)
            })
            this.pickOrderNumber = "";
        })
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
