<template>
    <div class="flex-row">
        <div class="tool-card-title">基本信息</div>
        <el-form :model="detailForm" ref="detailForm" :rules="detailFormRules" label-width="70px">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="维修仪器" prop="serialNumber">
                        <!-- <el-input
                            style="width: 100%"
                            v-model="detailForm.serialNumber"
                            disabled
                        ></el-input> -->
                        <el-autocomplete
                            disabled
                            v-model="detailForm.serialNumber" 
                            :fetch-suggestions="querySearchValidSN"
                            style="width:100%"
                        ></el-autocomplete>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="仪器类型">
                        <el-select
                            clearable
                            style="width: 100%"
                            v-model="detailForm.deviceType"
                            disabled
                            placeholder=""
                        >
                            <el-option
                                v-for="item in deviceTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="仪器名称">
                        <el-input
                            disabled
                            v-model="detailForm.invName"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                    <el-form-item label="是否失效">
                        <el-select
                            v-model="originForm.isBroke"
                            :disabled="!isEdit"
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in brokeList"
                                :value="item.value"
                                :label="item.label"
                                :key="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                    <el-form-item label="井号">
                        <el-input
                            :disabled="!isEdit"
                            v-model="originForm.wellNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                    <el-form-item label="作业号">
                        <el-input
                            :disabled="!isEdit"
                            v-model="originForm.jobNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.ASSEMBLE">
                    <el-form-item label="使用地点" size="normal">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.location"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="所有者">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.owner"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="现场联系人" label-width="90px">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.contactUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="联系方式">
                        <el-input
                            :disabled="!isEdit"
                            v-model="detailForm.contactNumber"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="接收日期">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.receiveDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="开始维修日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.startDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="预计完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="detailForm.estimatedEndDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="实际完成日期" label-width="100px">
                        <el-date-picker
                            :disabled="!isEdit"
                            placement="bottom-start"
                            clearable
                            style="width: 100%"
                            value-format="yyyy-MM-dd"
                            v-model="originForm.endDate"
                            type="date"
                        >
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修人员">
                        <el-input
                            disabled
                            v-model="detailForm.repairUser"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修耗时">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            disabled
                            v-model="detailForm.laborHours"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修状态">
                        <el-select
                            disabled
                            v-model="originForm.finish"
                            style="width: 100%"
                            placeholder=""
                        >
                            <el-option label="完成" :value="1"></el-option>
                            <el-option label="未完成" :value="0"></el-option>
                            <el-option label="滞留" :value="-1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                    <el-form-item label="入井趟次">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.run"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="入井时间">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.inWellHour"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="循环时间">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.circulateHrs"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="最高温度">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.maxBht"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="循环温度">
                        <InputNumber 
                            style="width:100%" 
                            align="left"
                            :disabled="!isEdit"
                            v-model="detailForm.circulateBht"
                        ></InputNumber>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="6" v-if="originForm.workOrderType===MaintainTypeEnum.DISASSEMBLE">
                    <el-form-item label="返回原因">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.returnReason"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="备注">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.notes"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="使用情况" size="normal">
                        <el-input
                            type="textarea"
                            :disabled="!isEdit"
                            :autosize="{ minRows: 3 }"
                            v-model="detailForm.usageInfo"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="车间检查发现" label-width="100px">
                        <el-input
                            :disabled="!isEdit"
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="detailForm.findings"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="维修措施" size="normal">
                        <el-input
                            type="textarea"
                            :disabled="!isEdit"
                            :autosize="{ minRows: 3 }"
                            v-model="detailForm.repairAction"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="对现场的建议" size="normal" label-width="100px">
                        <el-input
                            type="textarea"
                            :disabled="!isEdit"
                            :autosize="{ minRows: 3 }"
                            v-model="detailForm.suggestion"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
<script lang="ts">
import { apiGetFuzzySerialNumberList } from "@/api/tool-mantain";
import { apiGetDeviceInfo } from "@/api/tools";
import { MaintainTypeEnum, brokeList } from "@/utils/constant.downhole";
import { ElForm } from "element-ui/types/form";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
@Component({
    name: "BaseInfo",
})
export default class extends Vue {
    private form: any = {};
    private MaintainTypeEnum = MaintainTypeEnum;
    private originSerialNumber = "";
    private detailFormRules = {
        serialNumber: [
            {
                validator: this.validator,
                trigger: "blur",
            },
        ]
    }
    private brokeList = brokeList;
    @Prop({ default: () => [] }) failureTypeList!: any[];
    @Prop({ default: () => [] }) failureReasonTypeList!: any[];
    @Prop({ default: () => [] }) failureComponentTypeList!: any[];
    @Prop({ default: () => [] }) deviceTypeList!: any[];
    @Prop({ default: () => false }) isConfirm!: Boolean;
    @Prop({ default: () => false }) isFinished!: Boolean;
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => ({ workOrderPitToolsDetailList: [] }) }) originForm!: any;
    get detailForm() {
        return this.originForm.workOrderPitToolsDetailList[0] || {};
    }
    private async validator(_, __, callback) {
            // 只有在变化时才校验
            const serialNumber = this.detailForm.serialNumber;
            if(this.originSerialNumber === serialNumber){
                callback();
                return
            }
            if (!serialNumber) {
                callback();
            } else {
                const data = await apiGetDeviceInfo({serialNumber});
                const info = data?.data?.data||{};
                if(info.useStatus === "INUSE"){
                    callback(new Error("这个序列号正在被使用！"))
                }else{
                    callback();
                }
            }
        }
    private querySearchValidSN(query:string, cb:any){
        apiGetFuzzySerialNumberList({serialNumber:query}).then(res=>{
            const data = res.data.data || [];
            cb(data.map(item=>({value:item})))
        })
    }
    private async validateForm(){
        return await (this.$refs.detailForm as ElForm).validate();
    }
}
</script>
<style lang="scss" scoped>
.flex-row .el-row{
    display: flex;
    flex-wrap: wrap;
}
</style>
