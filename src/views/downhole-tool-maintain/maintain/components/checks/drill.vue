<template>
    <div>
        <!-- 近钻头 -->
        <el-table :data="tableData" border>
            <el-table-column prop="processName" label="工序内容" width="600">
                <template slot-scope="{ row }">
                    <el-input
                        v-if="!isConfirm"
                        type="textarea"
                        v-model="row.processName"
                    ></el-input>
                    <span v-else>{{ row.processName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="toolsCommonData.device" label="设备/检具">
            </el-table-column>
            <el-table-column prop="toolsCommonData.taskTime" label="工时/件">
                <template slot-scope="{ row }">
                    <InputNumber
                        align="left"
                        style="width: 100%"
                        v-if="!isConfirm"
                        v-model="row.toolsCommonData.taskTime"
                    ></InputNumber>
                    <span v-else>{{ row.toolsCommonData.taskTime }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="toolsCommonData.operator" label="操作者">
                <template slot-scope="{ row }">
                    <el-input
                        v-if="!isConfirm"
                        v-model="row.toolsCommonData.operator"
                    ></el-input>
                    <span v-else>{{ row.toolsCommonData.operator }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="toolsCommonData.date" label="日期">
                <template slot-scope="{ row }">
                    <el-date-picker
                        v-if="!isConfirm"
                        placement="bottom-start"
                        style="width: 100%"
                        v-model="row.toolsCommonData.date"
                        type="date"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                    <span v-else>{{ row.toolsCommonData.date }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="toolsCommonData.comments" label="备注">
                <template slot-scope="{ row }">
                    <el-input
                        v-if="!isConfirm"
                        type="textarea"
                        v-model="row.toolsCommonData.comments"
                    ></el-input>
                    <span v-else>{{ row.toolsCommonData.comments }}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { drillList } from "./data";
@Component({ name: "RingOutTest" })
export default class extends Vue {
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>{processList:drillList}}) checkData !:any
    get tableData() {
        return this.checkData.processList;
    }
}
</script>
