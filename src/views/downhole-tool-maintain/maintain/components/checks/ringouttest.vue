<template>
    <div>
        <el-button @click="addRow()" type="primary" style="margin-bottom:10px" v-if="!tableData.length&&!isConfirm">新增行</el-button>
        <el-table :data="tableData" border>
            <el-table-column prop="name" label="U=Uphole;D=Downhole">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.name" v-if="!isConfirm"></el-input>
                    <span v-else>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="value" label="Nominal">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.value" v-if="!isConfirm"></el-input>
                    <span v-else>{{scope.row.value}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="in" label="Incoming">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.in" v-if="!isConfirm"></el-input>
                    <span v-else>{{scope.row.in}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="out" label="Outgoing">
                <template slot-scope="scope">
                    <template v-if="!isConfirm">
                        <el-input v-model="scope.row.out" style="width:100%" class="tail-input"></el-input>
                            <i 
                                @click.stop="deleteCell(scope)"
                                class="el-icon-close hover-icon delete"
                            ></i>
                            <i
                                @click.stop="addRow(scope)"
                                class="el-icon-plus hover-icon add"
                            ></i>
                    </template>
                    <span v-else>{{scope.row.out}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "RingOutTest" })
export default class extends Vue {
    @Prop({default:()=>{ringOutTestList:[]}}) checkData !:any
    get tableData(){
        return this.checkData.ringOutTestList
    }
    private addRowItem = {name:'',value:'',in:'',out:''};
    @Prop({default:()=>false}) isConfirm !:Boolean
    private deleteCell(scope:any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope:any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem,
            });
        } else {
            this.tableData.push({
                ...this.addRowItem,
            });
        }
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
