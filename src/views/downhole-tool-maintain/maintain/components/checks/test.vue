<template>
    <div>
        <el-table
            :data="tableData"
            border
            :header-cell-style="discountHeaderStyle"
            :span-method="arraySpanMethod"
        >
            <el-table-column label="工序内容">
                <el-table-column prop="name" label="工序内容">
                    <template slot-scope="{ row }">
                        <template v-if="!row.name">
                            <span>{{ row.key }}: </span>
                            <el-radio-group
                                @change="onChangeStander"
                                v-model="row.value"
                            >
                                <el-radio :label="true">是</el-radio>
                                <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                        </template>
                        <span v-else>{{ row.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="keyvalue" label="工序内容">
                    <template slot-scope="{ row }">
                        <template v-if="typeof row.keyvalue === 'object'">
                            <span>{{ row.keyvalue.key }}: </span>
                            <el-radio-group
                                @change="onChange"
                                v-model="row.keyvalue.value"
                            >
                                <el-radio :label="true">是</el-radio>
                                <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                        </template>

                        <template v-else>
                            <el-input
                                @change="onChange"
                                v-model="row.keyvalue"
                            ></el-input>
                        </template>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="device" label="设备/检具"> </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { processList, standerList } from "./data";
// { [col] : { [row]: strategy } }
const rowMergeStrategy = {
    0: {
        0: [2, 1],
        3: [2, 1],
        5: [3, 1],
    },
    2: {
        0: [5, 1],
        5: [3, 1],
    },
};
const colMergeStrategy = {
    2: [1, 2],
};

const colMergeStrategyKeys = Object.keys(colMergeStrategy).map((item) => +item); //transfer keys to Number
@Component({ name: "RingOutTest" })
export default class extends Vue {
    // @Prop({default:()=>{ringOutTestList:[]}}) checkData !:any
    get tableData(): any[] {
        let ret: any[] = [];
        const processListLen = this.processList.length;
        for (let i = 0; i < processListLen; i++) {
            if (i === 1) {
                ret.push(this.standerList[0]);
            }
            if (i === 3) {
                ret.push(this.standerList[1]);
            }
            const item = this.processList[i];
            const name = item.name;
            const itemKeys = Object.keys(item).filter(
                (k) => k !== "toolsCommonData" && k !== "name" && item[k]
            );
            for (let j = 0; j < itemKeys.length; j++) {
                ret.push({
                    name,
                    // TODO: 无法双绑
                    keyvalue: item[itemKeys[j]],
                    ...item.toolsCommonData,
                });
            }
        }
        console.log(ret);
        return ret;
        return [
            {
                name: "传动轴-下TC动圈",
                keyvalue: false,
                device: "拆装架1",
            },
            {
                name: "传动轴-下TC动圈",
                keyvalue: "2",
                device: "拆装架1",
            },
            {
                name: "顺序装配",
                keyvalue: "",
                device: "拆装架1",
            },
            {
                name: "传动轴-上TC动圈",
                keyvalue: "3",
                device: "拆装架/深度尺",
            },
            {
                name: "传动轴-上TC动圈",
                keyvalue: "4",
                device: "拆装架/深度尺",
            },
            {
                name: "传动轴外壳-下TC静圈",
                keyvalue: "5",
                device: "拆装架/深度尺",
            },
            {
                name: "传动轴外壳-下TC静圈",
                keyvalue: "6",
                device: "拆装架/深度尺",
            },
            {
                name: "传动轴外壳-下TC静圈",
                keyvalue: "7",
                device: "拆装架/深度尺",
            },
        ];
        // return this.checkData.ringOutTestList;
    }
    private processList = processList;
    private standerList = standerList;
    private addRowItem = { name: "", value: "", in: "", out: "" };
    @Prop({ default: () => false }) isConfirm!: Boolean;
    // 合并表头
    private discountHeaderStyle({ row, column, rowIndex, columnIndex }) {
        if (rowIndex === 1) {
            //隐藏另外领两个头部单元格
            return { display: "none" };
        }
    }
    // 内容合并策略

    private arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        // TODO:
        {
            // row
            const colKeys = Object.keys(rowMergeStrategy).map((item) => +item); //transfer keys to Number
            const colKeysLen = colKeys.length;
            for (let col_i = 0; col_i < colKeysLen; col_i++) {
                const colKey = colKeys[col_i];
                if (columnIndex === colKey) {
                    const rowItem = rowMergeStrategy[colKey];
                    const rowKeys = Object.keys(rowItem).map((item) => +item); //transfer keys to Number
                    const rowKeysLen = rowKeys.length;
                    for (let row_i = 0; row_i < rowKeysLen; row_i++) {
                        const rowKey = rowKeys[row_i];
                        const mergeStrategy = rowItem[rowKey];
                        const offset = mergeStrategy[0];

                        if (rowIndex === rowKey) {
                            return mergeStrategy;
                        } else if (
                            rowIndex > rowKey &&
                            rowIndex < rowKey + offset
                        ) {
                            return [0, 0];
                        }
                    }
                }
            }
        }

        const colMergeStrategyKeysLen = colMergeStrategyKeys.length;

        for (let i = 0; i < colMergeStrategyKeysLen; i++) {
            const key = colMergeStrategyKeys[i];
            const mergeStrategy = colMergeStrategy[key];
            const offset = mergeStrategy[1];
            if (rowIndex === key) {
                // TODO:
                if (columnIndex === 0) {
                    return mergeStrategy;
                } else if (columnIndex > 0 && columnIndex < 0 + offset) {
                    return [0, 0];
                }
            }
        }
        return [1, 1];
    }
    private onChange() {
        console.log(this.processList);
    }
    private onChangeStander(){
        console.log(this.standerList)
    }
    private deleteCell(scope: any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope: any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem,
            });
        } else {
            this.tableData.push({
                ...this.addRowItem,
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.hover-icon {
    display: none;
    position: absolute;
    right: 5px;
}
.hover-icon.add {
    bottom: 5px;
}
.hover-icon.delete {
    top: 5px;
}

.el-table__row:hover .hover-icon {
    display: block;
}
.el-table__row:hover .tail-input {
    width: calc(100% - 20px) !important;
}
</style>
