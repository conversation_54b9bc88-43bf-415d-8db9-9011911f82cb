<template>
    <div>
        <!-- 螺杆 -->
        <el-table
            :data="tableData"
            border
            :header-cell-style="discountHeaderStyle"
        >
            <el-table-column label="工序内容" align="center">
                <el-table-column prop="name" label="name"> </el-table-column>
                <el-table-column prop="value1" label="device">
                    <template slot-scope="{ row }">
                        <template v-if="row.value1">
                            <template v-if="typeof row.value1 === 'object'">
                                <span>{{ row.value1.key }}: </span>
                                <el-radio-group
                                    @change="onChange"
                                    v-model="row.value1.value"
                                    :disabled="isConfirm"
                                >
                                    <el-radio :label="true">是</el-radio>
                                    <el-radio :label="false">否</el-radio>
                                </el-radio-group>
                            </template>

                            <template v-else>
                                <el-input
                                    v-if="!isConfirm"
                                    @change="onChange"
                                    v-model="row.value1"
                                ></el-input>
                                <span v-else>{{row.value1}}</span>
                            </template>
                        </template>
                        <template v-else>
                            <span></span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column prop="value2" label="taskTime" width="200">
                    <template slot-scope="{ row }">
                        <template v-if="row.value2">
                            <template v-if="typeof row.value2 === 'object'">
                                <span>{{ row.value2.key }}: </span>
                                <el-radio-group
                                    @change="onChange"
                                    v-model="row.value2.value"
                                    :disabled="isConfirm"
                                >
                                    <el-radio :label="true">是</el-radio>
                                    <el-radio :label="false">否</el-radio>
                                </el-radio-group>
                            </template>

                            <template v-else>
                                <el-input
                                    v-if="!isConfirm"
                                    @change="onChange"
                                    v-model="row.value2"
                                ></el-input>
                                <span v-else>{{row.value2}}</span>
                            </template>
                        </template>
                        <template v-else>
                            <span></span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column prop="value3" label="operator" width="220">
                    <template slot-scope="{ row }">
                        <template v-if="row.value3">
                            <template v-if="typeof row.value3 === 'object'">
                                <span>{{ row.value3.key }}: </span>
                                <el-radio-group
                                    @change="onChange"
                                    v-model="row.value3.value"
                                    :disabled="isConfirm"
                                >
                                    <el-radio :label="true">是</el-radio>
                                    <el-radio :label="false">否</el-radio>
                                </el-radio-group>
                            </template>

                            <template v-else>
                                <el-input
                                    v-if="!isConfirm"
                                    @change="onChange"
                                    v-model="row.value3"
                                ></el-input>
                                <span v-else>{{row.value3}}</span>
                            </template>
                        </template>
                        <template v-else>
                            <span></span>
                        </template>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column align="center" prop="toolsCommonData.device" label="设备/检具">
            </el-table-column>
            <el-table-column align="center" prop="toolsCommonData.taskTime" label="工时/件">
                <template slot-scope="{ row }">
                    <InputNumber
                        style="width:100%"
                        align="left"
                        v-if="!isConfirm"
                        v-model="row.toolsCommonData.taskTime"
                        @change="onChange"
                    ></InputNumber>
                    <span v-else>
                        {{row.toolsCommonData.taskTime}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="toolsCommonData.operator" label="操作者">
                <template slot-scope="{ row }">
                    <el-input v-model="row.toolsCommonData.operator" v-if="!isConfirm"></el-input>
                    <span v-else>
                        {{row.toolsCommonData.operator}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="toolsCommonData.date" label="日期">
                <template slot-scope="{ row }">
                    <el-date-picker
                        v-if="!isConfirm"
                        placement="bottom-start"
                        style="width: 100%"
                        v-model="row.toolsCommonData.date"
                        type="date"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                    <span v-else>
                        {{row.toolsCommonData.date}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="toolsCommonData.comments" label="备注">
                <template slot-scope="{ row }">
                    <el-input v-model="row.toolsCommonData.comments" v-if="!isConfirm"></el-input>
                    <span v-else>
                        {{row.toolsCommonData.comments}}
                    </span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { processList } from "./data";
@Component({ name: "RingOutTest" })
export default class extends Vue {
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>{processList:processList}}) checkData !:any
    get tableData() {
        return this.checkData.processList;
    }
    // 合并表头
    private discountHeaderStyle({ row, column, rowIndex, columnIndex }) {
        if (rowIndex === 1) {
            //隐藏另外领两个头部单元格
            return { display: "none" };
        }
    }
    private onChange() {}
}
</script>
