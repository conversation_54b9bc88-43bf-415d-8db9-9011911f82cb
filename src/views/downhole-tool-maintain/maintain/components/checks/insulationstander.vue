<template>
    <div>
        <!-- 绝缘短节标准 -->
        <template v-for="(item,key) in tableData">
            <div :key="key">
                <span style="display:inline-block;text-align:left;width:160px;margin-bottom:16px">
                    {{item.key}}：
                </span>
                <el-radio-group
                    v-model="item.value"
                    :disabled="isConfirm"
                >
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </div>
        </template>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { standerList } from "./data";
@Component({ name: "InsulationStander" })
export default class extends Vue {
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>{standerList:standerList}}) checkData !:any
    get tableData() {
        return this.checkData.standerList;
    }
}
</script>
