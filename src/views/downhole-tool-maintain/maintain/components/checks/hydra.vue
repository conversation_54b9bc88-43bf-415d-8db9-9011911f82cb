<template>
    <div style="width:600px">
        <!-- 水利震荡 -->
        <el-table :data="tableData" border :show-header="false">
            <el-table-column prop="key" label="key"> </el-table-column>
            <el-table-column prop="value" label="value">
                <template slot-scope="{ row }">
                    <el-input v-if="!isConfirm" v-model="row.value"></el-input>
                    <span v-else>{{row.value}}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component({ name: "Hydra" })
export default class extends Vue {
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>{torsionList:[]}}) checkData !:any
    get tableData() {
        return this.checkData.torsionList;
    }
}
</script>
