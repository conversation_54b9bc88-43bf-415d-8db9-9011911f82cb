<template>
    <ImageViewer :disabled="isEdit" class="fault-diagnosis">
        <el-form :model="form" label-width="150px" style="margin-left: -50px">
            <el-row :gutter="100">
                <el-col :span="12">
                    <el-form-item label="入厂拍照">
                        <!-- <el-input type="textarea" v-model="form.inPicture" :disabled="isConfirm"></el-input> -->
                        <upload-image
                            @deleteImageSuccess="deleteImageSuccess"
                            @uploadImageSuccess="uploadImageSuccess"
                            :src="form.inPicture"
                            v-if="isEdit"
                        ></upload-image>
                        <img v-else :src="form.inPicture" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="外观检测结果">
                        <quill-editor
                            v-if="isEdit"
                            class="editor"
                            style="width: 100%"
                            ref="faceCheckResults"
                            v-model="form.faceCheckResults"
                            :options="getSetting('faceCheckResults')"
                        />
                        <div
                            v-else
                            v-html="form.faceCheckResults"
                            style="width: 100%;height:240px;overflow:auto"
                        ></div>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="100">
                <el-col :span="12">
                    <el-form-item label="入厂初步检查">
                        <quill-editor
                            v-if="isEdit"
                            class="editor"
                            style="width: 100%"
                            ref="preliminaryTestResultEditor"
                            v-model="form.preliminaryTestResult"
                            :options="getSetting('preliminaryTestResultEditor')"
                        />
                        <div
                            v-else
                            v-html="form.preliminaryTestResult"
                            style="width: 100%;height:240px;overflow:auto"
                        ></div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="下载数据分析">
                        <quill-editor
                            v-if="isEdit"
                            class="editor"
                            style="width: 100%"
                            ref="dataAnalysisEditor"
                            v-model="form.dataAnalysis"
                            :options="getSetting('dataAnalysisEditor')"
                        />

                        <div
                            v-else
                            v-html="form.dataAnalysis"
                            style="width: 100%;height:240px;overflow:auto"
                        ></div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="复制故障现象结果">
                        <el-input
                            type="textarea"
                            :autosize="{ minRows: 10, maxRows: 10 }"
                            v-model="form.faultReappearResults"
                            :disabled="!isEdit"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="检测与测试结果">
                        <quill-editor
                            v-if="isEdit"
                            class="editor"
                            style="width: 100%"
                            ref="testResultEditor"
                            v-model="form.testResult"
                            :options="getSetting('testResultEditor')"
                        />
                        <div
                            v-else
                            v-html="form.testResult"
                            style="width: 100%;height:240px;overflow:auto"
                        ></div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="根本原因&改善方法">
                        <quill-editor
                            v-if="isEdit"
                            class="editor"
                            style="width: 100%"
                            ref="rootReasonAndImproveMethodEditor"
                            v-model="form.rootReasonAndImproveMethod"
                            :options="
                                getSetting('rootReasonAndImproveMethodEditor')
                            "
                        />
                        <div
                            v-else
                            v-html="form.rootReasonAndImproveMethod"
                            style="width: 100%;height:240px;overflow:auto"
                        ></div>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="维修措施">
                        <el-input
                            type="textarea"
                            :autosize="{ minRows: 10, maxRows: 10 }"
                            v-model="form.repairAdvice"
                            :disabled="!isEdit"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <form
            action
            method="post"
            enctype="multipart/form-data"
            id="uploadFormMulti"
        >
            <input
                style="display: none"
                id="ghostUploader"
                type="file"
                name="file"
                multiple
                accept="image/jpg,image/jpeg,image/png,image/gif"
                @change="uploadImg('uploadFormMulti')"
            />
        </form>
    </ImageViewer>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

import UploadImage from "@/components/UploadImage/index.vue";
import { apiUploadFile } from "@/api/file";
import ImageViewer from "@/components/ImageViewer/index.vue";
@Component({
    name: "FaultDiagnosis",
    components: { UploadImage, ImageViewer },
})
export default class extends Vue {
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({
        default: () => ({
            faultDiagnosis: {},
        }),
    })
    checkData!: any;
    get form() {
        return this.checkData.faultDiagnosis;
    }
    private getSetting(editorRef: string) {
        return {
            placeholder: "",
            modules: {
                toolbar: {
                    container: [
                        [
                            // { list: "ordered" },
                            // { list: "bullet" },
                            // { align: [] },
                            "image",
                        ],
                    ],
                    handlers: {
                        image: () => {
                            this.currentEditor = editorRef;
                            let quill = (this.$refs[editorRef] as any).quill;
                            const ghostUploaderDom: any = document.getElementById("ghostUploader");
                            // fixbug: 无法重复上传同一文件
                            if(ghostUploaderDom.value){
                                ghostUploaderDom.value = null;
                            }
                            // 获取光标所在位置
                            ghostUploaderDom.click();                            
                            this.insertPosition = quill.getSelection().index;
                        },
                    },
                },
                resizeImage: {
                    displayStyles: {
                        backgroundColor: "black",
                        border: "none",
                        color: "white",
                    },
                    modules: ["Resize", "DisplaySize"],
                },
            },
        };
    }
    private uploadImg() {
        let quill = (this.$refs[this.currentEditor] as any).quill;
        let files = (document.getElementById("ghostUploader") as any).files;
        console.log(files);
        Promise.all(files.map(file=>{
            const form = new FormData();
            form.append("file", file);
            return apiUploadFile(form).then(res=>{
                return res.data.data;
            })
        })).then((urls) => {
            urls.forEach((url) => {
                console.log(url);
                quill.insertEmbed(this.insertPosition, "image", url);
                this.insertPosition += 1;
            });
            quill.setSelection(this.insertPosition);
        });
    }
    private uploadImageSuccess(src: string) {
        this.form.inPicture = src;
    }
    private deleteImageSuccess() {
        this.form.inPicture = "";
    }
}
</script>
<style lang="scss" scoped>
.fault-diagnosis :deep() img {
    max-width: 100%;
}
</style>
<style>
.editor .ql-toolbar.ql-snow {
    padding: 0 !important;
}
</style>
