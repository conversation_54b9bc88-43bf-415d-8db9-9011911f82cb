<template>
    <div style="margin-bottom:20px">
        <div class="tool-card-title">维修信息</div>
        <el-form label-width="80px" v-if="isScrew">
            <div class="screw-title">螺杆参数：</div>
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="耐温">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.endureTemperature">
                        </InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="橡胶类型">
                        <el-select disabled v-model="computedForm.rubberType" style="width:100%" placeholder="">
                            <el-option
                                v-for="item in rubberTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="压差(MPa)">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.pressureSub"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="扭矩(KN·m)" label-width="100px">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.torque"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="弯度类型">
                        <el-select disabled v-model="computedForm.curve" style="width:100%">
                            <el-option
                                v-for="item in curveTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="角度">
                        <el-input style="width:100%" align="left" disabled @change="isAssembleChange = true" v-model="computedForm.angle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="泥浆类型">
                        <el-autocomplete
                            style="width:100%"
                            :disabled="!isEdit"
                            @change="isAssembleChange = true"
                            v-model="assembleForm.mudType"
                            :fetch-suggestions="querySearch"
                            placeholder=""
                        ></el-autocomplete>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="最大外径">
                        <InputNumber style="width:100%" align="left" :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.odMax"></InputNumber>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="扣型">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.claspType"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <div class="screw-title">部件信息：</div>
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="扶正器">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.stbDescribe"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="串轴承">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.bearing"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="水帽">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.waterCap"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="万向轴总成" label-width="90px">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.cardanShaft"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="上TC静圈">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.upTcStaticCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="上TC动圈">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.upTcDynamicCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="下TC静圈">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.downTcStaticCircle"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="下TC动圈">
                        <el-input :disabled="!isEdit" @change="isAssembleChange = true" v-model="assembleForm.downTcDynamicCircle"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table :data="tableData" border>
            <el-table-column prop="invName" label="名称" width="200"></el-table-column>
            <el-table-column prop="" :label="snLabel">
                <template slot-scope="scope">
                    <template v-if="isEdit">
                        <el-autocomplete
                            v-model="scope.row.serialNumber"
                            :fetch-suggestions="querySearchAsync"
                            @focus="onFocus(scope.row.invName)"
                            value-key="serialNumber"
                            @change="onSNChange"
                            @select="onSNChange"
                            @input="scope.row.valid = true"
                            style="width:200px; margin-bottom: 4px"
                        ></el-autocomplete>
                        
                        <div v-if="!scope.row.valid" class="sn-error-msg">
                            该部件已组装在其它仪器中
                        </div>
                        <el-select
                            style="width:200px;"
                            v-if="scope.row.invName==='定子'"
                            @change="onTableChange"
                            v-model="scope.row.rubberType"
                            placeholder="橡胶类型"
                            clearable
                        >
                            <el-option
                                v-for="item in rubberTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                        <div></div>
                        <el-select
                            style="width:80px;margin-right:10px"
                            v-if="scope.row.invName==='万向轴壳体'"
                            @change="onTableChange"
                            v-model="scope.row.curve"
                            placeholder="弯度类型"
                            clearable
                        >
                            <el-option
                                v-for="item in curveTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></el-option>
                        </el-select>
                        <InputNumber
                            align="left"
                            style="width:110px;"
                            v-if="scope.row.invName==='万向轴壳体'"
                            @change="onTableChange"
                            v-model="scope.row.angle"
                            placeholder="弯壳体角度"
                            clearable
                        ></InputNumber>
                    </template>
                    <!-- <el-input v-model="scope.row.serialNumber"></el-input> -->
                    
                    <span v-else>
                        <span v-if="scope.row.invName==='定子'" style="margin-right:10px">
                            {{ scope.row.serialNumber }} {{scope.row.rubberType ? `(${getRubberTypeStr(scope.row.rubberType)})` : ''}}
                        </span>
                        <span v-else-if="scope.row.invName==='万向轴壳体'" style="margin-right:10px">
                            {{ getKetiStr(scope.row) }}
                        </span>
                        <span v-else style="margin-right:10px">{{ scope.row.serialNumber }}</span>
                        <el-button
                            class="hover-btn"
                            type="text"
                            @click="onViewHistory(scope.row.serialNumber,scope.row.invName)"
                            v-if="scope.row.serialNumber"
                            >历史追溯</el-button
                        >
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="repairType" label="维修类型">
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.repairType"
                        v-if="isEdit"
                        @change="isRepairChange=true"
                    >
                        <el-option
                            v-for="item in transferRepairTypeList(scope.row.invName)"
                            :value="item.value"
                            :label="item.label"
                            :key="item.value"
                        ></el-option>
                    </el-select>
                    <span v-else>
                        {{ getRepairTypeForAll(scope.row) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="failureReason" label="是否失效">
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.isBroke"
                        v-if="isEdit"
                        @change="isRepairChange=true"
                    >
                        <el-option
                            v-for="item in brokeList"
                            :value="item.value"
                            :label="item.label"
                            :key="item.value"
                        ></el-option>
                    </el-select>
                    <span v-else>
                        {{ getBrokeStr(scope.row.isBroke) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="failureReason" label="故障原因">
                <template slot-scope="scope">
                    <el-input @change="isRepairChange = true" v-model="scope.row.failureReason" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.failureReason}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="repairAction" label="方案&执行措施">
                <template slot-scope="scope">
                    <el-input @change="isRepairChange = true" v-model="scope.row.repairAction" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.repairAction}}</span>
                </template>
            </el-table-column>
        </el-table>
        <HistoryTrack ref="historyTrack" />
    </div>
</template>
<script lang="ts">
import { apiGetCustomToolList } from "@/api/tools";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { getRubberTypeStr, MaintainTypeEnum, rubberTypeList, repairTypeList, getRepairType, curveTypeList, getCurveTypeStr, mudTypeList, brokeList, getBrokeStr } from "@/utils/constant.downhole";
import HistoryTrack from "./HistoryTrack.vue";
import { apiValidatePittoolsAssemble } from "@/api/downhole-maintain";
const screwRepairTypeInvNameKeyMap = {
    "传动轴": "transmissionShaftFaults",
    "传动轴壳体": "transmissionShaftHousingFaults",
    "转子": "rotorFaults",
    "万向轴壳体": "universalJointHousingFaults",
    "定子": "statorFaults",
    "防掉接头": "antiDropJointFaults",
    "代用接头": "replacementJointFaults"
};
@Component({ name: "Assemble", components: { HistoryTrack } })
export default class extends Vue {
    @Prop({ }) inComponentList!: any[];
    @Prop({ }) outComponentList!: any[];
    @Prop({default:()=>{}}) originForm !:any;
    @Prop({ default: () => false }) isEdit!: Boolean;
    @Prop({ default: () => false }) isScrew!: Boolean;
    @Prop({ default: () => ({
        antiDropJointFaults: [],
        replacementJointFaults: [],
        rotorFaults: [],
        statorFaults: [],
        transmissionShaftFaults: [],
        transmissionShaftHousingFaults: [],
        universalJointHousingFaults: []
    })}) repairTypeMap!:any;
    private currentInvName = "";
    private isAssembleChange = false;
    private isRepairChange = false;
    private computedForm:any = {
        angle: null,
        rubberType: null,
        curve: null
    }
    private rubberTypeList = rubberTypeList;
    private getRubberTypeStr = getRubberTypeStr;
    private MaintainTypeEnum = MaintainTypeEnum;
    private getRepairType = getRepairType;
    private curveTypeList = curveTypeList;
    private getCurveTypeStr = getCurveTypeStr;
    private mudTypeList = mudTypeList;
    private brokeList = brokeList;
    private getBrokeStr = getBrokeStr;
    private mounted() {}
    get snLabel(){
        return this.originForm.workOrderType === MaintainTypeEnum.ASSEMBLE? '出厂序列号' : '入厂序列号'
    }
    // 这一块应该有问题
    get tableData() {
        let componentList: any[] = [];
        switch (this.originForm.workOrderType){
            case MaintainTypeEnum.ASSEMBLE:
                componentList = this.outComponentList;
                break;
            case MaintainTypeEnum.DISASSEMBLE:
                componentList = this.inComponentList;
                break;
        }

        componentList.forEach(item => {
            if(item.invName==='万向轴壳体'){
                this.computedForm.angle = item.angle;
                this.computedForm.curve = item.curve;
            }
            if(item.invName==='定子'){
                this.computedForm.rubberType = item.rubberType;
            }
            item.valid = true;
        });
        return componentList;
    }
    get assembleForm() {
        return this.originForm.pitToolsDeviceAssembleRecord
    }
    private transferRepairTypeList(invName) {
        const target = screwRepairTypeInvNameKeyMap[invName]
        if(!target){
            // 权当是默认吧
            return repairTypeList;
        }
        return (Object.entries(this.repairTypeMap[target]) || []).map(([label,value])=>({label,value}))
    }
    private getRepairTypeForAll({invName, repairType}){
        const target = screwRepairTypeInvNameKeyMap[invName];
        if(target){
            return (Object.entries(this.repairTypeMap[target]) || []).find(([_,value])=>value===repairType)?.[0] || repairType
        }else{
            return getRepairType(repairType)
        }
    }
    private querySearch(queryString, cb) {
        let mudTypeList = this.mudTypeList;
        let results = queryString ? mudTypeList.filter(this.createFilter(queryString)) : mudTypeList;
        // 调用 callback 返回建议列表的数据
        cb(results);
    }
    createFilter(queryString) {
        return (item) => {
            return (item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        };
    }
    private onSNChange() {
        this.isAssembleChange = true;
        this.validateAssembleSN()
    }
    private getComputedFields(){
        const keti = this.tableData.find(item=>item.invName==='万向轴壳体')
        if(keti){
            this.computedForm.angle = keti.angle;
            this.computedForm.curve = keti.curve;
        }
        const dingzi = this.tableData.find(item=>item.invName==='定子')
        if(dingzi){
            this.computedForm.rubberType = dingzi.rubberType
        }
    }
    private getKetiStr(row){
        const { serialNumber, angle, curve } = row;
        const angleStr = angle||angle==0 ? angle+'°' : '';
        const curveStr = getCurveTypeStr(curve);
        return `${serialNumber||""} ${angleStr || curveStr ? `(${curveStr}${angleStr})` : ''}`
    }
    private onTableChange(){
        this.isAssembleChange = true;
        this.$nextTick(()=>{
            this.getComputedFields()
        })
    }
    private onFocus(invName: string) {
        this.currentInvName = invName;
    }
    private querySearchAsync(serialNumber: string, cb: any) {
        apiGetCustomToolList(
            { invName: this.currentInvName, serialNumber },
            { current: 1, size: 20 }
        ).then((res) => {
            cb(res.data.data?.records);
        });
    }
    private onViewHistory(serialNumber: string, invName: string) {
        (this.$refs.historyTrack as any).showDialog({ serialNumber, invName });
    }
    private async validateAssembleSN(){
        const validateInfo = await apiValidatePittoolsAssemble(this.originForm.pitToolsDeviceAssembleRecord);
        const validateData = validateInfo?.data?.data || [];
        validateData.forEach((target)=>{
            const idx = this.tableData.findIndex(item=>item.invName===target.invName&&item.serialNumber===target.serialNumber);
            this.tableData[idx].valid = false;
        })
        return validateData;
    }
}
</script>
<style lang="scss" scoped>
.hover-btn {
    display: none;
    padding: 0;
    margin-left: 20px;
}
.el-table__row:hover .hover-btn {
    display: inline-block;
}
.sn-error-msg{
    color: red;
    font-size: 12px;
}
.screw-title{
    // font-weight: bold;
    margin-bottom: 16px;
}
</style>
