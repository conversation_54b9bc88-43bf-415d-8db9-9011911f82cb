<template>
    <el-dialog :visible.sync="isHistoryTrackDialogVisible" title="历史追溯" width="80%">
        <el-tabs v-model="activeName" style="margin-top:-20px">
            <el-tab-pane label="历史服役" name="service" style="min-height:200px">
                <el-table v-if="activeName==='service'" :data="serviceTableData">
                    <el-table-column
                        prop="outPitToolsNumber"
                        label="出厂操作单号"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.commonId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.outPitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.outPitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="invName" label="部件名称"></el-table-column>
                    <el-table-column prop="serialNumber" label="序列号"></el-table-column>
                    <el-table-column prop="startDate" label="服役日期" width="100"></el-table-column>
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column prop="wellNumber" label="井号"></el-table-column>
                    <el-table-column prop="jobNumber" label="作业号"></el-table-column>
                    <el-table-column prop="circulateHrs" label="循环时间" width="80"></el-table-column>
                    <el-table-column prop="inWellHour" label="入井时间" width="80"></el-table-column>
                    <el-table-column prop="maxBht" label="最高温度" width="80"></el-table-column>
                    <el-table-column prop="run" label="入井趟次" width="80"></el-table-column>
                    <el-table-column
                        prop="pitToolsNumber"
                        label="入厂操作单号"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.endMwdId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.pitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.pitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="serviceTotal"
                    :page-size="pageSize"
                    @current-change="onServiceCurrentChange"
                    :current-page="serviceCurrentPage"
                ></el-pagination>
            </el-tab-pane>
            <el-tab-pane label="历史维修" name="maintain" style="min-height:200px">
                <el-table v-if="activeName==='maintain'" :data="maintainTableData">
                    <el-table-column prop="invName" label="部件名称"></el-table-column>
                    <el-table-column prop="serialNumber" label="序列号"></el-table-column>
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column
                        prop="pitToolsId"
                        label="维修单号"
                        min-width="110"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.pitToolsId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.pitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.pitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="" label="维修日期"></el-table-column>
                    <el-table-column prop="repairType" label="维修类型">
                        <template slot-scope="{row}">
                            <span>{{ getRepairType(row.repairType) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="failureReason" label="故障原因"></el-table-column>
                    <el-table-column prop="repairAction" label="方案&执行措施"></el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="maintainTotal"
                    :page-size="pageSize"
                    @current-change="onMaintainCurrentChange"
                    :current-page="maintainCurrentPage"
                ></el-pagination>
            </el-tab-pane>
            <el-tab-pane label="出厂列表" name="out" style="min-height:200px">
                <el-table v-if="activeName==='out'" :data="outTableData">
                    <el-table-column prop="invName" label="部件名称"></el-table-column>
                    <el-table-column prop="serialNumber" label="序列号"></el-table-column>
                    <el-table-column prop="parentInvName" label="母件品名"></el-table-column>
                    <el-table-column prop="parentSerialNumber" label="母件序列号"></el-table-column>
                    <el-table-column
                        prop="pitToolsNumber"
                        label="维修单号"
                        min-width="110"
                    >
                        <template slot-scope="{row}">
                                <router-link 
                                    tag="a" 
                                    style="color: blue" 
                                    target="_blank" 
                                    :to="`/downhole-tool-maintain/maintain?pitToolsId=${row.commonId}`" 
                                    v-if="p_PittoolsInfoView"
                                >
                                    {{ row.pitToolsNumber }}
                                </router-link>
                            <span v-else>{{ row.pitToolsNumber }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :hide-on-single-page="true"
                    style="margin-top:20px"
                    background
                    layout="prev, pager, next"
                    :total="outTotal"
                    :page-size="pageSize"
                    @current-change="onOutCurrentChange"
                    :current-page="outCurrentPage"
                ></el-pagination>
            </el-tab-pane>
        </el-tabs>
    </el-dialog>
</template>
<script lang="ts">
import { apiGetCoreOutList } from "@/api/mwd";
import { apiGetServiceHistoryList, apiGetMaintainHistoryList } from "@/api/tool-track";
import { getRepairType } from "@/utils/constant.downhole";
import { Component, Vue } from "vue-property-decorator";
@Component({ name: "HistoryTrack" })
export default class extends Vue {
    private activeName = "service";
    private isHistoryTrackDialogVisible = false;
    private serviceTableData:any[] = [];
    private maintainTableData:any[] = [];
    private pageSize = 10;
    private serviceCurrentPage = 1;
    private serviceTotal = 0;
    private maintainCurrentPage = 1;
    private maintainTotal = 0;
    private outTableData:any[] = [];
    private outCurrentPage = 1;
    private outTotal = 0;
    private getRepairType = getRepairType;
    get p_PittoolsInfoView(){
        return this.$checkBtnPermission('sys:pittools:info');
    }
    private async showDialog({ serialNumber, invName}:any){
        if(!serialNumber){
            return;
        }
        this.serialNumber = serialNumber;
        this.invName = invName;
        await this.getServiceHistoryList();
        await this.getMaintainHistoryList();
        await this.getOutList();
        this.isHistoryTrackDialogVisible = true;
        
    }
    private getServiceHistoryList(){
       return apiGetServiceHistoryList({toolType: "UNDER_WELL",serialNumber:this.serialNumber, invName: this.invName}, {current:this.serviceCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.serviceTableData = data.records || [];
            this.serviceTotal = data.total;
        })
    }
    private onServiceCurrentChange(currentPage: number) {
        this.serviceCurrentPage = currentPage;
        this.getServiceHistoryList();
    }
    private getMaintainHistoryList(){
        return apiGetMaintainHistoryList({toolType: "UNDER_WELL",serialNumber:this.serialNumber, invName: this.invName}, {current:this.maintainCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.maintainTableData = data.records || [];
            this.maintainTotal = data.total;
        })
    }
    private onMaintainCurrentChange(currentPage: number) {
        this.maintainCurrentPage = currentPage;
        this.getMaintainHistoryList();
    }
    private getOutList(){
        return apiGetCoreOutList({toolType: "UNDER_WELL",serialNumber:this.serialNumber, invName: this.invName}, {current:this.outCurrentPage,size:this.pageSize}).then(res=>{
            const data = res.data.data || {};
            this.outTableData = data.records || [];
            this.outTotal = data.total;
        })
    }
    private onOutCurrentChange(currentPage: number) {
        this.outCurrentPage = currentPage;
        this.getOutList();
    }
    
    
}
</script>