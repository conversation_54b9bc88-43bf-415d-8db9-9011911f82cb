<template>
    <div style="margin-top: 20px">
        <div class="tool-card-title">工时统计</div>
        <div  style="margin-bottom:10px" v-if="isEdit">
            <el-button @click="addRow()" type="primary" style="margin-right:10px">首行插入</el-button>
            <span style="float:right">
                <el-input v-model="erpLaborNo" clearable style="width:200px;margin-right:10px"></el-input>
                <el-button @click="onRelateERP" type="primary">关联ERP工时</el-button>
            </span>
            
        </div>
        
        <el-table :data="tableData" border>
            <el-table-column prop="serialNumber" label="员工姓名" width="200px" :key="Math.random()">
                <template slot="header">
                    <el-tooltip v-if="isEdit" class="item" effect="dark" content="输入关键字后请务必选择姓名, 否则该条数据将无法录入" placement="top-start">
                        <span>员工姓名<i class="el-icon-info"></i></span>
                    </el-tooltip>
                    <span v-else>员工姓名</span>
                </template>
                <template slot-scope="scope">
                    <el-autocomplete
                        popper-class="my-autocomplete"
                        v-model="scope.row.name"
                        :fetch-suggestions="querySearch"
                        placeholder="请输入员工姓名并选择"
                        @select="handleSelect($event, scope)"
                        v-if="isEdit"
                    >
                        <template slot-scope="{ item }">
                            <div class="name">{{ item.name }}</div>
                        </template>
                    </el-autocomplete>
                    <span v-else>{{scope.row.name}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="laborHours" label="人工用时" width="200px">
                <template slot-scope="scope">
                    <InputNumber v-model="scope.row.laborHours" v-if="isEdit" style="width:100%"></InputNumber>
                    <span v-else>{{scope.row.laborHours}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="machineHours" label="机器用时" width="200px">
                <template slot-scope="scope">
                    <InputNumber v-model="scope.row.machineHours" v-if="isEdit" style="width:100%"></InputNumber>
                    <span v-else>{{scope.row.machineHours}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注">
                <template slot-scope="scope">
                    <el-input v-model="scope.row.notes" style="width:100%" class="tail-input" v-if="isEdit"></el-input>
                    <span v-else>{{scope.row.notes}}</span>
                    <template v-if="isEdit">
                        <i 
                            @click.stop="deleteCell(scope)"
                            class="el-icon-close hover-icon delete"
                        ></i>
                        <i
                            @click.stop="addRow(scope)"
                            class="el-icon-plus hover-icon add"
                        ></i>
                    </template>
                    
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { apiGetLaborHours, apiGetLaborHoursBaseInfoList } from "@/api/downhole-maintain";
import { apiGetMemberList } from "@/api/users";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
@Component({ name: "Labor" })
export default class extends Vue {
    private addRowItem = {memberId:null,name:null,laborHours:null,machineHours:null,notes:null}
    private userList:any[] = [];
    private erpLaborNo = '';
    @Prop({default:()=>false}) isConfirm !:Boolean
    @Prop({default:()=>false}) isEdit !:Boolean
    @Prop({default:()=>{}}) originForm !:any;
    get tableData(){
        return this.originForm.repairMemberList || []
    }
    private deleteCell(scope:any) {
        let index = scope.$index;
        this.tableData.splice(index, 1);
    }
    private addRow(scope:any) {
        if (scope) {
            let index = scope.$index;
            this.tableData.splice(index + 1, 0, {
                ...this.addRowItem
            });
        } else {
            this.tableData.unshift({
                ...this.addRowItem
            });
        }
    }
    private querySearch(qs, cb) {
        apiGetMemberList({name:qs}).then(res=>{
            cb(res.data.data?.memberInfoList || [])
        })
    }
    private handleSelect(item, scope) {
        const {row, $index} = scope;
        const index = this.tableData.findIndex(td=>(td.memberId==item.memberId)&&td.name);
        if(index>=0&&index!==$index){
            this.$message.error('列表已经有该员工！')
            return;
        }
        row.name = item.name;
        row.memberId = item.memberId;
    }
    private onRelateERP() {
        if(!this.erpLaborNo){
            return
        }
        apiGetLaborHoursBaseInfoList({docNo: this.erpLaborNo}).then(res1=>{
            const baseInfoList = res1?.data?.data || [];
            if(baseInfoList[0]){
                apiGetLaborHours({transferDocId: baseInfoList[0].transferDocId}).then(res2=>{
                    const laborHoursList = res2?.data?.data || [];
                    if(laborHoursList.length>0){
                        this.tableData.push(...laborHoursList.map(item=>({
                            memberId: item.memberId,
                            name: item.memberName,
                            laborHours: item.laborHour,
                            machineHours: item.machineHour
                        })));
                    }else{
                        
                        this.$message.error('无有效数据')
                    }
                })
            }else{
                this.$message.error('无有效数据')
            }
        })
    }
}
</script>
<style lang="scss" scoped>
    .hover-icon {
        display: none;
        position: absolute;
        right: 5px;
    }
    .hover-icon.add {
        bottom: 5px;
    }
    .hover-icon.delete {
        top: 5px;
    }

    .el-table__row:hover .hover-icon {
        display: block;
    }
    .el-table__row:hover .tail-input{
        width: calc(100% - 20px)!important;
    }
</style>
