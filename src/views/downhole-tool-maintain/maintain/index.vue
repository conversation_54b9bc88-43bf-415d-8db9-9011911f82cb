<template>
    <div>
        <div class="tool-maintain-container" v-loading="loading">
            <div class="tool-card" style="position: relative">
                <!-- #region 创建工单 -->
                <template v-if="!pitToolsId">
                    <!-- <div class="tool-card-title">工单信息</div>

                    <div class="tool-card-title">基本信息</div> -->
                    <Create :deviceTypeList="deviceTypeList" />
                </template>
                <!-- #endregion -->
                <template v-else>
                    <div class="update-info" style="position: absolute; right:0; color: grey; font-size: 14px;line-height:20px">
                        <div v-if="detailForm.lastModifiedByStr">
                            最近编辑人: {{ detailForm.lastModifiedByStr }}
                        </div>
                        <div v-if="detailForm.updateTime">
                            更新于: {{ detailForm.updateTime}}
                        </div>
                    </div>
                    <div class="operators" :style="`top:calc(50% - ${operatorList.length*15+7}px)`" v-if="showOperators">
                        <div
                            class="operator"
                            v-for="item in operatorList"
                            :key="item.type"
                            v-html="item.title"
                            @click.stop="onClickOperator(item.type)"
                        ></div>
                    </div>

                    <div
                        class="operator-cover"
                        @click.stop="showOperators = true"
                        v-if="!showOperators&&operatorList.length>1"
                    >
                        操作
                    </div>
                    <div class="tool-card-title">
                        工单信息
                    </div>
                    <el-form :model="originForm" label-width="60px">
                        <el-row>
                            <el-col :span="6">
                                <el-form-item label="工单号">
                                    <el-input
                                        disabled
                                        v-model="originForm.pitToolsNumber"
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item label="工单类型" prop="workOrderType" label-width="80px">
                                    <el-select
                                        v-model="originForm.workOrderType"
                                        disabled
                                        placeholder=""
                                        style="width:100%"
                                    >
                                        <el-option
                                            v-for="item in MaintainTypeList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                    <BaseInfo
                        :isFinished="isFinished"
                        :originForm="originForm"
                        :failureTypeList="failureTypeList"
                        :failureReasonTypeList="failureReasonTypeList"
                        :failureComponentTypeList="failureComponentTypeList"
                        :deviceTypeList="deviceTypeList"
                        :isEdit="isEdit"
                        ref="baseInfo"
                    />

                    <!-- <InComponent
                        ref="inComponents"
                        :inComponentList="inComponentList"
                        :outComponentList="outComponentList"
                        :originForm="originForm"
                        :isEdit="isEdit"
                    /> -->
                    <!-- <Check :originForm="originForm" :isEdit="isEdit" /> -->
                    <!-- <Assemble
                        ref="assemble"
                        :inComponentList="inComponentList"
                        :outComponentList="outComponentList"
                        :originForm="originForm"
                        :isEdit="isEdit"
                        :isScrew="isScrew"
                    /> -->
                    <MaintainInfo
                        ref="maintainInfo"
                        :inComponentList="inComponentList"
                        :outComponentList="outComponentList"
                        :originForm="originForm"
                        :isEdit="isEdit"
                        :isScrew="isScrew"
                        :repairTypeMap="repairTypeMap"
                    />
                    <!-- <div class="tool-card-title">更换清单</div>
                    <Exchange :originForm="originForm" :isEdit="isEdit" /> -->
                    <LaborHours ref="laborHours" :isFinished="isFinished" :pitToolsId="pitToolsId" :originForm="originForm" :isEdit="isEdit" />
                    <FileManage :isFinished="isFinished" :pitToolsId="pitToolsId" :isEdit="isEdit" />
                    <div class="tool-card-title">完成工单</div>
                    <el-button type="primary" @click="onFinishWorkOrder" v-if="!isFinished">完成工单</el-button>
                    <span v-else>工单已完成</span>
                </template>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import {
  apiFinishPitToolsWorkOrder,
    apiGetPitToolsWorkOrderInfo,
    apiGetRepairTypeList,
    apiPitToolsAssemble,
    apiPitToolsComponentRepair,
    apiUpdatePitToolsWorkOrder,
} from "@/api/downhole-maintain";
import Create from "./components/create.vue";
import BaseInfo from "./components/base.vue";
import InComponent from "./components/inComponent.vue";
import Check from "./components/check.vue";
import Assemble from "./components/assemble.vue";
import MaintainInfo from "./components/maintainInfo.vue";
import Exchange from "./components/exchange.vue";
import LaborHours from './components/labor.vue'
import FileManage from './components/file.vue'
import getDict from "@/utils/getDict";
import { DEVICE_TYPE_SCREW, MaintainTypeEnum, MaintainTypeList } from "@/utils/constant.downhole";
@Component({
    components: {
        Create,
        BaseInfo,
        InComponent,
        Check,
        Assemble,
        MaintainInfo,
        Exchange,
        LaborHours,
        FileManage,
    },
})
export default class extends Vue {
    private showOperators = false;
    private finish = 0;
    private pitToolsId: number | null = 1;
    private originForm: any = {
        workOrderPitToolsDetailList: [],
        pitToolsDeviceAssembleRecord: {
            inComponentList: [],
            outComponentList: [],
        },
        pitToolsRepairCheckData: {
            checkData: {
                replaceItemList: [],
            },
        },
    };
    private inComponentList: any[] = [];
    private outComponentList: any[] = [];
    private failureTypeList: any[] = [];
    private failureReasonTypeList: any[] = [];
    private failureComponentTypeList: any[] = [];
    private deviceTypeList: any[] = [];
    private loading = false;
    private isEdit = false;
    private detailForm: any = {};
    private MaintainTypeList = MaintainTypeList;
    private repairTypeMap = {};
    get isNewOrder() {
        return !this.pitToolsId;
    }
    get isFinished() {
        return !!this.finish;
    }
    get p_MwdUpdate() {
        return this.$checkBtnPermission(`sys:pittools:update`);
    }
    // 是螺杆
    get isScrew(){
        return this.detailForm.deviceType===DEVICE_TYPE_SCREW
    }
    
    get operatorList() {
        return [
            {
                show: !this.isEdit,
                type: "EDIT",
                title: "编辑工单",
            },
            {
                show: this.isEdit,
                type: "SAVE",
                title: "保存编辑",
            },
            {
                show: this.isEdit,
                type: "CANCEL",
                title: "取消编辑",
            },
            // {
            //     show: !this.isFinished,
            //     type: "COMPLETE",
            //     title: "完成工单",
            // },
            // { show: this.isFinished, type: "EXPORT", title: "导出报告" },
            {
                show: true,
                type: "BACK",
                title: "返&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;回",
            },
        ].filter((item) => item.show);
    }
    private onClickOperator(type){
        switch(type){
            case "EDIT":
                this.isEdit = true;
                break;
            case "SAVE":
                this.saveOrder();
                break;
            case "CANCEL":
                this.getWorkOrderPitToolsInfo();
                this.isEdit = false;
                break;
            case "COMPLETE":
                this.onFinishWorkOrder();
                break;
            case "BACK":
                this.showOperators = false;
                break;
            default:
                return;
        }
    }
    private onFinishWorkOrder() {
        // if (this.originForm.status == 0) {
        //     this.$message.error("当前维修状态为未完成，请修改！");
        // } else {
            this.$confirm("确认完成工单?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                apiFinishPitToolsWorkOrder({
                    pitToolsId: this.pitToolsId,
                    finish: 1,
                }).then(()=>this.getWorkOrderPitToolsInfo())
            });
        // }
    }
    private async saveOrder(){
        const maintainInfoRefs:any = this.$refs.maintainInfo;
        const hasAssemble = maintainInfoRefs.isAssembleChange;
        const hasRepair = maintainInfoRefs.isRepairChange;
        const workOrderType = this.originForm.workOrderType;
        // 组装有可能会改序列号
        // const baseInfoRef:any = this.$refs.baseInfo;
        // const baseInfoValid = await baseInfoRef.validateForm();
        // if(!baseInfoValid){
        //     this.$message.error('基本信息中的维修仪器已被使用！')
        //     return;
        // }
        // 只有组装才会校验序列号 前期都可编辑，都需要校验
        if(true){
            const validateResult = await maintainInfoRefs.validateAssembleSN();
            if(validateResult?.length>0) {
                this.$message.error('维修信息中有已组装在其它仪器中的部件！')
                return;
            }
        }
        // trim工时列表
        // this.originForm.repairMemberList = this.originForm.repairMemberList.filter(item=>item.memberId&&item.name);
        // 保证record和componentList相关数据一致
        const pitToolsDeviceAssembleRecord = this.originForm.pitToolsDeviceAssembleRecord
        let componentList: any[] = [];
        if(workOrderType === MaintainTypeEnum.ASSEMBLE){
            componentList = pitToolsDeviceAssembleRecord.outComponentList;
        } else {
            componentList = pitToolsDeviceAssembleRecord.inComponentList;
        }
        componentList.forEach(item => {
            if(item.invName==='万向轴壳体'){
                pitToolsDeviceAssembleRecord.angle = item.angle;
                pitToolsDeviceAssembleRecord.curve = item.curve;
            }
            if(item.invName==='定子'){
                pitToolsDeviceAssembleRecord.rubberType = item.rubberType;
            }
        });
        // assemble
        if(hasAssemble){
            // 组装时添加deviceType字段
            // pitToolsDeviceAssembleRecord.deviceType = this.detailForm.deviceType;
            await apiPitToolsAssemble(workOrderType, pitToolsDeviceAssembleRecord);
        }
        // repair
        if(hasRepair){
            let pitToolsId = this.pitToolsId;
            let parentSerialNumber =
                this.originForm.workOrderPitToolsDetailList[0].serialNumber;
            let repairList = maintainInfoRefs.tableData;
            await apiPitToolsComponentRepair({
                pitToolsId,
                parentSerialNumber,
                repairList,
            })
        }
        maintainInfoRefs.isAssembleChange = false;
        maintainInfoRefs.isRepairChange = false;
        apiUpdatePitToolsWorkOrder(this.originForm).then(() => {
            this.getWorkOrderPitToolsInfo();
            this.isEdit = false;
            this.$message.success("更新成功！");
        });
    }
    private async mounted() {
        this.pitToolsId = Number(this.$route.query.pitToolsId);
        this.loading = true;
        try {
            [
                this.failureTypeList,
                this.failureComponentTypeList,
                this.failureReasonTypeList,
                this.deviceTypeList,
            ] = await getDict([13, 14, 15, 18]);
            await apiGetRepairTypeList({}).then(res=>{
                this.repairTypeMap = res.data.data || {};
            })
            if (this.pitToolsId) {
                await this.getWorkOrderPitToolsInfo();
                (this.$refs.laborHours as any).erpLabor = this.originForm.pitToolsNumber;
            }
        } finally {
            this.loading = false;
        }
    }
    private getWorkOrderPitToolsInfo() {
        return apiGetPitToolsWorkOrderInfo({ pitToolsId: this.pitToolsId }).then((res) => {
            const data = res.data.data || {};
            this.originForm = data;
            this.finish = data.finish;
            // this.initOriginForm();
            this.detailForm = data.workOrderPitToolsDetailList?.[0] || {};
            this.inComponentList =
                this.originForm.pitToolsDeviceAssembleRecord.inComponentList;
            this.outComponentList =
                this.originForm.pitToolsDeviceAssembleRecord.outComponentList;
            (this.$refs.baseInfo as any).originSerialNumber = this.detailForm.serialNumber;
        });
    }
    private initOriginForm() {
        if (!this.originForm.workOrderPitToolsDetailList?.length) {
            this.originForm.workOrderPitToolsDetailList = [];
        }
        if (!this.originForm.pitToolsDeviceAssembleRecord) {
            this.originForm.pitToolsDeviceAssembleRecord = {
                inComponentList: [],
                outComponentList: [],
            };
        }
        if (!this.originForm.pitToolsDeviceAssembleRecord.inComponentList) {
            this.originForm.pitToolsDeviceAssembleRecord.inComponentList = [];
        }
        if (!this.originForm.pitToolsDeviceAssembleRecord.outComponentList) {
            this.originForm.pitToolsDeviceAssembleRecord.outComponentList = [];
        }
        if (!this.originForm.pitToolsRepairCheckData) {
            this.originForm.pitToolsRepairCheckData = {
                checkData: {
                    replaceItemList: [],
                },
            };
        }
        if (!this.originForm.pitToolsRepairCheckData.checkData) {
            this.originForm.pitToolsRepairCheckData.checkData = {
                replaceItemList: [],
            };
        }
        if (!this.originForm.pitToolsRepairCheckData.checkData.replaceItemList) {
            this.originForm.pitToolsRepairCheckData.checkData.replaceItemList = [];
        }
    }
    onAddWorkOrderSuccess(pitToolsId: number) {
        this.pitToolsId = pitToolsId;
    }
    onFinishWorkOrderSuccess(finish: number) {
        this.finish = finish;
    }
    onGetFinish(finish: number) {
        this.finish = finish;
    }
}
</script>
<style lang="scss" scoped>
.operators {
    position: fixed;
    right: 30px;
    top: calc(50% - 97px);
    display: flex;
    flex-direction: column;
    justify-content: center;

    padding: 6px 0;
    margin: 2px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .operator {
        line-height: 30px;
        padding: 0 10px;
        margin: 0;
        font-size: 14px;
        text-align: center;
        color: #606266;
        cursor: pointer;
        outline: none;
        &:hover {
            background-color: #ecf5ff;
            color: #66b1ff;
        }
    }
}
.operator-cover {
    position: fixed;
    right: 30px;
    top: calc(50% - 40px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    color: white;
    padding-left: 6px;
    font-size: 14px;
    line-height: 2;
    width: 26px;
    height: 80px;
    background: rgba(67, 86, 152, 0.8);
    cursor: pointer;
    user-select: none;
    .operator {
        font-size: 14px;
        line-height: 1.4;
        border: 1px solid grey;
        padding: 4px;
    }
}
.step-container {
    display: flex;
    justify-content: space-between;

    .step {
        width: 140px;
        height: 60px;
        line-height: 58px;
        text-align: center;
        font-size: 18px;
        border: 1px solid #ccc;
        font-weight: 500;
        border-radius: 16px;
        cursor: pointer;
        user-select: none;
        &.active {
            background: pink;
            color: white;
            border-color: transparent;
        }
    }
}
.component-container {
    padding: 20px 0;
}
</style>
<style lang="scss">
.tool-card-title {
    padding-bottom: 20px;
    font-weight: 400;
    font-size: 20px;
    color: rgb(67, 86, 152);
    margin-left: -26px;
    &::before {
        display: inline-block;
        width: 16px;
        height: 24px;
        vertical-align: bottom;
        background: rgb(67, 86, 152);
        margin-right: 10px;
        content: "";
    }
}
</style>
<style lang="scss" scoped>
.tool-maintain-container {
    padding: 20px 40px;
    padding-right: 100px;
    margin: 12px;
    background: white;
    overflow: hidden;
    .tool-maintain-title {
        font-size: 16px;
        font-weight: 600;
    }
    .tool-card-subtitle {
        padding-bottom: 20px;
        font-weight: 400;
        font-size: 16px;
        color: rgb(67, 86, 152);
        margin-left: -12px;
        &::before {
            display: inline-block;
            margin-right: 6px;
            content: "-";
        }
    }
    .is-process {
        color: rgb(67, 86, 152) !important;
        font-weight: normal !important;
        .el-step__icon {
            border-color: rgb(67, 86, 152) !important;
        }
    }
    .active-step {
        .is-process {
            color: rgb(67, 86, 152) !important;
            font-weight: bold !important;
            .el-step__icon {
                border-color: rgb(67, 86, 152) !important;
            }
        }
    }
}
.el-table__expanded-cell {
    background: rgb(235, 238, 245);
}
.el-table__expanded-cell:hover {
    background: rgb(235, 238, 245) !important;
}
.active-step .el-step__title {
    color: rgb(67, 86, 152) !important;
    font-weight: bold;
}
</style>
