<template>
    <div class="app-container" style="height: 100%; overflow: hidden">
        <div class="app-card" style="height: 100%">
            <div class="app-card-title">井下工具台账</div>
            <el-row :gutter="20">
                <el-form :model="form" label-width="80px">
                    <el-col :span="6">
                        <el-form-item label="时间周期">
                            <el-date-picker
                                clearable
                                placement="bottom-start"
                                style="width: 100%"
                                value-format="yyyy-MM-dd"
                                v-model="confirmDate"
                                class="product-date-picker"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="统计维度">
                            <el-select style="width: 100%" v-model="form.type">
                                <el-option label="年" value="year"></el-option>
                                <el-option label="月" value="month"></el-option>
                                <el-option
                                    label="季"
                                    value="quarter"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="工具分类">
                            <el-select
                                multiple
                                clearable
                                :collapse-tags="true"
                                style="width: 100%"
                                v-model="form.deviceTypeList"
                            >
                                <el-option
                                    v-for="item in deviceTypeList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item label-width="0">
                            <el-button type="primary" @click="onSearch"
                                >查询</el-button
                            >
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            <el-row :gutter="10">
                <el-col
                    style="margin-bottom: 10px"
                    :span="12"
                    v-for="item in tableDataList"
                    :key="item.deviceType"
                >
                    <chart-table
                        :tableData="item.statisticsVos"
                        :chartKey="item.deviceType"
                    ></chart-table>
                </el-col>
            </el-row>
        </div>
    </div>
</template>
<script lang="ts">
import { Component } from "vue-property-decorator";
import Vue from "vue";
import ChartTable from "./chart&table.vue";
import { apiGetMwdWorkOrderStatistics } from "@/api/tool-mantain";
import getDict from "@/utils/getDict";
@Component({ components: { ChartTable } })
export default class extends Vue {
    private form: any = { deviceTypeList: [], type: "" };
    private confirmDate: string[] = ["", ""];
    private tableDataList: any[] = [];
    private deviceTypeList: any[] = [];
    private async mounted() {
        [this.deviceTypeList] = await getDict([18]);
    }
    private onSearch() {
        apiGetMwdWorkOrderStatistics(this.form).then((res) => {
            const data = res.data.data || [];
            this.tableDataList = data.filter((item: any) => item);
        });
    }
}
</script>
<style lang="scss" scoped></style>
<style lang="scss">
.product-date-picker .el-range-separator {
    width: 20px !important;
}
</style>
