export const colorList = {
  PM: 'rgb(79,129,189)',
  DF: 'rgb(192,80,77)',
  SF: 'rgb(155,187,89)',
  RatioDF: 'rgb(128,100,162)',
  TargetLine: 'rgb(75,172,198)'
}
export const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', "Jul", 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    }
  ],
  yAxis: [
    { type: 'value' },
    {
      type: 'value',
      position: 'right',
      splitLine: { show: false },
      axisLabel: {
        formatter(value: any) {
          return `${value * 100}%`
        }
      }
    }
  ],
  series: [
    {
      name: 'PM',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: 'DF',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: 'SF',
      type: 'bar',
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: 'Ratio of DF',
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      yAxisIndex: 1,
      data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
      name: 'Target Line',
      type: 'line',
      emphasis: {
        focus: 'series'
      },
      yAxisIndex: 1,
      data: [150, 232, 201, 154, 190, 330, 410]
    },
  ],
  animation: false
};