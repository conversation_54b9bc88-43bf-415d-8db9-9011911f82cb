import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { Message } from 'element-ui'
import { Route } from 'vue-router'
import { UserModule } from '@/store/modules/user'
import { PermissionModule } from '@/store/modules/permission'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/repair/add']
const tailor = '达坦综合管理平台'
const getPageTitle = (key: string) => {
  if (key) {
    return `${key} - ${tailor}`
  }
  return `${tailor}`
}

router.beforeEach(async (to: Route, _: Route, next: any) => {
  //TODO: 切换路由立即校验权限
  // Start progress bar
  NProgress.start()
  // Determine whether the user has logged in
  // if (UserModule.token) {
  //   if (to.path === '/login' || to.path === '/') {
  //     // login 或 '/'， 则跳转至默认路由
  //     try {
  //       await UserModule.GetUserInfo()
  //       const roles = UserModule.roles
  //       PermissionModule.GenerateRoutes(roles)
  //       router.addRoutes(PermissionModule.dynamicRoutes)
  //       // Hack: ensure addRoutes is complete
  //       // Set the replace: true, so the navigation will not leave a history record
  //       next({ path: PermissionModule.defaultPath })
  //     } catch (err) {
  //       // Remove token and redirect to login page
  //       UserModule.ResetToken()
  //       Message.error(err || 'Has Error')
  //       next(`/login?redirect=${to.path}`)
  //       NProgress.done()
  //     }
  //   } else {
  //     // Check whether the user has obtained his permission roles
  //     if (UserModule.roles.length === 0) {
  //       try {
  //         await UserModule.GetUserInfo()
  //         const roles = UserModule.roles
  //         PermissionModule.GenerateRoutes(roles)
  //         router.addRoutes(PermissionModule.dynamicRoutes)
  //         // Hack: ensure addRoutes is complete
  //         // Set the replace: true, so the navigation will not leave a history record
  //         next({ ...to, replace: true })
  //       } catch (err) {
  //         // Remove token and redirect to login page
  //         UserModule.ResetToken()
  //         Message.error(err || 'Has Error')
  //         next(`/login?redirect=${to.path}`)
  //         NProgress.done()
  //       }
  //     } else {
  //       next()
  //     }
  //   }
  // } else {
  //   // Has no token
  //   if (whiteList.indexOf(to.path) !== -1) {
  //     // In the free login whitelist, go directly
  //     next()
  //   } else {
  //     // Other pages that do not have permission to access are redirected to the login page.
  //     next(`/login?redirect=${to.path}`)
  //     NProgress.done()
  //   }
  // }
  if (UserModule.token) {
    if (to.path === '/login') {
      // If is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      // Check whether the user has obtained his permission roles
      if (UserModule.menuList.length === 0) {
        try {
          await UserModule.GetUserInfo();
          let menuList = UserModule.menuList;
          PermissionModule.GenerateRoutes(menuList)
          router.addRoutes(PermissionModule.routes)
          // Hack: ensure addRoutes is complete
          // Set the replace: true, so the navigation will not leave a history record
          next({ path: to.path === '/' ? PermissionModule.defaultPath : to.fullPath, replace: true })
        } catch (err) {
          // Remove token and redirect to login page
          UserModule.ResetToken()
          console.log(err)
          Message.error(err as string || 'Has Error')
          next(`/login?redirect=${to.fullPath}`)
          NProgress.done()
        }
      } else {
        if (to.path === '/') {
          next(PermissionModule.defaultPath)
        } {
          next()
        }
      }
    }
  } else {
    // Has no token
    if (whiteList.indexOf(to.path) !== -1) {
      // In the free login whitelist, go directly
      next()
    } else {
      // Other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

router.afterEach((to: Route) => {
  // Finish progress bar
  // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
  NProgress.done()

  // set page title
  document.title = getPageTitle(to.meta?.title)
})
