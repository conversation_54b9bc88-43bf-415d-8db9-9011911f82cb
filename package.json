{"name": "tartan-admin", "version": "1.0.0", "private": true, "scripts": {"serve": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "serve_local": "cross-env NODE_OPTIONS=--openssl-legacy-provider process.env.LOCAL_DEVELOPMENT=true vue-cli-service serve", "build": "vue-cli-service build", "build:report": "cross-env process.env.BUNDLE_ANALYZER=true vue-cli-service build", "lint": "vue-cli-service lint", "plop": "plop", "svg": "vsvg -s ./src/icons/svg -t ./src/icons/components --ext ts --es6"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g6": "^4.8.21", "@packy-tang/vue-tinymce": "^1.1.2", "@types/sortablejs": "^1.15.8", "axios": "^0.21.0", "browser-image-compression": "^2.0.2", "clipboard": "^2.0.6", "core-js": "^3.7.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "dompurify": "^3.2.5", "echarts": "^5.2.1", "element-ui": "^2.14.0", "file-saver": "^2.0.2", "fuse.js": "^6.4.3", "insert-css": "^2.0.0", "js-cookie": "^2.2.1", "moment": "^2.30.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "quill": "^2.0.3", "quill-image-resize-module": "^3.0.0", "sortablejs": "^1.15.6", "tinymce": "^5.10.2", "tinymce-vue": "^1.0.0", "tslib": "^2.8.1", "vant": "^2.13.6", "vue": "^2.6.12", "vue-avatar": "^2.3.3", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.0.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.4.9", "vue-svgicon": "^3.2.9", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "vuex-module-decorators": "^1.0.1", "xlsx": "^0.16.8", "xlsx-style": "^0.8.13"}, "devDependencies": {"@types/clipboard": "^2.0.1", "@types/compression": "^1.7.0", "@types/echarts": "^4.9.0", "@types/express": "^4.17.8", "@types/file-saver": "^2.0.1", "@types/js-cookie": "^2.2.6", "@types/node": "^14.14.6", "@types/nprogress": "^0.2.0", "@types/webpack-env": "^1.15.3", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "@vue/cli-plugin-babel": "^4.5.8", "@vue/cli-plugin-eslint": "^4.5.8", "@vue/cli-plugin-router": "^4.5.8", "@vue/cli-plugin-typescript": "^4.5.8", "@vue/cli-plugin-vuex": "^4.5.8", "@vue/cli-service": "^4.5.8", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^7.0.0", "babel-loader": "^8.1.0", "concurrently": "^5.3.0", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.2", "eslint-plugin-vue": "^7.1.0", "lint-staged": "^10.5.1", "plop": "^4.0.1", "progress-bar-webpack-plugin": "^2.1.0", "sass": "^1.29.0", "sass-loader": "^10.0.5", "style-resources-loader": "^1.3.3", "swagger-routes-express": "^3.2.1", "ts-jest": "^26.4.3", "ts-node-dev": "^1.0.0", "typescript": "3.8.3", "url-loader": "^4.1.1", "vue-cli-plugin-element": "^1.0.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.12", "webpack": "^4.4.2", "webpack-bundle-analyzer": "^4.10.2", "webpackbar": "^6.0.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}