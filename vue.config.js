// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');
const name = 'Tartan Admin';
const IS_DEV = process.env.NODE_ENV === 'development';
const BUNDLE_ANALYZER = process.env.BUNDLE_ANALYZER === 'true';
const isLocalDevelop = process.env.LOCAL_DEVELOPMENT === 'true';
// 版本
// TODO: 活见鬼, 这个玩意儿会影响打包产物的hash
// const VersionPlugin = require('./build/version.js');
const version = Date.now();
// 打包分析
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
// 进度条
// const chalk = require('chalk');
// const ProgressBarPlugin = require('progress-bar-webpack-plugin');
// const WebpackBar = require("webpackbar");
const plugins = [
  new (require('webpack').ProvidePlugin)({
    'window.Quill': 'quill/dist/quill.js',
    'Quill': 'quill/dist/quill.js'
  }),
  // new VersionPlugin(version),
  // new ProgressBarPlugin({
  //   format: '  build [:bar] ' + chalk.green.bold(':percent') + ' (:elapsed seconds)',
  //   clear: false
  // }),
  // new WebpackBar()
]
if(BUNDLE_ANALYZER){
  plugins.push(
    new BundleAnalyzerPlugin({
      openAnalyzer: false,
    })
  )
}
module.exports = {
  publicPath: '/',
  lintOnSave: IS_DEV,
  productionSourceMap: false,
  devServer: {
    port: 8080,
    host: '0.0.0.0',
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    progress: false,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: isLocalDevelop ? `http://localhost:9000/` : `http://************:9000/`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: '/',
        },
      },
      '/imgs/':{
        target: `http://*************:8200/imgs/`,
        // target: `http://localhost:9000/`,
        changeOrigin: true,
        pathRewrite: {
          '^/imgs/': '/',
        },
      }
    },
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [
        path.resolve(__dirname, 'src/styles/_variables.scss'),
        path.resolve(__dirname, 'src/styles/_mixins.scss'),
      ],
    },
  },
  chainWebpack(config) {
    // provide the app's title in html-webpack-plugin's options list so that
    // it can be accessed in index.html to inject the correct title.
    // https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-plugin
    config.plugin('html').tap(args => {
      args[0].title = name;
      return args;
    });
    config.module.rule('images').
        use('url-loader').
        loader('url-loader').
        tap(options => Object.assign(options, {limit: 10240}));
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial',
      },
    ]);

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch');

    // https://webpack.js.org/configuration/devtool/#development
    // Change development env source map if you want.
    // The default in vue-cli is 'eval-cheap-module-source-map'.
    // config
    //   .when(process.env.NODE_ENV === 'development',
    //     config => config.devtool('eval-cheap-source-map')
    //   )
    config.when(!IS_DEV,
        config => {
          config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial', // only package third parties that are initially dependent
              },
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
              },
              antv: {
                name: 'chunk-antv', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?@antv(.*)/, // in order to adapt to cnpm
              },
              echarts: {
                name: 'chunk-echarts', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?echarts(.*)/, // in order to adapt to cnpm
              },
              xlsx: {
                name: 'chunk-xlsx', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?xlsx(.*)/, // in order to adapt to cnpm
              },
              moment: {
                name: 'chunk-moment', // split elementUI into a single package
                priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                test: /[\\/]node_modules[\\/]_?moment(.*)/, // in order to adapt to cnpm
              },
              commons: {
                name: 'chunk-commons',
                test: path.resolve(__dirname, 'src/components'),
                minSize: 1000,
                minChunks: 5,
                priority: -20
              },
            },
          });
          // https://webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single');
        },
    );
    config.plugin('define').tap(args=>{
      args[0]['process.env'] = {
        ...args[0]['process.env'],
        version
      };
      return args;
    })
  },
  configureWebpack:{
    plugins,
    externals: {
      // xlsx-style的bug
      './cptable': 'var cptable'
    }
  }
};
